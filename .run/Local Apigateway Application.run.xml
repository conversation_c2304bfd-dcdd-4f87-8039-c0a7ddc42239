<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Local Apigateway Application" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
    <option name="ACTIVE_PROFILES" value="local" />
    <classpathModifications>
      <entry path="$PROJECT_DIR$/api-gateway/build/resources/test" />
    </classpathModifications>
    <module name="boss-store.api-gateway.test" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="com.sast.store.apigateway.ApigatewayApplication" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>