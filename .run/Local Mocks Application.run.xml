<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Local Mocks Application" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
    <module name="boss-store.testing-localmocks.test" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="com.sast.store.testing.localmocks.LocalMocksApplication" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.sast.store.testing.localmocks.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>