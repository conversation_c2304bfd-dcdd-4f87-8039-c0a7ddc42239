<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Local Contractmanagement Application" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
    <option name="ACTIVE_PROFILES" value="local" />
    <classpathModifications>
      <entry path="$PROJECT_DIR$/contractmanagement/build/resources/test" />
    </classpathModifications>
    <module name="boss-store.contractmanagement.test" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="com.sast.store.contractmanagement.ContractmanagementApplication" />
    <option name="VM_PARAMETERS" value="-Daws.maxAttempts=20" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>