plugins {
    id("bossstore.subproject-conventions")
    `java-library`
    `java-test-fixtures`
	id("org.springframework.boot") version "3.4.3" apply false
	id("io.swagger.core.v3.swagger-gradle-plugin") version "2.2.28"
}

dependencies {
    implementation(project(":commons:tenant"))
	implementation(platform(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES))
	implementation("jakarta.validation:jakarta.validation-api")
	implementation("jakarta.ws.rs:jakarta.ws.rs-api")
	implementation("org.springframework.boot:spring-boot-starter-validation")
	implementation("io.swagger.core.v3:swagger-annotations:2.2.28")
	implementation("com.fasterxml.jackson.core:jackson-annotations")
	implementation("com.fasterxml.jackson.core:jackson-databind")
	testImplementation("org.springframework.boot:spring-boot-starter-test")

    testFixturesImplementation(platform(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES))
    testFixturesImplementation("org.junit.jupiter:junit-jupiter-api")
    testFixturesImplementation("org.wiremock:wiremock-standalone:3.12.0")
}

tasks.resolve {
	outputFileName = "${project.name}"
	outputFormat = io.swagger.v3.plugins.gradle.tasks.ResolveTask.Format.JSONANDYAML
	prettyPrint = true
	sortOutput = true
	classpath = sourceSets["main"].runtimeClasspath
	buildClasspath = configurations["swaggerPluginDependencies"]
	resourcePackages = setOf("com.sast.store.ordermanagement.api")
	outputDir = file("$rootDir/${project.name}/build/swagger")
}

tasks.named("compileJava") {
    finalizedBy("resolve")
}
