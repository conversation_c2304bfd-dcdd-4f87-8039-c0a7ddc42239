package com.sast.store.ordermanagement.api;

import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotBlank;
import lombok.Builder;

@Builder
public record CheckoutInformationDto(
        @NotBlank String paymentMethodId,
        @Nullable BoschSepaCreditInformation boschSepaCreditInformation,
        @Nullable BoschAchCreditInformation boschAchCreditInformation
) {
    @Builder
    public record BoschSepaCreditInformation(
            @NotBlank String accountHolder,
            @NotBlank String bankName,
            @NotBlank String iban,
            @Nullable String bic
    ) { }

    @Builder
    public record BoschAchCreditInformation(
            @NotBlank String accountHolder,
            @NotBlank String accountNumber,
            @NotBlank String routingNumber,
            @NotBlank String bic,
            @NotBlank String bankName
    ) { }
}