package com.sast.store.ordermanagement.api;

import java.util.List;

public record PaymentMethodConfigDto(List<CheckoutInformationDto> paymentMethods) {

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {
        private List<CheckoutInformationDto> paymentMethods;

        private Builder() {
        }

        public Builder paymentMethods(final List<CheckoutInformationDto> paymentMethods) {
            this.paymentMethods = paymentMethods;
            return this;
        }

        public PaymentMethodConfigDto build() {
            return new PaymentMethodConfigDto(paymentMethods);
        }
    }
}