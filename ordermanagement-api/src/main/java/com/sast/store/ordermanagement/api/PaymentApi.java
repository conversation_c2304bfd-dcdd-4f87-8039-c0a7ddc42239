package com.sast.store.ordermanagement.api;

import com.sast.store.commons.tenant.api.Tenant;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

@Path("/payments")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public interface PaymentApi {

    @GET
    @Path("/{paymentId}")
    PaymentDto getPayment(@NotNull @HeaderParam("X-Tenant") Tenant tenantId, @PathParam("paymentId") String paymentId);

    @GET
    @Path("/paymentConfig")
    PaymentMethodConfigDto getPaymentConfig(@NotNull @HeaderParam("X-Tenant") Tenant tenantId);

}
