package com.sast.store.ordermanagement.api;

public record AddonLineItemDto(
    String addonLineItemId,
    String parentLineItemId,
    String name,
    Long quantity,
    AddonVariant addonVariant,
    MoneyDto itemPrice,
    MoneyDto totalPrice) {

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {
        private String addonLineItemId;
        private String parentLineItemId;
        private String name;
        private Long quantity;
        private AddonVariant addonVariant;
        private MoneyDto itemPrice;
        private MoneyDto totalPrice;

        private Builder() {
        }

        public Builder addonLineItemId(final String addonLineItemId) {
            this.addonLineItemId = addonLineItemId;
            return this;
        }

        public Builder parentLineItemId(final String parentLineItemId) {
            this.parentLineItemId = parentLineItemId;
            return this;
        }

        public Builder itemPrice(final MoneyDto itemPrice) {
            this.itemPrice = itemPrice;
            return this;
        }

        public Builder totalPrice(final MoneyDto totalPrice) {
            this.totalPrice = totalPrice;
            return this;
        }

        public Builder name(final String name) {
            this.name = name;
            return this;
        }

        public Builder quantity(final Long quantity) {
            this.quantity = quantity;
            return this;
        }

        public Builder addonVariant(final AddonVariant addonVariant) {
            this.addonVariant = addonVariant;
            return this;
        }

        public AddonLineItemDto build() {
            return new AddonLineItemDto(addonLineItemId, parentLineItemId, name, quantity, addonVariant, itemPrice, totalPrice);
        }
    }

    public record AddonVariant(
        String sku,
        String name) {

        public static Builder builder() {
            return new Builder();
        }

        public static final class Builder {
            private String sku;
            private String name;

            private Builder() {
            }

            public Builder sku(final String sku) {
                this.sku = sku;
                return this;
            }

            public Builder name(final String name) {
                this.name = name;
                return this;
            }

            public AddonVariant build() {
                return new AddonVariant(sku, name);
            }
        }
    }
}