package com.sast.store.ordermanagement.api;

import java.net.URI;

public record LocalizedLinkDto(
    String name,
    URI url,
    LinkType linkType) {

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {
        private String name;
        private URI url;
        private LinkType linkType;

        private Builder() {
        }

        public Builder name(final String name) {
            this.name = name;
            return this;
        }

        public Builder url(final URI url) {
            this.url = url;
            return this;
        }

        public Builder linkType(final LinkType linkType) {
            this.linkType = linkType;
            return this;
        }

        public LocalizedLinkDto build() {
            return new LocalizedLinkDto(name, url, linkType);
        }
    }

    public enum LinkType {
        SOFTWARE_LICENSE, PRIVACY_POLICY
    }
}
