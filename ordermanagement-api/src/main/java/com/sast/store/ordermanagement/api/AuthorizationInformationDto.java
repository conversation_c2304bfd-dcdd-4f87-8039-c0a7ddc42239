package com.sast.store.ordermanagement.api;

import java.net.URI;

public record AuthorizationInformationDto(URI redirectUrl, String paymentId) {

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {
        private URI redirectUrl;
        private String paymentId;

        private Builder() {
        }

        public Builder redirectUrl(final URI redirectUrl) {
            this.redirectUrl = redirectUrl;
            return this;
        }

        public Builder paymentId(final String paymentId) {
            this.paymentId = paymentId;
            return this;
        }

        public AuthorizationInformationDto build() {
            return new AuthorizationInformationDto(redirectUrl, paymentId);
        }
    }
}