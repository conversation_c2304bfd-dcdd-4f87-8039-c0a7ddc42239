package com.sast.store.ordermanagement.api;

import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

@Path("/commercetools")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public interface CommercetoolsExtensionApi {

    @POST
    @Path("/cartvalidation")
    void validateCart(String payload);
}
