package com.sast.store.ordermanagement.api;

import com.sast.store.commons.tenant.api.Tenant;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import java.util.List;

@Path("/invoices")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public interface InvoiceApi {

    @GET
    @Path("/")
    List<InvoiceDto> listAllInvoices(@NotNull @HeaderParam("X-Tenant") Tenant tenantId);

    @Operation(responses = {
        @ApiResponse(responseCode = "default", content = @Content(schema = @Schema(type = "string", format = "binary")))
    })
    @GET
    @Path("/{documentNumber}")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    Response download(@NotNull @HeaderParam("X-Tenant") Tenant tenantId, @NotNull @PathParam("documentNumber") String documentNumber);

    @GET
    @Path("/{documentNumber}/64")
    @Produces({MediaType.TEXT_PLAIN})
    Response download64(@NotNull @HeaderParam("X-Tenant") Tenant tenantId, @NotNull @PathParam("documentNumber") String documentNumber);
}
