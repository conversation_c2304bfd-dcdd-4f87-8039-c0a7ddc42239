package com.sast.store.ordermanagement.api;

import com.sast.store.commons.tenant.api.Tenant;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;

@Path("/cart")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public interface CartApi {

    @GET
    @Path("/")
    CartDto getCurrentCart(@NotNull @HeaderParam("X-Tenant") Tenant tenantId,
        @QueryParam("language") @DefaultValue("de") String languageCode,
        @QueryParam("country") @DefaultValue("DE") String countryCode);

    @POST
    @Path("/")
    CartDto addToCurrentCart(@NotNull @HeaderParam("X-Tenant") Tenant tenantId, @Valid CartAddDto cartUpdateDto);

    @PUT
    @Path("/")
    CartDto updateCurrentCart(@NotNull @HeaderParam("X-Tenant") Tenant tenantId, @Valid CartUpdateDto cartUpdateDto);

    @DELETE
    @Path("/")
    void clearCurrentCart(@NotNull @HeaderParam("X-Tenant") Tenant tenantId,
        @QueryParam("country") @DefaultValue("DE") String countryCode);

    @GET
    @Path("/checkout")
    AuthorizationInformationDto checkoutCurrentCart(@NotNull @HeaderParam("X-Tenant") Tenant tenantId,
        @QueryParam("country") @DefaultValue("DE") String countryCode,
        @QueryParam("paymentMethodId") @NotNull String paymentMethodId);

    @POST
    @Path("/checkout")
    AuthorizationInformationDto checkoutCart(@NotNull @HeaderParam("X-Tenant") Tenant tenantId, @Valid CartCheckoutDto cartCheckoutDto);
}
