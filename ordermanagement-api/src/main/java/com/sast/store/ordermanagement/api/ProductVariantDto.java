package com.sast.store.ordermanagement.api;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;

import java.net.URI;
import java.time.Period;
import java.util.List;

@Builder
public record ProductVariantDto(
    String name,
    String licenseType,
    String sku,
    @Schema(implementation = String.class) Period runtime,
    List<LocalizedLinkDto> agreements,
    URI priceList,
    List<Addon> addons
) {

    @Builder
    public record Addon(String id) {
    }
}
