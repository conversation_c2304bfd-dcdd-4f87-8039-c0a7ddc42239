package com.sast.store.ordermanagement.api;

public record LineItemDto(
        String lineItemId,
        String name,
        String productId,
        String sku,
        ProductVariantDto variant,
        MoneyDto itemPrice,
        Long quantity,
        MoneyDto totalPrice
) {
    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {
        private String lineItemId;
        private String name;
        private String productId;
        // FIXME move sku into ProductVariantDto
        private String sku;
        private ProductVariantDto variant;
        private MoneyDto itemPrice;
        private Long quantity;
        private MoneyDto totalPrice;

        private Builder() {
        }

        public Builder lineItemId(final String lineItemId) {
            this.lineItemId = lineItemId;
            return this;
        }

        public Builder name(final String name) {
            this.name = name;
            return this;
        }

        public Builder productId(final String productId) {
            this.productId = productId;
            return this;
        }

        public Builder sku(final String sku) {
            this.sku = sku;
            return this;
        }

        public Builder variant(final ProductVariantDto variant) {
            this.variant = variant;
            return this;
        }

        public Builder itemPrice(final MoneyDto totalPrice) {
            this.itemPrice = totalPrice;
            return this;
        }

        public Builder quantity(final Long quantity) {
            this.quantity = quantity;
            return this;
        }

        public Builder totalPrice(final MoneyDto totalPrice) {
            this.totalPrice = totalPrice;
            return this;
        }

        public LineItemDto build() {
            return new LineItemDto(lineItemId, name, productId, sku, variant, itemPrice, quantity, totalPrice);
        }
    }
}
