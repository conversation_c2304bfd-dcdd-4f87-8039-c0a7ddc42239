package com.sast.store.ordermanagement.api;

import java.time.ZonedDateTime;
import java.util.List;

public record OrderDto(
        String orderId,
        List<LineItemDto> lineItems,
        MoneyDto totalPrice,
        ZonedDateTime createdAt
) {

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {
        private String orderId;
        private List<LineItemDto> lineItems;
        private MoneyDto totalPrice;
        private ZonedDateTime createdAt;

        private Builder() {
        }

        public Builder orderId(final String orderId) {
            this.orderId = orderId;
            return this;
        }

        public Builder lineItems(final List<LineItemDto> lineItems) {
            this.lineItems = lineItems;
            return this;
        }

        public Builder totalPrice(final MoneyDto totalPrice) {
            this.totalPrice = totalPrice;
            return this;
        }

        public Builder createdAt(final ZonedDateTime createdAt) {
            this.createdAt = createdAt;
            return this;
        }

        public OrderDto build() {
            return new OrderDto(orderId, lineItems, totalPrice, createdAt);
        }
    }
}
