package com.sast.store.ordermanagement.api;

public record PaymentDto(String paymentId, String paymentStatus, String orderId) {

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {
        private String paymentId;
        private String paymentStatus;
        private String orderId;

        private Builder() {
        }

        public Builder paymentId(final String paymentId) {
            this.paymentId = paymentId;
            return this;
        }

        public Builder paymentStatus(final String paymentStatus) {
            this.paymentStatus = paymentStatus;
            return this;
        }

        public Builder orderId(final String orderId) {
            this.orderId = orderId;
            return this;
        }

        public PaymentDto build() {
            return new PaymentDto(paymentId, paymentStatus, orderId);
        }
    }
}