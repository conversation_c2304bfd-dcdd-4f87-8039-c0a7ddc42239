package com.sast.store.ordermanagement.api;

public record AddressDto(
        String city,
        String postalCode,
        String streetName,
        String streetNumber,
        String state,
        String region,
        String country,
        String email
) {
    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {
        private String city;
        private String postalCode;
        private String streetName;
        private String streetNumber;
        private String state;
        private String region;
        private String country;
        private String email;

        private Builder() {
        }

        public Builder city(final String city) {
            this.city = city;
            return this;
        }

        public Builder postalCode(final String postalCode) {
            this.postalCode = postalCode;
            return this;
        }

        public Builder streetName(final String streetName) {
            this.streetName = streetName;
            return this;
        }

        public Builder streetNumber(final String streetNumber) {
            this.streetNumber = streetNumber;
            return this;
        }

        public Builder state(final String state) {
            this.state = state;
            return this;
        }

        public Builder region(final String region) {
            this.region = region;
            return this;
        }

        public Builder country(final String country) {
            this.country = country;
            return this;
        }

        public Builder email(final String email) {
            this.email = email;
            return this;
        }

        public AddressDto build() {
            return new AddressDto(city, postalCode, streetName, streetNumber, state, region, country, email);
        }
    }
}
