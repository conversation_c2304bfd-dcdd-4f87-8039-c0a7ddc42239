package com.sast.store.ordermanagement.api;

import jakarta.validation.constraints.NotNull;

public record MoneyDto(@NotNull Double value, @NotNull String currencyCode) {

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {
        private Double value;
        private String currencyCode;

        private Builder() {
        }

        public Builder value(final Double value) {
            this.value = value;
            return this;
        }

        public Builder currencyCode(final String currencyCode) {
            this.currencyCode = currencyCode;
            return this;
        }

        public MoneyDto build() {
            return new MoneyDto(value, currencyCode);
        }
    }
}
