package com.sast.store.ordermanagement.api;

import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.List;

public record CartCheckoutDto(@NotNull String paymentMethodId,
    @Nullable @Size(min = 0, max = 2) List<@Size(min = 1, max = 50) String> notes) {

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {
        private String paymentMethodId;
        private List<String> notes;

        public Builder paymentMethodId(final String paymentMethodId) {
            this.paymentMethodId = paymentMethodId;
            return this;
        }

        public Builder notes(final List<String> notes) {
            this.notes = notes;
            return this;
        }

        public CartCheckoutDto build() {
            return new CartCheckoutDto(paymentMethodId, notes);
        }
    }
}