package com.sast.store.ordermanagement.api;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;

import java.util.List;

@Builder
public record CartAddDto(
    @NotNull String sku,
    @NotNull @Min(0) @Max(100) Long quantity,
    List<Addons> addons) {

    @Builder
    public record Addons(@NotNull String sku) {
    }
}
