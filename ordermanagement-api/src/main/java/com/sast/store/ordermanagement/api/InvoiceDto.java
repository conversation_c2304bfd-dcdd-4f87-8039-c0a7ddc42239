package com.sast.store.ordermanagement.api;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;
import java.util.List;

public record InvoiceDto(
    @NotNull String invoiceNumber,
    @NotNull List<String> orderIds,
    @NotNull @Schema(implementation = String.class) LocalDate invoiceDate,
    @NotNull InvoiceStatus status,
    @NotNull MoneyDto totalAmount) {

    public static final class Builder {
        private String invoiceNumber;
        private List<String> orderIds;
        private LocalDate invoiceDate;
        private InvoiceStatus status;
        private MoneyDto totalAmount;

        private Builder() {
            // Default constructor
        }

        public Builder invoiceNumber(final String invoiceNumber) {
            this.invoiceNumber = invoiceNumber;
            return this;
        }

        public Builder orderIds(final List<String> orderIds) {
            this.orderIds = orderIds;
            return this;
        }

        public Builder invoiceDate(final LocalDate invoiceDate) {
            this.invoiceDate = invoiceDate;
            return this;
        }

        public Builder status(final InvoiceStatus status) {
            this.status = status;
            return this;
        }

        public Builder totalAmount(final MoneyDto totalAmount) {
            this.totalAmount = totalAmount;
            return this;
        }

        public InvoiceDto build() {
            return new InvoiceDto(invoiceNumber, orderIds, invoiceDate, status, totalAmount);
        }
    }

    public static Builder builder() {
        return new Builder();
    }

}
