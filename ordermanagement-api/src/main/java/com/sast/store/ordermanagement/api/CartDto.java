package com.sast.store.ordermanagement.api;

import java.util.List;

public record CartDto(String id,
    List<LineItemDto> lineItems,
    List<AddonLineItemDto> addons,
    MoneyDto totalPrice,
    CompanyDto sellerCompany,
    AddressDto billingAddress) {

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {
        private String id;
        private List<LineItemDto> lineItems;
        private List<AddonLineItemDto> addons;
        private MoneyDto totalPrice;
        private CompanyDto sellerCompany;
        private AddressDto billingAddress;

        private Builder() {
        }

        public Builder id(final String id) {
            this.id = id;
            return this;
        }

        public Builder lineItems(final List<LineItemDto> lineItems) {
            this.lineItems = lineItems;
            return this;
        }

        public Builder addons(final List<AddonLineItemDto> addons) {
            this.addons = addons;
            return this;
        }

        public Builder totalPrice(final MoneyDto totalPrice) {
            this.totalPrice = totalPrice;
            return this;
        }

        public Builder sellerCompany(final CompanyDto sellerCompany) {
            this.sellerCompany = sellerCompany;
            return this;
        }

        public Builder billingAddress(final AddressDto billingAddress) {
            this.billingAddress = billingAddress;
            return this;
        }

        public CartDto build() {
            return new CartDto(id, lineItems, addons, totalPrice, sellerCompany, billingAddress);
        }
    }
}
