package com.sast.store.ordermanagement.test;

import com.github.tomakehurst.wiremock.common.ClasspathFileSource;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.github.tomakehurst.wiremock.junit5.WireMockExtension;
import com.sast.store.ordermanagement.api.AuthorizationInformationDto;
import com.sast.store.ordermanagement.api.CartDto;
import com.sast.store.ordermanagement.api.CheckoutInformationDto;
import com.sast.store.ordermanagement.api.CheckoutInformationDto.BoschAchCreditInformation;
import com.sast.store.ordermanagement.api.CheckoutInformationDto.BoschSepaCreditInformation;
import com.sast.store.ordermanagement.api.PaymentMethodConfigDto;

import java.util.Arrays;

import static com.github.tomakehurst.wiremock.client.ResponseDefinitionBuilder.okForJson;
import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.put;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathTemplate;

public class OrdermanagementMockExtension extends WireMockExtension {
    public static final int PORT = 8083;

    private static OrdermanagementMockExtension instance;

    public OrdermanagementMockExtension() {
        super(WireMockExtension
            .extensionOptions()
            .options(WireMockConfiguration.options()
                .fileSource(new ClasspathFileSource("ordermanagement"))
                .port(PORT))
            .configureStaticDsl(true));
        instance = this;
    }

    public static OrdermanagementMockExtension instance() {
        return instance;
    }

    public static OrdermanagementMockExtension withDefaultResponse() {
        withCartValidationResponse();
        withMeCompanyResponse();
        return instance;
    }

    public static OrdermanagementMockExtension withAddToCurrentCartResponse() {
        instance.stubFor(post(urlPathEqualTo("/rest/cart/"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json")
                .withBodyFile("get-current-cart-response.json")));

        return instance;
    }

    public static OrdermanagementMockExtension withUpdateCurrentCartResponse() {
        instance.stubFor(put(urlPathEqualTo("/rest/cart/"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json")
                .withBodyFile("get-current-cart-response.json")));

        return instance;
    }

    public static OrdermanagementMockExtension withCheckoutCartResponse(final String paymentId) {
        final var response = AuthorizationInformationDto.builder()
            .paymentId(paymentId)
            .build();

        instance.stubFor(post(urlPathEqualTo("/rest/cart/checkout"))
            .willReturn(okForJson(response)));

        return instance;
    }

    public static OrdermanagementMockExtension withGetCurrentCartResponse() {
        instance.stubFor(get(urlPathEqualTo("/rest/cart/"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json")
                .withBodyFile("get-current-cart-response.json")));

        return instance;
    }

    public static OrdermanagementMockExtension withGetCurrentCartResponse(final CartDto cartDto) {
        instance.stubFor(get(urlPathEqualTo("/rest/cart/"))
            .willReturn(okForJson(cartDto)));

        return instance;
    }

    public static OrdermanagementMockExtension withGetCurrentEmptyCartResponse() {
        instance.stubFor(get(urlPathEqualTo("/rest/cart/"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json")
                .withBodyFile("get-current-empty-cart-response.json")));

        return instance;
    }

    public static OrdermanagementMockExtension withCartValidationResponse() {
        instance.stubFor(post(urlPathMatching("/rest/commercetools/cartvalidation"))
            .willReturn(aResponse()
                .withStatus(204)
                .withHeader("content-type", "application/json")));

        return instance;
    }

    public static OrdermanagementMockExtension withCartValidationFailedResponse() {
        instance.stubFor(post(urlPathMatching("/rest/commercetools/cartvalidation"))
            .willReturn(aResponse()
                .withStatus(400)
                .withHeader("content-type", "application/json")));

        return instance;
    }

    public static OrdermanagementMockExtension withListAllInvoicesResponse() {
        instance.stubFor(get(urlPathEqualTo("/rest/invoices/"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json")
                .withBodyFile("list-all-invoices-response.json")));

        return instance;
    }

    public static OrdermanagementMockExtension withMeCompanyResponse() {
        instance.stubFor(get(urlPathMatching("/rest/me/company"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json")
                .withBodyFile("me-company-response.json")));

        return instance;
    }

    public static OrdermanagementMockExtension withGetPaymentResponse() {
        return withGetPaymentResponse(null, null);
    }

    public static OrdermanagementMockExtension withGetPaymentResponse(final String paymentStatus, final String orderId) {
        final var response = aResponse()
            .withStatus(200)
            .withHeader("content-type", "application/json")
            .withBodyFile("get-payment-response.json")
            .withTransformers("response-template");

        if (paymentStatus != null) {
            response.withTransformerParameter("paymentStatus", paymentStatus);
        }
        if (orderId != null) {
            response.withTransformerParameter("orderId", orderId);
        }

        instance.stubFor(get(urlPathTemplate("/rest/payments/{paymentId}"))
            .willReturn(response));

        return instance;
    }

    public static OrdermanagementMockExtension withGetPaymentConfigResponse(final String... paymentMethodIds) {
        final var response = PaymentMethodConfigDto.builder()
            .paymentMethods(Arrays.stream(paymentMethodIds)
                .map(OrdermanagementMockExtension::toCheckoutInformation)
                .toList())
            .build();

        instance.stubFor(get(urlPathMatching("/rest/payments/paymentConfig"))
            .willReturn(okForJson(response)));

        return instance;
    }

    private static CheckoutInformationDto toCheckoutInformation(final String paymentMethodId) {
        return switch (paymentMethodId) {
            case "ZERO/ZERO" -> CheckoutInformationDto.builder()
                .paymentMethodId(paymentMethodId)
                .build();
            case "BOSCH_TRANSFER/SEPA_CREDIT" -> CheckoutInformationDto.builder()
                .paymentMethodId(paymentMethodId)
                .boschSepaCreditInformation(BoschSepaCreditInformation.builder()
                    .accountHolder("Max Mustermann")
                    .bankName("Verprasskasse München")
                    .iban("**********************")
                    .bic("DEXYZABCXX")
                    .build())
                .build();
            case "BOSCH_TRANSFER/ACH_CREDIT" -> CheckoutInformationDto.builder()
                .paymentMethodId(paymentMethodId)
                .boschAchCreditInformation(BoschAchCreditInformation.builder()
                    .accountHolder("Max Sampleman")
                    .accountNumber("*********")
                    .routingNumber("*********")
                    .bic("BOFAUS3N")
                    .bankName("Squander Brothers Inc.")
                    .build())
                .build();
            default -> throw new IllegalStateException("Unexpected payment method: " + paymentMethodId);
        };
    }
}
