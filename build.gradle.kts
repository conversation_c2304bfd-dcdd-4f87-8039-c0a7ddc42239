plugins {
    id("com.dorongold.task-tree") version "4.0.0"
    id("se.bjurr.violations.violations-gradle-plugin") version "2.3.1"
}

tasks.register("violations", se.bjurr.violations.gradle.plugin.ViolationsTask::class) {
    group = LifecycleBasePlugin.VERIFICATION_GROUP
    description = "Aggregate all Checkstyle findings into one code-climate-file.json because GitLab can only handle one"

    codeClimateFile = file("build/code-climate-file.json")
    violationsFile = file("build/violations-file.json")
    printViolations = true
    maxViolations = 0
    violations = listOf(
        listOf("FINDBUGS", projectDir.path, ".*/findbugs/.*\\.xml\$", "Findbugs"),
        listOf("CHECKSTYLE", projectDir.path, ".*/checkstyle/.*\\.xml\$", "Checkstyle")
    )
}

tasks.register("checkShard") {
    group = LifecycleBasePlugin.VERIFICATION_GROUP
    description = "Runs the check task for a selection of projects"

    val selectedShard: String by project
    val totalShards: String by project

    println("Running check task for shard $selectedShard of $totalShards")

    val sortedProjects = subprojects.sorted()
    sortedProjects.map { println(it.path) }

    val shardNumber = selectedShard.toInt()
    val shardCount = totalShards.toInt()
    val shardLength = kotlin.math.ceil(subprojects.size.toDouble() / shardCount.toDouble()).toInt()

    println("Calculated shard length of $shardLength for ${subprojects.size} projects")

    val startIndex = (shardNumber - 1) * shardLength
    val endIndex = if (shardNumber < shardCount) startIndex + shardLength else sortedProjects.size

    println("Selecting projects from index $startIndex to index $endIndex")

    val selectedProjects = sortedProjects.subList(startIndex, endIndex)

    println("Selected ${selectedProjects.size} of ${sortedProjects.size} projects")
    selectedProjects.map { println(it.path) }

    dependsOn(selectedProjects.map { "${it.path}:check" })
}
