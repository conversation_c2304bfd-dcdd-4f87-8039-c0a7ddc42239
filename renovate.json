{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:recommended", "npm:unpublishSafe"], "packageRules": [{"matchPackageNames": ["jakarta.inject:jakarta.inject-api"], "allowedVersions": "!/\\.MR$/"}, {"description": ["Ignore timestamp based versions which were used until 2023"], "matchPackageNames": ["com.graphql-java:graphql-java-extended-scalars"], "allowedVersions": "!/^\\d{4}-\\d{2}-\\d{2}T.*$/"}, {"description": ["@wundergraph/vue-query depends on version 4"], "matchPackageNames": ["@tanstack/vue-query"], "allowedVersions": "<5"}, {"description": ["keycloak-js version should be kept in sync with UMP"], "matchPackageNames": ["keycloak-js"], "allowedVersions": "<=26"}, {"matchUpdateTypes": ["minor", "patch"], "groupName": "minor-dependencies"}, {"matchManagers": ["npm"], "matchUpdateTypes": ["minor", "patch", "major"], "groupName": "frontend-dependencies"}, {"description": ["The Playwright runner and NPM package must be updated together"], "matchPackageNames": ["**/boss-store-playwright-builder", "mcr.microsoft.com/playwright"], "groupName": "frontend-dependencies"}, {"description": ["Commercetools Java SDK consists of multiple artifacts that we should update together"], "matchSourceUrls": ["https://github.com/commercetools/commercetools-sdk-java-v2"], "groupName": "commercetools-sdk monorepo", "matchUpdateTypes": ["digest", "patch", "minor", "major"]}], "vulnerabilityAlerts": {"enabled": true}, "osvVulnerabilityAlerts": true, "dependencyDashboardOSVVulnerabilitySummary": "all"}