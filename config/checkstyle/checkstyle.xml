<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE module PUBLIC "-//Puppy Crawl//DTD Check Configuration 1.3//EN" "http://www.puppycrawl.com/dtds/configuration_1_3.dtd">
<module name="Checker">
    <property name="severity" value="warning"/>
    <module name="TreeWalker">
        <module name="HiddenField">
            <property name="severity" value="error"/>
            <property name="ignoreConstructorParameter" value="true"/>
            <property name="ignoreSetter" value="true"/>
            <property name="ignoreAbstractMethods" value="true"/>
            <property name="setterCanReturnItsClass" value="true"/>
            <property name="tokens" value="VARIABLE_DEF"/>
        </module>
        <module name="TypecastParenPad">
            <property name="severity" value="error"/>
            <property name="tokens" value="RPAREN,TYPECAST"/>
        </module>
        <module name="LocalVariableName">
            <property name="severity" value="error"/>
            <property name="format" value="^[_a-z][_a-zA-Z0-9]{0,39}$"/>
        </module>
        <module name="FinalLocalVariable">
            <property name="severity" value="error"/>
        </module>
        <module name="RedundantImport">
            <property name="severity" value="error"/>
        </module>
        <module name="StaticVariableName">
            <property name="severity" value="error"/>
            <property name="format" value="^[_a-z][_a-zA-Z0-9]{0,39}$"/>
        </module>
        <module name="ParameterName">
            <property name="severity" value="error"/>
        </module>
        <module name="WhitespaceAfter">
            <property name="severity" value="error"/>
            <property name="tokens" value="COMMA,SEMI"/>
        </module>
        <module name="LocalFinalVariableName">
            <property name="severity" value="error"/>
        </module>
        <module name="JavadocVariable">
            <property name="severity" value="ignore"/>
            <property name="accessModifiers" value="package"/>
        </module>
        <module name="TypeName">
            <property name="severity" value="error"/>
            <property name="tokens" value="CLASS_DEF"/>
        </module>
        <module name="UnnecessaryParentheses">
            <property name="severity" value="error"/>
        </module>
        <module name="MemberName">
            <property name="severity" value="error"/>
            <property name="format" value="^[_a-z][_a-zA-Z0-9]{0,39}$"/>
        </module>
        <module name="VisibilityModifier">
            <property name="severity" value="error"/>
            <property name="protectedAllowed" value="true"/>
        </module>
        <module name="IllegalImport">
            <property name="severity" value="error"/>
        </module>
        <module name="ParameterAssignment">
            <property name="severity" value="error"/>
        </module>
        <module name="JavadocStyle">
            <property name="severity" value="ignore"/>
            <property name="checkEmptyJavadoc" value="true"/>
            <property name="checkFirstSentence" value="false"/>
        </module>
        <module name="HideUtilityClassConstructor">
            <property name="severity" value="error"/>
            <property name="ignoreAnnotatedBy" value="SpringBootApplication"/>
        </module>
        <module name="BooleanExpressionComplexity">
            <property name="severity" value="error"/>
            <property name="max" value="4"/>
        </module>
        <module name="InnerAssignment">
            <property name="severity" value="error"/>
        </module>
        <module name="OuterTypeNumber">
            <property name="severity" value="error"/>
        </module>
        <module name="EmptyForInitializerPad">
            <property name="severity" value="info"/>
            <property name="option" value="space"/>
        </module>
        <module name="NestedIfDepth">
            <property name="severity" value="error"/>
            <property name="max" value="5"/>
        </module>
        <module name="ModifiedControlVariable">
            <property name="severity" value="error"/>
        </module>
        <module name="FallThrough">
            <property name="severity" value="error"/>
        </module>
        <module name="SuperClone">
            <property name="severity" value="error"/>
        </module>
        <module name="JavadocType">
            <property name="severity" value="error"/>
            <property name="tokens" value="INTERFACE_DEF"/>
        </module>
        <module name="JavaNCSS">
            <property name="severity" value="error"/>
            <property name="classMaximum" value="350"/>
            <property name="recordMaximum" value="350"/>
            <property name="fileMaximum" value="350"/>
        </module>
        <module name="RedundantModifier">
            <property name="severity" value="error"/>
            <property name="tokens" value="METHOD_DEF, VARIABLE_DEF, ANNOTATION_FIELD_DEF, INTERFACE_DEF"/>
        </module>
        <module name="LeftCurly">
            <property name="severity" value="error"/>
        </module>
        <module name="TrailingComment">
            <property name="severity" value="error"/>
        </module>
        <module name="MethodName">
            <property name="severity" value="error"/>
        </module>
        <module name="DeclarationOrder">
            <property name="severity" value="error"/>
        </module>
        <module name="NestedTryDepth">
            <property name="severity" value="warning"/>
        </module>
        <module name="NestedTryDepth">
            <property name="severity" value="error"/>
        </module>
        <module name="DefaultComesLast">
            <property name="severity" value="error"/>
        </module>
        <module name="OperatorWrap">
            <property name="severity" value="error"/>
        </module>
        <module name="EmptyBlock">
            <property name="severity" value="error"/>
            <property name="tokens"
                      value="LITERAL_DO,LITERAL_ELSE,LITERAL_FINALLY,LITERAL_IF,LITERAL_FOR,LITERAL_TRY,LITERAL_WHILE,STATIC_INIT"/>
        </module>
        <module name="EmptyBlock">
            <property name="severity" value="error"/>
            <property name="option" value="text"/>
            <property name="tokens" value="LITERAL_CATCH"/>
        </module>
        <module name="PackageDeclaration">
            <property name="severity" value="error"/>
        </module>
        <module name="MultipleVariableDeclarations">
            <property name="severity" value="error"/>
        </module>
        <module name="EqualsHashCode">
            <property name="severity" value="error"/>
        </module>
        <module name="GenericWhitespace">
            <property name="severity" value="error"/>
        </module>
        <module name="EmptyStatement">
            <property name="severity" value="error"/>
        </module>
        <module name="UnusedImports">
            <property name="severity" value="error"/>
        </module>
        <module name="WhitespaceAround">
            <property name="severity" value="error"/>
            <property name="tokens"
                      value="ASSIGN,BAND,BAND_ASSIGN,BOR,BOR_ASSIGN,BSR,BSR_ASSIGN,BXOR,BXOR_ASSIGN,COLON,DIV,DIV_ASSIGN,EQUAL,GE,GT,LAND,LCURLY,LE,LITERAL_ASSERT,LITERAL_CATCH,LITERAL_DO,LITERAL_ELSE,LITERAL_FINALLY,LITERAL_FOR,LITERAL_IF,LITERAL_RETURN,LITERAL_SYNCHRONIZED,LITERAL_TRY,LITERAL_WHILE,LOR,LT,MINUS,MINUS_ASSIGN,MOD,MOD_ASSIGN,NOT_EQUAL,PLUS,PLUS_ASSIGN,QUESTION,RCURLY,SL,SLIST,SL_ASSIGN,SR,SR_ASSIGN,STAR,STAR_ASSIGN,LITERAL_ASSERT"/>
            <property name="allowEmptyConstructors" value="true"/>
            <property name="allowEmptyMethods" value="true"/>
        </module>
        <module name="NoFinalizer">
            <property name="severity" value="warning"/>
        </module>
        <module name="NoWhitespaceAfter">
            <property name="severity" value="error"/>
            <property name="allowLineBreaks" value="false"/>
            <property name="tokens"
                      value="BNOT, DEC, DOT, INC, LNOT, UNARY_MINUS, UNARY_PLUS"/>
        </module>
        <module name="MutableException">
            <property name="severity" value="warning"/>
        </module>
        <module name="InterfaceIsType">
            <property name="severity" value="error"/>
        </module>
        <module name="StringLiteralEquality">
            <property name="severity" value="error"/>
        </module>
        <module name="NoWhitespaceBefore">
            <property name="severity" value="error"/>
        </module>
        <module name="IllegalType">
            <property name="severity" value="error"/>
            <property name="tokens" value="METHOD_DEF,PARAMETER_DEF"/>
            <property name="memberModifiers" value="LITERAL_PUBLIC,LITERAL_PROTECTED, LITERAL_STATIC"/>
        </module>
        <module name="AnonInnerLength">
            <property name="severity" value="warning"/>
            <property name="max" value="40"/>
        </module>
        <module name="IllegalToken">
            <property name="severity" value="warning"/>
            <property name="tokens" value="LITERAL_NATIVE"/>
        </module>
        <module name="ParenPad">
            <property name="severity" value="error"/>
        </module>
        <module name="FinalClass">
            <property name="severity" value="error"/>
        </module>
        <module name="UpperEll">
            <property name="severity" value="error"/>
        </module>
        <module name="NeedBraces">
            <property name="severity" value="error"/>
        </module>
        <module name="FinalParameters">
            <property name="severity" value="error"/>
        </module>
        <module name="PackageName">
            <property name="severity" value="warning"/>
            <property name="format" value="^com\.sast(\.[a-z_][a-z0-9_]*){0,6}$" />
        </module>
        <module name="AvoidNestedBlocks">
            <property name="severity" value="error"/>
            <property name="allowInSwitchCase" value="true"/>
        </module>
        <module name="SimplifyBooleanReturn">
            <property name="severity" value="error"/>
        </module>
        <module name="SimplifyBooleanExpression">
            <property name="severity" value="warning"/>
        </module>
        <module name="MissingDeprecated">
            <property name="severity" value="warning"/>
        </module>
        <module name="RegexpSinglelineJava">
            <property name="severity" value="error"/>
            <property name="format" value="System\.out\.print"/>
            <property name="message"
                      value="Using System.out to print to the console is not allowed."/>
            <property name="ignoreComments" value="true"/>
        </module>
        <module name="IllegalInstantiation">
            <property name="severity" value="warning"/>
            <property name="classes" value="java.lang.Boolean"/>
        </module>
        <module name="EqualsAvoidNull">
            <property name="severity" value="error"/>
        </module>
        <module name="AvoidStarImport">
            <property name="severity" value="error"/>
            <property name="excludes"
                      value="com.google.common.base.Preconditions,com.google.common.base.Strings.*"/>
        </module>
        <module name="EmptyForIteratorPad">
            <property name="severity" value="info"/>
            <property name="option" value="space"/>
        </module>
        <module name="NPathComplexity">
            <property name="severity" value="error"/>
        </module>
        <module name="MissingSwitchDefault">
            <property name="severity" value="error"/>
        </module>
        <module name="MethodParamPad">
            <property name="severity" value="error"/>
        </module>
        <module name="SuperFinalize">
            <property name="severity" value="error"/>
        </module>
        <module name="CovariantEquals">
            <property name="severity" value="error"/>
        </module>
        <module name="RightCurly">
            <property name="severity" value="error"/>
        </module>
        <module name="ConstantName">
            <property name="severity" value="error"/>
            <property name="format" value="^[A-Z](_?[A-Z0-9]+)*$"/>
        </module>
        <module name="ArrayTypeStyle">
            <property name="severity" value="error"/>
        </module>
        <module name="ThrowsCount">
            <property name="max" value="6"/>
        </module>
        <module name="ModifierOrder">
            <property name="severity" value="error"/>
        </module>
        <module name="SuppressWarningsHolder"/>
        <module name="SuppressionCommentFilter">
            <property name="offCommentFormat" value="CHECKSTYLE OFF\: ([\w\|]+)"/>
            <property name="onCommentFormat" value="CHECKSTYLE ON\: ([\w\|]+)"/>
            <property name="checkFormat" value="$1"/>
        </module>
        <module name="SuppressWithNearbyCommentFilter">
            <property name="commentFormat" value="CHECKSTYLE IGNORE (\d+) LINES"/>
            <property name="influenceFormat" value="$1"/>
        </module>
        <module name="CustomImportOrder">
            <property name="severity" value="error"/>
            <property name="customImportOrderRules" value="THIRD_PARTY_PACKAGE###SPECIAL_IMPORTS###STANDARD_JAVA_PACKAGE###STATIC"/>
            <property name="specialImportsRegExp" value="^javax\."/>
            <property name="standardPackageRegExp" value="^java\."/>
            <property name="sortImportsInGroupAlphabetically" value="true"/>
            <!--  separateLineBetweenGroups option is disabled because Intellij default has blank line between "java" and third party imports, and no blank line between "javax" and "java"  -->
            <property name="separateLineBetweenGroups" value="false"/>
        </module>
    </module>
    <module name="FileTabCharacter">
        <property name="severity" value="warning"/>
    </module>
    <module name="SuppressWarningsFilter"/>
    <module name="LineLength">
        <property name="fileExtensions" value="java"/>
        <property name="severity" value="error"/>
        <property name="max" value="140"/>
        <!-- allow package names, imports and urls to exceed the limit
             https://github.com/checkstyle/checkstyle/blob/63d583b7ee6044a9f45e3a3a5455e2be5841cfdf/src/main/resources/google_checks.xml#L55 -->
        <property name="ignorePattern"
                  value="^package.*|^import.*|href\s*=\s*&quot;[^&quot;]*&quot;|http://|https://|ftp://"/>
    </module>
    <module name="SuppressionSingleFilter">
        <property name="checks" value="HiddenFieldCheck"/>
        <property name="files" value=".*Dto\.java|.*Entity\.java"/>
    </module>
    <module name="SuppressWithPlainTextCommentFilter">
        <property name="offCommentFormat" value='^.*"""((?!;).)*$'/>
        <property name="onCommentFormat" value='^\s+.*""";'/>
    </module>
    <module name="SuppressionSingleFilter">
        <property name="checks" value="JavaNCSS"/>
        <property name="files" value=".*[\\/]src[\\/]test[\\/].*"/>
    </module>
    <module name="SuppressionSingleFilter">
        <property name="checks" value="MethodName"/>
        <property name="files" value=".*[\\/]src[\\/]test[\\/].*"/>
    </module>
</module>
