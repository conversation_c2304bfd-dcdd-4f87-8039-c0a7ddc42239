package com.sast.store.testing.commons;

import com.google.common.reflect.ClassPath;
import com.google.common.reflect.ClassPath.ClassInfo;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.HttpMethod;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import org.mockito.Mockito;

import java.io.IOException;
import java.io.InputStream;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.lang.reflect.Parameter;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.function.Predicate.not;

public final class JaxRsEndpointUtil {

    private JaxRsEndpointUtil() {
    }

    public static List<Endpoint> findEndpoints(final String basepackage) {
        return findRestClasses(basepackage)
            .filter(Class::isInterface)
            .flatMap(clazz -> Arrays.asList(clazz.getMethods()).stream()
                .map(t -> buildEndpoint(t)))
            .filter(Objects::nonNull)
            .toList();
    }

    public static List<Method> findRestMethodImplementations(final String basepackage) {
        return findRestClasses(basepackage)
            .filter(not(Class::isInterface))
            .flatMap(clazz -> Arrays
                .asList(clazz.getDeclaredMethods()).stream()
                .filter(Objects::nonNull))
            .filter(method -> Modifier.isPublic(method.getModifiers()))
            .toList();
    }

    private static Stream<Class<?>> findRestClasses(final String basepackage) {
        return loadClasses(basepackage).stream()
            .filter(clazz -> isRestClass(clazz) || hasRestInterface(clazz));
    }

    private static boolean hasRestInterface(final Class<?> clazz) {
        return Arrays.asList(clazz.getInterfaces()).stream()
            .anyMatch(c -> c.getAnnotation(Path.class) != null);
    }

    private static boolean isRestClass(final Class<?> clazz) {
        return clazz.getAnnotation(Path.class) != null;
    }

    private static Endpoint buildEndpoint(final Method method) {
        final Endpoint newEndpoint = new Endpoint();
        newEndpoint.method = resolveHttpMethod(method);
        if (newEndpoint.method == null) {
            return null;
        }
        final String classUri = getEndpointPath(method.getDeclaringClass());
        final Path path = method.getAnnotation(Path.class);
        newEndpoint.uri = path == null ? classUri : classUri + path.value();
        newEndpoint.path = buildPath(newEndpoint.uri);
        newEndpoint.mediaType = resolveMediaType(method);
        newEndpoint.annotations = method.getAnnotations();
        newEndpoint.body = buildBody(method);
        newEndpoint.queryParams = buildQueryParams(method);
        return newEndpoint;
    }

    private static Map<String, ?> buildQueryParams(final Method method) {
        return Arrays.asList(method.getParameters()).stream()
            .filter(p -> p.getAnnotation(QueryParam.class) != null)
            .collect(Collectors
                .toMap(p -> p.getAnnotation(QueryParam.class).value(),
                    p -> buildDefaults(p.getType())));
    }

    private static Object buildDefaults(final Class<?> type) {
        if (type == Integer.class) {
            return 1;
        }
        if (type == String.class) {
            return "";
        }
        if (type == InputStream.class) {
            return null;
        }
        return Mockito.mock(type, Mockito.RETURNS_MOCKS);
    }

    private static String buildPath(final String uri) {
        return uri.replace("{", "").replace("}", "");
    }

    private static Object buildBody(final Method method) {
        return Arrays.asList(method.getParameters()).stream()
            .filter(p -> p.getAnnotation(HeaderParam.class) == null)
            .filter(p -> p.getAnnotation(PathParam.class) == null)
            .filter(p -> p.getAnnotation(QueryParam.class) == null)
            .map(Parameter::getType)
            .map(type -> buildDefaults(type))
            .filter(Objects::nonNull)
            .findFirst()
            .orElse(null);
    }

    private static MediaType resolveMediaType(final Method method) {
        if (method.isAnnotationPresent(Consumes.class)) {
            return MediaType.valueOf(method.getAnnotation(Consumes.class).value()[0]);
        }
        if (method.getDeclaringClass().isAnnotationPresent(Consumes.class)) {
            return MediaType.valueOf(method.getDeclaringClass().getAnnotation(Consumes.class).value()[0]);
        }
        return null;
    }

    private static String resolveHttpMethod(final Method method) {
        if (method.isAnnotationPresent(GET.class)) {
            return HttpMethod.GET;
        }
        if (method.isAnnotationPresent(PUT.class)) {
            return HttpMethod.PUT;
        }
        if (method.isAnnotationPresent(POST.class)) {
            return HttpMethod.POST;
        }
        if (method.isAnnotationPresent(DELETE.class)) {
            return HttpMethod.DELETE;
        }
        return null;
    }

    private static String getEndpointPath(final Class<?> clazz) {
        final Annotation annotation = clazz.getAnnotation(Path.class);
        if (annotation != null) {
            return ((Path) annotation).value();
        }
        return "";
    }

    private static List<Class<?>> loadClasses(final String basepackage) {
        try {
            return ClassPath.from(Thread.currentThread().getContextClassLoader())
                .getTopLevelClassesRecursive(basepackage).stream()
                .map(JaxRsEndpointUtil::safeLoad)
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(Class::getName))
                .collect(Collectors.toList());
        } catch (final IOException e) {
            throw new RuntimeException(e);
        }
    }

    private static Class<?> safeLoad(final ClassInfo t) {
        try {
            return t.load();
        } catch (final NoClassDefFoundError e) {
            return null;
        }
    }

    public static class Endpoint {
        private MediaType mediaType;
        private String uri;
        private String method;
        private Object body;
        private String path;
        private Annotation[] annotations = new Annotation[] {};
        private Map<String, ?> queryParams;

        public MediaType getMediaType() {
            return mediaType;
        }

        public String getUri() {
            return uri;
        }

        public String getPath() {
            return path;
        }

        public String getMethod() {
            return method;
        }

        public Annotation[] getAnnotations() {
            return annotations;
        }

        public Object getBody() {
            return body;
        }

        public Map<String, ?> getQueryParams() {
            return queryParams;
        }

        @Override
        public String toString() {
            return "Endpoint [mediaType=" + mediaType + ", uri=" + uri + ", method=" + method + ", annotations="
                + Arrays.toString(annotations) + "]";
        }
    }

}
