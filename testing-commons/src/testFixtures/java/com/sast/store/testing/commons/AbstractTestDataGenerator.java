package com.sast.store.testing.commons;

import java.util.Deque;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.Map;

public abstract class AbstractTestDataGenerator<S extends AbstractTestDataGenerator<S>> {
    private Data data = new Data();

    private Map<String, LinkedList<Object>> data2 = new HashMap<>();

    public S clean() {
        data = new Data();
        data2 = new HashMap<>();

        return returnThis();
    }

    @SuppressWarnings("unchecked")
    private S returnThis() {
        return (S)this;
    }

    protected <T> T addData(final String key, final T obj) {
        final Deque<Object> value = data2.computeIfAbsent(key, k -> new LinkedList<>());
        value.add(obj);
        return obj;
    }

    protected LinkedList<?> getData(final String key) {
        return data2.get(key);
    }

    protected <T> LinkedList<T> getData(final Class<T> clazz) {
        return data.get(clazz);
    }

    protected <T> T setData(final T obj) {
        return data.set(obj);
    }

    public <T> int countAll(final Class<T> clazz) {
        return getData(clazz).size();
    }

    private static final class Data {
        private final Map<Class<?>, LinkedList<Object>> list = new HashMap<>();

        public <T> T set(final T obj) {
            final Deque<Object> value = list.computeIfAbsent(obj.getClass(), k -> new LinkedList<>());
            value.add(obj);
            return obj;
        }

        @SuppressWarnings("unchecked")
        public <T> LinkedList<T> get(final Class<T> clazz) {
            return (LinkedList<T>)list.get(clazz);
        }
    }
}
