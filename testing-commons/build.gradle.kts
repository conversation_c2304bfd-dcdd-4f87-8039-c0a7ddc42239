plugins {
    id("bossstore.subproject-conventions")
	`java-test-fixtures`
    id("org.springframework.boot") version "3.4.3" apply false
}

dependencies {
    testFixturesImplementation(platform(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES))
    testFixturesImplementation("org.junit.jupiter:junit-jupiter-api")
    testFixturesImplementation("org.junit.jupiter:junit-jupiter")
    testFixturesImplementation("org.wiremock.integrations:wiremock-spring-boot:3.9.0")
    testFixturesImplementation("org.mockito:mockito-core")
    testFixturesImplementation("org.mockito:mockito-junit-jupiter")
    testFixturesImplementation("jakarta.ws.rs:jakarta.ws.rs-api")
    testFixturesImplementation("com.google.guava:guava:33.4.0-jre")
}
