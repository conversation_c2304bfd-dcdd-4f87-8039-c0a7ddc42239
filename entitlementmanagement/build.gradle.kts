plugins {
    id("bossstore.subproject-conventions")
    id("org.springframework.boot") version "3.4.3"
    id("com.google.cloud.tools.jib") version "3.4.4"
}

dependencies {
    implementation(project(":entitlementmanagement-api"))
    implementation(project(":productmanagement-api"))
    implementation(project(":contractmanagement-api"))
    implementation(project(":external-clients"))
    implementation(project(":external-clients:commercetools"))
    implementation(project(":external-clients:brim-service"))
    implementation(project(":external-clients:dc-litmos"))
    implementation(project(":external-clients:dc-keycloak"))
    implementation(project(":external-clients:bc-central"))
    implementation(project(":external-clients:email-service"))
    implementation(project(":external-clients:ump"))
    implementation(project(":external-clients:countries-service"))
    implementation(project(":commons"))
    implementation(project(":commons:base-webapp"))
    implementation(project(":commons:jersey-client"))
    implementation(project(":commons:tenant"))
    implementation(project(":commons:aws"))
    implementation(enforcedPlatform(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES))
    implementation(project(":commons:hazelcast"))
    implementation(project(":commons:shedlock"))
    implementation("org.springframework.boot:spring-boot-starter-jersey")
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation("io.awspring.cloud:spring-cloud-aws-starter-sqs")
    implementation("com.github.ben-manes.caffeine:caffeine")
    implementation("org.glassfish.jersey.ext:jersey-proxy-client")
    implementation("com.google.guava:guava:33.4.0-jre")
    implementation("io.temporal:temporal-sdk:1.28.3")
    implementation("io.temporal:temporal-spring-boot-starter:1.28.3")
    runtimeOnly("org.glassfish.jersey.core:jersey-common") {
        because("Otherwise it can't find a converter for the plaintext output in case of errors")
    }

    testImplementation(testFixtures(project(":testing-awsmockup")))
    testImplementation(testFixtures(project(":testing-commons")))
    testImplementation(testFixtures(project(":external-clients:bc-central")))
    testImplementation(testFixtures(project(":external-clients:countries-service")))
    testImplementation(testFixtures(project(":external-clients:dc-keycloak")))
    testImplementation(testFixtures(project(":external-clients:dc-litmos")))
    testImplementation(testFixtures(project(":external-clients:ump")))
    testImplementation(testFixtures(project(":contractmanagement-api")))
    testImplementation(testFixtures(project(":productmanagement-api")))
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.springframework.security:spring-security-test")
    testImplementation("io.rest-assured:rest-assured")
    testImplementation("org.wiremock.integrations:wiremock-spring-boot:3.9.0")
    testImplementation("com.tngtech.keycloakmock:mock-junit5:0.17.0")
    testImplementation("org.jeasy:easy-random-core:5.0.0")
    testImplementation("com.amazonaws:DynamoDBLocal:2.5.4") {
        exclude(group = "software.amazon.awssdk", module = "url-connection-client")
    }
    testImplementation("org.mockito:mockito-core")
    testImplementation("org.mockito:mockito-junit-jupiter")
    testImplementation("io.temporal:temporal-testing:1.28.3")
}

jib {
    from.image = "gcr.io/distroless/java21-debian12"
    to.image = "${property("ecrEndpoint")}/bossstore-${project.name}:${project.version}"
}

tasks.bootTestRun {
    args = listOf("-Daws.maxAttempts=20", "--spring.profiles.active=local")
}
