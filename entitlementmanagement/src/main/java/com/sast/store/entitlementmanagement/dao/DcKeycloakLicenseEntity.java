package com.sast.store.entitlementmanagement.dao;

import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

import java.time.Instant;

@DynamoDbBean
public class DcKeycloakLicenseEntity extends LicenseEntity<DcKeycloakLicenseEntity> {

    private String email;
    private String firstname;
    private String lastname;
    private DcKeycloakLicenseType licenseType;
    private Instant freemiumEndDate;
    private TaskState freemiumExpiredTaskState;
    private TaskState freemiumExpiringSoonTaskState;

    public String getEmail() {
        return email;
    }

    public void setEmail(final String email) {
        this.email = email;
    }

    public DcKeycloakLicenseEntity withEmail(final String email) {
        this.email = email;
        return this;
    }

    public String getFirstname() {
        return firstname;
    }

    public void setFirstname(final String firstname) {
        this.firstname = firstname;
    }

    public DcKeycloakLicenseEntity withFirstname(final String firstname) {
        this.firstname = firstname;
        return this;
    }

    public String getLastname() {
        return lastname;
    }

    public void setLastname(final String lastname) {
        this.lastname = lastname;
    }

    public DcKeycloakLicenseEntity withLastname(final String lastname) {
        this.lastname = lastname;
        return this;
    }

    public DcKeycloakLicenseType getLicenseType() {
        return licenseType;
    }

    public void setLicenseType(final DcKeycloakLicenseType licenseType) {
        this.licenseType = licenseType;
    }

    public DcKeycloakLicenseEntity withLicenseType(final DcKeycloakLicenseType licenseType) {
        this.licenseType = licenseType;
        return this;
    }

    public TaskState getFreemiumExpiringSoonTaskState() {
        return freemiumExpiringSoonTaskState;
    }

    public void setFreemiumExpiringSoonTaskState(final TaskState expiringSoonTaskState) {
        this.freemiumExpiringSoonTaskState = expiringSoonTaskState;
    }

    public DcKeycloakLicenseEntity withFreemiumExpiringSoonTaskState(final TaskState expiringSoonTaskState) {
        this.freemiumExpiringSoonTaskState = expiringSoonTaskState;
        return this;
    }

    public void setFreemiumEndDate(final Instant endDate) {
        this.freemiumEndDate = endDate;
    }

    public Instant getFreemiumEndDate() {
        return freemiumEndDate;
    }

    public DcKeycloakLicenseEntity withFreemiumEndDate(final Instant endDate) {
        this.freemiumEndDate = endDate;
        return this;
    }

    public void setFreemiumExpiredTaskState(final TaskState freemiumExpiredTaskState) {
        this.freemiumExpiredTaskState = freemiumExpiredTaskState;
    }

    public TaskState getFreemiumExpiredTaskState() {
        return freemiumExpiredTaskState;
    }

    public DcKeycloakLicenseEntity withFreemiumExpiredTaskState(final TaskState freemiumExpiredTaskState) {
        this.freemiumExpiredTaskState = freemiumExpiredTaskState;
        return this;
    }

    @Override
    public String toString() {
        return "DcKeycloakLicenseEntity [email=" + email + ", firstname=" + firstname + ", lastname=" + lastname + ", licenseType="
            + licenseType + ", expiringSoonTaskState=" + freemiumExpiringSoonTaskState + ", " + super.toString() + "]";
    }


}
