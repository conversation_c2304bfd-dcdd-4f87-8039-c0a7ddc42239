package com.sast.store.entitlementmanagement.workflows;

import com.sast.store.contractmanagement.api.ContractEvent;
import com.sast.store.entitlementmanagement.activities.SendReminderEmailActivities;
import io.temporal.activity.ActivityOptions;
import io.temporal.failure.CanceledFailure;
import io.temporal.spring.boot.WorkflowImpl;
import io.temporal.workflow.Workflow;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;

@Slf4j
@WorkflowImpl(workers = "send-email-worker")
public class SendReminderEmailWorkflowImpl implements SendReminderEmailWorkflow {

    private static final Duration REMINDER_EXPIRATION_TIME = Duration.ofDays(7);

    private final ActivityOptions options = ActivityOptions.newBuilder()
            .setStartToCloseTimeout(REMINDER_EXPIRATION_TIME)
            .build();

    private final SendReminderEmailActivities activities =
            Workflow.newActivityStub(SendReminderEmailActivities.class, options);

    @Override
    public void run(final ContractEvent contractEvent) {
        while (true) {
            try {
                Workflow.sleep(REMINDER_EXPIRATION_TIME);
                activities.sendReminderEmail(contractEvent);
            } catch (CanceledFailure e) {
                LOG.info("Workflow with id {} was cancelled", Workflow.getInfo().getWorkflowId());
                break;
            }
        }
    }
}
