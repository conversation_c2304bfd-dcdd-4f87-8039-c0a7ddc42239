package com.sast.store.entitlementmanagement.dao;

import com.sast.store.commons.tenant.api.Tenant;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;

import java.util.Optional;

@Component
public class LitmosTeamDao extends AbstractDynamodbDao<LitmosTeamEntity> {

    public LitmosTeamDao(final DynamoDbEnhancedClient dynamoDbEnhancedClient) {
        super(dynamoDbEnhancedClient, "bossstore_litmos_teams", LitmosTeamEntity.class);
    }

    public Optional<LitmosTeamEntity> findByCompanyId(final Tenant tenant, final String companyId) {
        return findAllByPartitionValue(tenant.id() + "/" + companyId).findAny();
    }
}
