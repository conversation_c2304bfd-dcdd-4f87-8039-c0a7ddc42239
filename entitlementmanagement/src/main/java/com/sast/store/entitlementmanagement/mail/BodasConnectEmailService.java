package com.sast.store.entitlementmanagement.mail;

import com.sast.store.contractmanagement.api.ContractDto;
import com.sast.store.entitlementmanagement.config.AppConfiguration;
import com.sast.store.external.email.EmailServiceClient;
import com.sast.store.external.email.data.EmailRecipient;
import com.sast.store.external.email.data.EmailTenant;
import com.sast.store.external.email.data.TemplatedEmail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BodasConnectEmailService extends AbstractEntitlementEmailService {

    public BodasConnectEmailService(final EmailServiceClient emailServiceClient, final AppConfiguration appConfiguration) {
        super(emailServiceClient, appConfiguration);
    }

    public void notifyBodasLicenseNotCreated(final ContractDto contractDto) {
        LOG.info("Sending Bodas license not created email for contract {}", contractDto.contractId());

        emailServiceClient.sendIgnoringFailures(TemplatedEmail.builder()
                .to(EmailRecipient.forUmpCompanyId(contractDto.companyId()))
                .tenant(EmailTenant.valueOf(contractDto.tenant().id()))
                .templateData(BodasConnectReminderEmailDto.builder()
                        .marketplaceUrl(getMarketplaceUrl(contractDto.tenant()))
                        .build())
                .build());
    }
}
