package com.sast.store.entitlementmanagement.service;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.contractmanagement.api.ContractDto;
import com.sast.store.entitlementmanagement.config.AppConfiguration;
import com.sast.store.entitlementmanagement.dao.BcCentralLicenseEntity;
import com.sast.store.external.bccentral.BcCentralClient;
import com.sast.store.external.bccentral.api.ContractStatus;
import com.sast.store.external.bccentral.api.CreateBooking;
import com.sast.store.external.bccentral.api.CreateContractRequest;
import com.sast.store.external.bccentral.api.CreateContractResponse;
import com.sast.store.external.bccentral.api.Customer;
import com.sast.store.external.bccentral.api.GetContractResponse;
import com.sast.store.external.bccentral.api.UpdateBooking;
import com.sast.store.external.bccentral.api.UpdateContractRequest;
import com.sast.store.external.countriesservice.api.CountriesServiceClient;
import com.sast.store.external.ump.LenientUmpClient;
import com.sast.store.gen.client.ump.api.UmpExternalCompanyDto;
import com.sast.store.gen.client.ump.api.UmpUserDto;
import com.sast.store.productmanagement.api.ProductApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.ZoneOffset;
import java.util.Currency;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

@Component
@Slf4j
@RequiredArgsConstructor
public class BcCentralExporter {
    private static final String USER_UNKNOWN = "user unknown";

    private final BcCentralClient bcCentralClient;
    private final ProductApi productApi;
    private final LenientUmpClient lenientUmpClient;
    private final CountriesServiceClient countriesServiceClient;
    private final AppConfiguration appConfiguration;

    public Optional<ContractStatus> getContractStatus(final BcCentralLicenseEntity bcCentralLicenseEntity) {
        try {
            LOG.debug("Trying to fetch BC central contract with ID {}", bcCentralLicenseEntity.getBccentralContractId());
            final GetContractResponse contractResponse = bcCentralClient.getContract(bcCentralLicenseEntity.getBccentralContractId());
            if (contractResponse != null) {
                return Optional.ofNullable(contractResponse.status());
            }
            return Optional.empty();
        } catch (final IllegalStateException e) {
            LOG.error("BC central contract with id={} not found: {}",
                    bcCentralLicenseEntity.getBccentralContractId(), e.getMessage(), e);
            return  Optional.empty();
        }
    }

    public ContractStatus createContract(final BcCentralLicenseEntity license, final ContractDto contractDto) {
        final UmpExternalCompanyDto companyDetails = lenientUmpClient
                .getCompanyDetails(license.getTenant().id(), license.getCompany())
                .orElseThrow(() -> new IllegalArgumentException("Cannot find company with id %s"
                        .formatted(license.getCompany())));

        final CreateContractResponse response = bcCentralClient.createContract(CreateContractRequest.builder()
                .contractId(license.getBccentralContractId())
                .booking(asCreateBookings(contractDto))
                .customer(Customer.builder()
                        .currency(getCurrencyOfCountry(license.getTenant(), companyDetails.getCompanyCountry()))
                        .id(companyDetails.getBpmdId())
                        .name(companyDetails.getCompanyName())
                        .build())
                .tenant(com.sast.store.external.bccentral.api.Tenant.builder()
                        .name(license.getBccentralTenantName())
                        .owner(license.getOwnerEmails())
                        .build())
                .build());
        LOG.info("Created BC central contract with id={}: {}", license.getBccentralContractId(), response);
        return ContractStatus.PENDING;
    }

    public void updateContract(final BcCentralLicenseEntity license, final ContractDto contractDto) {
        final UmpExternalCompanyDto companyDetails = lenientUmpClient
                .getCompanyDetails(license.getTenant().id(), license.getCompany())
                .orElseThrow(() -> new IllegalArgumentException("Cannot get company details for company %s/%s"
                        .formatted(license.getTenant(), license.getCompany())));

        final String requestor;
        if (contractDto.lastModifiedByUserId() != null) {
            requestor = lenientUmpClient.getUser(license.getTenant().id(), contractDto.lastModifiedByUserId())
                    .map(UmpUserDto::getEmail)
                    .orElse(companyDetails.getCompanyEmail());
        } else {
            requestor = Optional.ofNullable(appConfiguration.tenants().get(license.getTenant()))
                    .map(AppConfiguration.TenantConfig::publicEmails)
                    .map(AppConfiguration.PublicEmailConfig::supportEmail)
                    .orElse(USER_UNKNOWN);
        }

        bcCentralClient.updateContract(UpdateContractRequest.builder()
                .contractId(license.getBccentralContractId())
                .requestorId(requestor)
                .requestId(UUID.randomUUID().toString())
                .customerCurrency(Currency.getInstance(getCurrencyOfCountry(license.getTenant(), companyDetails.getCompanyCountry())))
                .booking(asUpdateBookings(contractDto))
                .build());
    }

    private String getExternalProductId(final Tenant tenant, final String productId) {
        return productApi.getProductVariant(tenant, productId, null).orElseThrow().variants().getFirst().externalProductId();
    }

    private String getCurrencyOfCountry(final Tenant tenant, final String countryCode) {
        return countriesServiceClient
                .getCountry(com.sast.store.external.countriesservice.api.Tenant.valueOf(tenant.id()), countryCode)
                .getTenantConfigurations().stream().findAny().orElseThrow().getCurrency();
    }

    private List<CreateBooking> asCreateBookings(final ContractDto contract) {
        final CreateBooking baseContract = CreateBooking.builder()
                .licenseKey(contract.contractId())
                .materialNumber(getExternalProductId(contract.tenant(), contract.productId()))
                .build();

        final Stream<CreateBooking> addons = contract.addons().stream()
                .map(a -> CreateBooking.builder()
                        .licenseKey(a.contractId())
                        .materialNumber(getExternalProductId(contract.tenant(), a.productId()))
                        .build());

        return Stream.concat(Stream.of(baseContract), addons).toList();
    }

    private List<UpdateBooking> asUpdateBookings(final ContractDto contract) {
        final UpdateBooking baseContract = UpdateBooking.builder()
                .licenseKey(contract.contractId())
                .materialNumber(getExternalProductId(contract.tenant(), contract.productId()))
                .startDate(contract.startDate().atZoneSameInstant(ZoneOffset.UTC))
                .endDate(Optional.ofNullable(contract.endDate())
                        .map(endDate -> endDate.atZoneSameInstant(ZoneOffset.UTC)).orElse(null))
                .build();

        final Stream<UpdateBooking> addons = contract.addons().stream()
                .map(addonDto -> UpdateBooking.builder()
                        .licenseKey(addonDto.contractId())
                        .materialNumber(getExternalProductId(contract.tenant(), addonDto.productId()))
                        .startDate(addonDto.startDate().atZoneSameInstant(ZoneOffset.UTC))
                        .endDate(Optional.ofNullable(addonDto.endDate())
                                .map(endDate -> endDate.atZoneSameInstant(ZoneOffset.UTC)).orElse(null))
                        .build());

        return Stream.concat(Stream.of(baseContract), addons).toList();
    }
}
