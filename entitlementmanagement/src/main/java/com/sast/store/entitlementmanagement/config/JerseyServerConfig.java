package com.sast.store.entitlementmanagement.config;

import com.sast.store.commons.basewebapp.rest.CommonJerseyServerConfig;
import com.sast.store.entitlementmanagement.rest.LicenseRestService;
import jakarta.annotation.PostConstruct;
import jakarta.ws.rs.ApplicationPath;
import org.glassfish.jersey.server.ResourceConfig;
import org.springframework.context.annotation.Configuration;

@Configuration
@ApplicationPath("/rest")
public class JerseyServerConfig extends ResourceConfig {

    @PostConstruct
    public void init() {
        register(LicenseRestService.class);

        CommonJerseyServerConfig.defaultRegistrationsAndProperties(clazz -> register(clazz), (prop, val) -> property(prop, val));
    }
}

