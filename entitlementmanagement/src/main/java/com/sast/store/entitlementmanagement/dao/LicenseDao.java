package com.sast.store.entitlementmanagement.dao;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.entitlementmanagement.api.LicenseModel;
import jakarta.inject.Inject;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import software.amazon.awssdk.enhanced.dynamodb.mapper.BeanTableSchema;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;
import software.amazon.awssdk.services.dynamodb.model.DeleteItemRequest;
import software.amazon.awssdk.services.dynamodb.model.QueryRequest;
import software.amazon.awssdk.services.dynamodb.model.ScanRequest;

import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;

@Component
public class LicenseDao {

    private static final String TABLE_NAME = "BossstoreLicenses";

    @Inject
    private DynamoDbClient dynamoDbClient;

    @Inject
    private DynamoDbEnhancedClient dynamoDbEnhancedClient;

    private final Map<LicenseModel, BeanTableSchema<? extends LicenseEntity<?>>> tableSchemas = Map
        .of(
            LicenseModel.DCKEYCLOAK, TableSchema.fromBean(DcKeycloakLicenseEntity.class),
            LicenseModel.LITMOS, TableSchema.fromBean(LitmosLicenseEntity.class),
            LicenseModel.BCCENTRAL, TableSchema.fromBean(BcCentralLicenseEntity.class));

    public void save(final LicenseEntity<?> entity) {
        getTable(entity).putItem(entity);
    }

    public void delete(final LicenseEntity<?> entity) {
        dynamoDbClient.deleteItem(DeleteItemRequest.builder()
            .tableName(TABLE_NAME)
            .key(Map.of(
                "companyId", AttributeValue.builder().s(entity.getCompanyId()).build(),
                "licenseId", AttributeValue.builder().s(entity.getLicenseId()).build()))
            .build());
    }

    public Stream<LicenseEntity<?>> findAllByCompanyId(final Tenant tenant, final String companyId) {
        return dynamoDbClient.queryPaginator(QueryRequest.builder()
            .tableName(TABLE_NAME)
            .keyConditionExpression("companyId = :companyId")
            .expressionAttributeValues(Map.of(":companyId", AttributeValue.builder().s(tenant.id() + "/" + companyId).build()))
            .build())
            .items().stream()
            .map(this::mapItem);
    }

    public Optional<LicenseEntity<?>> findByCompanyIdAndLicenseId(final Tenant tenant, final String companyId,
        final String licenseId) {
        return dynamoDbClient.query(QueryRequest.builder()
            .tableName(TABLE_NAME)
            .keyConditionExpression("companyId = :companyId AND licenseId = :licenseId")
            .expressionAttributeValues(Map.of(
                ":companyId", AttributeValue.builder().s(tenant.id() + "/" + companyId).build(),
                ":licenseId", AttributeValue.builder().s(licenseId).build()))
            .build())
            .items().stream().findFirst()
            .map(this::mapItem);
    }

    public Stream<LicenseEntity<?>> findByContractId(final Tenant tenant, final String companyId, final String contractId) {
        return dynamoDbClient.queryPaginator(QueryRequest.builder()
            .tableName(TABLE_NAME)
            .keyConditionExpression("companyId = :companyId")
            .filterExpression("contractId = :contractId")
            .expressionAttributeValues(Map.of(
                ":companyId", AttributeValue.builder().s(tenant.id() + "/" + companyId).build(),
                ":contractId", AttributeValue.builder().s(contractId).build()))
            .build())
            .items().stream()
            .map(this::mapItem);
    }

    public Stream<LicenseEntity<?>> findAll() {
        return dynamoDbClient.scanPaginator(ScanRequest.builder()
            .tableName(TABLE_NAME)
            .build())
            .items().stream()
            .map(this::mapItem);
    }

    private LicenseEntity<?> mapItem(final Map<String, AttributeValue> item) {
        final LicenseModel licenseModel = LicenseModel.valueOf(item.get("licenseModel").s());
        return tableSchemas.get(licenseModel).mapToItem(item);
    }

    @SuppressWarnings("unchecked")
    private DynamoDbTable<LicenseEntity<?>> getTable(final LicenseEntity<?> entity) {
        final BeanTableSchema<? extends LicenseEntity<?>> beanTableSchema = tableSchemas.get(entity.getLicenseModel());
        return (DynamoDbTable<LicenseEntity<?>>) dynamoDbEnhancedClient.table(TABLE_NAME, beanTableSchema);
    }

    public Stream<BcCentralLicenseEntity> findByCancellationStateWithEndDate(final CancellationState state) {
        return dynamoDbClient.scanPaginator(ScanRequest.builder()
            .tableName(TABLE_NAME)
            .filterExpression("cancellationState = :state AND attribute_exists(endDate) AND licenseModel = :licenseModel")
            .expressionAttributeValues(Map.of(":state", AttributeValue.builder().s(state.name()).build(),
                ":licenseModel", AttributeValue.builder().s(LicenseModel.BCCENTRAL.name()).build()))
            .build())
            .items().stream()
            .map(this::mapItem)
            .map(BcCentralLicenseEntity.class::cast);
    }
}
