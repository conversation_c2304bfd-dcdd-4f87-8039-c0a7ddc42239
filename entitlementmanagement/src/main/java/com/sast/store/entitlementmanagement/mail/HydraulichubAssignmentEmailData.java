package com.sast.store.entitlementmanagement.mail;

import com.sast.store.external.email.data.EmailTemplateData;
import com.sast.store.external.email.data.EmailTenant;

import java.net.URI;

public record HydraulichubAssignmentEmailData(
    String firstName,
    String lastName,
    String changedByFirstName,
    String changedByLastName,
    String assignedUserEmail,
    URI hydraulicHubUrl) implements EmailTemplateData {

    @Override
    public String getTemplateName(final EmailTenant emailTenant) {
        return "rexroth/entitlements/hydraulichubAssignment";
    }

    public static class Builder {
        private String firstName;
        private String lastName;
        private String changedByFirstName;
        private String changedByLastName;
        private String assignedUserEmail;
        private URI hydraulicHubUrl;

        public Builder firstName(final String firstName) {
            this.firstName = firstName;
            return this;
        }

        public Builder lastName(final String lastName) {
            this.lastName = lastName;
            return this;
        }

        public Builder changedByFirstName(final String changedByFirstName) {
            this.changedByFirstName = changedByFirstName;
            return this;
        }

        public Builder changedByLastName(final String changedByLastName) {
            this.changedByLastName = changedByLastName;
            return this;
        }

        public Builder assignedUserEmail(final String assignedUserEmail) {
            this.assignedUserEmail = assignedUserEmail;
            return this;
        }

        public Builder hydraulicHubUrl(final URI hydraulicHubUrl) {
            this.hydraulicHubUrl = hydraulicHubUrl;
            return this;
        }

        public HydraulichubAssignmentEmailData build() {
            return new HydraulichubAssignmentEmailData(firstName, lastName, changedByFirstName, changedByLastName, assignedUserEmail,
                hydraulicHubUrl);
        }
    }

    public static Builder builder() {
        return new Builder();
    }
}
