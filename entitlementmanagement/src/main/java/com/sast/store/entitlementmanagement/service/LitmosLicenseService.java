package com.sast.store.entitlementmanagement.service;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.contractmanagement.api.ContractDto;
import com.sast.store.contractmanagement.api.ContractEvent;
import com.sast.store.entitlementmanagement.api.AssignLicenseDto.AssignLicenseData;
import com.sast.store.entitlementmanagement.api.AssignLicenseDto.DcKeycloakAssignLicenseDto;
import com.sast.store.entitlementmanagement.api.LicenseModel;
import com.sast.store.entitlementmanagement.config.AppConfiguration;
import com.sast.store.entitlementmanagement.dao.LicenseDao;
import com.sast.store.entitlementmanagement.dao.LicenseEntity;
import com.sast.store.entitlementmanagement.dao.LitmosLicenseEntity;
import com.sast.store.entitlementmanagement.dao.LitmosTeamDao;
import com.sast.store.entitlementmanagement.dao.LitmosTeamEntity;
import com.sast.store.entitlementmanagement.mail.AssignedUser;
import com.sast.store.external.litmos.LitmosClient;
import com.sast.store.external.ump.UmpClient;
import com.sast.store.gen.client.litmos.api.LitmosCreateTeamRequest;
import com.sast.store.gen.client.litmos.api.LitmosCreateUserRequest;
import com.sast.store.gen.client.litmos.api.LitmosCreateUserRequest.AccessLevelEnum;
import com.sast.store.gen.client.litmos.api.LitmosTeamInformation;
import com.sast.store.gen.client.litmos.api.LitmosUserDetails;
import com.sast.store.gen.client.litmos.api.LitmosUserInformation;
import com.sast.store.gen.client.ump.api.UmpExternalCompanyDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.dynamodb.model.ConditionalCheckFailedException;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Component
@Slf4j
@RequiredArgsConstructor
public class LitmosLicenseService implements LicenseServiceInterface {
    private final LitmosClient litmosClient;
    private final AppConfiguration appConfiguration;
    private final LicenseDao licenseDao;
    private final UmpClient umpClient;
    private final LitmosTeamDao litmosTeamDao;

    @Override
    public void createLicense(final Tenant tenant, final ContractDto contract,
        final String changedByUserId, final List<String> entitlements, final AssignLicenseData assignLicenseData) {
        final DcKeycloakAssignLicenseDto assignLicenseDto = DcKeycloakAssignLicenseDto.class.cast(assignLicenseData);
        final AssignedUser assignedUser = AssignedUser.builder()
            .emailAddress(assignLicenseDto.email())
            .firstName(assignLicenseDto.firstname())
            .lastName(assignLicenseDto.lastname())
            .build();
        final UmpExternalCompanyDto companyDetails = umpClient.getCompanyDetails(tenant.id(), contract.companyId());

        final LitmosUserInformation litmosUser = getOrCreateUserLitmos(assignedUser, companyDetails.getCompanyName());
        LOG.info("Litmos user: {}", litmosUser);

        final LitmosTeamInformation litmosTeam = getOrCreateTeamLitmos(tenant, contract.companyId(),
            companyDetails.getCompanyName());

        litmosClient.assignUsersToTeam(litmosTeam.getId(), List.of(litmosUser.getId()));
        LOG.info("Assigned user {} to team {}", assignedUser.emailAddress(), litmosTeam);

        final LitmosLicenseEntity licenseEntity = getOrCreateLicense(tenant, contract.companyId(), contract.contractId(),
            contract.productId(), assignedUser);
        LOG.info("Created license for user {}", licenseEntity);
    }

    private LitmosUserInformation getOrCreateUserLitmos(final AssignedUser assignedUser, final String companyName) {
        return litmosClient.getUsers(assignedUser.emailAddress()).stream().findFirst().orElseGet(() -> {
            final LitmosUserDetails litmosUserDetails = litmosClient.createUser(new LitmosCreateUserRequest()
                .firstName(assignedUser.firstName())
                .lastName(assignedUser.lastName())
                .email(assignedUser.emailAddress())
                .userName(assignedUser.emailAddress())
                .companyName(companyName)
                .accessLevel(AccessLevelEnum.LEARNER)
                .active(Boolean.TRUE)
                .timeZone("UTC")
                .skipFirstLogin(Boolean.TRUE));
            LOG.info("Created user in Litmos {}", litmosUserDetails);
            return litmosClient.getUsers(assignedUser.emailAddress()).stream().findFirst().orElseThrow();
        });
    }

    private LitmosTeamInformation getOrCreateTeamLitmos(final Tenant tenant, final String companyId, final String companyName) {
        return litmosTeamDao.findByCompanyId(tenant, companyId)
            .map(LitmosTeamEntity::getTeamId)
            .map(litmosClient::getTeam)
            .orElseGet(() -> {
                try {
                    final LitmosTeamEntity teamEntity = new LitmosTeamEntity().withCompanyId(tenant, companyId);
                    litmosTeamDao.create(teamEntity);

                    final String parentTeamId = appConfiguration.dcLicensing().litmosParentTeamId();
                    final LitmosCreateTeamRequest requestDto = new LitmosCreateTeamRequest()
                        .name(companyName)
                        .description("Managed by Boss Marketplace. Do not change!")
                        .parentTeamId(parentTeamId)
                        .copyParentCourses(Boolean.TRUE)
                        .copyParentCourseLibraries(Boolean.TRUE)
                        .copyParentLearningpathLibraries(Boolean.TRUE);
                    final LitmosTeamInformation team = litmosClient.createTeam(parentTeamId, requestDto);
                    LOG.info("Created team in Litmos {}", team);

                    teamEntity.setTeamId(team.getId());
                    litmosTeamDao.save(teamEntity);

                    return team;
                } catch (final ConditionalCheckFailedException e) {
                    LOG.warn("Litmos team already created concurrently", e);
                    // The concurrent team creation might still be in progress at this point. But we try our luck to get the team id on the
                    // existing LitmosTeamEntity.
                    return litmosTeamDao.findByCompanyId(tenant, companyId)
                        .map(LitmosTeamEntity::getTeamId)
                        .map(litmosClient::getTeam)
                        .orElseThrow();
                }
            });
    }

    private LitmosLicenseEntity getOrCreateLicense(final Tenant tenant, final String companyId, final String contractId,
        final String productId, final AssignedUser assignedUser) {
        return findLicense(tenant, companyId, contractId).orElseGet(() -> {
            final LitmosLicenseEntity license = new LitmosLicenseEntity()
                .withCompanyId(tenant, companyId)
                .withStartDate(Instant.now())
                .withLicenseId(UUID.randomUUID().toString())
                .withLicenseModel(getLicenseModel())
                .withEmail(assignedUser.emailAddress())
                .withFirstname(assignedUser.firstName())
                .withLastname(assignedUser.lastName())
                .withContractId(contractId)
                .withProductId(productId);
            licenseDao.save(license);
            return license;
        });
    }

    private Optional<LitmosLicenseEntity> findLicense(final Tenant tenant, final String companyId, final String contractId) {
        return licenseDao.findAllByCompanyId(tenant, companyId)
            .filter(l -> l.getLicenseModel() == LicenseModel.LITMOS)
            .filter(l -> l.getContractId().equals(contractId))
            .map(LitmosLicenseEntity.class::cast)
            .findAny();
    }

    @Override
    public LicenseModel getLicenseModel() {
        return LicenseModel.LITMOS;
    }

    @Override
    public void unassignLicense(final LicenseEntity<?> license, final String changedByUserId) {
        final LitmosLicenseEntity litmosLicense = LitmosLicenseEntity.class.cast(license);
        final LitmosUserInformation litmosUser = litmosClient
                .getUsers(litmosLicense.getEmail()).stream().findFirst()
                .orElseThrow(() -> new IllegalStateException(
                        "Cannot unassign license %s because litmosUser with email %s does not exist in litmos"
                                .formatted(litmosLicense.getLicenseId(), litmosLicense.getEmail())));
        final LitmosTeamEntity litmosTeam = litmosTeamDao
                .findByCompanyId(litmosLicense.getTenant(), litmosLicense.getCompany())
                .orElseThrow(() -> new IllegalStateException(
                        "Cannot unassign license %s because litmos team for companyId=%s does not exist in dynamodb"
                                .formatted(litmosLicense.getLicenseId(), litmosLicense.getCompanyId())));

        LOG.info("Unassigning litmos user with id={} from team with litmos teamId={}",
                litmosUser.getId(), litmosTeam.getTeamId());
        litmosClient.unassignUserFromTeam(litmosTeam.getTeamId(), litmosUser.getId());
        licenseDao.delete(litmosLicense);
        LOG.info("license {} deleted", litmosLicense);
    }

    @Override
    public void contractExpired(final LicenseEntity<?> license, final ContractEvent contract) {
        this.unassignLicense(license, contract.contractDto().cancelledByUserId());
    }
}
