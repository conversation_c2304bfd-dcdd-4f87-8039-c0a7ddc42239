package com.sast.store.entitlementmanagement.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class BCCentralCancellationTask {

    private final BcCentralLicenseCancellationService bcCentralLicenseCancellationService;

    @Scheduled(cron = "0 0 3 * * ?")
    @SchedulerLock(name = "entitlementmanagement.bcCentralCancellationTask", lockAtLeastFor = "PT60M", lockAtMostFor = "PT60M")
    public void processBCCentralCancellationsJob() {
        bcCentralLicenseCancellationService.processLicenseCancellations();
    }
}
