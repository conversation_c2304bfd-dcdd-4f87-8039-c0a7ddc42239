package com.sast.store.entitlementmanagement.service;

import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.models.order.Order;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.contractmanagement.api.ContractDto;
import com.sast.store.contractmanagement.api.ContractEvent;
import com.sast.store.entitlementmanagement.api.AssignLicenseDto.AssignLicenseData;
import com.sast.store.entitlementmanagement.api.AssignLicenseDto.BcCentralAssignLicenseDto;
import com.sast.store.entitlementmanagement.api.LicenseModel;
import com.sast.store.entitlementmanagement.dao.BcCentralLicenseEntity;
import com.sast.store.entitlementmanagement.dao.CancellationState;
import com.sast.store.entitlementmanagement.dao.LicenseDao;
import com.sast.store.entitlementmanagement.dao.LicenseEntity;
import com.sast.store.entitlementmanagement.workflows.SendReminderEmailWorkflow;
import com.sast.store.external.commercetools.util.CustomFieldProvider;
import com.sast.store.external.ump.LenientUmpClient;
import com.sast.store.gen.client.ump.api.UmpExternalCompanyDto;
import com.sast.store.gen.client.ump.api.UmpUserDto;
import io.temporal.client.WorkflowClient;
import io.temporal.client.WorkflowNotFoundException;
import io.temporal.client.WorkflowOptions;
import io.temporal.client.WorkflowStub;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Component
@Slf4j
@RequiredArgsConstructor
public class BcCentralLicenseService implements LicenseServiceInterface {
    private final LenientUmpClient lenientUmpClient;
    private final LicenseDao licenseDao;
    private final ProjectApiRoot commercetoolsClient;
    private final BcCentralExporter bcCentralExporter;
    private final WorkflowClient workflowClient;

    @Value("${spring.temporal.workers[0].task-queue}")
    private String workflowTaskQueueName;

    @Override
    public void createLicense(final Tenant tenant, final ContractDto contractDto,
        final String changedByUserId, final List<String> entitlements, final AssignLicenseData assignLicenseDto) {
        final BcCentralAssignLicenseDto assignLicenseData = BcCentralAssignLicenseDto.class.cast(assignLicenseDto);

        final BcCentralLicenseEntity license = getOrCreateLicense(tenant, contractDto, assignLicenseData);

        try {
            bcCentralExporter.getContractStatus(license)
                    .orElseGet(() -> bcCentralExporter.createContract(license, contractDto));
            cancelWorkflow(contractDto);
        } catch (final RuntimeException ex) {
            LOG.info("Unknown error during create contract request", ex);
            throw ex;
        }
    }

    private void cancelWorkflow(final ContractDto contract) {
        try {
            final SendReminderEmailWorkflow workflow = workflowClient.newWorkflowStub(
                SendReminderEmailWorkflow.class, contract.contractId());
            WorkflowStub.fromTyped(workflow).cancel();
        } catch (final WorkflowNotFoundException ex) {
            LOG.warn("Reminder email workflow does not exist or is already completed for contract id {}", contract.contractId());
        } catch (final Exception ex) {
            LOG.error("Error during cancellation of reminder email workflow for contract id {}", contract.contractId(), ex);
        }
    }

    private BcCentralLicenseEntity getOrCreateLicense(final Tenant tenant, final ContractDto contract,
        final BcCentralAssignLicenseDto assignLicenseData) {
        return findLicense(contract).orElseGet(() -> {
            final BcCentralLicenseEntity license = new BcCentralLicenseEntity()
                .withLicenseId(UUID.randomUUID().toString())
                .withCompanyId(tenant, contract.companyId())
                .withStartDate(Instant.now())
                .withBccentralContractId(UUID.randomUUID().toString())
                .withLicenseModel(getLicenseModel())
                .withBccentralTenantName(assignLicenseData.name())
                .withOwnerEmails(assignLicenseData.emails())
                .withContractId(contract.contractId())
                .withProductId(contract.productId());
            licenseDao.save(license);
            LOG.info("Created license {}", license);
            return license;
        });
    }

    private Optional<BcCentralLicenseEntity> findLicense(final ContractDto contract) {
        return licenseDao
            .findByContractId(contract.tenant(), contract.companyId(), contract.contractId())
            .filter(l -> l.getLicenseModel() == getLicenseModel())
            .map(BcCentralLicenseEntity.class::cast)
            .findAny();
    }

    @Override
    public LicenseModel getLicenseModel() {
        return LicenseModel.BCCENTRAL;
    }

    @Override
    public void unassignLicense(final LicenseEntity<?> license, final String changedByUserId) {
        final BcCentralLicenseEntity licenseEntity = BcCentralLicenseEntity.class.cast(license);
        LOG.info("Unassigning license {}", licenseEntity);
    }

    @Override
    public void contractCreated(final ContractEvent contractEvent) {
        final WorkflowOptions workflowOptions = WorkflowOptions.newBuilder()
                .setWorkflowId(contractEvent.contractDto().contractId())
                .setTaskQueue(workflowTaskQueueName)
                .build();

        final SendReminderEmailWorkflow workflow = workflowClient.newWorkflowStub(
                SendReminderEmailWorkflow.class, workflowOptions);
        WorkflowClient.start(workflow::run, contractEvent);
    }

    @Override
    public void contractCancelled(final LicenseEntity<?> license, final ContractEvent contract) {
        final BcCentralLicenseEntity licenseEntity = (BcCentralLicenseEntity) license;

        try {
            if (contract.contractDto().endDate() != null) {
                licenseEntity.setEndDate(contract.contractDto().endDate().toInstant());
                licenseEntity.setCancellationState(CancellationState.CANCELLATION_PENDING);
                licenseEntity.setCancelledByUserId(isUserIdAvailable(contract) ? contract.contractDto().cancelledByUserId() : null);
                LOG.debug("Setting cancellation pending for license {}", licenseEntity);
            } else {
                licenseEntity.setEndDate(null);
            }
            licenseDao.save(licenseEntity);
        } catch (final RuntimeException ex) {
            LOG.warn("Failed to send contract cancellation email for license {}", licenseEntity, ex);
        }
    }

    @Override
    public void contractUpdated(final LicenseEntity<?> license, final ContractEvent contract) {
        final BcCentralLicenseEntity licenseEntity = BcCentralLicenseEntity.class.cast(license);
        LOG.info("Received contract update: {}", contract.contractDto());
        bcCentralExporter.updateContract(licenseEntity, contract.contractDto());
        LOG.info("BC central contract {} updated", licenseEntity.getBccentralContractId());
    }

    @Override
    public Optional<BcCentralAssignLicenseDto> getDefaults(final Tenant tenant, final ContractDto contract) {
        final var name = lenientUmpClient.getCompanyDetails(tenant.id(), contract.companyId())
            .map(UmpExternalCompanyDto::getCompanyName);

        final var email = fetchOrderUserId(contract.orderNumber())
            .flatMap(userId -> lenientUmpClient.getUser(tenant.id(), userId))
            .map(UmpUserDto::getEmail);

        if (name.isPresent() || email.isPresent()) {
            return Optional.of(
                new BcCentralAssignLicenseDto(
                    contract.contractId(),
                    name
                        .map(this::stripSpecialChars)
                        .orElse(""),
                    email
                        .map(List::of)
                        .orElse(List.of())));
        } else {
            return Optional.empty();
        }
    }

    private String stripSpecialChars(final String s) {
        return s.replaceAll("[^a-zA-Z0-9\\- ]", "");
    }

    private Optional<String> fetchOrderUserId(final String orderNumber) {
        try {
            final Order order = commercetoolsClient.orders()
                .withOrderNumber(orderNumber)
                .get()
                .executeBlocking()
                .getBody();

            return CustomFieldProvider.getUserId(order);
        } catch (final Exception e) {
            LOG.warn("Failed to query buyer user id with orderNumber={}", orderNumber, e);
            return Optional.empty();
        }
    }

    private boolean isUserIdAvailable(final ContractEvent contract) {
        return contract.contractDto() != null && StringUtils.isNotBlank(contract.contractDto().cancelledByUserId());
    }
}
