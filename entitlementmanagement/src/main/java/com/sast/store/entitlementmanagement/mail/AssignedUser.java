package com.sast.store.entitlementmanagement.mail;

public record AssignedUser(
    String firstName,
    String lastName,
    String emailAddress) {

    public static final class Builder {
        private String firstName;
        private String lastName;
        private String emailAddress;

        private Builder() {
        }

        public Builder firstName(final String firstName) {
            this.firstName = firstName;
            return this;
        }

        public Builder lastName(final String lastName) {
            this.lastName = lastName;
            return this;
        }

        public Builder emailAddress(final String emailAddress) {
            this.emailAddress = emailAddress;
            return this;
        }

        public AssignedUser build() {
            return new AssignedUser(firstName, lastName, emailAddress);
        }
    }

    public static Builder builder() {
        return new Builder();
    }
}
