package com.sast.store.entitlementmanagement.service;

import com.sast.store.entitlementmanagement.api.LicenseModel;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class LicenseServiceRegistry {
    private static final Logger LOG = LoggerFactory.getLogger(LicenseServiceRegistry.class);
    @Inject
    private List<LicenseServiceInterface> services;

    private Map<LicenseModel, LicenseServiceInterface> registry;

    @PostConstruct
    void setup() {
        registry = services.stream().collect(Collectors.toMap(LicenseServiceInterface::getLicenseModel, Function.identity()));
        LOG.info("registered services {}", registry);
        if (registry.isEmpty()) {
            LOG.error("No services registered");
        }
        if (registry.size() != services.size()) {
            LOG.error("Not all services could be registered. Registered: {}, expected: {}", registry, services);
        }
    }

    public LicenseServiceInterface getService(final LicenseModel licenseModel) {
        final LicenseServiceInterface service = registry.get(licenseModel);
        if (service == null) {
            LOG.warn("Service not found: {}", licenseModel);
            throw new IllegalArgumentException();
        }
        return service;
    }

    public Collection<LicenseServiceInterface> getAll() {
        return registry.values();
    }
}
