package com.sast.store.entitlementmanagement.activities;

import com.sast.store.contractmanagement.api.ContractEvent;
import com.sast.store.entitlementmanagement.mail.BodasConnectEmailService;
import io.temporal.spring.boot.ActivityImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
@ActivityImpl(workers = "send-email-worker")
public class SendReminderEmailActivitiesImpl implements SendReminderEmailActivities {

    private final BodasConnectEmailService emailService;

    @Override
    public void sendReminderEmail(final ContractEvent contractEvent) {
        emailService.notifyBodasLicenseNotCreated(contractEvent.contractDto());
    }
}
