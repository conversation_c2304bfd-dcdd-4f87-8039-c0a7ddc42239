package com.sast.store.entitlementmanagement.mail;

import com.sast.store.external.email.data.EmailTemplateData;
import com.sast.store.external.email.data.EmailTenant;

import java.net.URI;
import java.time.LocalDateTime;
import java.time.Period;

public record RexrothExpirationSoonEmailDto(
    String firstName,
    String lastName,
    LocalDateTime endDate,
    String productName,
    URI marketplaceUrl,
    Period expiration) implements EmailTemplateData {

    @Override
    public String getTemplateName(final EmailTenant emailTenant) {
        return "rexroth/entitlements/hydraulichubExpirationSoon";
    }

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {
        private String firstName;
        private String lastName;
        private LocalDateTime endDate;
        private String productName;
        private URI marketplaceUrl;
        private Period expiration;

        private Builder() {
        }

        public Builder firstName(final String firstName) {
            this.firstName = firstName;
            return this;
        }

        public Builder lastName(final String lastName) {
            this.lastName = lastName;
            return this;
        }

        public Builder endDate(final LocalDateTime endDate) {
            this.endDate = endDate;
            return this;
        }

        public Builder productName(final String productName) {
            this.productName = productName;
            return this;
        }

        public Builder marketplaceUrl(final URI marketplaceUrl) {
            this.marketplaceUrl = marketplaceUrl;
            return this;
        }

        public Builder expiration(final Period expiration) {
            this.expiration = expiration;
            return this;
        }

        public RexrothExpirationSoonEmailDto build() {
            return new RexrothExpirationSoonEmailDto(firstName, lastName, endDate, productName, marketplaceUrl, expiration);
        }
    }

}
