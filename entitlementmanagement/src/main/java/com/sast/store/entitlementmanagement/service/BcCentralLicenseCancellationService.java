package com.sast.store.entitlementmanagement.service;

import com.google.common.annotations.VisibleForTesting;
import com.sast.store.entitlementmanagement.dao.BcCentralLicenseEntity;
import com.sast.store.entitlementmanagement.dao.CancellationState;
import com.sast.store.entitlementmanagement.dao.LicenseDao;
import com.sast.store.external.bccentral.BcCentralClient;
import com.sast.store.external.bccentral.api.DeleteContractRequest;
import com.sast.store.external.bccentral.api.DeleteContractResponse;
import com.sast.store.external.ump.UmpClient;
import com.sast.store.gen.client.ump.api.UmpUserDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class BcCentralLicenseCancellationService {
    private static final int CANCELLATION_WINDOW_IN_DAYS = 3;

    private final LicenseDao licenseDao;
    private final BcCentralClient bcCentralClient;
    private final UmpClient umpClient;

    public void processLicenseCancellations() {

        if (isNotInCancellationWindow()) {
            return;
        }

        final List<BcCentralLicenseEntity> licensesToCancel = licenseDao.findByCancellationStateWithEndDate(
            CancellationState.CANCELLATION_PENDING).collect(Collectors.toList());

        LOG.info("Found {} licenses for cancellation", licensesToCancel.size());

        licensesToCancel.forEach(license -> {
            try {
                final DeleteContractRequest deleteRequest = adaptRequest(license);
                final DeleteContractResponse response = bcCentralClient.deleteContract(deleteRequest);
                LOG.info("Cancelled license {}. Response: {}", license.getContractId(), response);

                license.setCancellationState(CancellationState.CANCELLED);
                licenseDao.save(license);
            } catch (Exception e) {
                LOG.error("Error cancelling license {}: {}", license.getContractId(), e.getMessage(), e);
            }
        });
    }

    private DeleteContractRequest adaptRequest(final BcCentralLicenseEntity license) {

        final String requestor = getRequestor(license);

        final DeleteContractRequest deleteRequest = DeleteContractRequest.builder()
            .contractId(license.getBccentralContractId())
            .terminationDate(license.getEndDate())
            .requestor(requestor)
            .build();

        return deleteRequest;
    }

    private String getRequestor(final BcCentralLicenseEntity license) {

        final String system = "system";
        if (StringUtils.isNotBlank(license.getCancelledByUserId())) {
            final UmpUserDto userDto = umpClient.getUser(license.getTenant().id(), license.getCancelledByUserId());
            return StringUtils.defaultIfBlank(userDto.getEmail(), system);
        }

        return system;
    }

    @VisibleForTesting
    boolean isNotInCancellationWindow() {
        final LocalDate now = now();
        final LocalDate windowEnd = now.with(TemporalAdjusters.lastDayOfMonth());
        final LocalDate windowStart = windowEnd.minusDays(CANCELLATION_WINDOW_IN_DAYS - 1);

        if (now.isBefore(windowStart)) {
            LOG.info("No licenses to cancel today; today's date is not in the cancellation period {} - {}.",
                windowStart, windowEnd);
            return true;
        }

        return false;
    }

    @VisibleForTesting
    LocalDate now() {
        return LocalDate.now();
    }

}
