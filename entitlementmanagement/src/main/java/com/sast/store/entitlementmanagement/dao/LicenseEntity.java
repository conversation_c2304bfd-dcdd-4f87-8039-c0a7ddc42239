package com.sast.store.entitlementmanagement.dao;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.entitlementmanagement.api.LicenseModel;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey;

import java.time.Instant;

@DynamoDbBean
public class LicenseEntity<T extends LicenseEntity<T>> {

    private String companyId;
    private String licenseId;
    private LicenseModel licenseModel;
    private String productId;
    private String contractId;
    private Instant startDate;
    private Instant endDate;
    private CancellationState cancellationState;

    @DynamoDbPartitionKey
    public String getCompanyId() {
        return companyId;
    }

    @DynamoDbIgnore
    public String getCompany() {
        return companyId.substring(companyId.indexOf('/') + 1);
    }

    @DynamoDbIgnore
    public Tenant getTenant() {
        return Tenant.fromString(companyId.substring(0, companyId.indexOf('/')));
    }

    public void setCompanyId(final String companyId) {
        this.companyId = companyId;
    }

    public T withCompanyId(final Tenant tenant, final String companyId) {
        this.companyId = tenant.id() + "/" + companyId;
        return returnThis();
    }

    @DynamoDbSortKey
    public String getLicenseId() {
        return licenseId;
    }

    public void setLicenseId(final String licenseId) {
        this.licenseId = licenseId;
    }

    public T withLicenseId(final String licenseId) {
        this.licenseId = licenseId;
        return returnThis();
    }

    public LicenseModel getLicenseModel() {
        return licenseModel;
    }

    public void setLicenseModel(final LicenseModel licenseModel) {
        this.licenseModel = licenseModel;
    }

    public T withLicenseModel(final LicenseModel licenseModel) {
        this.licenseModel = licenseModel;
        return returnThis();
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(final String productId) {
        this.productId = productId;
    }

    public T withProductId(final String productId) {
        this.productId = productId;
        return returnThis();
    }

    public String getContractId() {
        return contractId;
    }

    public void setContractId(final String contractId) {
        this.contractId = contractId;
    }

    public T withContractId(final String contractId) {
        this.contractId = contractId;
        return returnThis();
    }

    public Instant getStartDate() {
        return startDate;
    }

    public void setStartDate(final Instant startDate) {
        this.startDate = startDate;
    }

    public T withStartDate(final Instant startDate) {
        this.startDate = startDate;
        return returnThis();
    }

    public Instant getEndDate() {
        return endDate;
    }

    public void setEndDate(final Instant endDate) {
        this.endDate = endDate;
    }

    public T withEndDate(final Instant endDate) {
        this.endDate = endDate;
        return returnThis();
    }

    public CancellationState getCancellationState() {
        return cancellationState;
    }

    public void setCancellationState(final CancellationState cancellationState) {
        this.cancellationState = cancellationState;
    }

    public T withCancellationState(final CancellationState cancellationState) {
        this.cancellationState = cancellationState;
        return returnThis();
    }

    @SuppressWarnings("unchecked")
    private T returnThis() {
        return (T) this;
    }

    @Override
    public String toString() {
        return "LicenseEntity [companyId=" + companyId
                + ", licenseId=" + licenseId
                + ", licenseModel=" + licenseModel
                + ", productId=" + productId
                + ", contractId=" + contractId
                + ", startDate=" + startDate
                + ", endDate=" + endDate
                + ", cancellationState=" + cancellationState
                + "]";
    }

}
