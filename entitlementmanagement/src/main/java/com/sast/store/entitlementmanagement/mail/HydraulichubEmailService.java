package com.sast.store.entitlementmanagement.mail;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.contractmanagement.api.ContractDto;
import com.sast.store.contractmanagement.api.ContractEvent;
import com.sast.store.entitlementmanagement.config.AppConfiguration;
import com.sast.store.entitlementmanagement.dao.DcKeycloakLicenseEntity;
import com.sast.store.entitlementmanagement.dao.DcKeycloakLicenseType;
import com.sast.store.external.email.EmailServiceClient;
import com.sast.store.external.email.data.EmailRecipient;
import com.sast.store.external.email.data.EmailTenant;
import com.sast.store.external.email.data.TemplatedEmail;
import com.sast.store.external.ump.UmpClient;
import com.sast.store.gen.client.ump.api.UmpUserDto;
import com.sast.store.productmanagement.api.ProductApi;
import com.sast.store.productmanagement.api.ProductDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Period;
import java.time.ZoneOffset;
import java.util.Locale;
import java.util.Optional;

@Slf4j
@Service
public class HydraulichubEmailService extends AbstractEntitlementEmailService {
    private final ProductApi productApi;
    private final UmpClient umpClient;

    public HydraulichubEmailService(
            final EmailServiceClient emailServiceClient,
            final AppConfiguration appConfiguration,
            final ProductApi productApi,
            final UmpClient umpClient) {
        super(emailServiceClient, appConfiguration);
        this.productApi = productApi;
        this.umpClient = umpClient;

    }

    public void notifyAssignment(final DcKeycloakLicenseEntity license, final String changedByUserId) {
        final Tenant tenant = license.getTenant();
        final Optional<UmpUserDto> assignedByUser = fetchUser(tenant, changedByUserId);

        emailServiceClient.sendIgnoringFailures(TemplatedEmail.builder()
            .to(EmailRecipient.forEmailAddress(license.getEmail()))
            .tenant(EmailTenant.valueOf(tenant.id()))
            .localeOverride(fetchCompanyCommunicationLanguage(license.getTenant(), license.getCompany()).orElse(null))
            .templateData(HydraulichubAssignmentEmailData.builder()
                .firstName(license.getFirstname())
                .lastName(license.getLastname())
                .assignedUserEmail(license.getEmail())
                .changedByFirstName(assignedByUser.map(UmpUserDto::getFirstName).orElse(null))
                .changedByLastName(assignedByUser.map(UmpUserDto::getLastName).orElse(null))
                .hydraulicHubUrl(appConfiguration.dcLicensing().hydraulicHubLandingUrl())
                .build())
            .build());
    }

    public void notifyUnassignment(final DcKeycloakLicenseEntity license, final String changedByUserId) {
        final Tenant tenant = license.getTenant();

        final Optional<UmpUserDto> changedByUser = fetchUser(tenant, changedByUserId);
        emailServiceClient.sendIgnoringFailures(TemplatedEmail.builder()
            .to(EmailRecipient.forEmailAddress(license.getEmail()))
            .tenant(EmailTenant.valueOf(tenant.id()))
            .localeOverride(fetchCompanyCommunicationLanguage(license.getTenant(), license.getCompany()).orElse(null))
            .templateData(RexrothUnassignmentEmailDto.builder()
                .firstName(license.getFirstname())
                .lastName(license.getLastname())
                .changedByFirstName(changedByUser.map(UmpUserDto::getFirstName).orElse(null))
                .changedByLastName(changedByUser.map(UmpUserDto::getLastName).orElse(null))
                .productName(findProduct(tenant, license.getProductId()).map(ProductDto::name).orElse(null))
                .build())
            .build());
    }

    public void notifyContractCancellation(final DcKeycloakLicenseEntity license, final ContractEvent contractEvent) {
        final ContractDto contract = contractEvent.contractDto();
        final Optional<UmpUserDto> cancellationUser = fetchUser(license.getTenant(), contract.cancelledByUserId());
        emailServiceClient.sendIgnoringFailures(TemplatedEmail.builder()
            .to(EmailRecipient.forEmailAddress(license.getEmail()))
            .tenant(EmailTenant.valueOf(license.getTenant().id()))
            .localeOverride(fetchCompanyCommunicationLanguage(license.getTenant(), license.getCompany()).orElse(null))
            .templateData(RexrothCancellationEmailAssignedUserDto.builder()
                .productName(findProduct(license.getTenant(), contract.productId()).map(ProductDto::name).orElse(null))
                .firstName(license.getFirstname())
                .lastName(license.getLastname())
                .cancelledByFirstName(cancellationUser.map(UmpUserDto::getFirstName).orElse(null))
                .cancelledByLastName(cancellationUser.map(UmpUserDto::getLastName).orElse(null))
                .endDate(contract.endDate().toLocalDateTime())
                .marketplaceUrl(getMarketplaceUrl(license.getTenant()))
                .build())
            .build());
    }

    private Optional<ProductDto> findProduct(final Tenant tenant, final String productId) {
        return productApi.getProductVariant(tenant, productId, null);
    }

    private Optional<UmpUserDto> fetchUser(final Tenant tenant, final String userId) {
        if (userId == null) {
            return Optional.empty();
        }

        try {
            return Optional.ofNullable(umpClient.getUser(tenant.id(), userId));
        } catch (final Exception e) {
            LOG.warn("Failed to query UMP for user (uid={}, tenant={}): {}",
                userId, tenant, e.getMessage(), e);
            return Optional.empty();
        }
    }

    private Optional<Locale> fetchCompanyCommunicationLanguage(final Tenant tenant, final String companyId) {
        try {
            return Optional.ofNullable(umpClient.getCompanyDetails(tenant.id(), companyId).getCommunicationLanguage())
                .map(Locale::forLanguageTag);
        } catch (final Exception e) {
            LOG.warn("Failed to query UMP for company (companyId={}, tenant={}): {}",
                companyId, tenant, e.getMessage(), e);
            return Optional.empty();
        }
    }

    public void notifyExpiration(final DcKeycloakLicenseEntity license) {
        emailServiceClient.sendIgnoringFailures(TemplatedEmail.builder()
            .to(EmailRecipient.forEmailAddress(license.getEmail()))
            .tenant(EmailTenant.valueOf(license.getTenant().id()))
            .localeOverride(fetchCompanyCommunicationLanguage(license.getTenant(), license.getCompany()).orElse(null))
            .templateData(RexrothExpirationEmailDto.builder()
                .productName(findProduct(license.getTenant(), license.getProductId()).map(ProductDto::name).orElse(null))
                .firstName(license.getFirstname())
                .lastName(license.getLastname())
                .endDate(license.getFreemiumEndDate().atZone(ZoneOffset.UTC).toLocalDateTime())
                .marketplaceUrl(getMarketplaceUrl(license.getTenant()))
                .build())
            .build());
    }

    public void notifyExpirationSoon(final DcKeycloakLicenseEntity license) {
        final Period expiringSoonDuration = appConfiguration.dcLicensing().dcKeycloak().get(DcKeycloakLicenseType.FREEMIUM)
            .expiringSoonDuration();
        emailServiceClient.sendIgnoringFailures(TemplatedEmail.builder()
            .to(EmailRecipient.forEmailAddress(license.getEmail()))
            .tenant(EmailTenant.valueOf(license.getTenant().id()))
            .localeOverride(fetchCompanyCommunicationLanguage(license.getTenant(), license.getCompany()).orElse(null))
            .templateData(RexrothExpirationSoonEmailDto.builder()
                .productName(findProduct(license.getTenant(), license.getProductId()).map(ProductDto::name).orElse(null))
                .firstName(license.getFirstname())
                .lastName(license.getLastname())
                .endDate(license.getFreemiumEndDate().atZone(ZoneOffset.UTC).toLocalDateTime())
                .expiration(expiringSoonDuration)
                .marketplaceUrl(getMarketplaceUrl(license.getTenant()))
                .build())
            .build());
    }
}
