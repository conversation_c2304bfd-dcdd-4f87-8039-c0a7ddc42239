package com.sast.store.entitlementmanagement.service;

import jakarta.inject.Inject;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class LicenseExpirationTask {
    private static final Logger LOG = LoggerFactory.getLogger(LicenseExpirationTask.class);

    @Inject
    private DcKeycloakLicenseService licensingService;

    // random initial delay to avoid running at the same time with other pods
    @Scheduled(fixedRateString = "PT60M", initialDelayString = "PT${random.int[5,55]}M")
    @SchedulerLock(name = "contractmanagement.licenseExpirationTask", lockAtLeastFor = "PT60M", lockAtMostFor = "PT60M")
    public void scheduledTask() {
        LOG.info("started LicenseExpirationTask");
        try {
            licensingService.freemiumExpiration();
        } catch (final RuntimeException e) {
            LOG.warn("LicenseExpirationTask failed", e);
        }
    }
}
