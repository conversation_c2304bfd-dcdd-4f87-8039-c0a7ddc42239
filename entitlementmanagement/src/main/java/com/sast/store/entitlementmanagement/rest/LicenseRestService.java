package com.sast.store.entitlementmanagement.rest;

import com.sast.store.commons.basewebapp.keycloak.AuthenticationService;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.entitlementmanagement.api.AssignLicenseDto;
import com.sast.store.entitlementmanagement.api.AssignLicenseDto.AssignLicenseData;
import com.sast.store.entitlementmanagement.api.AssignLicenseDto.BcCentralAssignLicenseDto;
import com.sast.store.entitlementmanagement.api.AssignLicenseDto.DcKeycloakAssignLicenseDto;
import com.sast.store.entitlementmanagement.api.AssignLicenseDto.LitmosAssignLicenseDto;
import com.sast.store.entitlementmanagement.api.LicenseApi;
import com.sast.store.entitlementmanagement.api.LicenseDto;
import com.sast.store.entitlementmanagement.api.LicenseDto.BcCentralLicenseDto;
import com.sast.store.entitlementmanagement.api.LicenseDto.DcKeycloakLicenseDto;
import com.sast.store.entitlementmanagement.api.LicenseDto.LitmosLicenseDto;
import com.sast.store.entitlementmanagement.api.LicenseModel;
import com.sast.store.entitlementmanagement.dao.BcCentralLicenseEntity;
import com.sast.store.entitlementmanagement.dao.DcKeycloakLicenseEntity;
import com.sast.store.entitlementmanagement.dao.LicenseEntity;
import com.sast.store.entitlementmanagement.dao.LitmosLicenseEntity;
import com.sast.store.entitlementmanagement.service.LicensingService;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.BadRequestException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Set;

@Component
public class LicenseRestService implements LicenseApi {
    private static final Logger LOG = LoggerFactory.getLogger(LicenseRestService.class);

    @Inject
    private LicensingService licensingService;

    @Inject
    private AuthenticationService authenticationService;

    @Override
    @PreAuthorize("hasRole('DEFAULT')")
    public List<LicenseDto> getLicenses(@NotNull final Tenant tenant) {
        final String companyId = authenticationService.getCompanyId();

        return licensingService.getLicenses(tenant, companyId).stream()
            .map(this::toDto)
            .toList();
    }

    @Override
    @PreAuthorize("hasRole('DEFAULT')")
    public List<AssignLicenseDto> getDefaults(@NotNull final Tenant tenant, @NotNull final List<String> contractIds) {
        final String companyId = authenticationService.getCompanyId();

        return licensingService.getDefaults(tenant, companyId, Set.copyOf(contractIds)).stream()
            .map(this::toAssignLicenseDto)
            .toList();
    }

    @Override
    @PreAuthorize("hasRole('DEFAULT')")
    public void assignLicenses(@NotNull final Tenant tenant, @NotNull final List<@Valid AssignLicenseDto> assignLicenseDtos) {
        final String companyId = authenticationService.getCompanyId();
        final String userId = authenticationService.getUserId();

        assignLicenseDtos.forEach(assignLicenseDto -> {
            if (assignLicenseDto.licenseModel() != null) {
                // TODO validate email not assigned to another license
                final AssignLicenseData licenseData = switch (assignLicenseDto.licenseModel()) {
                    case DCKEYCLOAK -> assignLicenseDto.dcKeycloak();
                    case LITMOS -> assignLicenseDto.litmos();
                    case BCCENTRAL -> assignLicenseDto.bcCentral();
                };
                licensingService.createUserAndAssignRoles(tenant, companyId, userId, licenseData);
            } else {
                validateEmailNotAssignedToAnotherLicense(tenant, assignLicenseDtos, companyId);

                // legacy case
                licensingService.createUserAndAssignRoles(tenant, companyId, userId,
                    new DcKeycloakAssignLicenseDto(assignLicenseDto.firstname(), assignLicenseDto.lastname(),
                        assignLicenseDto.email(), assignLicenseDto.contractId()));
            }
        });
    }

    private void validateEmailNotAssignedToAnotherLicense(final Tenant tenant, final List<AssignLicenseDto> assignLicenseDtos,
        final String companyId) {
        final List<LicenseDto> alreadyAssigned = assignLicenseDtos.stream()
            .flatMap(a -> licensingService.findLicense(tenant, companyId, a.contractId())
                .map(this::toDto)
                .filter(l -> !Objects.equals(l.email(), a.email()))
                .stream())
            .toList();

        if (!alreadyAssigned.isEmpty()) {
            LOG.info("License(s) already assigned to contract {}", alreadyAssigned);
            throw new BadRequestException();
        }
    }

    @Override
    @PreAuthorize("hasRole('DEFAULT')")
    public void unassignLicense(@NotNull final Tenant tenantId, @NotNull final String licenseId) {
        final String companyId = authenticationService.getCompanyId();
        final String userId = authenticationService.getUserId();
        licensingService.unassignLicense(tenantId, licenseId, companyId, userId);
    }

    private LicenseDto toDto(final LicenseEntity<?> license) {
        return switch (license) {
            case final DcKeycloakLicenseEntity l -> LicenseDto.builder()
                .email(l.getEmail())
                .firstname(l.getFirstname())
                .lastname(l.getLastname())
                .contractId(l.getContractId())
                .licenseId(l.getLicenseId())
                .licenseModel(l.getLicenseModel())
                .dcKeycloak(DcKeycloakLicenseDto.builder()
                    .email(l.getEmail())
                    .firstname(l.getFirstname())
                    .lastname(l.getLastname())
                    .build())
                .build();
            case final LitmosLicenseEntity l -> LicenseDto.builder()
                .email(l.getEmail())
                .firstname(l.getFirstname())
                .lastname(l.getLastname())
                .contractId(l.getContractId())
                .licenseId(l.getLicenseId())
                .licenseModel(l.getLicenseModel())
                .litmos(LitmosLicenseDto.builder()
                    .email(l.getEmail())
                    .firstname(l.getFirstname())
                    .lastname(l.getLastname())
                    .build())
                .build();
            case final BcCentralLicenseEntity l -> LicenseDto.builder()
                .contractId(l.getContractId())
                .licenseId(l.getLicenseId())
                .licenseModel(l.getLicenseModel())
                .bcCentral(BcCentralLicenseDto.builder()
                    .name(l.getBccentralTenantName())
                    .emails(l.getOwnerEmails())
                    .build())
                .build();
            default -> throw new IllegalArgumentException("Unexpected Licensetype: " + license);
        };
    }

    private AssignLicenseDto toAssignLicenseDto(final AssignLicenseData assignLicenseData) {
        return switch (assignLicenseData) {
            case LitmosAssignLicenseDto dto -> AssignLicenseDto.builder()
                .contractId(dto.contractId())
                .licenseModel(LicenseModel.LITMOS)
                .litmos(dto)
                .build();
            case DcKeycloakAssignLicenseDto dto -> AssignLicenseDto.builder()
                .contractId(dto.contractId())
                .licenseModel(LicenseModel.DCKEYCLOAK)
                .dcKeycloak(dto)
                .build();
            case BcCentralAssignLicenseDto dto -> AssignLicenseDto.builder()
                .contractId(dto.contractId())
                .licenseModel(LicenseModel.BCCENTRAL)
                .bcCentral(dto)
                .build();
            default -> throw new IllegalArgumentException("Unsupported AssignLicenseData: " + assignLicenseData);
        };
    }
}
