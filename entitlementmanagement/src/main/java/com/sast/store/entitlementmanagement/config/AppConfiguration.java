package com.sast.store.entitlementmanagement.config;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.entitlementmanagement.dao.DcKeycloakLicenseType;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotNull;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.net.URI;
import java.time.Period;
import java.util.List;
import java.util.Map;

@ConfigurationProperties(prefix = "bossstore")
@Validated
public record AppConfiguration(
    @NotNull DcLicensing dcLicensing,
    @NotNull Map<Tenant, TenantConfig> tenants,
    @NotNull URI productmanagementUrl,
    @NotNull URI contractmanagementUrl
) {
    public record DcLicensing(
        @NotNull Map<DcKeycloakLicenseType, DcKeycloakConfig> dcKeycloak,
        @Nullable String litmosParentTeamId,
        @NotNull URI hydraulicHubLandingUrl
    ) { }

    public record DcKeycloakConfig(
        @NotNull List<String> roleNames,
        @NotNull String rolesClientUUID,
        @Nullable List<String> roleNamesToRemove,
        @Nullable Period duration,
        @Nullable Period expiringSoonDuration
    ) { }

    public record TenantConfig(
        @NotNull PublicUrlConfig publicUrls,
        @NotNull PublicEmailConfig publicEmails
    ) { }

    public record PublicUrlConfig(
        @NotNull URI marketplaceUrl,
        @NotNull URI contractManagementUrl
    ) { }

    public record PublicEmailConfig(
        @NotNull @Email String supportEmail
    ) { }
}
