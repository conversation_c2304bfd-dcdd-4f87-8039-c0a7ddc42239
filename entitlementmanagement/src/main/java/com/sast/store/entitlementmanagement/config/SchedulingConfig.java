package com.sast.store.entitlementmanagement.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Enable scheduling unless explicitly disabled (e.g. in tests).
 */
@Configuration
@EnableScheduling
@ConditionalOnProperty(prefix = "bossstore.scheduling", name = "enabled", havingValue = "true", matchIfMissing = true)
public class SchedulingConfig {
}
