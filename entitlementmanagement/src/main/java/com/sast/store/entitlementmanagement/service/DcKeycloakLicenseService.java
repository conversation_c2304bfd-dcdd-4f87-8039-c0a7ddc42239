package com.sast.store.entitlementmanagement.service;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.contractmanagement.api.ContractDto;
import com.sast.store.contractmanagement.api.ContractEvent;
import com.sast.store.entitlementmanagement.api.AssignLicenseDto.AssignLicenseData;
import com.sast.store.entitlementmanagement.api.AssignLicenseDto.DcKeycloakAssignLicenseDto;
import com.sast.store.entitlementmanagement.api.LicenseModel;
import com.sast.store.entitlementmanagement.config.AppConfiguration;
import com.sast.store.entitlementmanagement.config.AppConfiguration.DcKeycloakConfig;
import com.sast.store.entitlementmanagement.dao.DcKeycloakLicenseEntity;
import com.sast.store.entitlementmanagement.dao.DcKeycloakLicenseType;
import com.sast.store.entitlementmanagement.dao.HydraulichubFreemiumUsersDao;
import com.sast.store.entitlementmanagement.dao.HydraulichubFreemiumUsersEntity;
import com.sast.store.entitlementmanagement.dao.LicenseDao;
import com.sast.store.entitlementmanagement.dao.LicenseEntity;
import com.sast.store.entitlementmanagement.dao.TaskState;
import com.sast.store.entitlementmanagement.mail.AssignedUser;
import com.sast.store.entitlementmanagement.mail.HydraulichubEmailService;
import com.sast.store.external.dckeycloak.DcKeycloakClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.Period;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Component
@Slf4j
@RequiredArgsConstructor
public class DcKeycloakLicenseService implements LicenseServiceInterface {
    private final DcKeycloakClient dcKeycloakClient;
    private final AppConfiguration appConfiguration;
    private final LicenseDao licenseDao;
    private final HydraulichubEmailService emailService;
    private final HydraulichubFreemiumUsersDao dcKeycloakFreemiumUsersDao;

    @Override
    public void createLicense(final Tenant tenant, final ContractDto contract,
        final String changedByUserId, final List<String> entitlements, final AssignLicenseData assignLicenseData) {
        final DcKeycloakAssignLicenseDto assignLicenseDto = DcKeycloakAssignLicenseDto.class.cast(assignLicenseData);
        final AssignedUser assignedUser = AssignedUser.builder()
            .emailAddress(assignLicenseDto.email())
            .firstName(assignLicenseDto.firstname())
            .lastName(assignLicenseDto.lastname())
            .build();
        final UserRepresentation keycloakUser = getOrCreateUserKeycloak(assignedUser);
        LOG.info("User: {} {} {} {} {}", keycloakUser.getId(), keycloakUser.getUsername(), keycloakUser.getEmail(),
            keycloakUser.getFirstName(), keycloakUser.getLastName());

        entitlements.stream().filter(e -> e.startsWith(LicenseModel.DCKEYCLOAK + ":"))
            .map(e -> e.split(":")[1])
            .map(DcKeycloakLicenseType::valueOf)
            .forEach(e -> {
                if (DcKeycloakLicenseType.PREMIUM.equals(e)) {
                    createLicensePremium(tenant, contract.companyId(), contract.contractId(), contract.productId(), assignedUser,
                        changedByUserId, keycloakUser);
                } else if (DcKeycloakLicenseType.FREEMIUM.equals(e)) {
                    createLicenseFreemium(tenant, contract.companyId(), contract.contractId(), contract.productId(), assignedUser,
                        changedByUserId, keycloakUser);
                } else {
                    LOG.warn("Unknown entitlement: {}", e);
                }
            });
    }

    private void createLicenseFreemium(final Tenant tenant, final String companyId, final String contractId,
        final String productId, final AssignedUser assignedUser, final String changedByUserId, final UserRepresentation keycloakUser) {
        // assign freemium roles
        final DcKeycloakConfig freemiumConfig = appConfiguration.dcLicensing().dcKeycloak().get(DcKeycloakLicenseType.FREEMIUM);
        final ArrayList<String> roles = new ArrayList<>(freemiumConfig.roleNames());
        // only assign all roles if user has not been assigned freemium roles before
        final Optional<HydraulichubFreemiumUsersEntity> previousUser = dcKeycloakFreemiumUsersDao
            .findByEmail(assignedUser.emailAddress());
        if (previousUser.isPresent()) {
            roles.removeAll(freemiumConfig.roleNamesToRemove());
        }
        dcKeycloakClient.assignRoles(keycloakUser.getId(), freemiumConfig.rolesClientUUID(), roles);
        final DcKeycloakLicenseEntity license1 = getOrCreateLicense(tenant, companyId, contractId, productId, assignedUser,
            DcKeycloakLicenseType.FREEMIUM);
        LOG.info("Created license {}", license1);

        if (previousUser.isEmpty()) {
            dcKeycloakFreemiumUsersDao.save(new HydraulichubFreemiumUsersEntity().withEmail(assignedUser.emailAddress()));
        }
        emailService.notifyAssignment(license1, changedByUserId);
    }

    private void createLicensePremium(final Tenant tenant, final String companyId, final String contractId,
        final String productId, final AssignedUser assignedUser, final String changedByUserId, final UserRepresentation keycloakUser) {
        final DcKeycloakConfig config = appConfiguration.dcLicensing().dcKeycloak().get(DcKeycloakLicenseType.PREMIUM);

        dcKeycloakClient.assignRoles(keycloakUser.getId(), config.rolesClientUUID(), config.roleNames());

        final DcKeycloakLicenseEntity license = getOrCreateLicense(tenant, companyId, contractId, productId, assignedUser,
            DcKeycloakLicenseType.PREMIUM);
        LOG.info("Created license {}", license);
        emailService.notifyAssignment(license, changedByUserId);
    }

    private UserRepresentation getOrCreateUserKeycloak(final AssignedUser assignedUser) {
        return dcKeycloakClient.getUser(assignedUser.emailAddress()).orElseGet(() -> {
            dcKeycloakClient.createUser(assignedUser.emailAddress(), assignedUser.firstName(), assignedUser.lastName());
            LOG.info("Created user {}", assignedUser.emailAddress());
            return dcKeycloakClient.getUser(assignedUser.emailAddress()).orElseThrow();
        });
    }

    private DcKeycloakLicenseEntity getOrCreateLicense(final Tenant tenant, final String companyId, final String contractId,
        final String productId, final AssignedUser assignedUser, final DcKeycloakLicenseType licenseType) {
        final DcKeycloakConfig config = appConfiguration.dcLicensing().dcKeycloak().get(licenseType);
        final Instant endDate = Optional.ofNullable(config.duration())
            .map(ZonedDateTime.now()::plus)
            .map(ZonedDateTime::toInstant)
            .orElse(null);

        return licenseDao.findByContractId(tenant, companyId, contractId)
            .filter(l -> l.getLicenseModel() == LicenseModel.DCKEYCLOAK)
            .map(DcKeycloakLicenseEntity.class::cast)
            .filter(l -> l.getLicenseType() == licenseType)
            .findAny()
            .orElseGet(() -> {
                final DcKeycloakLicenseEntity license = new DcKeycloakLicenseEntity()
                    .withStartDate(Instant.now())
                    .withFreemiumEndDate(endDate)
                    .withCompanyId(tenant, companyId)
                    .withLicenseId(UUID.randomUUID().toString())
                    .withLicenseModel(getLicenseModel())
                    .withEmail(assignedUser.emailAddress())
                    .withFirstname(assignedUser.firstName())
                    .withLastname(assignedUser.lastName())
                    .withContractId(contractId)
                    .withProductId(productId)
                    .withLicenseType(licenseType);
                licenseDao.save(license);
                return license;
            });
    }

    @Override
    public LicenseModel getLicenseModel() {
        return LicenseModel.DCKEYCLOAK;
    }

    @Override
    public void unassignLicense(final LicenseEntity<?> license, final String changedByUserId) {
        final DcKeycloakLicenseEntity licenseEntity = (DcKeycloakLicenseEntity) license;
        final Optional<UserRepresentation> user = dcKeycloakClient.getUser(licenseEntity.getEmail());

        if (licenseEntity.getLicenseType() == null) {
            throw new IllegalArgumentException("licenseType is not defined for license " + license);
        }

        if (user.isPresent()) {
            final DcKeycloakConfig config = appConfiguration.dcLicensing().dcKeycloak().get(licenseEntity.getLicenseType());
            dcKeycloakClient.unassignRoles(user.get().getId(), config.rolesClientUUID(), config.roleNames());
        } else {
            LOG.warn("user {} not found in dckeycloak, role unassignment for license {} skipped",
                licenseEntity.getEmail(), licenseEntity);
        }
        licenseDao.delete(licenseEntity);
        LOG.info("license {} deleted", licenseEntity);
        try {
            emailService.notifyUnassignment(licenseEntity, changedByUserId);
        } catch (final RuntimeException ex) {
            LOG.warn("Failed to send unassignment email for license {}", licenseEntity, ex);
        }
    }

    @Override
    public void contractCancelled(final LicenseEntity<?> license, final ContractEvent contract) {
        final DcKeycloakLicenseEntity licenseEntity = (DcKeycloakLicenseEntity) license;

        try {
            emailService.notifyContractCancellation(licenseEntity, contract);
        } catch (final RuntimeException ex) {
            LOG.warn("Failed to send unassignment email for license {}", licenseEntity, ex);
        }
    }

    public void freemiumExpiration() {
        final Instant now = Instant.now();
        licenseDao.findAll()
            .filter(l -> l.getLicenseModel() == LicenseModel.DCKEYCLOAK)
            .map(DcKeycloakLicenseEntity.class::cast)
            .filter(l -> l.getLicenseType() == DcKeycloakLicenseType.FREEMIUM)
            .filter(l -> l.getFreemiumEndDate() != null)
            .filter(l -> l.getFreemiumEndDate().isBefore(now))
            .filter(l -> l.getFreemiumExpiredTaskState() == null)
            .forEach(license -> {
                try {
                    freemiumExpired(license);
                    licenseDao.save(license.withFreemiumExpiredTaskState(TaskState.COMPLETED));
                } catch (final Exception e) {
                    LOG.warn("Error processing license expiration for license {}", license, e);
                }
            });
    }

    public void freemiumExpired(final LicenseEntity<?> license) {
        final DcKeycloakLicenseEntity licenseEntity = (DcKeycloakLicenseEntity) license;
        LOG.info("license expired, removing roles {}", licenseEntity);

        final Optional<UserRepresentation> user = dcKeycloakClient.getUser(licenseEntity.getEmail());

        final DcKeycloakConfig config = appConfiguration.dcLicensing().dcKeycloak().get(licenseEntity.getLicenseType());
        dcKeycloakClient.unassignRoles(user.get().getId(), config.rolesClientUUID(), config.roleNamesToRemove());

        emailService.notifyExpiration(licenseEntity);
    }

    public void licenseExpirationSoon() {
        final Period expiringSoonDuration = appConfiguration.dcLicensing().dcKeycloak().get(DcKeycloakLicenseType.FREEMIUM)
            .expiringSoonDuration();
        final Instant cutoff = Instant.now().atZone(ZoneOffset.UTC).plus(expiringSoonDuration).toInstant();
        licenseDao.findAll()
            .filter(l -> l.getLicenseModel() == LicenseModel.DCKEYCLOAK)
            .map(DcKeycloakLicenseEntity.class::cast)
            .filter(l -> l.getFreemiumEndDate() != null)
            .filter(l -> l.getFreemiumEndDate().isBefore(cutoff))
            .filter(l -> l.getFreemiumExpiringSoonTaskState() == null)
            .forEach(license -> {
                try {
                    emailService.notifyExpirationSoon(license);
                    licenseDao.save(license.withFreemiumExpiringSoonTaskState(TaskState.COMPLETED));
                    LOG.info("license {} notified", license);
                } catch (final Exception e) {
                    LOG.warn("Error processing license expiration for license {}", license, e);
                }
            });

    }

    @Override
    public void contractExpired(final LicenseEntity<?> license, final ContractEvent contract) {
        this.unassignLicense(license, contract.contractDto().cancelledByUserId());
    }
}
