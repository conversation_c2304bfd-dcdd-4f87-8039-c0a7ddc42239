package com.sast.store.entitlementmanagement.mail;

import com.sast.store.external.email.data.EmailTemplateData;
import com.sast.store.external.email.data.EmailTenant;

import java.net.URI;
import java.time.LocalDateTime;

public record RexrothCancellationEmailAssignedUserDto(
    String firstName,
    String lastName,
    String cancelledByFirstName,
    String cancelledByLastName,
    String productName,
    LocalDateTime endDate,
    URI marketplaceUrl) implements EmailTemplateData {

    @Override
    public String getTemplateName(final EmailTenant emailTenant) {
        return "rexroth/entitlements/cancelConfirmation";
    }

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {
        private String firstName;
        private String lastName;
        private String cancelledByFirstName;
        private String cancelledByLastName;
        private String productName;
        private LocalDateTime endDate;
        private URI marketplaceUrl;

        private Builder() {
        }

        public Builder firstName(final String firstName) {
            this.firstName = firstName;
            return this;
        }

        public Builder lastName(final String lastName) {
            this.lastName = lastName;
            return this;
        }

        public Builder cancelledByFirstName(final String cancelledByFirstName) {
            this.cancelledByFirstName = cancelledByFirstName;
            return this;
        }

        public Builder cancelledByLastName(final String cancelledByLastName) {
            this.cancelledByLastName = cancelledByLastName;
            return this;
        }

        public Builder productName(final String productName) {
            this.productName = productName;
            return this;
        }

        public Builder endDate(final LocalDateTime endDate) {
            this.endDate = endDate;
            return this;
        }

        public Builder marketplaceUrl(final URI marketplaceUrl) {
            this.marketplaceUrl = marketplaceUrl;
            return this;
        }

        public RexrothCancellationEmailAssignedUserDto build() {
            return new RexrothCancellationEmailAssignedUserDto(firstName, lastName, cancelledByFirstName, cancelledByLastName,
                productName, endDate, marketplaceUrl);
        }
    }
}
