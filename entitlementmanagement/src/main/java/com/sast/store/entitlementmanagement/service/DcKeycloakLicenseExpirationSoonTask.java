package com.sast.store.entitlementmanagement.service;

import jakarta.inject.Inject;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class DcKeycloakLicenseExpirationSoonTask {
    private static final Logger LOG = LoggerFactory.getLogger(DcKeycloakLicenseExpirationSoonTask.class);

    @Inject
    private DcKeycloakLicenseService dcKeycloakLicensingService;

    // random initial delay to avoid running at the same time with other pods
    @Scheduled(fixedRateString = "PT60M", initialDelayString = "PT${random.int[5,55]}M")
    @SchedulerLock(name = "contractmanagement.dcKeycloakLicenseExpirationSoonTask", lockAtLeastFor = "PT60M", lockAtMostFor = "PT60M")
    public void scheduledTask() {
        LOG.info("started LicenseExpirationSoonTask");
        try {
            dcKeycloakLicensingService.licenseExpirationSoon();
        } catch (final RuntimeException e) {
            LOG.warn("LicenseExpirationSoonTask failed", e);
        }
    }
}
