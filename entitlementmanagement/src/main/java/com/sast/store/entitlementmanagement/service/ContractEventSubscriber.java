package com.sast.store.entitlementmanagement.service;

import com.hazelcast.core.HazelcastInstance;
import com.sast.store.commons.hazelcast.QueueSubscriber;
import com.sast.store.contractmanagement.api.ContractEvent;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.sast.store.commons.hazelcast.Queue.contractCancellationEvents;
import static com.sast.store.commons.hazelcast.Queue.contractCreateEvents;
import static com.sast.store.commons.hazelcast.Queue.contractExpirationEvents;
import static com.sast.store.commons.hazelcast.Queue.contractUpdateEvents;
import static com.sast.store.commons.hazelcast.Queue.subscribe;


@Component
@Slf4j
@RequiredArgsConstructor
public class ContractEventSubscriber {
    private final LicensingService licensingService;
    private final HazelcastInstance hazelcastInstance;

    private QueueSubscriber<ContractEvent> contractCancellationSubscription;
    private QueueSubscriber<ContractEvent> contractExpirationSubscription;
    private QueueSubscriber<ContractEvent> contractUpdateSubscription;
    private QueueSubscriber<ContractEvent> contractCreateSubscription;

    @PostConstruct
    public void onStartup() {
        contractCancellationSubscription = subscribe(
                contractCancellationEvents(hazelcastInstance), this::onContractCancellationEvent);
        contractExpirationSubscription = subscribe(
                contractExpirationEvents(hazelcastInstance), this::onExpirationEvent);
        contractUpdateSubscription = subscribe(
                contractUpdateEvents(hazelcastInstance), this::onUpdateEvent);
        contractCreateSubscription = subscribe(
                contractCreateEvents(hazelcastInstance), this::onCreateEvent);
    }

    public void onContractCancellationEvent(final ContractEvent event) {
        LOG.info("received contract cancellation event {}", event);
        licensingService.processContractCancellation(event);
    }

    public void onExpirationEvent(final ContractEvent event) {
        LOG.info("received contract expiration event {}", event);
        licensingService.processContractExpiration(event);
    }

    public void onUpdateEvent(final ContractEvent event) {
        LOG.info("received contract change event {}", event);
        licensingService.processContractUpdate(event);
    }

    public void onCreateEvent(final ContractEvent event) {
        LOG.info("received contract creation event {}", event);
        licensingService.processContractCreation(event);
    }
}
