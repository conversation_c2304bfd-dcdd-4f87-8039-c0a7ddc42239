package com.sast.store.entitlementmanagement.dao;

import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;

@DynamoDbBean
public class HydraulichubFreemiumUsersEntity {
    private String email;

    @DynamoDbPartitionKey
    public String getEmail() {
        return email;
    }

    public void setEmail(final String email) {
        this.email = email;
    }

    public HydraulichubFreemiumUsersEntity withEmail(final String email) {
        this.email = email;
        return this;
    }

}