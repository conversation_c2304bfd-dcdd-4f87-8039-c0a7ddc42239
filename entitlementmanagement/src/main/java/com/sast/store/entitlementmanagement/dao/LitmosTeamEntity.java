package com.sast.store.entitlementmanagement.dao;

import com.sast.store.commons.tenant.api.Tenant;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;

@DynamoDbBean
public class LitmosTeamEntity {
    private String company;
    private String teamId;

    @DynamoDbPartitionKey
    public String getCompany() {
        return company;
    }

    public void setCompany(final String company) {
        this.company = company;
    }

    public LitmosTeamEntity withCompanyId(final Tenant tenant, final String umpCompanyId) {
        this.company = tenant.id() + "/" + umpCompanyId;
        return this;
    }

    @DynamoDbIgnore
    public Tenant getTenant() {
        return Tenant.fromString(company.substring(0, company.indexOf('/')));
    }

    @DynamoDbIgnore
    public String getCompanyId() {
        return company.substring(company.indexOf('/') + 1);
    }

    public String getTeamId() {
        return teamId;
    }

    public void setTeamId(final String teamId) {
        this.teamId = teamId;
    }

    public LitmosTeamEntity withTeamId(final String teamId) {
        this.teamId = teamId;
        return this;
    }

}
