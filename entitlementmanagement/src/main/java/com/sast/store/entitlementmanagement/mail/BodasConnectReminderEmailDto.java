package com.sast.store.entitlementmanagement.mail;

import com.sast.store.external.email.data.EmailTemplateData;
import com.sast.store.external.email.data.EmailTenant;
import lombok.Builder;

import java.net.URI;

@Builder
public record BodasConnectReminderEmailDto(URI marketplaceUrl) implements EmailTemplateData {

    @Override
    public String getTemplateName(final EmailTenant emailTenant) {
        return "rexroth/entitlements/bodasconnectSetupReminder";
    }
}
