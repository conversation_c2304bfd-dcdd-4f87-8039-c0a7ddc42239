package com.sast.store.entitlementmanagement.dao;

import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

@DynamoDbBean
public class LitmosLicenseEntity extends LicenseEntity<LitmosLicenseEntity> {

    private String email;
    private String firstname;
    private String lastname;

    public String getEmail() {
        return email;
    }

    public void setEmail(final String email) {
        this.email = email;
    }

    public LitmosLicenseEntity withEmail(final String email) {
        this.email = email;
        return this;
    }

    public String getFirstname() {
        return firstname;
    }

    public void setFirstname(final String firstname) {
        this.firstname = firstname;
    }

    public LitmosLicenseEntity withFirstname(final String firstname) {
        this.firstname = firstname;
        return this;
    }

    public String getLastname() {
        return lastname;
    }

    public void setLastname(final String lastname) {
        this.lastname = lastname;
    }

    public LitmosLicenseEntity withLastname(final String lastname) {
        this.lastname = lastname;
        return this;
    }

    @Override
    public String toString() {
        return "LitmosLicenseEntity [email=" + email + ", firstname=" + firstname + ", lastname=" + lastname + ", "
            + super.toString() + "]";
    }

}
