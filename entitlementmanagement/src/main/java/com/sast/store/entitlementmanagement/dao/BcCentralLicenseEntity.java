package com.sast.store.entitlementmanagement.dao;

import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

import java.util.List;

@DynamoDbBean
public class BcCentralLicenseEntity extends LicenseEntity<BcCentralLicenseEntity> {

    private String bccentralContractId;
    private String bccentralTenantName;
    private String cancelledByUserId;
    private List<String> ownerEmails;

    public String getBccentralContractId() {
        return bccentralContractId;
    }

    public void setBccentralContractId(final String bccentralContractId) {
        this.bccentralContractId = bccentralContractId;
    }

    public BcCentralLicenseEntity withBccentralContractId(final String bccentralContractId) {
        this.bccentralContractId = bccentralContractId;
        return this;
    }

    public String getBccentralTenantName() {
        return bccentralTenantName;
    }

    public void setBccentralTenantName(final String bccentralTenantName) {
        this.bccentralTenantName = bccentralTenantName;
    }

    public BcCentralLicenseEntity withBccentralTenantName(final String bccentralTenantName) {
        this.bccentralTenantName = bccentralTenantName;
        return this;
    }

    public List<String> getOwnerEmails() {
        return ownerEmails;
    }

    public void setOwnerEmails(final List<String> ownerEmails) {
        this.ownerEmails = ownerEmails;
    }

    public BcCentralLicenseEntity withOwnerEmails(final List<String> ownerEmails) {
        this.ownerEmails = ownerEmails;
        return this;
    }

    public String getCancelledByUserId() {
        return cancelledByUserId;
    }

    public void setCancelledByUserId(final String cancelledByUserId) {
        this.cancelledByUserId = cancelledByUserId;
    }

    public BcCentralLicenseEntity withCancelledByUserId(final String cancelledByUserId) {
        this.cancelledByUserId = cancelledByUserId;
        return this;
    }
}
