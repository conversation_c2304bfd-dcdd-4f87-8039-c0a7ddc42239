package com.sast.store.entitlementmanagement.mail;

import com.sast.store.external.email.data.EmailTemplateData;
import com.sast.store.external.email.data.EmailTenant;

public record RexrothUnassignmentEmailDto(
        String firstName,
        String lastName,
        String changedByFirstName,
        String changedByLastName,
        String productName
) implements EmailTemplateData {

    @Override
    public String getTemplateName(final EmailTenant emailTenant) {
        return "rexroth/entitlements/unassignment";
    }

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {
        private String firstName;
        private String lastName;
        private String changedByFirstName;
        private String changedByLastName;
        private String productName;

        private Builder() {
        }

        public Builder firstName(final String firstName) {
            this.firstName = firstName;
            return this;
        }

        public Builder lastName(final String lastName) {
            this.lastName = lastName;
            return this;
        }

        public Builder changedByFirstName(final String changedByFirstName) {
            this.changedByFirstName = changedByFirstName;
            return this;
        }

        public Builder changedByLastName(final String changedByLastName) {
            this.changedByLastName = changedByLastName;
            return this;
        }

        public Builder productName(final String productName) {
            this.productName = productName;
            return this;
        }

        public RexrothUnassignmentEmailDto build() {
            return new RexrothUnassignmentEmailDto(firstName, lastName, changedByFirstName, changedByLastName, productName);
        }
    }

}
