package com.sast.store.entitlementmanagement.service;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.contractmanagement.api.ContractApi;
import com.sast.store.contractmanagement.api.ContractDto;
import com.sast.store.contractmanagement.api.ContractEvent;
import com.sast.store.entitlementmanagement.api.AssignLicenseDto.AssignLicenseData;
import com.sast.store.entitlementmanagement.dao.LicenseDao;
import com.sast.store.entitlementmanagement.dao.LicenseEntity;
import com.sast.store.productmanagement.api.ProductApi;
import com.sast.store.productmanagement.api.ProductDto;
import com.sast.store.productmanagement.api.ProductDto.Variant;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.ClientErrorException;
import jakarta.ws.rs.NotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;

@Component
@Slf4j
@RequiredArgsConstructor
public class LicensingService {
    private final LicenseDao licenseDao;
    private final ProductApi productApi;
    private final LicenseServiceRegistry licenseServices;
    private final ContractApi contractApi;

    public void createUserAndAssignRoles(final Tenant tenant, final String companyId, final String changedByUserId,
                                         final AssignLicenseData assignLicenseDto) {
        LOG.info("createUserAndAssignRoles {}", assignLicenseDto);

        final ContractDto contract = contractApi.getContract(tenant, companyId, assignLicenseDto.contractId());

        final List<String> entitlements = getEntitlements(tenant, contract.productId());
        LOG.info("entitlements for product {}: {}", contract.productId(), entitlements);

        licenseServices.getAll()
                .stream()
                .filter(service -> service.supports(entitlements))
                .forEach(service -> service.createLicense(tenant, contract, changedByUserId, entitlements, assignLicenseDto));
    }

    private List<String> getEntitlements(final Tenant tenant, final String productId) {
        return productApi.getProductVariant(tenant, productId, null)
                .map(ProductDto::variants)
                .map(List<Variant>::getFirst)
                .map(Variant::entitlements)
                .orElse(List.of());
    }

    public Optional<LicenseEntity<?>> findLicense(final Tenant tenant, final String companyId, final String contractId) {
        return licenseDao.findByContractId(tenant, companyId, contractId).findAny();
    }

    public List<LicenseEntity<?>> getLicenses(@NotNull final Tenant tenant, final String companyId) {
        return licenseDao.findAllByCompanyId(tenant, companyId).toList();
    }

    public List<AssignLicenseData> getDefaults(@NotNull final Tenant tenant, final String companyId, final Set<String> contractIds) {
        return contractIds.stream()
                .flatMap(contractId -> fetchContract(tenant, companyId, contractId).stream())
                .flatMap(contractDto -> toDefaults(tenant, contractDto))
                .toList();
    }

    private Optional<ContractDto> fetchContract(final Tenant tenant, final String companyId, final String contractId) {
        try {
            return Optional.ofNullable(contractApi.getContract(tenant, companyId, contractId));
        } catch (final ClientErrorException e) {
            LOG.warn("Failed to fetch contract with tenant={}, companyId={}, contractId={}", tenant, companyId, contractId, e);
            return Optional.empty();
        }
    }

    private Stream<AssignLicenseData> toDefaults(final Tenant tenant, final ContractDto contract) {
        return productApi.getProductVariant(tenant, contract.productId(), null)
                .stream()
                .map(ProductDto::variants)
                .map(List::getFirst)
                .map(Variant::entitlements)
                .flatMap(entitlements -> licenseServices.getAll()
                        .stream()
                        .filter(service -> service.supports(entitlements))
                        .flatMap(service -> service.getDefaults(tenant, contract).stream()));
    }

    public void unassignLicense(@NotNull final Tenant tenantId,
                                @NotNull final String licenseId,
                                final String companyId,
                                final String changedByUserId) {
        LOG.info("unassignLicense for licenseId {}", licenseId);
        final LicenseEntity<?> license = licenseDao.findByCompanyIdAndLicenseId(tenantId, companyId, licenseId)
                .orElseThrow(() -> {
                    LOG.info("License not found {}", licenseId);
                    return new NotFoundException();
                });

        final LicenseServiceInterface licenseService = licenseServices.getService(license.getLicenseModel());
        licenseService.unassignLicense(license, changedByUserId);
    }

    public void processContractCancellation(final ContractEvent contractEvent) {
        final ContractDto contractDto = contractEvent.contractDto();
        final Tenant tenant = contractDto.tenant();
        licenseDao
            .findByContractId(tenant, contractDto.companyId(), contractDto.contractId())
            .filter(license -> license.getTenant().equals(tenant))
            .forEach(license -> {
                final LicenseServiceInterface licenseService = licenseServices.getService(license.getLicenseModel());
                licenseService.contractCancelled(license, contractEvent);
            });

    }

    public void processContractExpiration(final ContractEvent contractEvent) {
        final ContractDto contractDto = contractEvent.contractDto();
        final Tenant tenant = contractDto.tenant();
        licenseDao.findByContractId(tenant, contractDto.companyId(), contractDto.contractId())
            .filter(license -> license.getTenant().equals(tenant))
            .forEach(license -> {
                final LicenseServiceInterface licenseService = licenseServices.getService(license.getLicenseModel());
                licenseService.contractExpired(license, contractEvent);
            });
    }

    public void processContractUpdate(final ContractEvent contractEvent) {
        final ContractDto contractDto = contractEvent.contractDto();
        final Tenant tenant = contractDto.tenant();
        licenseDao.findByContractId(tenant, contractDto.companyId(), contractDto.contractId())
                .filter(license -> license.getTenant().equals(tenant))
                .forEach(license -> {
                    final LicenseServiceInterface licenseService = licenseServices.getService(license.getLicenseModel());
                    licenseService.contractUpdated(license, contractEvent);
                });
    }

    public void processContractCreation(final ContractEvent contractEvent) {
        final List<String> entitlements = getEntitlements(contractEvent.contractDto().tenant(),
                contractEvent.contractDto().productId());

        licenseServices.getAll()
                .stream()
                .filter(service -> service.supports(entitlements))
                .forEach(service -> service.contractCreated(contractEvent));
    }
}
