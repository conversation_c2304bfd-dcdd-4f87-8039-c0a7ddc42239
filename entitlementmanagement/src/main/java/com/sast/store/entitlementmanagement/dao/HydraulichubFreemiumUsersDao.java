package com.sast.store.entitlementmanagement.dao;

import org.springframework.stereotype.Component;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;

import java.util.Optional;

@Component
public class HydraulichubFreemiumUsersDao extends AbstractDynamodbDao<HydraulichubFreemiumUsersEntity> {

    public HydraulichubFreemiumUsersDao(final DynamoDbEnhancedClient dynamoDbEnhancedClient) {
        super(dynamoDbEnhancedClient, "bossstore_hydraulichub_freemium_users", HydraulichubFreemiumUsersEntity.class);
    }

    public Optional<HydraulichubFreemiumUsersEntity> findByEmail(final String email) {
        return findAllByPartitionValue(email).findAny();
    }

}