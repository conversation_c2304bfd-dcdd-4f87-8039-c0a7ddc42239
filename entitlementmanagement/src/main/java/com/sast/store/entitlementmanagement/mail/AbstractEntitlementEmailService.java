package com.sast.store.entitlementmanagement.mail;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.entitlementmanagement.config.AppConfiguration;
import com.sast.store.external.email.EmailServiceClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.net.URI;
import java.util.Optional;

@RequiredArgsConstructor
@Slf4j
public class AbstractEntitlementEmailService {

    protected final EmailServiceClient emailServiceClient;
    protected final AppConfiguration appConfiguration;

    protected URI getMarketplaceUrl(final Tenant tenant) {
        return Optional.ofNullable(appConfiguration.tenants().get(tenant))
                .map(AppConfiguration.TenantConfig::publicUrls)
                .map(AppConfiguration.PublicUrlConfig::marketplaceUrl)
                .orElseThrow(() -> new IllegalArgumentException("Marketplace URL not available for tenant " + tenant));
    }
}
