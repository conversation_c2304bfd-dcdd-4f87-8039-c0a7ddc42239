package com.sast.store.entitlementmanagement.service;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.contractmanagement.api.ContractDto;
import com.sast.store.contractmanagement.api.ContractEvent;
import com.sast.store.entitlementmanagement.api.AssignLicenseDto.AssignLicenseData;
import com.sast.store.entitlementmanagement.api.LicenseModel;
import com.sast.store.entitlementmanagement.dao.LicenseEntity;

import java.util.List;
import java.util.Optional;

public interface LicenseServiceInterface {

    void createLicense(Tenant tenant, ContractDto contract, String changedByUserId,
        List<String> entitlements, AssignLicenseData assignLicenseDto);

    LicenseModel getLicenseModel();

    default boolean supports(final List<String> entitlements) {
        return entitlements.stream().anyMatch(e -> e.equals(getLicenseModel().name()) || e.startsWith(getLicenseModel().name() + ":"));
    }

    void unassignLicense(LicenseEntity<?> license, String changedByUserId);

    default void contractCreated(final ContractEvent contractEvent) {
    }

    default void contractCancelled(final LicenseEntity<?> license, final ContractEvent contract) {
    }

    default void contractExpired(final LicenseEntity<?> license, final ContractEvent contract) {
    }

    default void contractUpdated(final LicenseEntity<?> license, final ContractEvent contract) {
    }

    default Optional<? extends AssignLicenseData> getDefaults(Tenant tenant, ContractDto contractDto) {
        return Optional.empty();
    }
}
