package com.sast.store.entitlementmanagement.dao;

import software.amazon.awssdk.core.pagination.sync.SdkIterable;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Expression;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import software.amazon.awssdk.enhanced.dynamodb.model.Page;
import software.amazon.awssdk.enhanced.dynamodb.model.PageIterable;
import software.amazon.awssdk.enhanced.dynamodb.model.PutItemEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

public abstract class AbstractDynamodbDao<T> {

    private final DynamoDbEnhancedClient dynamoDbEnhancedClient;
    private final DynamoDbTable<T> table;
    private final Class<T> entityClass;

    public AbstractDynamodbDao(final DynamoDbEnhancedClient dynamoDbEnhancedClient, final String tableName,
            final Class<T> entityClass) {
        this.dynamoDbEnhancedClient = dynamoDbEnhancedClient;
        this.table = this.dynamoDbEnhancedClient.table(tableName, TableSchema.fromBean(entityClass));
        this.entityClass = entityClass;
    }

    public void save(final T entity) {
        table.putItem(entity);
    }

    public void create(final T entity) {
        table.putItem(PutItemEnhancedRequest.builder(this.entityClass)
            .conditionExpression(Expression.builder()
                .expression("attribute_not_exists(" + table.tableSchema().tableMetadata().primaryPartitionKey() + ")")
                .build()
            )
            .item(entity)
            .build()
        );
    }

    public void delete(final T entity) {
        table.deleteItem(entity);
    }

    public void save(final Iterable<T> entity) {
        entity.forEach(this::save);
    }

    public List<T> findAllAsList() {
        return table().scan().items().stream().toList();
    }

    public Stream<T> findAll() {
        return table().scan().items().stream();
    }

    protected Optional<T> findByPartitionValueAndSortValue(final String partitionValue, final String sortValue) {
        return Optional.ofNullable(table().getItem(Key.builder()
                .partitionValue(partitionValue)
                .sortValue(sortValue)
                .build()));
    }

    protected Stream<T> findAllByPartitionValue(final String partitionValue) {
        final PageIterable<T> result = table()
                .query(QueryConditional
                        .keyEqualTo(Key.builder()
                                .partitionValue(partitionValue)
                                .build()));
        return result.items().stream();
    }

    protected Optional<T> findByPartitionValueAndSortValue(final String partitionValue, final String sortValue,
            final String index) {
        final SdkIterable<Page<T>> result = table().index(index)
                .query(QueryConditional.keyEqualTo(Key.builder()
                        .partitionValue(partitionValue)
                        .sortValue(sortValue)
                        .build()));
        return result.stream().map(Page::items).flatMap(List::stream).findFirst();
    }

    protected Stream<T> findAllByPartitionValue(final String partitionValue, final String index) {
        final SdkIterable<Page<T>> result = table().index(index)
                .query(QueryConditional.keyEqualTo(Key.builder()
                        .partitionValue(partitionValue)
                        .build()));
        return result.stream().map(Page::items).flatMap(List::stream);
    }

    protected DynamoDbTable<T> table() {
        return table;
    }
}
