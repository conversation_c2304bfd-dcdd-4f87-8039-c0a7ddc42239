package com.sast.store.entitlementmanagement;

import com.sast.store.commons.EnableCommonsAutoconfiguration;
import com.sast.store.external.EnableExternalClientsAutoconfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;

@SpringBootApplication
@EnableCaching
@EnableExternalClientsAutoconfiguration
@EnableCommonsAutoconfiguration
public class EntitlementmanagementApplication {

    public static void main(final String[] args) {
        SpringApplication.run(EntitlementmanagementApplication.class, args);
    }

}
