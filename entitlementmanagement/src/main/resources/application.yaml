logging:
  level:
    com.sast: DEBUG

management:
  endpoint:
    health:
      show-details: always
      probes.enabled: true
  endpoints.web.exposure.include: health, info, prometheus
  prometheus.metrics.export.enabled: true

spring:
  cache:
    type: caffeine
    cache-names: companies,companyDetails,users,user,companyManagers,subCompanies,companyInformation,listAllCountries,getCountry
    caffeine.spec: maximumSize=100000,expireAfterWrite=300s
  security.oauth2.resourceserver.jwt.issuer-uri: http://localhost:8000/auth/realms/baam
  temporal:
    connection:
      target: temporal-frontend.temporal.svc.cluster.local:7233
    workers:
      - name: send-email-worker
        task-queue: entitlement-management
    workersAutoDiscovery:
      packages:
        - com.sast.store.entitlementmanagement.workflows
        - com.sast.store.entitlementmanagement.activities

bossstore:
  dcLicensing:
    hydraulic-hub-landing-url: https://develop.hydraulic-hub.boschrexroth.com/login
  emails:
    queueName: EmailserviceMail
    fromAddress: <EMAIL>
  tenants:
    rexroth:
      public-urls:
        marketplace-url: https://localhost:8000/foo/bar/baz
        contract-management-url: https://localhost:8000/florp
      public-emails:
        support-email: <EMAIL>
  commercetools:
    clientId: gp83Agv3Z8RMX0tmNtljlFbo
    clientSecret: nED0ZFVUhUQq0QxzvsrIO5yJLH9USTXO
    projectKey: glorious-new-store
