package com.sast.store.entitlementmanagement;

import com.sast.store.contractmanagement.test.ContractServiceMockExtension;
import com.sast.store.entitlementmanagement.util.CommercetoolsMockExtension;
import com.sast.store.entitlementmanagement.workflows.SendReminderEmailWorkflowImpl;
import com.sast.store.external.bccentral.BcCentralMockExtension;
import com.sast.store.external.countriesservice.util.CountriesServiceMockExtension;
import com.sast.store.external.dckeycloak.test.DcKeycloakMockExtension;
import com.sast.store.external.litmos.test.LitmosMockExtension;
import com.sast.store.external.ump.test.UmpMockExtension;
import com.sast.store.productmanagement.test.ProductServiceMockExtension;
import com.sast.store.testing.awsmockup.junit.DynamodbMockExtension;
import com.sast.store.testing.awsmockup.junit.SnsMockExtension;
import com.sast.store.testing.awsmockup.junit.SqsMockExtension;
import com.tngtech.keycloakmock.api.ServerConfig;
import com.tngtech.keycloakmock.junit5.KeycloakMockExtension;
import io.restassured.RestAssured;
import io.restassured.filter.log.RequestLoggingFilter;
import io.restassured.filter.log.ResponseLoggingFilter;
import io.restassured.specification.RequestSpecification;
import io.temporal.testing.TestWorkflowExtension;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.util.StreamUtils;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;

import static com.tngtech.keycloakmock.api.TokenConfig.aTokenConfig;

@ExtendWith(SqsMockExtension.class)
@ExtendWith(SnsMockExtension.class)
@ExtendWith(DynamodbMockExtension.class)
@ExtendWith(DcKeycloakMockExtension.class)
@ExtendWith(LitmosMockExtension.class)
@ExtendWith(ProductServiceMockExtension.class)
@ExtendWith(ContractServiceMockExtension.class)
@ExtendWith(UmpMockExtension.class)
@ExtendWith(CommercetoolsMockExtension.class)
@ExtendWith(BcCentralMockExtension.class)
@ExtendWith(CountriesServiceMockExtension.class)
@SpringBootTest(
    classes = EntitlementmanagementApplication.class,
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    properties = {
        // disable caching of health endpoints
        "endpoints.health.time-to-live=0",
        "logging.level.com.sast=DEBUG",
        "logging.level.com.sast.store.commons.jerseyclient.ClientRequestResponseLogger=TRACE",
        "logging.level.commercetools=DEBUG",
        "spring.cloud.aws.region.static=eu-central-1",
        "spring.cloud.aws.credentials.access-key=fakekey",
        "spring.cloud.aws.credentials.secret-key=fakekey",
        "bossstore.scheduling.enabled=false",
        "bossstore.bccentral.url=http://localhost:34890",
        "bossstore.bccentral.username=test",
        "bossstore.bccentral.password=test",
        "bossstore.bccentral.stage=QA",
        "bossstore.dckeycloak.url=http://localhost:34561",
        "bossstore.dckeycloak.clientId=clientid",
        "bossstore.dckeycloak.clientSecret=clientsecret",
        "bossstore.dckeycloak.realm=realm",
        "bossstore.dcLicensing.litmosParentTeamId=02_Rexroth_Partner",
        "bossstore.dcLicensing.dcKeycloak.PREMIUM.roleNames=role1,role2",
        "bossstore.dcLicensing.dcKeycloak.PREMIUM.rolesClientUUID=e58a68d8-cab6-4516-b51a-dc2fd05a1107",
        "bossstore.dcLicensing.dcKeycloak.FREEMIUM.roleNames=role1,role2,role3",
        "bossstore.dcLicensing.dcKeycloak.FREEMIUM.roleNamesToRemove=role1,role2",
        "bossstore.dcLicensing.dcKeycloak.FREEMIUM.rolesClientUUID=e58a68d8-cab6-4516-b51a-dc2fd05a1107",
        "bossstore.dcLicensing.dcKeycloak.FREEMIUM.duration=P30D",
        "bossstore.dcLicensing.dcKeycloak.FREEMIUM.expiringSoonDuration=P5D",
        "bossstore.litmos.url=http://localhost:34729",
        "bossstore.litmos.apikey=apikey",
        "bossstore.hazelcast.enabled=local",
        "bossstore.ump.url=http://localhost:40101",
        "bossstore.ump.username=admin",
        "bossstore.ump.password=admin",
        "bossstore.countriesservice.url=http://localhost:40002",
        "bossstore.productmanagementUrl=http://localhost:34642/rest",
        "bossstore.contractmanagementUrl=http://localhost:34643/rest",
        "spring.temporal.connection.target=",
    })
@Import({CommercetoolsMockExtension.CommercetoolsMockClientConfig.class})
public abstract class AbstractComponentTest {
    protected static final String COMPANY_ID = UUID.randomUUID().toString();

    // CHECKSTYLE OFF: StaticVariableNameCheck
    protected static RequestSpecification VALID_KEYCLOAK_TOKEN;

    @RegisterExtension
    private static final KeycloakMockExtension KEYCLOAK_MOCK = new KeycloakMockExtension(
        ServerConfig.aServerConfig()
            .withPort(8000)
            .withDefaultRealm("baam")
            .build());

    @RegisterExtension
    private static final TestWorkflowExtension WORKFLOW_EXTENSION = TestWorkflowExtension.newBuilder()
            .registerWorkflowImplementationTypes(SendReminderEmailWorkflowImpl.class)
            .setDoNotStart(true)
            .build();


    @Value("http://localhost:${local.server.port}")
    protected String host;

    @DynamicPropertySource
    private static void overrideProperties(final DynamicPropertyRegistry registry) {
        registry.add("spring.cloud.aws.sqs.endpoint", () -> "http://localhost:" + SqsMockExtension.RANDOM_PORT);
        registry.add("spring.cloud.aws.dynamodb.endpoint", () -> "http://localhost:" + DynamodbMockExtension.PORT);
        registry.add("spring.cloud.aws.sns.endpoint", () -> "http://localhost:" + SnsMockExtension.getDefaultPort());
    }

    @BeforeAll
    public static void setUpUrl() {
        Locale.setDefault(Locale.US);
        RestAssured.enableLoggingOfRequestAndResponseIfValidationFails();
        RestAssured.filters(new RequestLoggingFilter(), new ResponseLoggingFilter());
    }

    @BeforeEach
    public void setUpKeycloakToken() {
        setupKeycloakToken("VIEW_ORDER_DETAILS", "EDIT_PAYMENT_DETAILS", "VIEW_PRICE", "PLACE_ORDER", "MANAGE_BACKOFFICE", "DEFAULT");
    }

    protected static void setupKeycloakToken(final String... roles) {
        final String accessToken = KEYCLOAK_MOCK.getAccessToken(aTokenConfig()
            .withSubject("user")
            .withAuthorizedParty("bossstore-frontend")
            .withClaims(Map.of(
                "resource_access", Map.of("bossstore-backend", Map.of("roles", Arrays.asList(roles))),
                "name", "John Doe",
                "preferred_username", "7cb0fdd4-9503-41e0-a279-84ad098c8a00",
                "given_name", "John",
                "family_name", "Doe",
                "email", "<EMAIL>",
                "company_name", "THE Company",
                "communication_language", "de",
                "company_id", COMPANY_ID))
            .build());
        VALID_KEYCLOAK_TOKEN = RestAssured.given().auth().preemptive()
            .oauth2(accessToken);
    }

    protected String loadJson(final String path) {
        try (final InputStream resourceInputStream = getClass().getResourceAsStream(path)) {
            if (resourceInputStream == null) {
                throw new FileNotFoundException(path);
            }
            return StreamUtils.copyToString(resourceInputStream, StandardCharsets.UTF_8);
        } catch (final IOException e) {
            throw new RuntimeException(e);
        }
    }
}
