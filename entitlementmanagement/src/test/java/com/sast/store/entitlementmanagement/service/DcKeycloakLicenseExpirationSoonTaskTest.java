package com.sast.store.entitlementmanagement.service;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.contractmanagement.api.ContractType;
import com.sast.store.contractmanagement.test.ContractServiceMockExtension;
import com.sast.store.entitlementmanagement.AbstractComponentTest;
import com.sast.store.entitlementmanagement.dao.DcKeycloakLicenseEntity;
import com.sast.store.entitlementmanagement.dao.DcKeycloakLicenseType;
import com.sast.store.entitlementmanagement.dao.LicenseDao;
import com.sast.store.entitlementmanagement.dao.TaskState;
import com.sast.store.entitlementmanagement.util.ContractmanagementTestDataGenerator;
import com.sast.store.external.dckeycloak.test.DcKeycloakMockExtension;
import com.sast.store.external.ump.test.UmpMockExtension;
import com.sast.store.productmanagement.test.ProductServiceMockExtension;
import com.sast.store.testing.awsmockup.junit.EmailUtil;
import jakarta.inject.Inject;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.json.BasicJsonTester;
import software.amazon.awssdk.services.sqs.model.Message;

import java.time.Instant;
import java.time.ZoneOffset;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.InstanceOfAssertFactories.type;

public class DcKeycloakLicenseExpirationSoonTaskTest extends AbstractComponentTest {

    @Inject
    private DcKeycloakLicenseService dcKeycloakLicenseService;

    @Inject
    private ContractmanagementTestDataGenerator testDataGenerator;

    @Inject
    private LicenseDao licenseDao;

    @Test
    public void testLicenseIsExpired() {
        DcKeycloakMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withDefaultResponse();
        UmpMockExtension.withDefaultResponse();
        ContractServiceMockExtension
            .withGetContractResponse(b -> b
                .companyId(COMPANY_ID)
                .contractId("contractId")
                .contractType(ContractType.SUBSCRIPTION));
        testDataGenerator
            .withDcKeycloakLicense().havingValue(l -> l
                .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                .withContractId("contractId")
                .withLicenseType(DcKeycloakLicenseType.PREMIUM)
                .withFreemiumExpiringSoonTaskState(null)
                .withFreemiumEndDate(Instant.now().atZone(ZoneOffset.UTC).plusDays(5).minusMinutes(1).toInstant()));
        final DcKeycloakLicenseEntity license = testDataGenerator.getDcKeycloakLicense();

        dcKeycloakLicenseService.licenseExpirationSoon();

        assertThat(licenseDao.findAll().toList().get(0)).asInstanceOf(type(DcKeycloakLicenseEntity.class)).satisfies(l -> {
            assertThat(l.getFreemiumExpiringSoonTaskState()).isEqualTo(TaskState.COMPLETED);
        });

        final List<Message> messages = EmailUtil.awaitEmails(1);
        assertThat(messages).hasSize(1).first().extracting(Message::body).extracting(new BasicJsonTester(getClass())::from)
            .satisfies(m -> {
                assertThat(m).extractingJsonPathStringValue("tenant").isEqualTo("rexroth");
                assertThat(m).extractingJsonPathStringValue("locale").isEqualTo("de");
                assertThat(m).extractingJsonPathStringValue("to[0]").isEqualTo(license.getEmail());
                assertThat(m).extractingJsonPathStringValue("templateName").isEqualTo("rexroth/entitlements/hydraulichubExpirationSoon");
                assertThat(m).extractingJsonPathStringValue("properties.firstName").isEqualTo(license.getFirstname());
                assertThat(m).extractingJsonPathStringValue("properties.lastName").isEqualTo(license.getLastname());
                assertThat(m).extractingJsonPathStringValue("properties.endDate").isNotNull();
                assertThat(m).extractingJsonPathStringValue("properties.marketplaceUrl").isEqualTo("https://localhost:8000/foo/bar/baz");
                assertThat(m).extractingJsonPathStringValue("properties.productName").isEqualTo("Hydraulic Hub");
                assertThat(m).extractingJsonPathStringValue("properties.expiration").isEqualTo("P5D");
            });
    }
}
