package com.sast.store.entitlementmanagement;

import com.sast.store.external.litmos.LitmosClient;
import com.sast.store.external.litmos.test.LitmosMockExtension;
import com.sast.store.gen.client.litmos.api.LitmosCreateTeamRequest;
import com.sast.store.gen.client.litmos.api.LitmosCreateUserRequest;
import com.sast.store.gen.client.litmos.api.LitmosCreateUserRequest.AccessLevelEnum;
import com.sast.store.gen.client.litmos.api.LitmosTeamInformation;
import com.sast.store.gen.client.litmos.api.LitmosUserDetails;
import com.sast.store.gen.client.litmos.api.LitmosUserInformation;
import jakarta.inject.Inject;
import org.junit.jupiter.api.Test;

import java.util.List;

public class LitmosTest extends AbstractComponentTest {

    @Inject
    private LitmosClient litmosClient;

    @Test
    public void testUserLifecycle() {
        LitmosMockExtension.withDefaultResponse();
        final LitmosUserDetails user = litmosClient
            .createUser(new LitmosCreateUserRequest()
                .firstName("firstname")
                .lastName("lastname")
                .email("<EMAIL>")
                .userName("<EMAIL>")
                .companyName("Bosch")
                .country("DE")
                .accessLevel(AccessLevelEnum.LEARNER)
                .active(Boolean.TRUE)
                .timeZone("UTC")
                .skipFirstLogin(Boolean.TRUE));
        final List<LitmosUserInformation> users = litmosClient.getUsers("<EMAIL>");
        final LitmosTeamInformation team = litmosClient
            .createTeam(new LitmosCreateTeamRequest()
                .name("Test Team")
                .description("Test Team Description")
                .parentTeamId(null)
                .copyParentCourses(Boolean.FALSE));
        litmosClient.assignCoursesToTeam(team.getId(), List.of("DoVWfPLcxig1"));
        litmosClient.assignUsersToTeam(team.getId(), List.of(user.getId()));
    }
}
