package com.sast.store.entitlementmanagement;

import com.sast.store.commons.basewebapp.security.Unprotected;
import com.sast.store.testing.commons.JaxRsEndpointUtil;
import com.sast.store.testing.commons.JaxRsEndpointUtil.Endpoint;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import io.restassured.specification.RequestSpecification;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.hamcrest.Matchers.oneOf;

public class WebSecurityAllInterfacesComponentTest extends AbstractComponentTest {
    private static final Logger LOG = LoggerFactory.getLogger(WebSecurityAllInterfacesComponentTest.class);

    static Stream<Arguments> allRestEndpoints() throws Exception {
        return JaxRsEndpointUtil
            .findEndpoints(EntitlementmanagementApplication.class.getPackage().getName()).stream()
            .map(Arguments::of);
    }

    static Stream<Arguments> allRestMethods() throws Exception {
        return JaxRsEndpointUtil
            .findRestMethodImplementations(EntitlementmanagementApplication.class.getPackage().getName()).stream()
            .map(Arguments::of);
    }

    @ParameterizedTest
    @MethodSource("allRestEndpoints")
    void testAllInterfacesInvalidAuth(final Endpoint endpoint) {
        LOG.debug("testing invalid authentication for endpoint {}", endpoint);

        final RequestSpecification request = RestAssured.given();
        if (endpoint.getBody() != null) {
            request.body(endpoint.getBody());
        }
        if (endpoint.getQueryParams() != null) {
            request.queryParams(endpoint.getQueryParams());
        }
        request
            .auth().preemptive().basic("user", "password1")
            .header("X-Tenant", "rexroth")
            .contentType(endpoint.getMediaType().toString())
            .when()
            .request(endpoint.getMethod(), host + "/rest" + endpoint.getPath())
            .then().statusCode(401).contentType(ContentType.JSON);
    }

    @ParameterizedTest
    @MethodSource("allRestEndpoints")
    void testAllInterfacesMissingAuth(final Endpoint endpoint) {
        LOG.debug("testing missing authentication for endpoint {}", endpoint);

        final RequestSpecification request = RestAssured.given();
        if (endpoint.getBody() != null) {
            request.body(endpoint.getBody());
        }
        if (endpoint.getQueryParams() != null) {
            request.queryParams(endpoint.getQueryParams());
        }
        request
            .header("X-Tenant", "rexroth")
            .contentType(endpoint.getMediaType().toString())
            .when()
            .request(endpoint.getMethod(), host + "/rest" + endpoint.getPath())
            .then().statusCode(401).contentType(ContentType.JSON);
    }

    @ParameterizedTest
    @MethodSource("allRestMethods")
    void testAllEndpointsCheckAuthorization(final Method method) {
        final List<?> annotations = Arrays.asList(method.getAnnotations()).stream()
            .map(Annotation::annotationType)
            .toList();

        assertThat("public rest interface " + method + " is missing a @PreAuthorize or @Unprotected annotation", annotations,
            hasItem(oneOf(PreAuthorize.class, Unprotected.class)));
    }

}