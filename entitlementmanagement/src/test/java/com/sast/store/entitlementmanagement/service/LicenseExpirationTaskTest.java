package com.sast.store.entitlementmanagement.service;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.contractmanagement.api.ContractType;
import com.sast.store.contractmanagement.test.ContractServiceMockExtension;
import com.sast.store.entitlementmanagement.AbstractComponentTest;
import com.sast.store.entitlementmanagement.dao.DcKeycloakLicenseEntity;
import com.sast.store.entitlementmanagement.dao.DcKeycloakLicenseType;
import com.sast.store.entitlementmanagement.dao.LicenseDao;
import com.sast.store.entitlementmanagement.util.ContractmanagementTestDataGenerator;
import com.sast.store.external.dckeycloak.test.DcKeycloakMockExtension;
import com.sast.store.external.ump.test.UmpMockExtension;
import com.sast.store.productmanagement.test.ProductServiceMockExtension;
import com.sast.store.testing.awsmockup.junit.EmailUtil;
import jakarta.inject.Inject;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.json.BasicJsonTester;
import software.amazon.awssdk.services.sqs.model.Message;

import java.time.Instant;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class LicenseExpirationTaskTest extends AbstractComponentTest {

    @Inject
    private DcKeycloakLicenseService licensingService;

    @Inject
    private ContractmanagementTestDataGenerator testDataGenerator;

    @Inject
    private LicenseDao licenseDao;

    @Test
    public void testLicenseIsExpired() {
        UmpMockExtension.withDefaultResponse();
        DcKeycloakMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withDefaultResponse();
        ContractServiceMockExtension
            .withGetContractResponse(b -> b
                .companyId(COMPANY_ID)
                .contractId("contractId")
                .contractType(ContractType.SUBSCRIPTION));
        testDataGenerator
            .withDcKeycloakLicense().havingValue(l -> l
                .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                .withContractId("contractId")
                .withLicenseType(DcKeycloakLicenseType.FREEMIUM)
                .withFreemiumExpiredTaskState(null)
                .withFreemiumEndDate(Instant.parse("2024-10-09T10:23:58Z")));
        final DcKeycloakLicenseEntity license = testDataGenerator.getDcKeycloakLicense();

        licensingService.freemiumExpiration();

        assertThat(licenseDao.findAll()).hasSize(1);
        DcKeycloakMockExtension.instance().verify(1,
            WireMock.deleteRequestedFor(WireMock.urlEqualTo(
                "/admin/realms/realm/users/d4e22f66-a386-40e2-9e17-b4a5add5dac0"
                    + "/role-mappings/clients/e58a68d8-cab6-4516-b51a-dc2fd05a1107"))
                .withRequestBody(WireMock.equalToJson("""
                    [
                        {
                            "id": "bd3d709c-028b-4d94-b065-48f53718f85e",
                            "name": "role1",
                            "description": "",
                            "scopeParamRequired": null,
                            "composite": false,
                            "composites": null,
                            "clientRole": true,
                            "containerId": "b16d2c3d-7e41-4b15-9023-85273d93771d",
                            "attributes": null
                        },
                        {
                            "id": "630d9cd2-762d-43c5-9c7a-81086261fc0e",
                            "name": "role2",
                            "description": "",
                            "scopeParamRequired": null,
                            "composite": false,
                            "composites": null,
                            "clientRole": true,
                            "containerId": "b16d2c3d-7e41-4b15-9023-85273d93771d",
                            "attributes": null
                        }
                    ]""", true, false)));

        final List<Message> messages = EmailUtil.awaitEmails(1);
        assertThat(messages).hasSize(1).first().extracting(Message::body).extracting(new BasicJsonTester(getClass())::from)
            .satisfies(m -> {
                assertThat(m).extractingJsonPathStringValue("tenant").isEqualTo("rexroth");
                assertThat(m).extractingJsonPathStringValue("locale").isEqualTo("de");
                assertThat(m).extractingJsonPathStringValue("to[0]").isEqualTo(license.getEmail());
                assertThat(m).extractingJsonPathStringValue("templateName").isEqualTo("rexroth/entitlements/hydraulichubExpiration");
                assertThat(m).extractingJsonPathStringValue("properties.firstName").isEqualTo(license.getFirstname());
                assertThat(m).extractingJsonPathStringValue("properties.lastName").isEqualTo(license.getLastname());
                assertThat(m).extractingJsonPathStringValue("properties.endDate").isEqualTo("2024-10-09T10:23:58");
                assertThat(m).extractingJsonPathStringValue("properties.marketplaceUrl").isEqualTo("https://localhost:8000/foo/bar/baz");
                assertThat(m).extractingJsonPathStringValue("properties.productName").isEqualTo("Hydraulic Hub");
            });
    }

    @Test
    public void testLicenseIsNotExpired() {
        DcKeycloakMockExtension.withDefaultResponse();
        testDataGenerator.withDcKeycloakLicense().havingValue(l -> l.withFreemiumEndDate(Instant.now().plusSeconds(60)));

        licensingService.freemiumExpiration();

        assertThat(licenseDao.findAll()).hasSize(1);
        DcKeycloakMockExtension.instance().verify(0, WireMock.deleteRequestedFor(WireMock.anyUrl()));

    }
}
