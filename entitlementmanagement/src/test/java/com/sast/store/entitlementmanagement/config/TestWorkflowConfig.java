package com.sast.store.entitlementmanagement.config;

import io.temporal.client.WorkflowClient;
import io.temporal.testing.TestWorkflowEnvironment;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class TestWorkflowConfig {

    @Bean
    public TestWorkflowEnvironment testWorkflowEnvironment() {
        return TestWorkflowEnvironment.newInstance();
    }

    @Bean
    public WorkflowClient workflowClient(final TestWorkflowEnvironment testWorkflowEnvironment) {
        return testWorkflowEnvironment.getWorkflowClient();
    }
}
