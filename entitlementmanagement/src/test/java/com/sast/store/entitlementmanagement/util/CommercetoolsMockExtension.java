package com.sast.store.entitlementmanagement.util;

import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.defaultconfig.ApiRootBuilder;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.github.tomakehurst.wiremock.junit5.WireMockExtension;
import com.sast.store.external.commercetools.CommercetoolsConfiguration;
import com.sast.store.external.commercetools.CommercetoolsRestMetricsJerseyClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.containing;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching;

public class CommercetoolsMockExtension extends WireMockExtension {

    private static CommercetoolsMockExtension instance;

    public CommercetoolsMockExtension() {
        super(WireMockExtension
            .extensionOptions()
            .options(WireMockConfiguration.options()
                .port(40000))
            .configureStaticDsl(true));
        instance = this;
    }

    public static CommercetoolsMockExtension get() {
        return instance;
    }

    public static CommercetoolsMockExtension withDefaultResponse() {
        withProductProjectionsSearchResponse();
        withCartResponse();
        withCartCreationResponse();
        withCartRecalculateResponse(3, 60000);
        withCartDeleteResponse();
        withCustomerGroupsResponse();
        withOrderResponse();
        withCreateOrderResponse();
        withUpdateOrderResponse();
        return instance;
    }

    public static CommercetoolsMockExtension withCustomerGroupsResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/customer-groups/key=.*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody(
                    """
                        {
                          "id": "e0723008-15c7-4dcb-8aba-028ad747d90b",
                          "version": 1,
                          "versionModifiedAt": "2024-04-12T13:47:40.669Z",
                          "createdAt": "2024-04-12T13:47:40.669Z",
                          "lastModifiedAt": "2024-04-12T13:47:40.669Z",
                          "lastModifiedBy": {
                            "clientId": "HS19nIjzrbmIUDIhVgGP3xmq",
                            "isPlatformClient": false
                          },
                          "createdBy": {
                            "clientId": "HS19nIjzrbmIUDIhVgGP3xmq",
                            "isPlatformClient": false
                          },
                          "name": "IDW000",
                          "key": "IDW000"
                        }
                        """)
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withCartCreationResponse() {
        instance.stubFor(WireMock.post(urlPathMatching("/.*/carts"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("commercetools/cart-create-response.json")
                .withTransformers("response-template")));

        return instance;

    }

    public static CommercetoolsMockExtension withCartResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/carts"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("commercetools/carts-get-response.json")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withCartRecalculateResponse(final int totalQuantity, final int totalAmount) {
        instance.stubFor(WireMock.post(urlPathMatching("/.*/carts/key.*"))
                .withRequestBody(containing("recalculate"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("content-type", "application/json;charset=UTF-8")
                        .withBodyFile("commercetools/cart-recalculate-response.json.hbs")
                        .withTransformers("response-template")
                        .withTransformerParameter("totalQuantity", totalQuantity)
                        .withTransformerParameter("totalAmount", totalAmount)
                ));

        return instance;
    }

    public static CommercetoolsMockExtension withCartDeleteResponse() {
        instance.stubFor(WireMock.delete(urlPathMatching("/.*/carts/key.*"))
                .willReturn(aResponse()
                        .withStatus(201)));

        return instance;
    }

    public static CommercetoolsMockExtension withNonEmptyCartResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/carts"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("commercetools/carts-get-non-empty-response.json")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withProductProjectionsSearchResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/product-projections/search"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("commercetools/product-projection-search-response.json")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withOrderResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/orders/order-number=.*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("commercetools/order-get-response.json")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withCreateOrderResponse() {
        instance.stubFor(WireMock.post(urlPathMatching("/.*/orders"))
                .willReturn(WireMock.aResponse()
                        .withStatus(201)
                        .withHeader("content-type", "application/json;charset=UTF-8")
                        .withBodyFile("commercetools/order-create-response.json")
                        .withTransformers("response-template")));
        return instance;
    }

    public static CommercetoolsMockExtension withCreateOrderFailure() {
        instance.stubFor(WireMock.post(urlPathMatching("/.*/orders"))
                .willReturn(WireMock.aResponse()
                        .withStatus(500)
                        .withHeader("content-type", "application/json;charset=UTF-8")
                        .withBody("{}")));
        return instance;
    }

    public static CommercetoolsMockExtension withUpdateOrderResponse() {
        instance.stubFor(WireMock.post(urlPathMatching("/.*/orders/.*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("commercetools/order-update-response.json")
                .withTransformers("response-template")));

        return instance;
    }



    @TestConfiguration
    public static class CommercetoolsMockClientConfig {
        private static final Logger LOG = LoggerFactory.getLogger(CommercetoolsMockClientConfig.class);

        private static final String MOCK_ENDPOINT = "http://localhost:40000";

        @Bean
        @Primary
        public ProjectApiRoot commercetoolsMockClient(CommercetoolsConfiguration appConfiguration,
                                                      CommercetoolsRestMetricsJerseyClientInterceptor metricsInterceptor) {
            LOG.warn("using {} as commercetools api endpoint", MOCK_ENDPOINT);
            return ApiRootBuilder.of().defaultClient(MOCK_ENDPOINT)
                    .addMiddleware(metricsInterceptor)
                    .withApiBaseUrl(MOCK_ENDPOINT)
                    .build(appConfiguration.projectKey());
        }
    }
}
