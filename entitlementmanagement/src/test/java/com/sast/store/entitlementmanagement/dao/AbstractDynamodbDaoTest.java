package com.sast.store.entitlementmanagement.dao;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.entitlementmanagement.AbstractComponentTest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.Test;
import software.amazon.awssdk.services.dynamodb.model.ConditionalCheckFailedException;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

class AbstractDynamodbDaoTest extends AbstractComponentTest {

    @Inject
    private LitmosTeamDao litmosTeamDao;

    @Test
    void create_putsNonExistingEntity() {
        litmosTeamDao.create(new LitmosTeamEntity().withCompanyId(Tenant.REXROTH, "test-company-id"));

        assertThat(litmosTeamDao.findByCompanyId(Tenant.REXROTH, "test-company-id"))
            .isPresent();
    }

    @Test
    void create_throwsAndDoesNotReplaceExistingEntity() {
        litmosTeamDao.save(new LitmosTeamEntity()
            .withCompanyId(Tenant.REXROTH, "test-company-id")
            .withTeamId("test-team-id")
        );

        assertThrows(
            ConditionalCheckFailedException.class,
            () -> litmosTeamDao.create(new LitmosTeamEntity().withCompanyId(Tenant.REXROTH, "test-company-id"))
        );

        assertThat(litmosTeamDao.findByCompanyId(Tenant.REXROTH, "test-company-id"))
            .hasValueSatisfying(p -> assertThat(p.getTeamId()).isEqualTo("test-team-id"));
    }
}
