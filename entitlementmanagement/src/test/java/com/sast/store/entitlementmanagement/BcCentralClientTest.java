package com.sast.store.entitlementmanagement;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.sast.store.external.bccentral.BcCentralClient;
import com.sast.store.external.bccentral.BcCentralMockExtension;
import com.sast.store.external.bccentral.api.CreateBooking;
import com.sast.store.external.bccentral.api.CreateContractRequest;
import com.sast.store.external.bccentral.api.Customer;
import com.sast.store.external.bccentral.api.DeleteContractRequest;
import com.sast.store.external.bccentral.api.Tenant;
import com.sast.store.external.bccentral.api.UpdateBooking;
import com.sast.store.external.bccentral.api.UpdateContractRequest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.Currency;
import java.util.List;

import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson;
import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo;
import static org.assertj.core.api.Assertions.assertThat;

class BcCentralClientTest extends AbstractComponentTest {

    @Inject
    private BcCentralClient bcCentralClient;

    @Test
    void getMaterialNumbers() {
        BcCentralMockExtension.withGetMaterialNumbersResponse();

        final var materialNumbers = bcCentralClient.getMaterialNumbers();

        assertThat(materialNumbers).isNotNull();
        assertThat(materialNumbers.iotServices()).isNotEmpty();
    }

    @Test
    void createContract() {
        BcCentralMockExtension.withCreateContractResponse();

        final var response = bcCentralClient.createContract(CreateContractRequest.builder()
                .contractId("test-contract-id-12345689")
                .customer(Customer.builder()
                        .id("test-user-id-987654321")
                        .currency("EUR")
                        .name("TEST Herbert")
                        .build())
                .tenant(Tenant.builder()
                        .name("TEST Herbert")
                        .owner(List.of("<EMAIL>"))
                        .build())
                .booking(List.of(CreateBooking.builder()
                        .licenseKey("test-license-key")
                        .materialNumber("R917014006")
                        .build()))
                .build());

        assertThat(response).isNotNull();

        BcCentralMockExtension.get()
                .verify(postRequestedFor(urlPathEqualTo("/data-recorder-service/v2/pog8306"))
                        .withHeader("X-Metadata", WireMock.containing("boss_contract_creation")
                                .and(WireMock.containing("timestamp="))
                                .and(WireMock.containing("sender=BOSS"))
                                .and(WireMock.containing("version=X.X"))
                                .and(WireMock.containing("stage=QA")))
                        .withRequestBody(equalToJson(loadJson("/bccentral/create-contract-request.json")))
                );
    }

    @Test
    void updateContract() {
        BcCentralMockExtension.withUpdateContractResponse();

        final var response = bcCentralClient.updateContract(UpdateContractRequest.builder()
                .contractId("test-contract-id-12345689")
                .requestorId("<EMAIL>")
                .requestId("R1234")
                .customerCurrency(Currency.getInstance("EUR"))
                .booking(List.of(
                        UpdateBooking.builder()
                                .materialNumber("R12345")
                                .licenseKey("PRX_BC_1000")
                                .startDate(ZonedDateTime.parse("2024-07-01T00:00:00Z"))
                                .endDate(ZonedDateTime.parse("2025-03-31T23:59:59Z"))
                                .build(),
                        UpdateBooking.builder()
                                .materialNumber("R12346")
                                .licenseKey("PRX_BC_1000")
                                .startDate(ZonedDateTime.parse("2025-04-01T00:00:00Z"))
                                .build()
                ))
                .build());

        assertThat(response).isNotNull();

        BcCentralMockExtension.get()
                .verify(postRequestedFor(urlPathEqualTo("/data-recorder-service/v2/pog8306"))
                        .withHeader("X-Metadata", WireMock.containing("boss_contract_update")
                                .and(WireMock.containing("timestamp="))
                                .and(WireMock.containing("sender=BOSS"))
                                .and(WireMock.containing("version=X.X"))
                                .and(WireMock.containing("stage=QA")))
                        .withRequestBody(equalToJson(loadJson("/bccentral/update-contract-request.json")))
                );
    }

    @Test
    void getContract() {
        BcCentralMockExtension.withGetContractResponse();

        final var response = bcCentralClient.getContract("test-contract-id-12345689");

        assertThat(response).isNotNull();
    }

    @Test
    void deleteContract() {
        BcCentralMockExtension.withDeleteContractResponse();

        final var response = bcCentralClient.deleteContract(DeleteContractRequest.builder()
                .contractId("test-contract-id-12345689")
                .terminationDate(Instant.parse("2024-07-01T00:00:00Z"))
                .requestor("<EMAIL>")
                .build());

        assertThat(response).isNotNull();

        BcCentralMockExtension.get()
                .verify(postRequestedFor(urlPathEqualTo("/data-recorder-service/v2/pog8306"))
                        .withHeader("X-Metadata", WireMock.containing("boss_contract_deletion")
                                .and(WireMock.containing("timestamp="))
                                .and(WireMock.containing("sender=BOSS"))
                                .and(WireMock.containing("version=X.X"))
                                .and(WireMock.containing("stage=QA")))
                        .withRequestBody(equalToJson(loadJson("/bccentral/delete-contract-request.json")))
                );
    }
}
