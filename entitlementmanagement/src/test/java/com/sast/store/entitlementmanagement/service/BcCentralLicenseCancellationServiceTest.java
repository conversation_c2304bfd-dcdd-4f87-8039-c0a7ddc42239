package com.sast.store.entitlementmanagement.service;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.entitlementmanagement.api.LicenseModel;
import com.sast.store.entitlementmanagement.dao.BcCentralLicenseEntity;
import com.sast.store.entitlementmanagement.dao.CancellationState;
import com.sast.store.entitlementmanagement.dao.LicenseDao;
import com.sast.store.external.bccentral.BcCentralClient;
import com.sast.store.external.bccentral.api.DeleteContractRequest;
import com.sast.store.external.bccentral.api.DeleteContractResponse;
import com.sast.store.external.ump.UmpClient;
import com.sast.store.gen.client.ump.api.UmpUserDto;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.temporal.TemporalAdjusters;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BcCentralLicenseCancellationServiceTest {

    public static final String TEST_CONTRACT_1 = "test-contract-1";
    private final LicenseDao licenseDao = mock(LicenseDao.class);
    private final BcCentralClient bcCentralClient = mock(BcCentralClient.class);
    private final UmpClient umpClient = mock(UmpClient.class);

    private final BcCentralLicenseCancellationService cancellationService =
        spy(new BcCentralLicenseCancellationService(licenseDao, bcCentralClient, umpClient));

    @Test
    void processLicenseCancellations_whenTargetDateNotEqualLastDay_shouldNotCancel() {
        when(cancellationService.now()).thenReturn(LocalDate.of(2023, 4, 10));

        cancellationService.processLicenseCancellations();

        verify(licenseDao, never()).findByCancellationStateWithEndDate(any());
    }

    @Test
    void processLicenseCancellations_whenTargetDateEqualsLastDay_shouldCancelLicenses() {
        final LocalDate fakeToday = LocalDate.of(2023, 1, 29);
        when(cancellationService.now()).thenReturn(fakeToday);
        final BcCentralLicenseEntity license = mockLicenseDao(fakeToday.with(TemporalAdjusters.lastDayOfMonth()));
        mockBcCentralResponse();
        mockGetUser();

        cancellationService.processLicenseCancellations();

        verify(bcCentralClient, times(1))
            .deleteContract(argThat(req -> req.contractId().equals(TEST_CONTRACT_1)));
        assertThat(license.getCancellationState()).isEqualTo(CancellationState.CANCELLED);
        verify(licenseDao, times(1)).save(license);
    }

    private void mockBcCentralResponse() {
        final DeleteContractResponse dummyResponse = DeleteContractResponse.builder()
            .inputDataId("dummy")
            .collectionName("dummy")
            .inputFileSize(123L)
            .build();
        when(bcCentralClient.deleteContract(any(DeleteContractRequest.class)))
            .thenReturn(dummyResponse);
    }

    private void mockGetUser() {
        final UmpUserDto userDto = mock(UmpUserDto.class);
        when(userDto.getEmail()).thenReturn("test-email");
        when(umpClient.getUser(any(), any())).thenReturn(userDto);
    }

    private BcCentralLicenseEntity mockLicenseDao(final LocalDate lastDayOfMonth) {
        final BcCentralLicenseEntity license = getBcCentralLicenseEntity(lastDayOfMonth);
        when(licenseDao.findByCancellationStateWithEndDate(CancellationState.CANCELLATION_PENDING))
            .thenReturn(Stream.of(license));
        return license;
    }

    private BcCentralLicenseEntity getBcCentralLicenseEntity(final LocalDate lastDayOfMonth) {
        final Instant fakeEndDate = lastDayOfMonth.atStartOfDay(ZoneOffset.UTC).toInstant();
        final BcCentralLicenseEntity license = new BcCentralLicenseEntity();
        license.withCompanyId(Tenant.REXROTH, "test-company");
        license.withLicenseId("test-license-1");
        license.withBccentralContractId(TEST_CONTRACT_1);
        license.withCancelledByUserId("test-user");
        license.withLicenseModel(LicenseModel.BCCENTRAL);
        license.setEndDate(fakeEndDate);
        license.setCancellationState(CancellationState.CANCELLATION_PENDING);
        return license;
    }
}
