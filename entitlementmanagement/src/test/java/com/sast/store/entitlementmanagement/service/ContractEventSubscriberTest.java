package com.sast.store.entitlementmanagement.service;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.hazelcast.config.Config;
import com.hazelcast.core.Hazelcast;
import com.hazelcast.core.HazelcastInstance;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.contractmanagement.api.ContractDto;
import com.sast.store.contractmanagement.api.ContractEvent;
import com.sast.store.contractmanagement.api.ContractEventHeader;
import com.sast.store.contractmanagement.api.ContractType;
import com.sast.store.entitlementmanagement.AbstractComponentTest;
import com.sast.store.entitlementmanagement.dao.DcKeycloakLicenseEntity;
import com.sast.store.entitlementmanagement.util.ContractmanagementTestDataGenerator;
import com.sast.store.external.bccentral.BcCentralMockExtension;
import com.sast.store.external.countriesservice.util.CountriesServiceMockExtension;
import com.sast.store.external.dckeycloak.test.DcKeycloakMockExtension;
import com.sast.store.external.ump.test.UmpMockExtension;
import com.sast.store.productmanagement.test.ProductServiceMockExtension;
import com.sast.store.testing.awsmockup.junit.EmailUtil;
import jakarta.inject.Inject;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.json.BasicJsonTester;
import software.amazon.awssdk.services.sqs.model.Message;

import java.time.Duration;
import java.time.OffsetDateTime;
import java.time.Period;
import java.util.List;
import java.util.UUID;

import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo;
import static org.assertj.core.api.Assertions.assertThat;

public class ContractEventSubscriberTest extends AbstractComponentTest {

    @Inject
    private ContractmanagementTestDataGenerator testDataGenerator;

    @Inject
    private ContractEventSubscriber contractEventSubscriber;

    @Autowired
    private Config hazelcastConfig;

    @Test
    public void testContractCancellationEventHandling() {
        UmpMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withDefaultResponse();
        testDataGenerator
                .withDcKeycloakLicense().havingValue(e -> e
                        .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                        .withEmail("<EMAIL>")
                        .withContractId("BC_90000000001"));

        // spin up a second hazelcast instance to simulate the event being sent from another node
        final HazelcastInstance hazelcastInstance2 = Hazelcast.newHazelcastInstance(hazelcastConfig);
        Awaitility.await().atMost(Duration.ofSeconds(10)).untilAsserted(() -> {
            assertThat(hazelcastInstance2.getCluster().getMembers()).hasSize(2);
        });

        final ContractEvent payload = ContractEvent.builder()
                .header(ContractEventHeader.builder().eventId(UUID.randomUUID().toString()).build())
                .contractDto(ContractDto.builder()
                        .tenant(Tenant.REXROTH)
                        .companyId(COMPANY_ID)
                        .contractId("BC_90000000001")
                        .orderNumber("asdasdasd")
                        .contractType(ContractType.SUBSCRIPTION)
                        .productId("DC_12345678Z100")
                        .endDate(OffsetDateTime.parse("2024-10-28T23:59:59+01:00"))
                        .startDate(OffsetDateTime.parse("2024-07-28T12:00:00+02:00"))
                        .cancelledByUserId("564b26de-edd4-4494-869a-448e83838269")
                        .addons(List.of())
                        .build())
                .build();
        hazelcastInstance2.getQueue("contractcancellationevents").add(payload);

        final List<Message> messages = EmailUtil.awaitEmails(1);
        assertThat(messages).hasSize(1);
        assertThat(messages).first().extracting(Message::body).extracting(new BasicJsonTester(getClass())::from)
                .satisfies(m -> {
                    assertThat(m).extractingJsonPathStringValue("templateName").isEqualTo("rexroth/entitlements/cancelConfirmation");
                });
        hazelcastInstance2.shutdown();
    }

    @Test
    public void testContractCancellationEvent() {
        UmpMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withDefaultResponse();
        testDataGenerator
                .withDcKeycloakLicense().havingValue(e -> e
                        .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                        .withEmail("<EMAIL>")
                        .withContractId("BC_90000000001"));
        final DcKeycloakLicenseEntity license = testDataGenerator.getDcKeycloakLicense();

        final ContractEvent payload = ContractEvent.builder()
                .header(ContractEventHeader.builder().eventId(UUID.randomUUID().toString()).build())
                .contractDto(ContractDto.builder()
                        .tenant(Tenant.REXROTH)
                        .companyId(COMPANY_ID)
                        .contractId("BC_90000000001")
                        .orderNumber("asdasdasd")
                        .contractType(ContractType.SUBSCRIPTION)
                        .productId("DC_12345678Z100")
                        .endDate(OffsetDateTime.parse("2024-10-28T23:59:59+01:00"))
                        .startDate(OffsetDateTime.parse("2024-07-28T12:00:00+02:00"))
                        .cancelledByUserId("564b26de-edd4-4494-869a-448e83838269")
                        .addons(List.of())
                        .build())
                .build();
        contractEventSubscriber.onContractCancellationEvent(payload);

        final List<Message> messages = EmailUtil.awaitEmails(1);
        assertThat(messages).hasSize(1);
        assertThat(messages).first().extracting(Message::body).extracting(new BasicJsonTester(getClass())::from)
                .satisfies(m -> {
                    assertThat(m).extractingJsonPathStringValue("tenant").isEqualTo("rexroth");
                    assertThat(m).extractingJsonPathStringValue("locale").isEqualTo("de");
                    assertThat(m).extractingJsonPathStringValue("to[0]").isEqualTo("<EMAIL>");
                    assertThat(m).extractingJsonPathStringValue("templateName").isEqualTo("rexroth/entitlements/cancelConfirmation");
                    assertThat(m).extractingJsonPathStringValue("properties.cancelledByFirstName").isEqualTo("myFirstname");
                    assertThat(m).extractingJsonPathStringValue("properties.cancelledByLastName").isEqualTo("myLastname");
                    assertThat(m).extractingJsonPathStringValue("properties.firstName").isEqualTo(license.getFirstname());
                    assertThat(m).extractingJsonPathStringValue("properties.lastName").isEqualTo(license.getLastname());
                    assertThat(m).extractingJsonPathStringValue("properties.endDate").isEqualTo("2024-10-28T23:59:59");
                    assertThat(m).extractingJsonPathStringValue("properties.marketplaceUrl")
                            .isEqualTo("https://localhost:8000/foo/bar/baz");
                    assertThat(m).extractingJsonPathStringValue("properties.productName").isEqualTo("Hydraulic Hub");
                });
    }

    @Test
    public void testContractExpirationEvent() {
        UmpMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withDefaultResponse();
        DcKeycloakMockExtension.withDefaultResponse();
        testDataGenerator
                .withDcKeycloakLicense().havingValue(e -> e
                        .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                        .withEmail("<EMAIL>")
                        .withContractId("BC_90000000001"));
        final DcKeycloakLicenseEntity license = testDataGenerator.getDcKeycloakLicense();

        final ContractEvent payload = ContractEvent.builder()
                .header(ContractEventHeader.builder().eventId(UUID.randomUUID().toString()).build())
                .contractDto(ContractDto.builder()
                        .tenant(Tenant.REXROTH)
                        .companyId(COMPANY_ID)
                        .contractId("BC_90000000001")
                        .orderNumber("asdasdasd")
                        .contractType(ContractType.SUBSCRIPTION)
                        .productId("DC_12345678Z100")
                        .endDate(OffsetDateTime.parse("2024-10-28T23:59:59+01:00"))
                        .startDate(OffsetDateTime.parse("2024-07-28T12:00:00+02:00"))
                        .cancelledByUserId("564b26de-edd4-4494-869a-448e83838269")
                        .addons(List.of())
                        .build())
                .build();
        contractEventSubscriber.onExpirationEvent(payload);

        final List<Message> messages = EmailUtil.awaitEmails(1);
        assertThat(messages).hasSize(1);
        assertThat(messages).first().extracting(Message::body).extracting(new BasicJsonTester(getClass())::from)
                .satisfies(m -> {
                    assertThat(m).extractingJsonPathStringValue("tenant").isEqualTo("rexroth");
                    assertThat(m).extractingJsonPathStringValue("locale").isEqualTo("de");
                    assertThat(m).extractingJsonPathStringValue("to[0]").isEqualTo("<EMAIL>");
                    assertThat(m).extractingJsonPathStringValue("templateName").isEqualTo("rexroth/entitlements/unassignment");
                    assertThat(m).extractingJsonPathStringValue("properties.changedByFirstName").isEqualTo("myFirstname");
                    assertThat(m).extractingJsonPathStringValue("properties.changedByLastName").isEqualTo("myLastname");
                    assertThat(m).extractingJsonPathStringValue("properties.firstName").isEqualTo(license.getFirstname());
                    assertThat(m).extractingJsonPathStringValue("properties.lastName").isEqualTo(license.getLastname());
                    assertThat(m).extractingJsonPathStringValue("properties.productName").isEqualTo("Hydraulic Hub");
                });
    }

    @Test
    public void testContractUpdateEvent() {
        UmpMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withDefaultResponse();
        CountriesServiceMockExtension.withDefaultResponse();
        BcCentralMockExtension.withDefaultResponse();
        testDataGenerator
                .withBcCentralLicense().havingValue(e -> e
                        .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                        .withContractId("BC_90000000001"));

        final ContractEvent payload = ContractEvent.builder()
                .header(ContractEventHeader.builder().eventId(UUID.randomUUID().toString()).build())
                .contractDto(ContractDto.builder()
                        .tenant(Tenant.REXROTH)
                        .companyId(COMPANY_ID)
                        .contractId("BC_90000000001")
                        .orderNumber("asdasdasd")
                        .productId("DC_12345678Z100")
                        .startDate(OffsetDateTime.parse("2024-07-28T12:00:00+02:00"))
                        .addons(List.of(ContractDto.AddonDto.builder()
                                        .contractId("BC_90000000002")
                                        .productId("DC_12345678A100")
                                        .startDate(OffsetDateTime.parse("2024-08-01T02:00:00+02:00"))
                                        .endDate(OffsetDateTime.parse("2024-08-30T01:59:59+02:00"))
                                        .contractType(ContractType.CONSUMPTION)
                                        .contractPeriod(Period.ofMonths(1))
                                        .noticePeriod(Period.ofDays(7))
                                        .build(),
                                ContractDto.AddonDto.builder()
                                        .contractId("BC_90000000003")
                                        .productId("DC_12345678A200")
                                        .startDate(OffsetDateTime.parse("2024-09-01T02:00:00+02:00"))
                                        .contractType(ContractType.CONSUMPTION)
                                        .contractPeriod(Period.ofMonths(1))
                                        .noticePeriod(Period.ofDays(7))
                                        .build()
                        ))
                        .build())
                .build();

        contractEventSubscriber.onUpdateEvent(payload);

        BcCentralMockExtension.get()
                .verify(postRequestedFor(urlPathEqualTo("/data-recorder-service/v2/pog8306"))
                        .withHeader("X-Metadata", WireMock.containing("boss_contract_update")));
    }
}
