package com.sast.store.entitlementmanagement.service;

import org.junit.jupiter.api.Test;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

class BCCentralCancellationTaskTest {

    @Test
    void processJobForDate_whenDaysRemainingIs3_callsProcessLicenseCancellations() {
        final BcCentralLicenseCancellationService mockCancellationService = mock(BcCentralLicenseCancellationService.class);
        final BCCentralCancellationTask task = new BCCentralCancellationTask(mockCancellationService);

        task.processBCCentralCancellationsJob();

        verify(mockCancellationService, times(1)).processLicenseCancellations();
    }

}