package com.sast.store.entitlementmanagement.service;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.contractmanagement.api.ContractDto;
import com.sast.store.entitlementmanagement.AbstractComponentTest;
import com.sast.store.entitlementmanagement.util.CommercetoolsMockExtension;
import com.sast.store.external.ump.test.UmpMockExtension;
import com.sast.store.productmanagement.test.ProductServiceMockExtension;
import jakarta.inject.Inject;
import org.junit.jupiter.api.Test;
import org.springframework.cache.CacheManager;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching;
import static org.assertj.core.api.Assertions.assertThat;

class BcCentralLicenseServiceTest extends AbstractComponentTest {

    @Inject
    private BcCentralLicenseService bcCentralLicenseService;

    @Inject
    private CacheManager cacheManager;

    @Test
    void getDefaults_forUnknown_returnsNoDefaults() {
        final var contractDto = ContractDto.builder()
            .companyId("doesNotExist")
            .orderNumber("doesNotExist")
            .build();

        final var result = bcCentralLicenseService.getDefaults(Tenant.REXROTH, contractDto);

        assertThat(result).isNotPresent();
    }

    @Test
    void getDefaults_forContract_returnsDefaults() {
        CommercetoolsMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withDefaultResponse();
        UmpMockExtension.withDefaultResponse();

        final var contractDto = ContractDto.builder()
            .companyId("********-7c7e-4471-8391-4962e7bbe537")
            .orderNumber("123456")
            .build();

        final var result = bcCentralLicenseService.getDefaults(Tenant.REXROTH, contractDto);

        assertThat(result)
            .isPresent()
            .hasValueSatisfying(dto -> {
                assertThat(dto.name())
                    .isEqualTo("UPM Tester");
                assertThat(dto.emails())
                    .containsExactly("<EMAIL>");
            });
    }

    @Test
    void getDefaults_forWeirdCompanyName_returnsStippedCompanyName() {
        CommercetoolsMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withDefaultResponse();
        UmpMockExtension.withDefaultResponse();

        // Need to clear the cache to ensure the mocked response below will be taken into account
        cacheManager.getCache("companyDetails").clear();

        UmpMockExtension.instance()
            .stubFor(get(urlPathMatching("/internal/companies/[^/]*[/]*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody(
                    """
                        {
                            "companyName": "U%M‑Te$ter 47",
                            "companyId": "********-7c7e-4471-8391-4962e7bbe537",
                            "companyTypes": [
                                "INTEGRATOR"
                            ],
                            "taxId": "ATU73208478",
                            "website": null,
                            "creditLimit": 5000.0,
                            "friendlyName": null,
                            "phoneNumber": null,
                            "appPurchaseEmail": null,
                            "appSalesEmail": null,
                            "costCenter": null,
                            "companyStatus": "APPROVED_COMMERCIAL",
                            "bpmdId": "4900240656",
                            "externalCustomerId": "89003",
                            "billingAddress": {
                                "city": "Dienten am Hochkönig",
                                "postalCode": "5652",
                                "street": "Berg",
                                "houseNumber": "18",
                                "state": null,
                                "region": null
                            },
                            "businessAddress": {
                                "city": "Dienten am Hochkönig",
                                "postalCode": "5652",
                                "street": "Berg",
                                "houseNumber": "18",
                                "state": null,
                                "region": null
                            },
                            "distributor": null,
                            "communicationLanguage": "de",
                            "licensingEmail": "<EMAIL>",
                            "customerGroup": {
                                "customizationId": "IDW000",
                                "id": "INDEPENDENT_WORKSHOP"
                            },
                            "companyCountry": "AT",
                            "isBankTransferEnabled": true,
                            "isManualAppApprovalEnabled": true,
                            "isOwnAppsPurchaseEnabled": false,
                            "isSubCompanyManagementEnabled": true,
                            "isInternal": false,
                            "isManaged": false,
                            "isDirect": true,
                            "companyEmail": "<EMAIL>"
                        }
                        """)));

        final var contractDto = ContractDto.builder()
            .companyId("********-7c7e-4471-8391-4962e7bbe537")
            .orderNumber("123456")
            .build();

        final var result = bcCentralLicenseService.getDefaults(Tenant.REXROTH, contractDto);

        assertThat(result)
            .isPresent()
            .hasValueSatisfying(dto -> {
                assertThat(dto.name())
                    .isEqualTo("UMTeter 47");
            });
    }
}
