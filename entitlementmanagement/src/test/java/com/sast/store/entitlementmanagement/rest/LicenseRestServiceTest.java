package com.sast.store.entitlementmanagement.rest;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.contractmanagement.api.ContractEvent;
import com.sast.store.contractmanagement.api.ContractType;
import com.sast.store.contractmanagement.test.ContractServiceMockExtension;
import com.sast.store.entitlementmanagement.AbstractComponentTest;
import com.sast.store.entitlementmanagement.activities.SendReminderEmailActivities;
import com.sast.store.entitlementmanagement.api.LicenseModel;
import com.sast.store.entitlementmanagement.dao.BcCentralLicenseEntity;
import com.sast.store.entitlementmanagement.dao.DcKeycloakLicenseEntity;
import com.sast.store.entitlementmanagement.dao.DcKeycloakLicenseType;
import com.sast.store.entitlementmanagement.dao.HydraulichubFreemiumUsersDao;
import com.sast.store.entitlementmanagement.dao.LicenseDao;
import com.sast.store.entitlementmanagement.dao.LicenseEntity;
import com.sast.store.entitlementmanagement.dao.LitmosLicenseEntity;
import com.sast.store.entitlementmanagement.dao.LitmosTeamDao;
import com.sast.store.entitlementmanagement.dao.LitmosTeamEntity;
import com.sast.store.entitlementmanagement.util.ContractmanagementTestDataGenerator;
import com.sast.store.entitlementmanagement.workflows.SendReminderEmailWorkflow;
import com.sast.store.external.bccentral.BcCentralMockExtension;
import com.sast.store.external.countriesservice.util.CountriesServiceMockExtension;
import com.sast.store.external.dckeycloak.test.DcKeycloakMockExtension;
import com.sast.store.external.litmos.test.LitmosMockExtension;
import com.sast.store.external.ump.test.UmpMockExtension;
import com.sast.store.productmanagement.test.ProductServiceMockExtension;
import com.sast.store.testing.awsmockup.junit.EmailUtil;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import io.temporal.client.WorkflowClient;
import io.temporal.client.WorkflowOptions;
import io.temporal.testing.TestWorkflowEnvironment;
import io.temporal.worker.Worker;
import jakarta.inject.Inject;
import org.hamcrest.Matchers;
import org.json.JSONException;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.springframework.boot.test.json.BasicJsonTester;
import software.amazon.awssdk.services.sqs.model.Message;

import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

import static com.github.tomakehurst.wiremock.client.WireMock.absent;
import static com.github.tomakehurst.wiremock.client.WireMock.matchingJsonPath;
import static com.github.tomakehurst.wiremock.client.WireMock.not;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.within;
import static org.assertj.core.api.InstanceOfAssertFactories.type;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.withSettings;

class LicenseRestServiceTest extends AbstractComponentTest {

    @Inject
    private ContractmanagementTestDataGenerator testDataGenerator;

    @Inject
    private LicenseDao licenseDao;

    @Inject
    private HydraulichubFreemiumUsersDao freemiumUsersDao;

    @Inject
    private LitmosTeamDao litmosTeamDao;

    @Inject
    private WorkflowClient workflowClient;


    @Test
    void testGetLicensesEmptyDatabase() {
        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .header("X-Tenant", "rexroth")
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .when().get(host + "/rest/licenses")
            .then().statusCode(200)
            .body(".", Matchers.hasSize(0));
    }

    @Test
    void testGetLicenses() {
        testDataGenerator
            .withLitmosLicense().havingValue(e -> e.withCompanyId(Tenant.REXROTH, COMPANY_ID));
        final LitmosLicenseEntity license = testDataGenerator.getLitmostLicense();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .header("X-Tenant", "rexroth")
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .when().get(host + "/rest/licenses")
            .then().statusCode(200)
            .body(".", Matchers.hasSize(1))
            .body("[0].firstname", Matchers.equalTo(license.getFirstname()))
            .body("[0].lastname", Matchers.equalTo(license.getLastname()))
            .body("[0].email", Matchers.equalTo(license.getEmail()))
            .body("[0].contractId", Matchers.equalTo(license.getContractId()))
            .body("[0].licenseId", Matchers.equalTo(license.getLicenseId()));
    }

    @Test
    void testGetDefaultsForNoContractIds() {
        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .header("X-Tenant", "rexroth")
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .when()
            .get(host + "/rest/licenses/defaults")
            .then()
            .statusCode(200)
            .body(".", Matchers.hasSize(0));
    }

    @Test
    void testGetDefaultsForNonExistingContractId() {
        ContractServiceMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .header("X-Tenant", "rexroth")
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .when()
            .get(host + "/rest/licenses/defaults?contractIds=doesnotexist")
            .then()
            .statusCode(200)
            .body(".", Matchers.hasSize(0));
    }

    @Test
    void testGetDefaultsForExistingContractIdWithoutDefaults() {
        ContractServiceMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .header("X-Tenant", "rexroth")
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .when()
            .get(host + "/rest/licenses/defaults?contractIds=yedUsFwdkelQbxeTeQOvaScfqIOOmaa")
            .then()
            .statusCode(200)
            .body(".", Matchers.hasSize(0));
    }

    @Test
    void testAssignContractNotFound() {
        ContractServiceMockExtension.withGetContractNotFoundResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .body("""
                [{
                 "firstname": "firstname",
                 "lastname": "lastname",
                 "email": "email",
                 "contractId": "{contractid}"
                }]""".replace("{contractid}", "wrongContractId"))
            .header("X-Tenant", "rexroth")
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .when().post(host + "/rest/licenses")
            .then().statusCode(404);

        assertThat(licenseDao.findAll().toList()).isEmpty();
    }

    @Test
    void testAssignContractMissingLicenseModel() {
        ContractServiceMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .body("""
                [{
                 "contractId": "contractId"
                }]""")
            .header("X-Tenant", "rexroth")
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .when().post(host + "/rest/licenses")
            .then().statusCode(400);

        assertThat(licenseDao.findAll().toList()).isEmpty();
    }

    @Test
    void testAssignContractMissingData() {
        ContractServiceMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .body("""
                [{
                 "licenseModel": "BCCENTRAL"
                }]""")
            .header("X-Tenant", "rexroth")
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .when().post(host + "/rest/licenses")
            .then().statusCode(400);

        assertThat(licenseDao.findAll().toList()).isEmpty();
    }

    @Test
    void testAssignContractUser() throws JSONException {
        LitmosMockExtension.withDefaultResponse();
        DcKeycloakMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withDefaultResponse();
        UmpMockExtension.withDefaultResponse();
        ContractServiceMockExtension
            .withGetContractResponse(b -> b
                .companyId(COMPANY_ID)
                .contractId("contractId")
                .contractType(ContractType.SUBSCRIPTION));

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .body("""
                [{
                 "firstname": "firstname",
                 "lastname": "lastname",
                 "email": "email",
                 "contractId": "{contractid}"
                }]""".replace("{contractid}", "contractId"))
            .header("X-Tenant", "rexroth")
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .when().post(host + "/rest/licenses")
            .then().statusCode(204);

        assertThat(licenseDao.findAll().toList()).hasSize(2).anySatisfy(l -> {
            assertThat(l).asInstanceOf(type(DcKeycloakLicenseEntity.class)).satisfies(dc -> {
                assertThat(dc.getFirstname()).isEqualTo("firstname");
                assertThat(dc.getLastname()).isEqualTo("lastname");
                assertThat(dc.getEmail()).isEqualTo("email");
                assertThat(dc.getContractId()).isEqualTo("contractId");
            });
        });

        DcKeycloakMockExtension.instance().verify(1,
            WireMock.postRequestedFor(WireMock.urlEqualTo(
                "/admin/realms/realm/users/d4e22f66-a386-40e2-9e17-b4a5add5dac0"
                    + "/role-mappings/clients/e58a68d8-cab6-4516-b51a-dc2fd05a1107"))
                .withRequestBody(WireMock.equalToJson("""
                    [
                        {
                            "id": "bd3d709c-028b-4d94-b065-48f53718f85e",
                            "name": "role1",
                            "description": "",
                            "scopeParamRequired": null,
                            "composite": false,
                            "composites": null,
                            "clientRole": true,
                            "containerId": "b16d2c3d-7e41-4b15-9023-85273d93771d",
                            "attributes": null
                        },
                        {
                            "id": "630d9cd2-762d-43c5-9c7a-81086261fc0e",
                            "name": "role2",
                            "description": "",
                            "scopeParamRequired": null,
                            "composite": false,
                            "composites": null,
                            "clientRole": true,
                            "containerId": "b16d2c3d-7e41-4b15-9023-85273d93771d",
                            "attributes": null
                        }
                    ]""")));
        LitmosMockExtension.instance().verify(1,
            WireMock.postRequestedFor(WireMock.urlPathEqualTo("/v1.svc/teams/fNDDifPRKkM1/users"))
                .withRequestBody(WireMock.equalToJson("""
                    [{
                        "Id": "WZVU5BFm2hI1"
                    }]""")));

        LitmosMockExtension.instance().verify(1,
            WireMock.postRequestedFor(WireMock.urlPathEqualTo("/v1.svc/teams/02_Rexroth_Partner/teams"))
                .withRequestBody(WireMock.equalToJson("""
                    {
                        "Name": "UPM Tester",
                        "Description": "Managed by Boss Marketplace. Do not change!",
                        "ParentTeamId" : "02_Rexroth_Partner",
                        "CopyParentCourses" : true,
                        "CopyParentCourseLibraries" : true,
                        "CopyParentLearningpathLibraries" : true
                    }""")));

        assertThat(litmosTeamDao.findAll().toList()).hasSize(1).first().satisfies(l -> {
            assertThat(l.getCompanyId()).isEqualTo(COMPANY_ID);
            assertThat(l.getTenant()).isEqualTo(Tenant.REXROTH);
            assertThat(l.getTeamId()).isEqualTo("fNDDifPRKkM1");
        });

        final List<Message> messages = EmailUtil.awaitEmails(1);

        JSONAssert.assertEquals("""
            {
              "to": [
                "email"
              ],
              "tenant": "rexroth",
              "properties": {
                "firstName": "firstname",
                "lastName": "lastname",
                "changedByFirstName": "myFirstname",
                "changedByLastName": "myLastname",
                "assignedUserEmail": "email",
                "hydraulicHubUrl": "https://develop.hydraulic-hub.boschrexroth.com/login"
              },
              "locale": "de",
              "templateName": "rexroth/entitlements/hydraulichubAssignment"
            }
            """, messages.getFirst().body(), JSONCompareMode.LENIENT);
    }

    @Test
    void testAssignUserLitmosTeamAlreayExists() throws JSONException {
        LitmosMockExtension.withDefaultResponse();
        DcKeycloakMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withDefaultResponse();
        UmpMockExtension.withDefaultResponse();
        ContractServiceMockExtension
            .withGetContractResponse(b -> b
                .companyId(COMPANY_ID)
                .contractId("contractId")
                .contractType(ContractType.SUBSCRIPTION));
        litmosTeamDao.save(new LitmosTeamEntity().withCompanyId(Tenant.REXROTH, COMPANY_ID).withTeamId("fNDDifPRKkM1"));

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .body("""
                [{
                 "firstname": "firstname",
                 "lastname": "lastname",
                 "email": "email",
                 "contractId": "{contractid}"
                }]""".replace("{contractid}", "contractId"))
            .header("X-Tenant", "rexroth")
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .when().post(host + "/rest/licenses")
            .then().statusCode(204);

        LitmosMockExtension.instance().verify(1,
            WireMock.postRequestedFor(WireMock.urlPathEqualTo("/v1.svc/teams/fNDDifPRKkM1/users"))
                .withRequestBody(WireMock.equalToJson("""
                    [{
                        "Id": "WZVU5BFm2hI1"
                    }]""")));

        LitmosMockExtension.instance().verify(0,
            WireMock.postRequestedFor(WireMock.urlPathEqualTo("/v1.svc/teams")));

        assertThat(EmailUtil.awaitEmails(1)).hasSize(1);
    }

    @Test
    void testAssignUserLitmosTeamCreatedConcurrentlyExists() throws JSONException {
        LitmosMockExtension.withDefaultResponse();
        DcKeycloakMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withDefaultResponse();
        UmpMockExtension.withDefaultResponse();
        ContractServiceMockExtension
            .withGetContractResponse(b -> b
                .companyId(COMPANY_ID)
                .contractId("contractId")
                .contractType(ContractType.SUBSCRIPTION));
        litmosTeamDao.save(new LitmosTeamEntity().withCompanyId(Tenant.REXROTH, COMPANY_ID).withTeamId("fNDDifPRKkM1"));

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .body("""
                [{
                 "firstname": "firstname",
                 "lastname": "lastname",
                 "email": "email",
                 "contractId": "{contractid}"
                }]""".replace("{contractid}", "contractId"))
            .header("X-Tenant", "rexroth")
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .when().post(host + "/rest/licenses")
            .then().statusCode(204);

        LitmosMockExtension.instance().verify(1,
            WireMock.postRequestedFor(WireMock.urlPathEqualTo("/v1.svc/teams/fNDDifPRKkM1/users"))
                .withRequestBody(WireMock.equalToJson("""
                    [{
                        "Id": "WZVU5BFm2hI1"
                    }]""")));

        LitmosMockExtension.instance().verify(0,
            WireMock.postRequestedFor(WireMock.urlPathEqualTo("/v1.svc/teams")));

        assertThat(EmailUtil.awaitEmails(1)).hasSize(1);
    }

    @Test
    void testAssignContractUserFreemium() throws JSONException {
        LitmosMockExtension.withDefaultResponse();
        DcKeycloakMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withGetVariantResponse("DCKEYCLOAK:FREEMIUM");
        UmpMockExtension.withDefaultResponse();
        ContractServiceMockExtension
            .withGetContractResponse(b -> b
                .companyId(COMPANY_ID)
                .contractId("contractId")
                .contractType(ContractType.SUBSCRIPTION));

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .body("""
                [{
                 "firstname": "firstname",
                 "lastname": "lastname",
                 "email": "email",
                 "contractId": "{contractid}"
                }]""".replace("{contractid}", "contractId"))
            .header("X-Tenant", "rexroth")
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .when().post(host + "/rest/licenses")
            .then().statusCode(204);

        assertThat(licenseDao.findAll().toList()).hasSize(1)
            .anySatisfy(l -> {
                assertThat(l).asInstanceOf(type(DcKeycloakLicenseEntity.class)).satisfies(dc -> {
                    assertThat(dc.getCompany()).isEqualTo(COMPANY_ID);
                    assertThat(dc.getTenant()).isEqualTo(Tenant.REXROTH);
                    assertThat(dc.getFirstname()).isEqualTo("firstname");
                    assertThat(dc.getLastname()).isEqualTo("lastname");
                    assertThat(dc.getEmail()).isEqualTo("email");
                    assertThat(dc.getContractId()).isEqualTo("contractId");
                    assertThat(dc.getLicenseType()).isEqualTo(DcKeycloakLicenseType.FREEMIUM);
                    assertThat(dc.getFreemiumEndDate()).isCloseTo(ZonedDateTime.now().plusDays(30).toInstant(),
                        within(1, ChronoUnit.MINUTES));
                });
            });

        // premium roles are assigned
        DcKeycloakMockExtension.instance().verify(1,
            WireMock.postRequestedFor(WireMock.urlEqualTo(
                "/admin/realms/realm/users/d4e22f66-a386-40e2-9e17-b4a5add5dac0"
                    + "/role-mappings/clients/e58a68d8-cab6-4516-b51a-dc2fd05a1107"))
                .withRequestBody(WireMock.matchingJsonPath("$..name", WireMock.containing("role1"))));
        DcKeycloakMockExtension.instance().verify(1,
            WireMock.postRequestedFor(WireMock.urlEqualTo(
                "/admin/realms/realm/users/d4e22f66-a386-40e2-9e17-b4a5add5dac0"
                    + "/role-mappings/clients/e58a68d8-cab6-4516-b51a-dc2fd05a1107"))
                .withRequestBody(WireMock.matchingJsonPath("$..name", WireMock.containing("role2"))));

        // freemium roles are assigned
        DcKeycloakMockExtension.instance().verify(1,
            WireMock.postRequestedFor(WireMock.urlEqualTo(
                "/admin/realms/realm/users/d4e22f66-a386-40e2-9e17-b4a5add5dac0"
                    + "/role-mappings/clients/e58a68d8-cab6-4516-b51a-dc2fd05a1107"))
                .withRequestBody(WireMock.matchingJsonPath("$..name", WireMock.containing("role3"))));

        final List<Message> messages = EmailUtil.awaitEmails(1);
        assertThat(messages).hasSize(1);
    }

    @Test
    void testAssignMultipleLicenses() {
        LitmosMockExtension.withDefaultResponse();
        DcKeycloakMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withDefaultResponse();
        UmpMockExtension.withDefaultResponse();
        ContractServiceMockExtension.withGetContractResponse(
            b -> b
                .companyId(COMPANY_ID)
                .contractId("contractId1")
                .contractType(ContractType.SUBSCRIPTION),
            b -> b
                .companyId(COMPANY_ID)
                .contractId("contractId2")
                .contractType(ContractType.SUBSCRIPTION));

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .body("""
                [
                  {
                    "firstname": "firstname",
                    "lastname": "lastname",
                    "email": "email",
                    "contractId": "contractId1"
                  },
                  {
                    "firstname": "firstname",
                    "lastname": "lastname",
                    "email": "email",
                    "contractId": "contractId2"
                  }
                ]""")
            .header("X-Tenant", "rexroth")
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .when().post(host + "/rest/licenses")
            .then().statusCode(204);

        assertThat(licenseDao.findAll().toList())
            .hasSize(4)
            .map(LicenseEntity::getContractId)
            .containsExactlyInAnyOrder("contractId1", "contractId1", "contractId2", "contractId2");
    }

    @Test
    void testUnassignDcKeycloakLicense() {
        LitmosMockExtension.withDefaultResponse();
        DcKeycloakMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withDefaultResponse();
        UmpMockExtension.withDefaultResponse();
        testDataGenerator
            .withDcKeycloakLicense().havingValue(e -> e
                .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                .withLicenseType(DcKeycloakLicenseType.PREMIUM)
                .withContractId("contractId"));
        ContractServiceMockExtension
            .withGetContractResponse(b -> b
                .companyId(COMPANY_ID)
                .contractId("contractId")
                .contractType(ContractType.SUBSCRIPTION));
        final DcKeycloakLicenseEntity license = testDataGenerator.getDcKeycloakLicense();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .header("X-Tenant", "rexroth")
            .accept(ContentType.JSON)
            .when().delete(host + "/rest/licenses/{licenseId}", license.getLicenseId())
            .then().statusCode(204);

        assertThat(licenseDao.findAll().toList()).isEmpty();

        DcKeycloakMockExtension.instance().verify(1,
            WireMock.deleteRequestedFor(WireMock.urlEqualTo(
                "/admin/realms/realm/users/d4e22f66-a386-40e2-9e17-b4a5add5dac0"
                    + "/role-mappings/clients/e58a68d8-cab6-4516-b51a-dc2fd05a1107"))
                .withRequestBody(WireMock.equalToJson("""
                    [
                        {
                            "id": "bd3d709c-028b-4d94-b065-48f53718f85e",
                            "name": "role1",
                            "description": "",
                            "scopeParamRequired": null,
                            "composite": false,
                            "composites": null,
                            "clientRole": true,
                            "containerId": "b16d2c3d-7e41-4b15-9023-85273d93771d",
                            "attributes": null
                        },
                        {
                            "id": "630d9cd2-762d-43c5-9c7a-81086261fc0e",
                            "name": "role2",
                            "description": "",
                            "scopeParamRequired": null,
                            "composite": false,
                            "composites": null,
                            "clientRole": true,
                            "containerId": "b16d2c3d-7e41-4b15-9023-85273d93771d",
                            "attributes": null
                        }
                    ]""")));

        final List<Message> messages = EmailUtil.awaitEmails(1);

        assertThat(messages).hasSize(1).first().extracting(Message::body).extracting(new BasicJsonTester(getClass())::from)
            .satisfies(m -> {
                assertThat(m).extractingJsonPathStringValue("tenant").isEqualTo("rexroth");
                assertThat(m).extractingJsonPathStringValue("locale").isEqualTo("de");
                assertThat(m).extractingJsonPathStringValue("to[0]").isEqualTo(testDataGenerator.getDcKeycloakLicense().getEmail());
                assertThat(m).extractingJsonPathStringValue("templateName").isEqualTo("rexroth/entitlements/unassignment");
                assertThat(m).extractingJsonPathStringValue("properties.firstName")
                    .isEqualTo(testDataGenerator.getDcKeycloakLicense().getFirstname());
                assertThat(m).extractingJsonPathStringValue("properties.lastName")
                    .isEqualTo(testDataGenerator.getDcKeycloakLicense().getLastname());
                assertThat(m).extractingJsonPathStringValue("properties.changedByFirstName").isEqualTo("myFirstname");
                assertThat(m).extractingJsonPathStringValue("properties.changedByLastName").isEqualTo("myLastname");
                assertThat(m).extractingJsonPathStringValue("properties.productName").isEqualTo("Hydraulic Hub");
            });
    }

    @Test
    void testUnassignLitmosLicense() {
        LitmosMockExtension.withDefaultResponse();
        DcKeycloakMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withDefaultResponse();
        UmpMockExtension.withDefaultResponse();
        testDataGenerator
            .withLitmosLicense().havingValue(e -> e
                .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                .withContractId("contractId"));
        ContractServiceMockExtension
            .withGetContractResponse(b -> b
                .companyId(COMPANY_ID)
                .contractId("contractId")
                .contractType(ContractType.SUBSCRIPTION));

        final LitmosLicenseEntity license = testDataGenerator.getLitmostLicense();
        litmosTeamDao.save(new LitmosTeamEntity().withCompanyId(Tenant.REXROTH, COMPANY_ID).withTeamId("fNDDifPRKkM1"));
        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .header("X-Tenant", "rexroth")
            .accept(ContentType.JSON)
            .when().delete(host + "/rest/licenses/{licenseId}", license.getLicenseId())
            .then().statusCode(204);

        assertThat(licenseDao.findAll().toList()).isEmpty();

        LitmosMockExtension.instance().verify(1,
            WireMock.deleteRequestedFor(WireMock.urlPathEqualTo("/v1.svc/teams/fNDDifPRKkM1/users/WZVU5BFm2hI1")));
    }

    @Test
    void testUnassignLitmosLicenseWithUnknownUserThrows() {
        LitmosMockExtension.withDefaultResponse();
        DcKeycloakMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withDefaultResponse();
        UmpMockExtension.withDefaultResponse();
        testDataGenerator
                .withLitmosLicense().havingValue(e -> e
                        .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                        .withEmail("<EMAIL>")
                        .withContractId("contractId"));
        ContractServiceMockExtension
                .withGetContractResponse(b -> b
                        .companyId(COMPANY_ID)
                        .contractId("contractId")
                        .contractType(ContractType.SUBSCRIPTION));

        LitmosMockExtension.withSearchUsersEmptyResponse();

        final LitmosLicenseEntity license = testDataGenerator.getLitmostLicense();
        litmosTeamDao.save(new LitmosTeamEntity().withCompanyId(Tenant.REXROTH, COMPANY_ID).withTeamId("fNDDifPRKkM1"));
        RestAssured
                .given(VALID_KEYCLOAK_TOKEN)
                .header("X-Tenant", "rexroth")
                .accept(ContentType.JSON)
                .when().delete(host + "/rest/licenses/{licenseId}", license.getLicenseId())
                .then().statusCode(503);

        assertThat(licenseDao.findAll().toList()).isNotEmpty();

        LitmosMockExtension.instance().verify(0,
                WireMock.deleteRequestedFor(WireMock.urlPathEqualTo("/v1.svc/teams/fNDDifPRKkM1/users/WZVU5BFm2hI1")));
    }

    @Test
    void testUnassignLitmosLicenseWithUnknownTeamThrows() {
        LitmosMockExtension.withDefaultResponse();
        DcKeycloakMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withDefaultResponse();
        UmpMockExtension.withDefaultResponse();
        testDataGenerator
                .withLitmosLicense().havingValue(e -> e
                        .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                        .withEmail("<EMAIL>")
                        .withContractId("contractId"));
        ContractServiceMockExtension
                .withGetContractResponse(b -> b
                        .companyId(COMPANY_ID)
                        .contractId("contractId")
                        .contractType(ContractType.SUBSCRIPTION));

        final LitmosLicenseEntity license = testDataGenerator.getLitmostLicense();
        RestAssured
                .given(VALID_KEYCLOAK_TOKEN)
                .header("X-Tenant", "rexroth")
                .accept(ContentType.JSON)
                .when().delete(host + "/rest/licenses/{licenseId}", license.getLicenseId())
                .then().statusCode(503);

        assertThat(licenseDao.findAll().toList()).isNotEmpty();

        LitmosMockExtension.instance().verify(0,
                WireMock.deleteRequestedFor(WireMock.urlPathEqualTo("/v1.svc/teams/fNDDifPRKkM1/users/WZVU5BFm2hI1")));
    }

    @Test
    void testAssignUserFreemiumTwice() {
        LitmosMockExtension.withDefaultResponse();
        DcKeycloakMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withGetVariantResponse("DCKEYCLOAK:FREEMIUM");
        UmpMockExtension.withDefaultResponse();
        ContractServiceMockExtension
            .withGetContractResponse(b -> b
                .companyId(COMPANY_ID)
                .contractId("contractId")
                .contractType(ContractType.SUBSCRIPTION));

        // first assignment
        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .body("""
                [{
                 "firstname": "firstname",
                 "lastname": "lastname",
                 "email": "email",
                 "contractId": "{contractid}"
                }]""".replace("{contractid}", "contractId"))
            .header("X-Tenant", "rexroth")
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .when().post(host + "/rest/licenses")
            .then().statusCode(204);

        // unassignment
        licenseDao.findAll().toList().forEach(l -> {
            RestAssured
                .given(VALID_KEYCLOAK_TOKEN)
                .header("X-Tenant", "rexroth")
                .accept(ContentType.JSON)
                .when().delete(host + "/rest/licenses/{licenseId}", l.getLicenseId())
                .then().statusCode(204);
        });

        // second assignment
        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .body("""
                [{
                 "firstname": "firstname",
                 "lastname": "lastname",
                 "email": "email",
                 "contractId": "{contractid}"
                }]""".replace("{contractid}", "contractId"))
            .header("X-Tenant", "rexroth")
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .when().post(host + "/rest/licenses")
            .then().statusCode(204);

        // now we only have one freemium license
        assertThat(licenseDao.findAll().toList()).hasSize(1).allSatisfy(l -> {
            assertThat(l).asInstanceOf(type(DcKeycloakLicenseEntity.class)).satisfies(dc -> {
                assertThat(dc.getLicenseType()).isEqualTo(DcKeycloakLicenseType.FREEMIUM);
                assertThat(dc.getFreemiumEndDate()).isNotNull();
            });
        });

        assertThat(freemiumUsersDao.findAll().toList()).hasSize(1).allSatisfy(f -> {
            assertThat(f.getEmail()).isEqualTo("email");
        });

        // premium roles are assigned only once
        DcKeycloakMockExtension.instance().verify(1,
            WireMock.postRequestedFor(WireMock.urlEqualTo(
                "/admin/realms/realm/users/d4e22f66-a386-40e2-9e17-b4a5add5dac0"
                    + "/role-mappings/clients/e58a68d8-cab6-4516-b51a-dc2fd05a1107"))
                .withRequestBody(WireMock.matchingJsonPath("$..name", WireMock.containing("role1"))));
        DcKeycloakMockExtension.instance().verify(1,
            WireMock.postRequestedFor(WireMock.urlEqualTo(
                "/admin/realms/realm/users/d4e22f66-a386-40e2-9e17-b4a5add5dac0"
                    + "/role-mappings/clients/e58a68d8-cab6-4516-b51a-dc2fd05a1107"))
                .withRequestBody(WireMock.matchingJsonPath("$..name", WireMock.containing("role2"))));

        // freemium roles are assigned twice
        DcKeycloakMockExtension.instance().verify(2,
            WireMock.postRequestedFor(WireMock.urlEqualTo(
                "/admin/realms/realm/users/d4e22f66-a386-40e2-9e17-b4a5add5dac0"
                    + "/role-mappings/clients/e58a68d8-cab6-4516-b51a-dc2fd05a1107"))
                .withRequestBody(WireMock.matchingJsonPath("$..name", WireMock.containing("role3"))));
    }

    @Test
    void testAssignContractBccentralMissingDetails() {
        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .body("""
                [{
                 "licenseModel": "BCCENTRAL",
                 "bcCentral": {
                     "contractId": "contractid"
                 }
                }]""")
            .header("X-Tenant", "rexroth")
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .when().post(host + "/rest/licenses")
            .then().statusCode(400);
    }

    @Test
    void testAssignContractBccentral(TestWorkflowEnvironment testWorkflowEnvironment, Worker worker) {

        createAndStartWorkflow(testWorkflowEnvironment, worker);

        CountriesServiceMockExtension.withDefaultResponse();
        BcCentralMockExtension
            .withDefaultResponse()
            .withGetContractNotFoundResponse();
        ProductServiceMockExtension.withGetVariantResponse("BCCENTRAL");
        UmpMockExtension.withDefaultResponse();
        ContractServiceMockExtension
            .withGetContractResponse(b -> b
                .companyId(COMPANY_ID)
                .contractId("contractId")
                .contractType(ContractType.SUBSCRIPTION));

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .body("""
                [{
                 "licenseModel": "BCCENTRAL",
                 "bcCentral": {
                     "contractId": "contractId",
                     "name": "mytenantname",
                     "emails": ["<EMAIL>"]
                 }
                }]""")
            .header("X-Tenant", "rexroth")
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .when().post(host + "/rest/licenses")
            .then().statusCode(204);

        assertThat(licenseDao.findAll().toList()).hasSize(1).allSatisfy(l -> {
            assertThat(l).asInstanceOf(type(BcCentralLicenseEntity.class)).satisfies(dc -> {
                assertThat(dc.getBccentralContractId()).isNotBlank();
                assertThat(dc.getBccentralTenantName()).isEqualTo("mytenantname");
                assertThat(dc.getOwnerEmails()).isEqualTo(List.of("<EMAIL>"));
                assertThat(dc.getContractId()).isEqualTo("contractId");
                assertThat(dc.getLicenseModel()).isEqualTo(LicenseModel.BCCENTRAL);
            });
        });

        BcCentralMockExtension.get().verify(1,
            WireMock.postRequestedFor(WireMock.urlPathEqualTo("/data-recorder-service/v2/pog8306"))
                .withRequestBody(matchingJsonPath("$.contractId", not(absent())))
                .withRequestBody(matchingJsonPath("$.customer.id", WireMock.equalTo("**********")))
                .withRequestBody(matchingJsonPath("$.customer.currency", WireMock.equalTo("EUR")))
                .withRequestBody(matchingJsonPath("$.customer.name", WireMock.equalTo("UPM Tester")))
                .withRequestBody(matchingJsonPath("$.tenant.name", WireMock.equalTo("mytenantname")))
                .withRequestBody(matchingJsonPath("$.tenant.owner[0]", WireMock.equalTo("<EMAIL>")))
                .withRequestBody(matchingJsonPath("$.booking[0].licenseKey", WireMock.equalTo("contractId")))
                .withRequestBody(matchingJsonPath("$.booking[0].materialNumber", WireMock.equalTo("R917014006"))));
    }

    private void createAndStartWorkflow(TestWorkflowEnvironment testWorkflowEnvironment, Worker worker) {
        // so we need to create a workflow, so it can be canceled
        SendReminderEmailActivities mockedActitivies =
                mock(SendReminderEmailActivities.class, withSettings().withoutAnnotations());

        worker.registerActivitiesImplementations(mockedActitivies);
        testWorkflowEnvironment.start();

        WorkflowOptions workflowOptions = WorkflowOptions.newBuilder()
                .setWorkflowId("contractId")
                .setTaskQueue("test")
                .build();
        final SendReminderEmailWorkflow workflow =
                workflowClient.newWorkflowStub(SendReminderEmailWorkflow.class, workflowOptions);
        WorkflowClient.start(workflow::run, ContractEvent.builder().build());
    }
}
