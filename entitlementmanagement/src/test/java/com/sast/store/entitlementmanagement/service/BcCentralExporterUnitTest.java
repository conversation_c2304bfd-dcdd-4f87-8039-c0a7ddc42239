package com.sast.store.entitlementmanagement.service;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.contractmanagement.api.ContractDto;
import com.sast.store.entitlementmanagement.config.AppConfiguration;
import com.sast.store.entitlementmanagement.dao.BcCentralLicenseEntity;
import com.sast.store.external.bccentral.BcCentralClient;
import com.sast.store.external.bccentral.api.ContractStatus;
import com.sast.store.external.bccentral.api.CreateBooking;
import com.sast.store.external.bccentral.api.CreateContractRequest;
import com.sast.store.external.bccentral.api.Customer;
import com.sast.store.external.bccentral.api.GetContractResponse;
import com.sast.store.external.bccentral.api.UpdateBooking;
import com.sast.store.external.bccentral.api.UpdateContractRequest;
import com.sast.store.external.countriesservice.api.CountriesServiceClient;
import com.sast.store.external.countriesservice.api.CountryDto;
import com.sast.store.external.ump.LenientUmpClient;
import com.sast.store.gen.client.ump.api.UmpExternalCompanyDto;
import com.sast.store.gen.client.ump.api.UmpUserDto;
import com.sast.store.productmanagement.api.ProductApi;
import com.sast.store.productmanagement.api.ProductDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.OffsetDateTime;
import java.util.Currency;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
class BcCentralExporterUnitTest {
    private static final String BC_CENTRAL_ID = "asdjkhaksjd89uz";
    private static final String COMPANY_ID = "d25ebf9b-55d1-44d2-a72d-35243effd9bf";
    private static final String MAIN_PRODUCT_ID = "LRX_PC_10000M_DE";
    private static final String MAIN_MATERIAL_NUMBER = "R1234100001";
    private static final String ADDON_PRODUCT_ID = "LRX_PC_20000A_DE";
    private static final String ADDON_MATERIAL_NUMBER = "R1234200001";
    private static final String OTHER_ADDON_PRODUCT_ID = "LRX_PC_21000A_DE";
    private static final String OTHER_ADDON_MATERIAL_NUMBER = "R1234200002";
    private static final String COMPANY_COUNTRY = "DE";
    private static final String BPMD_ID = "4000001234";
    private static final String COMPANY_NAME = "Druck-Dödel Hydraulik";
    private static final String BC_TENANT_NAME = "Druck-Dödel Hydraulik";
    private static final String EUR = "EUR";
    private static final List<String> OWNER_EMAILS = List.of("<EMAIL>", "<EMAIL>");
    private static final String MAIN_CONTRACT_ID = "LRX_BC_091283098123";
    private static final String ADDON_CONTRACT_ID = "LRX_BC_091283098124";
    private static final String OTHER_ADDON_CONTRACT_ID = "LRX_BC_091283098125";
    private static final OffsetDateTime EARLY_START = OffsetDateTime.parse("2025-02-01T00:00:00Z");
    private static final OffsetDateTime END_DATE = OffsetDateTime.parse("2025-03-31T23:59:59Z");
    private static final OffsetDateTime LATER_START = OffsetDateTime.parse("2025-04-01T00:00:00Z");
    private static final String USER_ID = "d25ebf9b-55d1-44d2-a72d-35243effd9bf";
    private static final String USER_EMAIL = "<EMAIL>";
    private static final String COMPANY_EMAIL = "<EMAIL>";
    private static final String SUPPORT_EMAIL = "<EMAIL>";

    @Mock
    private BcCentralClient bcCentralClient;

    @Mock
    private ProductApi productApi;

    @Mock
    private LenientUmpClient lenientUmpClient;

    @Mock
    private CountriesServiceClient countriesServiceClient;

    @InjectMocks
    private BcCentralExporter bcCentralExporter;

    @Captor
    private ArgumentCaptor<CreateContractRequest> createContractRequestCaptor;

    @Captor
    private ArgumentCaptor<UpdateContractRequest> updateContractRequestCaptor;

    @BeforeEach
    public void setup() {
        final AppConfiguration appConfiguration = new AppConfiguration(null,
                Map.of(Tenant.REXROTH,
                        new AppConfiguration.TenantConfig(
                                new AppConfiguration.PublicUrlConfig(null, null),
                                new AppConfiguration.PublicEmailConfig(SUPPORT_EMAIL))),
                null, null);
        bcCentralExporter = new BcCentralExporter(bcCentralClient, productApi, lenientUmpClient,
                countriesServiceClient, appConfiguration);
    }

    @Test
    public void getContractStatusReturnsContractIfItExists() {
        when(bcCentralClient.getContract(BC_CENTRAL_ID))
                .thenReturn(GetContractResponse.builder().status(ContractStatus.SUCCESS).build());

        final Optional<ContractStatus> actualStatus = bcCentralExporter
                .getContractStatus(new BcCentralLicenseEntity().withBccentralContractId(BC_CENTRAL_ID));

        assertThat(actualStatus).isNotEmpty().contains(ContractStatus.SUCCESS);
    }

    @Test
    public void getContractStatusReturnsEmptyIfStatusIsNull() {
        when(bcCentralClient.getContract(BC_CENTRAL_ID))
                .thenReturn(GetContractResponse.builder().status(null).build());

        final Optional<ContractStatus> actualStatus = bcCentralExporter
                .getContractStatus(new BcCentralLicenseEntity().withBccentralContractId(BC_CENTRAL_ID));

        assertThat(actualStatus).isEmpty();
    }

    @Test
    public void getContractStatusReturnsEmptyIfResponseIsNull() {
        when(bcCentralClient.getContract(BC_CENTRAL_ID))
                .thenReturn(null);

        final Optional<ContractStatus> actualStatus = bcCentralExporter
                .getContractStatus(new BcCentralLicenseEntity().withBccentralContractId(BC_CENTRAL_ID));

        assertThat(actualStatus).isEmpty();
    }

    @Test
    public void getContractStatusReturnsEmptyIfIllegalStateExceptionIsThrown() {
        when(bcCentralClient.getContract(BC_CENTRAL_ID))
                .thenThrow(new IllegalStateException());

        final Optional<ContractStatus> actualStatus = bcCentralExporter
                .getContractStatus(new BcCentralLicenseEntity().withBccentralContractId(BC_CENTRAL_ID));

        assertThat(actualStatus).isEmpty();
    }

    @Test
    public void getContractStatusThrowsIfRuntimeExceptionIsThrown() {
        when(bcCentralClient.getContract(BC_CENTRAL_ID))
                .thenThrow(new RuntimeException());

        assertThatThrownBy(() -> bcCentralExporter
                .getContractStatus(new BcCentralLicenseEntity().withBccentralContractId(BC_CENTRAL_ID)))
                .isInstanceOf(RuntimeException.class);
    }

    @Test
    public void createContractSendsCreateRequestToBcCentral() {
        mockUmpCompany();
        mockCountriesService();
        mockProduct(MAIN_PRODUCT_ID, MAIN_MATERIAL_NUMBER);
        mockProduct(ADDON_PRODUCT_ID, ADDON_MATERIAL_NUMBER);

        final ContractStatus actualStatus = bcCentralExporter.createContract(new BcCentralLicenseEntity()
                        .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                        .withBccentralTenantName(BC_TENANT_NAME)
                        .withBccentralContractId(BC_CENTRAL_ID)
                        .withOwnerEmails(OWNER_EMAILS),
                ContractDto.builder()
                        .contractId(MAIN_CONTRACT_ID)
                        .tenant(Tenant.REXROTH)
                        .productId(MAIN_PRODUCT_ID)
                        .addons(List.of(ContractDto.AddonDto.builder()
                                .contractId(ADDON_CONTRACT_ID)
                                .productId(ADDON_PRODUCT_ID)
                                .build()))
                        .build());

        assertThat(actualStatus).isEqualTo(ContractStatus.PENDING);
        verify(bcCentralClient, times(1)).createContract(createContractRequestCaptor.capture());
        assertThat(createContractRequestCaptor.getValue()).satisfies(createContractRequest -> {
            assertThat(createContractRequest.contractId()).isEqualTo(BC_CENTRAL_ID);
            assertThat(createContractRequest.booking()).containsExactly(
                    CreateBooking.builder().licenseKey(MAIN_CONTRACT_ID).materialNumber(MAIN_MATERIAL_NUMBER).build(),
                    CreateBooking.builder().licenseKey(ADDON_CONTRACT_ID).materialNumber(ADDON_MATERIAL_NUMBER).build());
            assertThat(createContractRequest.customer()).isEqualTo(Customer.builder()
                    .currency(EUR)
                    .id(BPMD_ID)
                    .name(COMPANY_NAME)
                    .build());
            assertThat(createContractRequest.tenant()).isEqualTo(com.sast.store.external.bccentral.api.Tenant.builder()
                    .name(BC_TENANT_NAME)
                    .owner(OWNER_EMAILS)
                    .build());
        });
    }

    @Test
    public void createContractThrowsIfCompanyCannotBeQueried() {
        when(lenientUmpClient.getCompanyDetails("rexroth", COMPANY_ID))
                .thenReturn(Optional.empty());


        assertThatThrownBy(() -> bcCentralExporter.createContract(new BcCentralLicenseEntity()
                        .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                        .withBccentralTenantName(BC_TENANT_NAME)
                        .withBccentralContractId(BC_CENTRAL_ID)
                        .withOwnerEmails(OWNER_EMAILS),
                ContractDto.builder()
                        .contractId(MAIN_CONTRACT_ID)
                        .tenant(Tenant.REXROTH)
                        .productId(MAIN_PRODUCT_ID)
                        .addons(List.of(ContractDto.AddonDto.builder()
                                .contractId(ADDON_CONTRACT_ID)
                                .productId(ADDON_PRODUCT_ID)
                                .build()))
                        .build()))
                .isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void updateContractSendsUpdateRequestToBcCentralWithUserRequestor() {
        mockUmpCompany();
        mockUmpUser();
        mockCountriesService();
        mockProduct(MAIN_PRODUCT_ID, MAIN_MATERIAL_NUMBER);
        mockProduct(ADDON_PRODUCT_ID, ADDON_MATERIAL_NUMBER);
        mockProduct(OTHER_ADDON_PRODUCT_ID, OTHER_ADDON_MATERIAL_NUMBER);

        bcCentralExporter.updateContract(new BcCentralLicenseEntity()
                        .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                        .withBccentralTenantName(BC_TENANT_NAME)
                        .withBccentralContractId(BC_CENTRAL_ID)
                        .withOwnerEmails(OWNER_EMAILS),
                ContractDto.builder()
                        .contractId(MAIN_CONTRACT_ID)
                        .tenant(Tenant.REXROTH)
                        .productId(MAIN_PRODUCT_ID)
                        .startDate(EARLY_START)
                        .lastModifiedByUserId(USER_ID)
                        .addons(List.of(ContractDto.AddonDto.builder()
                                        .contractId(ADDON_CONTRACT_ID)
                                        .productId(ADDON_PRODUCT_ID)
                                        .startDate(EARLY_START)
                                        .endDate(END_DATE)
                                        .build(),
                                ContractDto.AddonDto.builder()
                                        .contractId(OTHER_ADDON_CONTRACT_ID)
                                        .productId(OTHER_ADDON_PRODUCT_ID)
                                        .startDate(LATER_START)
                                        .build()))
                        .build());

        verify(bcCentralClient, times(1)).updateContract(updateContractRequestCaptor.capture());
        assertThat(updateContractRequestCaptor.getValue()).satisfies(updateContractRequest -> {
            assertThat(updateContractRequest.contractId()).isEqualTo(BC_CENTRAL_ID);
            assertThat(updateContractRequest.requestorId()).isEqualTo(USER_EMAIL);
            assertThat(updateContractRequest.requestId()).isNotBlank();
            assertThat(updateContractRequest.customerCurrency()).isEqualTo(Currency.getInstance(EUR));
            assertThat(updateContractRequest.booking()).containsExactly(
                    UpdateBooking.builder().licenseKey(MAIN_CONTRACT_ID).materialNumber(MAIN_MATERIAL_NUMBER)
                            .startDate(EARLY_START.toZonedDateTime()).build(),
                    UpdateBooking.builder().licenseKey(ADDON_CONTRACT_ID).materialNumber(ADDON_MATERIAL_NUMBER)
                            .startDate(EARLY_START.toZonedDateTime()).endDate(END_DATE.toZonedDateTime()).build(),
                    UpdateBooking.builder().licenseKey(OTHER_ADDON_CONTRACT_ID).materialNumber(OTHER_ADDON_MATERIAL_NUMBER)
                            .startDate(LATER_START.toZonedDateTime()).build());
        });
    }

    @Test
    public void updateContractSendsUpdateRequestToBcCentralWithUnknownUidRequestorHasCompanyEmail() {
        mockUmpCompany();
        when(lenientUmpClient.getUser("rexroth", "foo")).thenReturn(Optional.empty());
        mockCountriesService();
        mockProduct(MAIN_PRODUCT_ID, MAIN_MATERIAL_NUMBER);
        mockProduct(ADDON_PRODUCT_ID, ADDON_MATERIAL_NUMBER);
        mockProduct(OTHER_ADDON_PRODUCT_ID, OTHER_ADDON_MATERIAL_NUMBER);

        bcCentralExporter.updateContract(new BcCentralLicenseEntity()
                        .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                        .withBccentralTenantName(BC_TENANT_NAME)
                        .withBccentralContractId(BC_CENTRAL_ID)
                        .withOwnerEmails(OWNER_EMAILS),
                ContractDto.builder()
                        .contractId(MAIN_CONTRACT_ID)
                        .tenant(Tenant.REXROTH)
                        .productId(MAIN_PRODUCT_ID)
                        .startDate(EARLY_START)
                        .lastModifiedByUserId("foo")
                        .addons(List.of(ContractDto.AddonDto.builder()
                                        .contractId(ADDON_CONTRACT_ID)
                                        .productId(ADDON_PRODUCT_ID)
                                        .startDate(EARLY_START)
                                        .endDate(END_DATE)
                                        .build(),
                                ContractDto.AddonDto.builder()
                                        .contractId(OTHER_ADDON_CONTRACT_ID)
                                        .productId(OTHER_ADDON_PRODUCT_ID)
                                        .startDate(LATER_START)
                                        .build()))
                        .build());

        verify(bcCentralClient, times(1)).updateContract(updateContractRequestCaptor.capture());
        assertThat(updateContractRequestCaptor.getValue()).satisfies(updateContractRequest -> {
            assertThat(updateContractRequest.contractId()).isEqualTo(BC_CENTRAL_ID);
            assertThat(updateContractRequest.requestorId()).isEqualTo(COMPANY_EMAIL);
            assertThat(updateContractRequest.requestId()).isNotBlank();
            assertThat(updateContractRequest.customerCurrency()).isEqualTo(Currency.getInstance(EUR));
            assertThat(updateContractRequest.booking()).containsExactly(
                    UpdateBooking.builder().licenseKey(MAIN_CONTRACT_ID).materialNumber(MAIN_MATERIAL_NUMBER)
                            .startDate(EARLY_START.toZonedDateTime()).build(),
                    UpdateBooking.builder().licenseKey(ADDON_CONTRACT_ID).materialNumber(ADDON_MATERIAL_NUMBER)
                            .startDate(EARLY_START.toZonedDateTime()).endDate(END_DATE.toZonedDateTime()).build(),
                    UpdateBooking.builder().licenseKey(OTHER_ADDON_CONTRACT_ID).materialNumber(OTHER_ADDON_MATERIAL_NUMBER)
                            .startDate(LATER_START.toZonedDateTime()).build());
        });
    }

    @Test
    public void updateContractSendsUpdateRequestToBcCentralWithoutUserRequestorHasSupportEmail() {
        mockUmpCompany();
        mockCountriesService();
        mockProduct(MAIN_PRODUCT_ID, MAIN_MATERIAL_NUMBER);
        mockProduct(ADDON_PRODUCT_ID, ADDON_MATERIAL_NUMBER);
        mockProduct(OTHER_ADDON_PRODUCT_ID, OTHER_ADDON_MATERIAL_NUMBER);

        bcCentralExporter.updateContract(new BcCentralLicenseEntity()
                        .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                        .withBccentralTenantName(BC_TENANT_NAME)
                        .withBccentralContractId(BC_CENTRAL_ID)
                        .withOwnerEmails(OWNER_EMAILS),
                ContractDto.builder()
                        .contractId(MAIN_CONTRACT_ID)
                        .tenant(Tenant.REXROTH)
                        .productId(MAIN_PRODUCT_ID)
                        .startDate(EARLY_START)
                        .addons(List.of(ContractDto.AddonDto.builder()
                                        .contractId(ADDON_CONTRACT_ID)
                                        .productId(ADDON_PRODUCT_ID)
                                        .startDate(EARLY_START)
                                        .endDate(END_DATE)
                                        .build(),
                                ContractDto.AddonDto.builder()
                                        .contractId(OTHER_ADDON_CONTRACT_ID)
                                        .productId(OTHER_ADDON_PRODUCT_ID)
                                        .startDate(LATER_START)
                                        .build()))
                        .build());

        verify(bcCentralClient, times(1)).updateContract(updateContractRequestCaptor.capture());
        assertThat(updateContractRequestCaptor.getValue()).satisfies(updateContractRequest -> {
            assertThat(updateContractRequest.contractId()).isEqualTo(BC_CENTRAL_ID);
            assertThat(updateContractRequest.requestorId()).isEqualTo(SUPPORT_EMAIL);
            assertThat(updateContractRequest.requestId()).isNotBlank();
            assertThat(updateContractRequest.customerCurrency()).isEqualTo(Currency.getInstance(EUR));
            assertThat(updateContractRequest.booking()).containsExactly(
                    UpdateBooking.builder().licenseKey(MAIN_CONTRACT_ID).materialNumber(MAIN_MATERIAL_NUMBER)
                            .startDate(EARLY_START.toZonedDateTime()).build(),
                    UpdateBooking.builder().licenseKey(ADDON_CONTRACT_ID).materialNumber(ADDON_MATERIAL_NUMBER)
                            .startDate(EARLY_START.toZonedDateTime()).endDate(END_DATE.toZonedDateTime()).build(),
                    UpdateBooking.builder().licenseKey(OTHER_ADDON_CONTRACT_ID).materialNumber(OTHER_ADDON_MATERIAL_NUMBER)
                            .startDate(LATER_START.toZonedDateTime()).build());
        });
    }

    private void mockUmpCompany() {
        when(lenientUmpClient.getCompanyDetails("rexroth", COMPANY_ID))
                .thenReturn(Optional.of(new UmpExternalCompanyDto()
                        .companyCountry(COMPANY_COUNTRY)
                        .companyEmail(COMPANY_EMAIL)
                        .bpmdId(BPMD_ID)
                        .companyName(COMPANY_NAME)));
    }

    private void mockUmpUser() {
        when(lenientUmpClient.getUser("rexroth", USER_ID))
                .thenReturn(Optional.of(new UmpUserDto()
                        .email(USER_EMAIL)));
    }

    private void mockCountriesService() {
        when(countriesServiceClient.getCountry(com.sast.store.external.countriesservice.api.Tenant.rexroth, COMPANY_COUNTRY))
                .thenReturn(new CountryDto.Builder()
                        .tenantConfigurations(List.of(new CountryDto.TenantConfigurationDto.Builder()
                                .currency(EUR)
                                .build()))
                        .build());
    }

    private void mockProduct(final String productId, final String materialNumber) {
        when(productApi.getProductVariant(Tenant.REXROTH, productId, null))
                .thenReturn(Optional.of(ProductDto.builder()
                        .variants(List.of(ProductDto.Variant.builder()
                                .externalProductId(materialNumber).build()))
                        .build()));
    }
}
