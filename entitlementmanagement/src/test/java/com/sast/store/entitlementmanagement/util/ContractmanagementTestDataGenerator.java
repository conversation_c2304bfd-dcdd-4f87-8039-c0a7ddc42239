package com.sast.store.entitlementmanagement.util;

import com.sast.store.entitlementmanagement.api.LicenseModel;
import com.sast.store.entitlementmanagement.dao.BcCentralLicenseEntity;
import com.sast.store.entitlementmanagement.dao.DcKeycloakLicenseEntity;
import com.sast.store.entitlementmanagement.dao.LicenseDao;
import com.sast.store.entitlementmanagement.dao.LitmosLicenseEntity;
import com.sast.store.testing.commons.AbstractTestDataGenerator;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import org.jeasy.random.EasyRandom;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.function.Function;

@Component
public class ContractmanagementTestDataGenerator extends AbstractTestDataGenerator<ContractmanagementTestDataGenerator> {

    protected ContractmanagementTestDataGenerator generator;

    @Inject
    private LicenseDao licenseDao;

    private LitmosLicenseGenerator litmosLicenseGenerator;
    private DcKeycloakLicenseGenerator dcKeycloakLicenseGenerator;
    private BcCentralLicenseGenerator bcCentralLicenseGenerator;

    private final EasyRandom rnd = new EasyRandom();

    @PostConstruct
    public void init() {
        litmosLicenseGenerator = this.new LitmosLicenseGenerator(this);
        dcKeycloakLicenseGenerator = this.new DcKeycloakLicenseGenerator(this);
        bcCentralLicenseGenerator = this.new BcCentralLicenseGenerator(this);
        this.generator = this;
    }

    public LitmosLicenseGenerator withLitmosLicense() {
        final LitmosLicenseEntity license = generator.rnd.nextObject(LitmosLicenseEntity.class);
        license.setLicenseModel(LicenseModel.LITMOS);
        generator.licenseDao.save(license);
        generator.setData(license);
        return generator.litmosLicenseGenerator;
    }

    public class LitmosLicenseGenerator extends ContractmanagementTestDataGenerator {

        public LitmosLicenseGenerator(final ContractmanagementTestDataGenerator generator) {
            this.generator = generator;
        }

        public LitmosLicenseGenerator havingValue(final Function<LitmosLicenseEntity, LitmosLicenseEntity> consumer) {
            final LitmosLicenseEntity key = new LitmosLicenseEntity();
            key.setCompanyId(generator.getLitmostLicense().getCompanyId());
            key.setLicenseId(generator.getLitmostLicense().getLicenseId());

            final LitmosLicenseEntity newLicense = consumer.apply(generator.getLitmostLicense());
            generator.licenseDao.delete(key);
            generator.getData(LitmosLicenseEntity.class).removeLast();

            generator.licenseDao.save(newLicense);
            generator.setData(newLicense);
            return this;
        }
    }

    public DcKeycloakLicenseGenerator withDcKeycloakLicense() {
        final DcKeycloakLicenseEntity license = generator.rnd.nextObject(DcKeycloakLicenseEntity.class);
        license.setLicenseModel(LicenseModel.DCKEYCLOAK);
        generator.licenseDao.save(license);
        generator.setData(license);
        return generator.dcKeycloakLicenseGenerator;
    }

    public class DcKeycloakLicenseGenerator extends ContractmanagementTestDataGenerator {

        public DcKeycloakLicenseGenerator(final ContractmanagementTestDataGenerator generator) {
            this.generator = generator;
        }

        public DcKeycloakLicenseGenerator havingValue(final Function<DcKeycloakLicenseEntity, DcKeycloakLicenseEntity> consumer) {
            final DcKeycloakLicenseEntity key = new DcKeycloakLicenseEntity();
            key.setCompanyId(generator.getDcKeycloakLicense().getCompanyId());
            key.setLicenseId(generator.getDcKeycloakLicense().getLicenseId());

            final DcKeycloakLicenseEntity newLicense = consumer.apply(generator.getDcKeycloakLicense());
            generator.licenseDao.delete(key);
            generator.getData(DcKeycloakLicenseEntity.class).removeLast();

            generator.licenseDao.save(newLicense);
            generator.setData(newLicense);
            return this;
        }
    }

    public BcCentralLicenseGenerator withBcCentralLicense() {
        final BcCentralLicenseEntity license = generator.rnd.nextObject(BcCentralLicenseEntity.class);
        license.setLicenseModel(LicenseModel.BCCENTRAL);
        generator.licenseDao.save(license);
        generator.setData(license);
        return generator.bcCentralLicenseGenerator;
    }

    public class BcCentralLicenseGenerator extends ContractmanagementTestDataGenerator {
        public BcCentralLicenseGenerator(final ContractmanagementTestDataGenerator generator) {
            this.generator = generator;
        }

        public BcCentralLicenseGenerator havingValue(final Function<BcCentralLicenseEntity, BcCentralLicenseEntity> consumer) {
            final BcCentralLicenseEntity key = new BcCentralLicenseEntity();
            key.setCompanyId(generator.getBcCentralLicense().getCompanyId());
            key.setLicenseId(generator.getBcCentralLicense().getLicenseId());

            final BcCentralLicenseEntity newLicense = consumer.apply(generator.getBcCentralLicense());
            generator.licenseDao.delete(key);
            generator.getData(BcCentralLicenseEntity.class).removeLast();

            generator.licenseDao.save(newLicense);
            generator.setData(newLicense);
            return this;
        }
    }

    public LitmosLicenseEntity getLitmostLicense() {
        return generator.getData(LitmosLicenseEntity.class).getLast();
    }

    public LinkedList<LitmosLicenseEntity> getLitmosLicenses() {
        return generator.getData(LitmosLicenseEntity.class);
    }
    
    public DcKeycloakLicenseEntity getDcKeycloakLicense() {
        return generator.getData(DcKeycloakLicenseEntity.class).getLast();
    }

    public BcCentralLicenseEntity getBcCentralLicense() {
        return generator.getData(BcCentralLicenseEntity.class).getLast();
    }

    public LinkedList<DcKeycloakLicenseEntity> getDcKeycloakLicenses() {
        return generator.getData(DcKeycloakLicenseEntity.class);
    }
}
