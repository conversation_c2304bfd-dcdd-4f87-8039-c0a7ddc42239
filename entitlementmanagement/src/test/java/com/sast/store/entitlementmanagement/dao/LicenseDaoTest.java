package com.sast.store.entitlementmanagement.dao;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.entitlementmanagement.AbstractComponentTest;
import com.sast.store.entitlementmanagement.api.LicenseModel;
import jakarta.inject.Inject;
import org.junit.jupiter.api.Test;
import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;

class LicenseDaoTest extends AbstractComponentTest {

    @Inject
    private LicenseDao licenseDao;

    @Test
    void testFindByCancellationState() {
        final BcCentralLicenseEntity license = new BcCentralLicenseEntity();
        license.withCompanyId(Tenant.REXROTH, "test-company");
        license.withLicenseId("test-license-1");
        license.withContractId("test-contract-1");
        license.withLicenseModel(LicenseModel.BCCENTRAL);
        license.setCancellationState(CancellationState.CANCELLATION_PENDING);
        license.setEndDate(Instant.now().plusSeconds(3600));

        licenseDao.save(license);

        final List<LicenseEntity<?>> results = licenseDao.findByCancellationStateWithEndDate(CancellationState.CANCELLATION_PENDING)
            .collect(Collectors.toList());

        assertThat(results)
            .extracting(LicenseEntity::getLicenseId)
            .contains("test-license-1");
    }
}
