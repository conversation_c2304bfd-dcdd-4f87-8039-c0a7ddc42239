server:
  port: 8085

logging:
  level:
    com.sast: TRACE

spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://sso.mp-dc-d.com/auth/realms/rexroth
  cloud:
    aws:
      region:
        static: eu-central-1
      credentials:
        access-key: fakekey
        secret-key: fakekey
      sqs:
        endpoint: http://localhost:40123
      dynamodb:
        endpoint: http://localhost:9324
      sns:
        endpoint: http://localhost:9311
  temporal:
    connection:
      target: localhost:7233

bossstore:
  commercetools:
    clientId: SSAIdVhBpKt80ChCMjZwxi4c
    clientSecret: ********************************
    projectKey: sast-dev

  hazelcast:
    enabled: local
  bccentral:
    url: http://localhost:34890
    username: test
    password: test
    stage: QA
  dckeycloak:
    url: http://localhost:34561
    clientId: clientid
    clientSecret: clientsecret
    realm: realm
  dcLicensing:
    dcKeycloak:
      PREMIUM:
        roleNames: role1,role2
        rolesClientUUID: e58a68d8-cab6-4516-b51a-dc2fd05a1107
      FREEMIUM:
        roleNames: role1,role2,role3
        roleNamesToRemove: role1,role2
        rolesClientUUID: e58a68d8-cab6-4516-b51a-dc2fd05a1107
        duration: P30D
        expiringSoonDuration: P5D
    litmosParentTeamId: 02_Rexroth_Partner
  litmos:
    url: http://localhost:34729
    apikey: apikey
  countriesservice:
    url: http://localhost:40003
  ump:
    url: http://localhost:40101
  productmanagementUrl: http://localhost:8082/rest
  contractmanagementUrl: http://localhost:8084/rest
