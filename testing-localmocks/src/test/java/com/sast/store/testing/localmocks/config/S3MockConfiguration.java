package com.sast.store.testing.localmocks.config;

import com.sast.store.testing.awsmockup.junit.S3MockExtension;
import com.sast.store.testing.commons.DummyExtensionContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.nio.file.Paths;

@Configuration
public class S3MockConfiguration {
    @Bean
    public S3MockExtension s3MockExtension() throws Exception {
        final S3MockExtension s3MockExtension = new S3MockExtension();
        s3MockExtension.beforeAll(new DummyExtensionContext());
        S3MockExtension.getMock().putContent("brim-service-documents-dev", "DRX_BD_022760012307.pdf",
            Paths.get(getClass().getResource("/s3/brim-service-documents-dev/DRX_BD_022760012307.pdf").toURI()));
        return s3MockExtension;
    }
}
