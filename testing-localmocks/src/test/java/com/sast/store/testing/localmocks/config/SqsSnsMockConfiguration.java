package com.sast.store.testing.localmocks.config;

import com.sast.store.testing.awsmockup.junit.SnsMockExtension;
import com.sast.store.testing.awsmockup.junit.SqsMockExtension;
import com.sast.store.testing.commons.DummyExtensionContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SqsSnsMockConfiguration {
    @Bean
    public SqsMockExtension sqsMockExtension() {
        final var sqsMockExtension = new SqsMockExtension(40123);
        sqsMockExtension.beforeAll(new DummyExtensionContext());
        return sqsMockExtension;
    }

    @Bean
    public SnsMockExtension snsMockExtension() {
        final var snsMockExtension = new SnsMockExtension();
        snsMockExtension.beforeAll(new DummyExtensionContext());
        return snsMockExtension;
    }
}
