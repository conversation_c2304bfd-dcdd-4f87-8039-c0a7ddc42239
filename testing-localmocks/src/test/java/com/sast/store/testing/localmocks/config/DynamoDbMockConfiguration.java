package com.sast.store.testing.localmocks.config;

import com.amazonaws.services.dynamodbv2.document.Item;
import com.sast.store.testing.awsmockup.DynamoDbMock;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DynamoDbMockConfiguration {
    @Bean
    public DynamoDbMock dynamoDbMock() {
        return new DynamoDbMock(9324).start();
    }

    @Bean
    public DynamoDbMock contractmanagementDynamoDbMock(final DynamoDbMock dynamoDbMock) {
        return dynamoDbMock
            .withTable("BossstoreBrimContracts", "companyId", "contractId")
            .withItem("BossstoreBrimContracts", Item.fromJSON("""
                {
                    "companyId": "rexroth/eb6542c3-6db0-41da-9575-a4b96818158d",
                    "contractId": "BC_1000800001",
                    "orderNumber": "928374",
                    "productId": "DRX_PR_HUB_DE_3M",
                    "startDate": "2024-07-16T14:37:12.247+02:30",
                    "contractType": "SUBSCRIPTION",
                    "contractPeriod": "P3M",
                    "noticePeriod": "P1M",
                    "cancellationState": "ACTIVE"
                }"""))
            .withItem("BossstoreBrimContracts", Item.fromJSON("""
                {
                    "companyId": "rexroth/eb6542c3-6db0-41da-9575-a4b96818158d",
                    "contractId": "BC_1000800002",
                    "orderNumber": "928374",
                    "productId": "DRX_PR_HUB_DE_3M",
                    "startDate": "2024-07-16T14:37:12.247+02:30",
                    "endDate": "2024-07-20T14:37:12.247+02:30",
                    "contractType": "SUBSCRIPTION",
                    "contractPeriod": "P3M",
                    "noticePeriod": "P1M",
                    "cancellationState": "CANCELLED"
                }"""))
            .withItem("BossstoreBrimContracts", Item.fromJSON("""
                {
                    "companyId": "rexroth/eb6542c3-6db0-41da-9575-a4b96818158d",
                    "contractId": "BC_1000800003",
                    "orderNumber": "928374",
                    "productId": "DRX_PR_HUB_DE_3M",
                    "startDate": "2024-07-16T14:37:12.247+02:30",
                    "contractType": "SUBSCRIPTION",
                    "contractPeriod": "P3M",
                    "noticePeriod": "P1M",
                    "cancellationState": "CANCELLATION_PENDING"
                }"""))
            .withItem("BossstoreBrimContracts", Item.fromJSON("""
                {
                    "companyId": "rexroth/eb6542c3-6db0-41da-9575-a4b96818158d",
                    "contractId": "BC_1000800004",
                    "orderNumber": "928374",
                    "productId": "DRX_PR_HUB_DE_FREE",
                    "startDate": "2024-07-16T14:37:12.247+02:30",
                    "contractType": "SUBSCRIPTION",
                    "contractPeriod": "P3M",
                    "noticePeriod": "P1M",
                    "cancellationState": "ACTIVE"
                }"""))
            .withItem("BossstoreBrimContracts", Item.fromJSON("""
                {
                    "companyId": "rexroth/eb6542c3-6db0-41da-9575-a4b96818158d",
                    "contractId": "BC_1000800005",
                    "orderNumber": "928375",
                    "productId": "DRX_PC_14007M_DE",
                    "startDate": "2025-01-12T12:34:56.789+02:30",
                    "contractType": "CONSUMPTION",
                    "contractPeriod": "P1M",
                    "noticePeriod": "P7D",
                    "cancellationState": "ACTIVE"
                }"""));
    }

    @Bean
    public DynamoDbMock dynamoDb(final DynamoDbMock dynamoDbMock) {
        return dynamoDbMock
            .withTable("BossstoreLicenses", "companyId", "licenseId")
            .withItem("BossstoreLicenses", Item.fromJSON("""
                {
                    "companyId": "rexroth/eb6542c3-6db0-41da-9575-a4b96818158d",
                    "contractId": "BC_1000800001",
                    "email": "<EMAIL>",
                    "firstname": "test",
                    "lastname": "name",
                    "licenseId": "519e4792-88b1-47ef-8caf-201ee2501aff",
                    "licenseModel": "DCKEYCLOAK",
                    "licenseType": "PREMIUM",
                    "productId": "DRX_PR_HUB_DE_1Y",
                    "startDate": "2024-11-29T08:59:34.696037941Z"
                }"""))
            .withItem("BossstoreLicenses", Item.fromJSON("""
                {
                     "companyId": "rexroth/eb6542c3-6db0-41da-9575-a4b96818158d",
                     "contractId": "BC_1000800001",
                     "email": "<EMAIL>",
                     "firstname": "test",
                     "lastname": "name",
                     "licenseId": "64de1458-4015-4621-8115-5b1c3f561afd",
                     "licenseModel": "LITMOS",
                     "productId": "DRX_PR_HUB_DE_1Y",
                     "startDate": "2024-11-29T08:59:34.696037941Z"
                 }"""))
            .withTable("bossstore_litmos_teams", "company")
            .withTable("bossstore_hydraulichub_freemium_users", "email");
    }

    @Bean
    public DynamoDbMock ordermanagementDynamoDbMock(final DynamoDbMock dynamoDbMock) {
        return dynamoDbMock
            .withTable("bossstore_invoice_entity", "documentNumber", "company", "documentDate")
            .withItem("bossstore_invoice_entity", Item.fromJSON("""
                {
                  "documentNumber": "022760012307",
                  "company": "eb6542c3-6db0-41da-9575-a4b96818158d",
                  "creationDate": "2024-09-25",
                  "documentDate": "2024-09-25",
                  "lineItems": [
                    {
                      "netAmount": 200,
                      "position": 1,
                      "productId": "DRX_PR_HUB_DE_1Y",
                      "quantity": 1
                    }
                  ],
                  "orderNumbers": [
                    "753068535012117"
                  ],
                  "s3File": {
                    "bucket": "brim-service-documents-dev",
                    "contentType": "application/pdf",
                    "key": "DRX_BD_022760012307.pdf",
                    "sha256sum": "3eff9b2c1f449472ec9c5f59295261a3cf02e0dff43ea7a000f8446f4c030f8e"
                  },
                  "tenant": "rexroth",
                  "totalAmounts": {
                    "currency": "EUR",
                    "grossAmount": 238,
                    "netAmount": 200,
                    "taxAmount": 38
                  },
                  "version": 1
                }"""));
    }

    @Bean
    public DynamoDbMock shedlockDynamoDbMock(final DynamoDbMock dynamoDbMock) {
        return dynamoDbMock
            .withTable("bossstore_shedlock", "_id");
    }
}
