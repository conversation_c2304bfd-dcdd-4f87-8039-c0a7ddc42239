package com.sast.store.testing.localmocks.config;

import com.sast.store.external.bccentral.BcCentralMockExtension;
import com.sast.store.external.countriesservice.util.CountriesServiceMockExtension;
import com.sast.store.external.dckeycloak.test.DcKeycloakMockExtension;
import com.sast.store.external.litmos.test.LitmosMockExtension;
import com.sast.store.external.pgw.test.PgwMockExtension;
import com.sast.store.external.ump.test.UmpMockExtension;
import com.sast.store.testing.commons.DummyExtensionContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class WiremockConfiguration {
    @Bean
    public UmpMockExtension umpMockExtension() throws Exception {
        final var umpMockExtension = new UmpMockExtension();
        umpMockExtension.beforeAll(new DummyExtensionContext());
        UmpMockExtension.withDefaultResponse();
        return umpMockExtension;
    }

    @Bean
    public CountriesServiceMockExtension countriesMockExtension() throws Exception {
        final var countriesMockExtension = new CountriesServiceMockExtension(40003);
        countriesMockExtension.beforeAll(new DummyExtensionContext());
        CountriesServiceMockExtension.withDefaultResponse();
        return countriesMockExtension;
    }

    @Bean
    public BcCentralMockExtension bcCentralMockExtension() throws Exception {
        final var bcCentralMockExtension = new BcCentralMockExtension();
        bcCentralMockExtension.beforeAll(new DummyExtensionContext());
        BcCentralMockExtension.withDefaultResponse();
        return bcCentralMockExtension;
    }

    @Bean
    public DcKeycloakMockExtension dcKeycloakMockExtension() throws Exception {
        final var dcKeycloakMockExtension = new DcKeycloakMockExtension();
        dcKeycloakMockExtension.beforeAll(new DummyExtensionContext());
        DcKeycloakMockExtension.withDefaultResponse();
        return dcKeycloakMockExtension;
    }

    @Bean
    public LitmosMockExtension litmosMockExtension() throws Exception {
        final var litmosMockExtension = new LitmosMockExtension();
        litmosMockExtension.beforeAll(new DummyExtensionContext());
        LitmosMockExtension.withDefaultResponse();
        return litmosMockExtension;
    }

    @Bean
    public PgwMockExtension pgwMockExtension() throws Exception {
        final var pgwMockExtension = new PgwMockExtension();
        pgwMockExtension.beforeAll(new DummyExtensionContext());
        PgwMockExtension.withDefaultResponse();
        return pgwMockExtension;
    }
}
