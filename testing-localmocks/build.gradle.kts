plugins {
    id("bossstore.subproject-conventions")
    id("org.springframework.boot") version "3.4.3"
}

dependencies {
    implementation(enforcedPlatform(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES))
    implementation(enforcedPlatform("io.awspring.cloud:spring-cloud-aws-dependencies:3.3.0"))
    implementation("org.springframework.boot:spring-boot-starter")

    testImplementation(testFixtures(project(":external-clients:bc-central")))
    testImplementation(testFixtures(project(":external-clients:countries-service")))
    testImplementation(testFixtures(project(":external-clients:dc-keycloak")))
    testImplementation(testFixtures(project(":external-clients:dc-litmos")))
    testImplementation(testFixtures(project(":external-clients:pgw")))
    testImplementation(testFixtures(project(":external-clients:ump")))
    testImplementation(testFixtures(project(":testing-awsmockup")))
    testImplementation(testFixtures(project(":testing-commons")))
    testImplementation("org.junit.jupiter:junit-jupiter-api")
    testImplementation("org.wiremock:wiremock-standalone:3.9.2")
    testImplementation("io.awspring.cloud:spring-cloud-aws-starter-s3")
    testImplementation("com.amazonaws:DynamoDBLocal:2.5.4") {
        exclude(group = "software.amazon.awssdk", module = "url-connection-client")
    }
}

tasks.bootJar {
    enabled = false
}
