plugins {
    id("bossstore.subproject-conventions")
    `java-library`
    id("org.springframework.boot") version "3.4.3" apply false
}

dependencies {
    implementation(platform(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES))
    implementation("org.springframework:spring-context")
    implementation("org.springframework.boot:spring-boot-autoconfigure")
    implementation("org.slf4j:slf4j-api")
    api(platform("net.javacrumbs.shedlock:shedlock-bom:6.3.0"))
    api("net.javacrumbs.shedlock:shedlock-spring")
    implementation("net.javacrumbs.shedlock:shedlock-provider-dynamodb2")
    api(platform("io.awspring.cloud:spring-cloud-aws-dependencies:3.3.0"))
    api("io.awspring.cloud:spring-cloud-aws-starter-dynamodb")
}
