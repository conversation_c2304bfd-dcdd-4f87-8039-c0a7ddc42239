package com.sast.store.commons.hazelcast;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.BlockingQueue;

public class QueuePublisher<T extends Record> {
    private static final Logger LOG = LoggerFactory.getLogger(QueuePublisher.class);

    private final BlockingQueue<T> queue;

    public QueuePublisher(final BlockingQueue<T> queue) {
        this.queue = queue;
    }

    public void publish(final T event) {
        LOG.info("sending event {}", event);
        queue.add(event);
    }

}