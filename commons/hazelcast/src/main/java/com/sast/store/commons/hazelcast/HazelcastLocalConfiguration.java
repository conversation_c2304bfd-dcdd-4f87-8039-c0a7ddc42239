package com.sast.store.commons.hazelcast;

import com.hazelcast.config.Config;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Hazelcast configuration without kubernetes for local development and tests
 */
@Configuration
@ConditionalOnProperty(prefix = "bossstore.hazelcast", name = "enabled", havingValue = "local")
public class HazelcastLocalConfiguration {
    private static final Logger LOG = LoggerFactory.getLogger(HazelcastLocalConfiguration.class);

    @Bean
    public Config hazelcastConfig() {
        LOG.info("HazelcastLocalConfiguration initialized");
        final Config config = HazelcastConfiguration.hazelcastCommonConfig();
        config.getNetworkConfig().getJoin().getMulticastConfig()
            .setEnabled(false);
        config.getNetworkConfig().getJoin().getTcpIpConfig()
            .setEnabled(true)
            .addMember("127.0.0.1");
        return config;
    }
}
