package com.sast.store.commons.hazelcast;

import com.hazelcast.config.Config;
import com.hazelcast.config.PartitionGroupConfig.MemberGroupType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(prefix = "bossstore.hazelcast", name = "enabled", havingValue = "kubernetes", matchIfMissing = true)
public class HazelcastKubernetesConfiguration {

    private static final Logger LOG = LoggerFactory.getLogger(HazelcastKubernetesConfiguration.class);

    // changing the cluster name will create a new cluster so beware!
    // in case of not backward compatible upgrade of hazelcast, it should be changed
    @Value("${bossstore.hazelcast.cluster-name:bossstore-hz-default-5-5}")
    private String clusterName;

    // k8s services need to be labeled with hazelcastCluster=bossstore-hazelcast so they can be discovered
    @Value("${bossstore.hazelcast.service-label:bossstore-hazelcast}")
    private String serviceLabel;

    @Value("${bossstore.hazelcast.namespace:bossstore}")
    private String namespace;

    @Bean
    public Config hazelcastConfig() {
        LOG.info("HazelcastKubernetesConfiguration initialized, cluster-name={}", clusterName);
        final Config config = HazelcastConfiguration.hazelcastCommonConfig();
        config
            .setProperty("hazelcast.shutdownhook.policy", "GRACEFUL")
            .setProperty("hazelcast.graceful.shutdown.max.wait", "600")
            .setProperty("hazelcast.cluster.version.auto.upgrade.enabled", "true");
        config.getPartitionGroupConfig()
            .setEnabled(true)
            .setGroupType(MemberGroupType.ZONE_AWARE);
        config.getNetworkConfig().getJoin().getMulticastConfig()
            .setEnabled(false);
        config.getNetworkConfig().getJoin().getKubernetesConfig()
            .setEnabled(true)
            .setProperty("namespace", namespace)
            .setProperty("service-label-name", "hazelcastCluster")
            .setProperty("service-label-value", serviceLabel);
        config.setClusterName(clusterName);
        return config;
    }
}
