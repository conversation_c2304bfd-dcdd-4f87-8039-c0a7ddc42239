package com.sast.store.commons.hazelcast;

import com.hazelcast.collection.IQueue;
import com.hazelcast.core.HazelcastInstance;

import java.util.concurrent.BlockingQueue;
import java.util.function.Consumer;

public final class Queue {
    private Queue() {
    }

    public static <T> QueueSubscriber<T> subscribe(final BlockingQueue<T> queue, final Consumer<T> messageConsumer) {
        return new QueueSubscriber<T>(queue, messageConsumer);
    }

    public static <T extends Record> QueuePublisher<T> publish(final BlockingQueue<T> queue) {
        return new QueuePublisher<T>(queue);
    }

    public static <T> IQueue<T> contractCancellationEvents(final HazelcastInstance hazelcastInstance) {
        return hazelcastInstance.getQueue("contractcancellationevents");
    }

    public static <T> IQueue<T> contractExpirationEvents(final HazelcastInstance hazelcastInstance) {
        return hazelcastInstance.getQueue("contractexpirationevents");
    }

    public static <T> IQueue<T> contractUpdateEvents(final HazelcastInstance hazelcastInstance) {
        return hazelcastInstance.getQueue("contractupdateevents");
    }

    public static <T> IQueue<T> contractCreateEvents(final HazelcastInstance hazelcastInstance) {
        return hazelcastInstance.getQueue("contractcreateevents");
    }
}
