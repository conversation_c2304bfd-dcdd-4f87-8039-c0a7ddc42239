package com.sast.store.commons.hazelcast;

import com.hazelcast.config.Config;

/**
 * Common hazelcast configuration
 */
public final class HazelcastConfiguration {
    private HazelcastConfiguration() {
    }

    public static Config hazelcastCommonConfig() {
        final Config config = new Config();
        config.setProperty("hazelcast.logging.type", "slf4j");
        // config for distributed sets/maps
        config.getSetConfig("default")
            .setBackupCount(1);
        config.getMapConfig("default")
            .setBackupCount(1);
        // config for distributed queues
        config.getSerializationConfig().getCompactSerializationConfig()
            .addSerializer(new InstantSerializer())
            .addSerializer(new PeriodSerializer());
        return config;
    }
}
