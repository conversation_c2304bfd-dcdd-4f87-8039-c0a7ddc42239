package com.sast.store.commons.hazelcast;

import com.hazelcast.core.HazelcastInstanceNotActiveException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Consumer;

public final class QueueSubscriber<T> {
    private static final Logger LOG = LoggerFactory.getLogger(QueueSubscriber.class);
    private final ExecutorService executor = Executors.newSingleThreadExecutor();

    public QueueSubscriber(final BlockingQueue<T> queue, final Consumer<T> messageConsumer) {
        executor.submit(() -> this.subscribeToQueue(queue, messageConsumer));
    }

    private void subscribeToQueue(final BlockingQueue<T> queue, final Consumer<T> messageConsumer) {
        LOG.info("subscribed to queue {}", queue);
        while (true) {
            try {
                final T message = queue.take();
                messageConsumer.accept(message);
            } catch (final InterruptedException | HazelcastInstanceNotActiveException e) {
                LOG.info("stopped to consume from queue {}", queue);
                Thread.currentThread().interrupt();
                break;
            } catch (final RuntimeException e) {
                LOG.info("consumer failed for queue {}", queue, e);
            }

        }
    }

}
