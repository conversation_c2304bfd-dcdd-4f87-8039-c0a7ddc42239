package com.sast.store.commons.hazelcast;

import com.hazelcast.nio.serialization.compact.CompactReader;
import com.hazelcast.nio.serialization.compact.CompactSerializer;
import com.hazelcast.nio.serialization.compact.CompactWriter;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;

public class InstantSerializer implements CompactSerializer<Instant> {
    @Override
    public Instant read(final CompactReader reader) {
        final OffsetDateTime value = reader.readTimestampWithTimezone("instant");
        return value.toInstant();
    }

    @Override
    public void write(final CompactWriter writer, final Instant value) {
        writer.writeTimestampWithTimezone("instant", value.atOffset(ZoneOffset.UTC));
    }

    @Override
    public Class<Instant> getCompactClass() {
        return Instant.class;
    }

    @Override
    public String getTypeName() {
        return "instant";
    }
}