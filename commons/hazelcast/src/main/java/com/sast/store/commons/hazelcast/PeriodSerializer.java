package com.sast.store.commons.hazelcast;

import com.hazelcast.nio.serialization.compact.CompactReader;
import com.hazelcast.nio.serialization.compact.CompactSerializer;
import com.hazelcast.nio.serialization.compact.CompactWriter;

import java.time.Period;

public class PeriodSerializer implements CompactSerializer<Period> {
    @Override
    public Period read(final CompactReader reader) {
        final int years = reader.readInt32("years");
        final int months = reader.readInt32("months");
        final int days = reader.readInt32("days");
        return Period.of(years, months, days);
    }

    @Override
    public void write(final CompactWriter writer, final Period value) {
        writer.writeInt32("years", value.getYears());
        writer.writeInt32("months", value.getMonths());
        writer.writeInt32("days", value.getDays());
    }

    @Override
    public Class<Period> getCompactClass() {
        return Period.class;
    }

    @Override
    public String getTypeName() {
        return "period";
    }
}