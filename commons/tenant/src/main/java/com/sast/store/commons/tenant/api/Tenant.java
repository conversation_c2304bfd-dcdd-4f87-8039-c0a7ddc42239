package com.sast.store.commons.tenant.api;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum Tenant {
    BAAM("baam"),
    AZENA("azena"),
    REXROTH("rexroth");

    private final String id;

    Tenant(final String id) {
        this.id = id;
    }

    @JsonValue
    public String id() {
        return id;
    }

    /**
     * Convert tenant id to tenant instance.
     * <p>
     * Do not rename. Jersey searches for {@code fromString} to map header params and uses {@code valueOf} if it does not find it.
     *
     * @param id tenant id
     * @return tenant instance
     */
    @JsonCreator
    public static Tenant fromString(final String id) {
        for (final Tenant tenant : Tenant.values()) {
            if (tenant.id.equals(id)) {
                return tenant;
            }
        }

        throw new IllegalArgumentException("No tenant constant for id %s".formatted(id));
    }

    @Override
    public String toString() {
        return id;
    }
}
