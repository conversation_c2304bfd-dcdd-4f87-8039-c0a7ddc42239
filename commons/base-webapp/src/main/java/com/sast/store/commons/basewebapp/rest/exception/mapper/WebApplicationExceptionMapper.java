package com.sast.store.commons.basewebapp.rest.exception.mapper;

import com.sast.store.commons.basewebapp.rest.error.ApiErrorDto;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.ext.ExceptionMapper;
import jakarta.ws.rs.ext.Provider;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Provider
public class WebApplicationExceptionMapper implements ExceptionMapper<WebApplicationException> {
    @Override
    public Response toResponse(final WebApplicationException e) {
        final Response exceptionResponse = e.getResponse();

        LOG.info("Handling WebApplicationException statusCode={}, exception type={}, message={}",
                exceptionResponse.getStatus(), e.getClass().getSimpleName(), e.getMessage());
        LOG.debug(e.getMessage(), e);

        final ApiErrorDto errorDto = ApiErrorDto.builder()
                .errorCode(Response.Status.Family.familyOf(exceptionResponse.getStatus()).name())
                .responseEntity(exceptionResponse.getEntity())
                .errorMessage(e.getMessage())
                .build();

        return Response
                .status(exceptionResponse.getStatus())
                .entity(errorDto)
                .type(MediaType.APPLICATION_JSON)
                .build();
    }
}
