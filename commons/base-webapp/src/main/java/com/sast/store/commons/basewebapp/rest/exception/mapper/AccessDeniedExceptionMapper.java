package com.sast.store.commons.basewebapp.rest.exception.mapper;

import com.sast.store.commons.basewebapp.rest.error.ApiErrorDto;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.ext.Provider;
import org.glassfish.jersey.spi.ExtendedExceptionMapper;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * Avoid detailed error response for authorization errors
 */
@Provider
public class AccessDeniedExceptionMapper implements ExtendedExceptionMapper<AccessDeniedException> {
    @Override
    public Response toResponse(final AccessDeniedException exception) {
        final boolean isLoggedIn = !(SecurityContextHolder.getContext().getAuthentication() instanceof AnonymousAuthenticationToken);
        final Response.Status status = isLoggedIn ? Response.Status.FORBIDDEN : Response.Status.UNAUTHORIZED;
        return Response
                .status(status)
                .type(MediaType.APPLICATION_JSON)
                .entity(ApiErrorDto.builder()
                        .errorCode(status.name())
                        .errorMessage(status.getReasonPhrase())
                        .build())
                .build();
    }

    @Override
    public boolean isMappable(final AccessDeniedException e) {
        return true;
    }
}
