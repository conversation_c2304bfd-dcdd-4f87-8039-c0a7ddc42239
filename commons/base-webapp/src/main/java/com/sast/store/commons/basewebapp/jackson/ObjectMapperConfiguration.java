package com.sast.store.commons.basewebapp.jackson;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class ObjectMapperConfiguration {
    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        return new ObjectMapper()
                /* json decimal/ints are usually unlimited length */
                /* if type is Object or Number map it to BigDecimal or BigInteger */
                .enable(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS)
                .enable(DeserializationFeature.USE_BIG_INTEGER_FOR_INTS)
                /* backwards compatibility */
                .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                /* ignore unknown polymorphic subtypes */
                .disable(DeserializationFeature.FAIL_ON_INVALID_SUBTYPE)
                /* dont serialize nulls */
                .setSerializationInclusion(JsonInclude.Include.NON_ABSENT)
                /* serialize dates as ISO-8601 String */
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                /* don't convert timezones to server timezones in OffsetDateTime fields */
                .disable(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE)
                /* register converters for Optionals and java.time.* Objects */
                .registerModules(new Jdk8Module(), new JavaTimeModule());
    }
}
