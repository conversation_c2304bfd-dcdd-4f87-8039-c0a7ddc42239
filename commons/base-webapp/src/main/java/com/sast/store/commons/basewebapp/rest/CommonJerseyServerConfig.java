package com.sast.store.commons.basewebapp.rest;

import com.sast.store.commons.basewebapp.rest.exception.mapper.AccessDeniedExceptionMapper;
import com.sast.store.commons.basewebapp.rest.exception.mapper.GenericExceptionMapper;
import com.sast.store.commons.basewebapp.rest.exception.mapper.JsonOnlyValidationExceptionMapper;
import com.sast.store.commons.basewebapp.rest.exception.mapper.JsonProcessingExceptionMapper;
import com.sast.store.commons.basewebapp.rest.exception.mapper.ProcessingExceptionMapper;
import com.sast.store.commons.basewebapp.rest.exception.mapper.WebApplicationExceptionMapper;
import org.glassfish.jersey.jackson.internal.jackson.jaxrs.json.JacksonJaxbJsonProvider;
import org.glassfish.jersey.server.ServerProperties;

import java.util.function.BiConsumer;
import java.util.function.Consumer;

public final class CommonJerseyServerConfig {
    private CommonJerseyServerConfig() {
    }

    public static void defaultRegistrationsAndProperties(final Consumer<Class<?>> register, final BiConsumer<String, Object> property) {

        // exception mappers
        register.accept(ProcessingExceptionMapper.class);
        register.accept(AccessDeniedExceptionMapper.class);
        register.accept(WebApplicationExceptionMapper.class);
        register.accept(GenericExceptionMapper.class);
        register.accept(JsonOnlyValidationExceptionMapper.class);

        // disable automatic error messages that leak internal information
        register.accept(JacksonJaxbJsonProvider.class);
        register.accept(JsonProcessingExceptionMapper.class);

        // error handling
        property.accept(ServerProperties.BV_SEND_ERROR_IN_RESPONSE, Boolean.TRUE);
        // necessary so spring security works properly in terms of authorization
        property.accept(ServerProperties.RESPONSE_SET_STATUS_OVER_SEND_ERROR, Boolean.TRUE);
        // disable WADL for xml
        property.accept(ServerProperties.WADL_FEATURE_DISABLE, Boolean.TRUE);
    }

    public static void gatewayRegistrationsAndProperties(final Consumer<Class<?>> register, final BiConsumer<String, Object> property) {

        // exception mappers
        register.accept(GenericExceptionMapper.class);
        register.accept(AccessDeniedExceptionMapper.class);

        // disable automatic error messages that leak internal information
        register.accept(JacksonJaxbJsonProvider.class);
        register.accept(JsonProcessingExceptionMapper.class);

        // error handling
        property.accept(ServerProperties.BV_SEND_ERROR_IN_RESPONSE, Boolean.TRUE);
        // necessary so spring security works properly in terms of authorization
        property.accept(ServerProperties.RESPONSE_SET_STATUS_OVER_SEND_ERROR, Boolean.TRUE);
        // disable WADL for xml
        property.accept(ServerProperties.WADL_FEATURE_DISABLE, Boolean.TRUE);
    }
}
