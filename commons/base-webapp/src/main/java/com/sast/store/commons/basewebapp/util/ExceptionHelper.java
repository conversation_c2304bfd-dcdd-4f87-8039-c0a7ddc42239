package com.sast.store.commons.basewebapp.util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.UncheckedIOException;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Function;

public final class ExceptionHelper {

    private static final Logger LOG = LoggerFactory.getLogger(ExceptionHelper.class);

    private ExceptionHelper() {
        // hide
    }

    /**
     * convert IOException to UncheckedIOException
     */
    public static <T, E extends IOException> Consumer<T> wrapIoException(final ConsumerWithIoException<T, E> consumer) {
        return argument -> {
            try {
                consumer.apply(argument);
            } catch (final IOException e) {
                throw new UncheckedIOException(e);
            }
        };
    }

    /**
     * convert Exception throwing function safely into an Optional
     */
    public static <T, R> Function<T, Optional<R>> safeWrapOptional(final FunctionWithException<T, R, ? extends Exception> consumer) {
        return argument -> {
            try {
                return Optional.ofNullable(consumer.apply(argument));
            } catch (final Exception e) {
                LOG.warn("Error caught while extracting element function", e);
                return Optional.empty();
            }
        };
    }

    @FunctionalInterface
    public interface ConsumerWithIoException<T, E extends IOException> {
        void apply(T t) throws E;
    }

    @FunctionalInterface
    public interface FunctionWithException<T, R, E extends Exception> {
        R apply(T t) throws E;
    }
}
