package com.sast.store.commons.basewebapp.keycloak;

import jakarta.inject.Inject;
import jakarta.ws.rs.client.ClientRequestContext;
import jakarta.ws.rs.client.ClientRequestFilter;
import jakarta.ws.rs.core.HttpHeaders;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Collections;

@Component
public class KeycloakTokenClientRequestFilter implements ClientRequestFilter {

    @Inject
    private AuthenticationService authenticationService;

    @Override
    public void filter(final ClientRequestContext requestContext) {
        final RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        // Only try to add keycloak token if we actually are inside of a
        // HttpServletRequest. Otherwise (e.g. SQS subscriber), don't.
        if (requestAttributes instanceof final ServletRequestAttributes attributes) {
            authenticationService.getKeycloakSecurityContext()
                    .map(Jwt::getTokenValue)
                    .ifPresent(token -> setAuthorizationBearerIfAbsent(requestContext, token));
        }
    }

    private static void setAuthorizationBearerIfAbsent(final ClientRequestContext requestContext, final String value) {
        requestContext.getHeaders().computeIfAbsent(HttpHeaders.AUTHORIZATION,
                header -> Collections.singletonList("Bearer " + value));
    }
}
