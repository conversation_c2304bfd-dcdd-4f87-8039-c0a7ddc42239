package com.sast.store.commons.basewebapp.health;

import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.actuate.health.CompositeHealth;
import org.springframework.boot.actuate.health.HealthComponent;
import org.springframework.boot.actuate.health.HealthEndpoint;
import org.springframework.boot.actuate.health.Status;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Configuration;

/**
 * provide the result of the health check in prometheus output as well
 */
@Configuration
@ConditionalOnClass(value = { HealthEndpoint.class, MeterRegistry.class })
public class HealthMetricsConfig {
    private static final Logger LOG = LoggerFactory.getLogger(HealthMetricsConfig.class);
    private static final String HEALTH_METRICS = "health";
    @Inject
    private MeterRegistry registry;
    @Inject
    private HealthEndpoint healthEndpoint;

    @PostConstruct
    public void registerHealthMetrics() {
        Gauge.builder(HEALTH_METRICS, healthEndpoint, this::getStatusCode).strongReference(true)
            .register(registry);
    }

    private int getStatusCode(final HealthEndpoint health) {
        final HealthComponent healthComponent = health.health();
        final Status status = healthComponent.getStatus();
        if (Status.UP.equals(status)) {
            return 1;
        }
        if (healthComponent instanceof final CompositeHealth compositeHealth) {
            LOG.warn("Health check failed: {}", compositeHealth.getComponents());
        } else {
            LOG.warn("Health check failed for unknown reason: {}", healthComponent);

        }
        return 0;
    }

}
