package com.sast.store.commons.basewebapp.rest.exception.mapper;

import com.sast.store.commons.basewebapp.rest.error.ApiErrorDto;
import jakarta.annotation.Priority;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.ValidationException;
import jakarta.ws.rs.core.Configuration;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.ext.ExceptionMapper;
import jakarta.ws.rs.ext.Provider;
import lombok.extern.slf4j.Slf4j;
import org.glassfish.jersey.server.ServerProperties;
import org.glassfish.jersey.server.validation.internal.LocalizationMessages;
import org.glassfish.jersey.server.validation.internal.ValidationHelper;

/**
 * This exception mapper is supposed to replace org.glassfish.jersey.server.validation.internal.ValidationExceptionMapper,
 * which does its own content negotiation and defaults to text/plain if the incoming request has no "Accepts" header.
 * Since we want to ensure as much as possible that we return JSON responses, and we're only building APIs here,
 * this mapper returns something functionally similar to jersey's ValidationExceptionMapper, but wrapped in our
 * error DTO and with a JSON body.
 * Since it is supposed to replace a built-in mapper, it is declared with the highest possible priority. Hopefully that
 * will be good enough.
 */
@Provider
@Priority(1)
@Slf4j
public class JsonOnlyValidationExceptionMapper implements ExceptionMapper<ValidationException> {
    private static final String ERROR_CODE = "validationError";

    @Context
    private Configuration config;

    public JsonOnlyValidationExceptionMapper() {
    }

    public Response toResponse(final ValidationException exception) {
        if (exception instanceof final ConstraintViolationException cve) {
            LOG.info(LocalizationMessages.CONSTRAINT_VIOLATIONS_ENCOUNTERED(), exception);

            final ApiErrorDto errorDto = ApiErrorDto.builder()
                    .errorCode(ERROR_CODE)
                    .errorMessage(cve.getMessage())
                    .responseEntity(shouldReturnValidationErrors()
                            ? ValidationHelper.constraintViolationToValidationErrors(cve) : null)
                    .build();

            return Response
                    .status(ValidationHelper.getResponseStatus(cve))
                    .type(MediaType.APPLICATION_JSON_TYPE)
                    .entity(errorDto)
                    .build();
        } else {
            LOG.warn(LocalizationMessages.VALIDATION_EXCEPTION_RAISED(), exception);
            return Response.serverError()
                    .type(MediaType.APPLICATION_JSON)
                    .entity(ApiErrorDto.builder()
                            .errorMessage(exception.getMessage())
                            .errorCode(ERROR_CODE)
                            .build())
                    .build();
        }
    }

    private boolean shouldReturnValidationErrors() {
        final Object outputValidationErrorsProperty = this.config
                .getProperty(ServerProperties.BV_SEND_ERROR_IN_RESPONSE);
        return outputValidationErrorsProperty != null && Boolean.parseBoolean(outputValidationErrorsProperty.toString());
    }
}