package com.sast.store.commons.basewebapp.keycloak;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.core.GrantedAuthorityDefaults;

@Configuration
public class KeyCloakRolePrefixConfig {
    @Bean
    public GrantedAuthorityDefaults grantedAuthorityDefaults() {
        // Remove the default ROLE_ prefix that spring boot otherwise expects
        return new GrantedAuthorityDefaults("");
    }
}
