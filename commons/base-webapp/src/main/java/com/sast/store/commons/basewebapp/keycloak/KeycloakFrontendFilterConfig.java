package com.sast.store.commons.basewebapp.keycloak;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

@Configuration
public class KeycloakFrontendFilterConfig {

    @Value("${keycloak.clientNames:bossstore-frontend,bossstore-backoffice-frontend,bossstore-internal}")
    private Set<String> clientNames;

    @Bean
    public KeycloakTokenFrontendFilter getKeycloakTokenFrontendFilter() {
        return new KeycloakTokenFrontendFilter(clientNames);
    }
}
