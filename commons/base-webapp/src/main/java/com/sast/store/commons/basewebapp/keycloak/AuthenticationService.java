package com.sast.store.commons.basewebapp.keycloak;

import jakarta.ws.rs.NotAuthorizedException;
import jakarta.ws.rs.core.Response;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Component("AuthenticationService")
public class AuthenticationService {

    /**
     * company ID field in keycloak token
     */
    static final String COMPANY_ID = "company_id";
    /**
     * user ID field in keycloak token
     */
    static final String PREFERRED_USERNAME = "preferred_username";

    /**
     * communication language field in keycloak token, e.g. "de"
     */
    static final String COMMUNICATION_LANGUAGE = "communication_language";

    private static final String COMPANY_NAME = "company_name";

    private static final Logger LOG = LoggerFactory.getLogger(AuthenticationService.class);


    private Optional<Jwt> getCurrentPrincipal() {
        return Optional.ofNullable(SecurityContextHolder.getContext())
                .map(SecurityContext::getAuthentication)
                .map(Authentication::getPrincipal)
                .filter(Jwt.class::isInstance).map(Jwt.class::cast);
    }

    private Jwt getCurrentPrincipalOrUnauthorizedException() {
        return getCurrentPrincipal().orElseThrow(() -> {
            final String message = "No keycloak authorization data found.";
            LOG.info(message);
            return new NotAuthorizedException(message,
                    Response.status(Response.Status.UNAUTHORIZED).entity(message).build());
        });
    }

    public String getUserId() {
        return getCurrentPrincipalOrUnauthorizedException().getClaimAsString(PREFERRED_USERNAME);
    }

    public Optional<String> getUserIdIfExists() {
        return getCurrentPrincipal()
                .map(jwt -> jwt.getClaimAsString(PREFERRED_USERNAME))
                .filter(Strings::isNotBlank);
    }

    public String getCompanyId() {
        final Object companyId = getCurrentPrincipalOrUnauthorizedException().getClaimAsString(COMPANY_ID);
        if (companyId instanceof final String string) {
            return string;
        }
        final String message = "Keycloak token did not contain a valid company ID.";
        LOG.info(message);
        throw new NotAuthorizedException(message,
                Response.status(Response.Status.UNAUTHORIZED).entity(message).build());
    }

    public String validateCompanyId(final String companyId) {
        if (companyId == null) {
            return getCompanyId();
        }
        final String companyIdFromToken = getCompanyId();
        if (!companyId.equals(companyIdFromToken)) {
            final String message = "Company ID does not match the one in the token.";
            LOG.info(message);
            throw new NotAuthorizedException(message,
                Response.status(Response.Status.UNAUTHORIZED).entity(message).build());
        }
        return companyId;
    }

    public Optional<String> getCompanyIdIfExists() {
        return getCurrentPrincipal()
                .map(jwt -> jwt.getClaimAsString(COMPANY_ID))
                .filter(Strings::isNotBlank);
    }

    public Optional<String> getCommunicationLanguage() {
        return getCurrentPrincipal()
            .map(jwt -> jwt.getClaimAsString(COMMUNICATION_LANGUAGE))
            .filter(Strings::isNotBlank);
    }

    public Optional<String> getCompanyName() {
        return getCurrentPrincipal()
            .map(jwt -> jwt.getClaimAsString(COMPANY_NAME))
            .filter(Strings::isNotBlank);
    }

    public Set<String> getRoles() {
        final SecurityContext context = SecurityContextHolder.getContext();
        if (context == null || context.getAuthentication() == null
                || context.getAuthentication().getAuthorities() == null) {
            LOG.debug("Authorities not found");
            return Collections.emptySet();
        }
        return context.getAuthentication().getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toSet());
    }

    Optional<Jwt> getKeycloakSecurityContext() {
        return getCurrentPrincipal();
    }

    public UserInfo getUserInfo() {
        return new UserInfo.Builder()
                .companyId(getCompanyId())
                .userId(getUserId())
                .roles(getRoles().stream()
                        .map(UserInfo.UserRole::fromString)
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .collect(Collectors.toSet()))
                .build();
    }
}
