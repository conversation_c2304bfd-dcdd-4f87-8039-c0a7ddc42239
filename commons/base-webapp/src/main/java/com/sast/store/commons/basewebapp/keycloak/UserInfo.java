package com.sast.store.commons.basewebapp.keycloak;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashSet;
import java.util.Optional;
import java.util.Set;

public class UserInfo {
    private static final Logger LOG = LoggerFactory.getLogger(UserInfo.class);

    private final String userId;
    private final String companyId;
    private final Set<UserRole> roles;

    public UserInfo(final Builder builder) {
        this.userId = builder.userId;
        this.companyId = builder.companyId;
        this.roles = builder.roles;
    }

    public String getUserId() {
        return userId;
    }

    public String getCompanyId() {
        return companyId;
    }

    public Set<UserRole> getRoles() {
        return Set.copyOf(roles);
    }

    @Override
    public String toString() {
        return this.getClass().getSimpleName() + " [userid=" + userId + ", companyId=" + companyId + ", roles=" + roles + "]";
    }

    // CHECKSTYLE OFF: HiddenFieldCheck
    public static class Builder {
        private String userId;
        private String companyId;
        private Set<UserRole> roles = new HashSet<>();

        public Builder userId(final String userId) {
            this.userId = userId;
            return this;
        }

        public Builder roles(final Set<UserRole> roles) {
            this.roles = Set.copyOf(roles);
            return this;
        }

        public Builder companyId(final String companyId) {
            this.companyId = companyId;
            return this;
        }

        public UserInfo build() {
            return new UserInfo(this);
        }
    }

    /**
     * Does not encompass all roles that may appear in the token
     */
    // CHECKSTYLE OFF: Javadoc
    public enum UserRole {
        EDIT_PAYMENT_DETAILS,
        PLACE_ORDER,
        PLACE_MOTO_ORDER,
        VIEW_ORDER_DETAILS,
        VIEW_PRICE;

        public static Optional<UserRole> fromString(final String role) {
            try {
                return Optional.of(valueOf(role));
            } catch (final IllegalArgumentException e) {
                LOG.debug("Unknown user role {}", role);
                return Optional.empty();
            }
        }
    }
}
