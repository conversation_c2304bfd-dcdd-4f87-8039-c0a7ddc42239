package com.sast.store.commons.basewebapp.security;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.context.annotation.RequestScope;

import static org.springframework.security.web.util.matcher.AntPathRequestMatcher.antMatcher;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity
public class BaseWebSecurityConfiguration {

    @Order(10)
    @Bean
    public SecurityFilterChain baseFilterChain(final HttpSecurity http) throws Exception {
        return http
            .securityMatchers(s -> s
                .requestMatchers(antMatcher("/rest/**")))
            .csrf(csrf -> csrf.disable())
            .sessionManagement(c -> c.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .oauth2ResourceServer(oauth2 -> oauth2.jwt(Customizer.withDefaults()))
            .build();
    }

    @Bean
    @RequestScope
    public JwtAuthenticationToken bearerToken(final HttpServletRequest request) {
        return (JwtAuthenticationToken) request.getUserPrincipal();
    }
}
