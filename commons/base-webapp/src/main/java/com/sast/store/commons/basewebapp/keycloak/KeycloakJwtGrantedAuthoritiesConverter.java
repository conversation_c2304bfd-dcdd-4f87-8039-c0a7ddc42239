package com.sast.store.commons.basewebapp.keycloak;

import org.springframework.core.convert.converter.Converter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public class KeycloakJwtGrantedAuthoritiesConverter implements Converter<Jwt, Collection<GrantedAuthority>> {

    private final String clientName;

    KeycloakJwtGrantedAuthoritiesConverter(final String clientName) {
        this.clientName = clientName;
    }

    @Override
    public Collection<GrantedAuthority> convert(final Jwt jwt) {
        final Collection<GrantedAuthority> grantedAuthorities = new ArrayList<>();
        for (final String authority : getAuthorities(jwt)) {
            grantedAuthorities.add(new SimpleGrantedAuthority(authority));
        }
        return grantedAuthorities;
    }

    /**
     * extract the client-specific roles from the jwt token
     */
    @SuppressWarnings("unchecked")
    private List<String> getAuthorities(final Jwt jwt) {
        return Optional.ofNullable(jwt.getClaimAsMap("resource_access"))
            .map(claims -> claims.get(clientName))
            .filter(Map.class::isInstance)
            .map(Map.class::cast)
            .map(claim -> claim.get("roles"))
            .filter(List.class::isInstance)
            .map(List.class::cast)
            .orElse(List.of());
    }
}
