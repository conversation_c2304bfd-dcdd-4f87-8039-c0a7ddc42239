package com.sast.store.commons.basewebapp.rest.exception.mapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.sast.store.commons.basewebapp.rest.error.ApiErrorDto;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import jakarta.ws.rs.ext.ExceptionMapper;
import jakarta.ws.rs.ext.Provider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Return generic error text for json deserialization errors to avoid exposing internal information
 */
@Provider
public class JsonProcessingExceptionMapper implements ExceptionMapper<JsonProcessingException> {
    private static final Logger LOG = LoggerFactory.getLogger(JsonProcessingExceptionMapper.class);

    @Override
    public Response toResponse(final JsonProcessingException ex) {
        LOG.info("Caught JsonProcessingException: {}", ex.getMessage());
        LOG.debug(ex.getMessage(), ex);
        return Response.status(Status.BAD_REQUEST)
            .type(MediaType.APPLICATION_JSON)
            .entity(ApiErrorDto.builder()
                    .errorCode(Status.BAD_REQUEST.name())
                    .errorMessage("Error processing json in request")
                    .build())
            .build();
    }
}
