package com.sast.store.commons.basewebapp.rest.exception.mapper;

import com.sast.store.commons.basewebapp.rest.error.ApiErrorDto;
import jakarta.ws.rs.ProcessingException;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import jakarta.ws.rs.ext.ExceptionMapper;
import jakarta.ws.rs.ext.Provider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Maps {@link ProcessingException} to the HTTP 503 service unavailable.
 */
@Provider
public class ProcessingExceptionMapper implements ExceptionMapper<ProcessingException> {

    private static final Logger LOG = LoggerFactory.getLogger(ProcessingExceptionMapper.class);

    @Override
    public Response toResponse(final ProcessingException ex) {
        final Status status = Status.SERVICE_UNAVAILABLE;
        LOG.info("statusCode={}, reason={}, message={}, cause={}", status.getStatusCode(), status.getReasonPhrase(), ex.getMessage(),
            String.valueOf(ex.getCause()));
        return Response.status(status)
                .type(MediaType.APPLICATION_JSON)
                .entity(ApiErrorDto.builder()
                        .errorMessage(ex.getMessage())
                        .errorCode(status.name())
                        .build())
                .build();
    }

}
