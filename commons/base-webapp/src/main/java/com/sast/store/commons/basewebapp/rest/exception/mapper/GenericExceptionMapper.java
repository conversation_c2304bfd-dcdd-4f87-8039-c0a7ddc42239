package com.sast.store.commons.basewebapp.rest.exception.mapper;

import com.sast.store.commons.basewebapp.rest.error.ApiErrorDto;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import jakarta.ws.rs.core.UriInfo;
import jakarta.ws.rs.ext.Provider;
import org.glassfish.jersey.spi.ExtendedExceptionMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.AccessDeniedException;

/**
 * Maps unhandled exceptions to HTTP 503 service unavailable.
 *
 * Do not include information about the exception in the response to avoid exposing potential security issues.
 *
 * Used in gateway services.
 */
@Provider
public class GenericExceptionMapper implements ExtendedExceptionMapper<Exception> {
    private static final Logger LOG = LoggerFactory.getLogger(GenericExceptionMapper.class);

    @Context
    private UriInfo uriInfo;

    @Override
    public Response toResponse(final Exception ex) {
        final Status status = Status.SERVICE_UNAVAILABLE;
        LOG.warn("url={}, queryParams={}, statusCode={}, reason={}, exception={}, message={}, cause={}", uriInfo.getPath(),
            uriInfo.getQueryParameters(), status.getStatusCode(), status.getReasonPhrase(), ex.getClass().getSimpleName(),
            ex.getMessage(), String.valueOf(ex.getCause()), ex);
        return Response.status(status)
                .type(MediaType.APPLICATION_JSON)
                .entity(ApiErrorDto.builder()
                        .errorMessage(status.getReasonPhrase())
                        .errorCode(status.name())
                        .build())
                .build();
    }

    /**
     * Do not handle authentication and authorization issues even if they are not mapped by
     * {@link AccessDeniedExceptionMapper}. Also don't handle {@link WebApplicationException}s.
     */
    @Override
    public boolean isMappable(final Exception exception) {
        return !(exception instanceof AccessDeniedException || exception instanceof WebApplicationException);
    }

}
