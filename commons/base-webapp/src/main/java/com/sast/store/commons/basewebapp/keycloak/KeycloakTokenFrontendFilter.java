package com.sast.store.commons.basewebapp.keycloak;

import jakarta.inject.Inject;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.filter.GenericFilterBean;

import java.io.IOException;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;


public class KeycloakTokenFrontendFilter extends GenericFilterBean {
    private static final Logger LOG = LoggerFactory.getLogger(KeycloakTokenFrontendFilter.class);

    private final Set<String> clientIds;

    @Inject
    private AuthenticationService authenticationService;

    public KeycloakTokenFrontendFilter(final String clientIds) {
        this.clientIds = Set.of(Objects.requireNonNull(clientIds));
    }

    public KeycloakTokenFrontendFilter(final Set<String> clientIds) {
        this.clientIds = Objects.requireNonNull(clientIds);
    }

    @Override
    public void doFilter(final ServletRequest servletRequest, final ServletResponse servletResponse,
            final FilterChain filterChain)
            throws IOException, ServletException {
        final Optional<Jwt> optionalKeycloakSecurityContext = authenticationService.getKeycloakSecurityContext();
        if (optionalKeycloakSecurityContext.isPresent()) {
            final Jwt keycloakSecurityContext = optionalKeycloakSecurityContext.get();
            final String currentClientId = getClientId(keycloakSecurityContext);
            if (!clientIds.contains(currentClientId)) {
                LOG.warn("Received Keycloak token with wrong clientId={} for userId={}", currentClientId,
                        authenticationService.getUserId());
                ((HttpServletResponse) servletResponse).sendError(HttpServletResponse.SC_FORBIDDEN,
                        "Client not allowed");
                return;
            }
        }
        filterChain.doFilter(servletRequest, servletResponse);
    }

    private String getClientId(final Jwt keycloakSecurityContext) {
        return keycloakSecurityContext.getClaimAsString("azp");
    }
}
