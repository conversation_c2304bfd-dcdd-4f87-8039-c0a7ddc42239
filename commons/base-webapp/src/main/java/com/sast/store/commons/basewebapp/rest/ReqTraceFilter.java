package com.sast.store.commons.basewebapp.rest;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.MDC;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.UUID;

// Should be executed before all other filters.
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class ReqTraceFilter implements Filter {
    /**
     * Constant used in http header to recognize id used in logging to trace requests between microservices. Expected to
     * be displayed in logs.
     */
    public static final String REQ_TRACING_ID = "req-tracing-id";

    @Override
    public void doFilter(final ServletRequest req, final ServletResponse resp, final FilterChain chain)
        throws IOException, ServletException {
        final HttpServletRequest servletRequest = (HttpServletRequest) req;
        setupMdcTracing(servletRequest);
        try {
            chain.doFilter(req, resp);
        } finally {
            reset();
        }
    }

    public static void setupMdcTracing(final HttpServletRequest req) {
        headerToMDC(req, REQ_TRACING_ID);
    }

    private static void headerToMDC(final HttpServletRequest request, final String keyName) {
        String traceId = request.getHeader(keyName);
        if (traceId == null) {
            traceId = generateTraceId();
        }
        MDC.put(keyName, traceId);
    }

    public static void reset() {
        MDC.clear();
    }

    private static String generateTraceId() {
        return UUID.randomUUID().toString();
    }
}
