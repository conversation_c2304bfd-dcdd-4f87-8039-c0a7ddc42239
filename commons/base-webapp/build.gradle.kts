plugins {
    id("bossstore.subproject-conventions")
    `java-library`
    id("org.springframework.boot") version "3.4.3" apply false
}

dependencies {
    implementation(project(":commons:jersey-client"))

    implementation(platform(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES))
    implementation("org.springframework:spring-context")
    implementation("org.springframework.boot:spring-boot-starter")
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation("org.springframework.boot:spring-boot-starter-jersey")
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-oauth2-resource-server")
    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.boot:spring-boot-starter-cache")
    implementation("org.springframework.security:spring-security-oauth2-jose")

    implementation("com.fasterxml.jackson.core:jackson-databind")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jdk8")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310")

    implementation("io.micrometer:micrometer-registry-prometheus")
    implementation("org.glassfish.jersey.ext:jersey-micrometer")
    implementation("net.logstash.logback:logstash-logback-encoder:8.0")
    implementation("com.github.ben-manes.caffeine:caffeine")
}
