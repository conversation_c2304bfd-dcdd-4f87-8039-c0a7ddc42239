package com.sast.store.commons.jerseyclient;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.ws.rs.client.Client;
import jakarta.ws.rs.client.ClientBuilder;
import jakarta.ws.rs.ext.ContextResolver;
import org.glassfish.jersey.client.ClientConfig;
import org.glassfish.jersey.client.ClientProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
@ComponentScan
public class JerseyClientConfig {

    private final ObjectMapper objectMapper;
    private final AppTracingIdFilter appTracingIdFilter;
    private final RestMetricsJerseyClientInterceptor restMetricsJerseyClientInterceptor;
    private final ClientErrorHandlingFilter errorHandlingFilter;

    public JerseyClientConfig(final ObjectMapper objectMapper,
                              final AppTracingIdFilter appTracingIdFilter,
                              final RestMetricsJerseyClientInterceptor restMetricsJerseyClientInterceptor,
                              final ClientErrorHandlingFilter errorHandlingFilter) {
        this.objectMapper = objectMapper;
        this.appTracingIdFilter = appTracingIdFilter;
        this.restMetricsJerseyClientInterceptor = restMetricsJerseyClientInterceptor;
        this.errorHandlingFilter = errorHandlingFilter;
    }

    // internal rest client without performance tracing and longer timeouts
    @Bean
    @Primary
    public Client restClient() {
        return ClientBuilder.newClient(new ClientConfig()
            .property(ClientProperties.CONNECT_TIMEOUT, 60_000)
            .property(ClientProperties.READ_TIMEOUT, 60_000)
            .property(ClientProperties.ASYNC_THREADPOOL_SIZE, 100))
            .register(appTracingIdFilter)
            .register(errorHandlingFilter)
            .register(new ContextResolver<ObjectMapper>() {
                // don't convert this to a lambda, it will stop working
                @Override
                public ObjectMapper getContext(final Class<?> type) {
                    return objectMapper;
                }
            });
    }

    // external rest client with performance tracing, timeouts should be slightly
    // shorter than for the internal rest client so we can still handle the timeout
    @Bean
    public Client externalRestClient() {
        return ClientBuilder.newClient(new ClientConfig()
            .property(ClientProperties.CONNECT_TIMEOUT, 30_000)
            .property(ClientProperties.READ_TIMEOUT, 30_000)
            .property(ClientProperties.ASYNC_THREADPOOL_SIZE, 100))
            .register(appTracingIdFilter)
            .register(restMetricsJerseyClientInterceptor)
            .register(new ContextResolver<ObjectMapper>() {
                // don't convert this to a lambda, it will stop working
                @Override
                public ObjectMapper getContext(final Class<?> type) {
                    return objectMapper;
                }
            });
    }
}
