package com.sast.store.commons.jerseyclient;

import jakarta.ws.rs.client.Client;
import jakarta.ws.rs.client.Entity;
import jakarta.ws.rs.client.WebTarget;
import jakarta.ws.rs.core.Form;
import org.glassfish.jersey.client.authentication.HttpAuthenticationFeature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.time.Duration;
import java.time.Instant;

@Component
@ConditionalOnProperty(prefix = "bossstore.internalauthentication", name = "url")
public class InternalAuthenticationTokenManager {
    private static final Logger LOG = LoggerFactory.getLogger(InternalAuthenticationTokenManager.class);
    // refresh token 60 seconds before it expires, so it doesn't suddenly expire during a request
    private static final Duration MIN_VALIDITY = Duration.ofSeconds(60);

    private Token currentToken;
    private Instant expirationTime;

    private final WebTarget tokenService;

    public InternalAuthenticationTokenManager(@Qualifier("externalRestClient") final Client client,
        final ClientRequestResponseLogger clientRequestResponseLogger,
        final RestMetricsJerseyClientInterceptor metricsInterceptor,
        @Value("${bossstore.internalauthentication.url}") final URI serverUrl,
        @Value("${bossstore.internalauthentication.realm}") final String realm,
        @Value("${bossstore.internalauthentication.clientId}") final String clientId,
        @Value("${bossstore.internalauthentication.clientSecret}") final String clientSecret) {

        tokenService = client
            .target(serverUrl).path("realms").path(realm).path("protocol/openid-connect/token")
            .register(clientRequestResponseLogger)
            .property(metricsInterceptor.getName(), metricsInterceptor.load("/realms/{realm}/protocol/openid-connect/token", serverUrl))
            .register(HttpAuthenticationFeature.basic(clientId, clientSecret));
    }

    // token should be valid for 1h, so we regularly check if it's already expired
    @Scheduled(fixedRateString = "PT5M")
    public void refreshAccessToken() {
        getAccessToken();
    }

    public String getAccessTokenString() {
        return getAccessToken().access_token();
    }

    public synchronized Token getAccessToken() {
        if (currentToken == null || isTokenExpired()) {
            getNewToken();
        }
        return currentToken;
    }

    public Token getNewToken() {
        final Form form = new Form()
            .param("grant_type", "client_credentials");
        final Instant requestTime = Instant.now();
        synchronized (this) {
            currentToken = tokenService.request().post(Entity.form(form), Token.class);
            expirationTime = requestTime.plusSeconds(currentToken.expires_in());
        }
        LOG.debug("refreshed internal access token, expires at {}", expirationTime);
        return currentToken;
    }

    private synchronized boolean isTokenExpired() {
        return !Instant.now().plus(MIN_VALIDITY).isBefore(expirationTime);
    }

    public synchronized void expireToken(final String token) {
        if (currentToken == null) {
            return;
        }
        if (token.equals(currentToken.access_token())) {
            // force new token on next request
            expirationTime = Instant.now();
        }
    }

    public static record Token(String access_token, Integer expires_in) {
    }
}
