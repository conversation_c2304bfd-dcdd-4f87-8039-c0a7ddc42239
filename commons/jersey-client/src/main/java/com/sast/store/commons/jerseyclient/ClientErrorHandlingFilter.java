package com.sast.store.commons.jerseyclient;

import jakarta.ws.rs.ClientErrorException;
import jakarta.ws.rs.InternalServerErrorException;
import jakarta.ws.rs.client.ClientRequestContext;
import jakarta.ws.rs.client.ClientResponseContext;
import jakarta.ws.rs.client.ClientResponseFilter;
import jakarta.ws.rs.core.Response.Status.Family;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;

/*
 * Filter that maps error responses from the server to matching ws-rs exceptions.
 * 
 * Should be used in conjunction with client configuration that registers this filter. 
 * 
 * @see JerseyClientConfig
 */
@Component
public class ClientErrorHandlingFilter implements ClientResponseFilter {
    private static final Logger LOG = LoggerFactory.getLogger(ClientErrorHandlingFilter.class);

    @Override
    public void filter(final ClientRequestContext requestContext, final ClientResponseContext responseContext) throws IOException {
        if (Family.familyOf(responseContext.getStatus()) == Family.SUCCESSFUL) {
            return;
        }
        if (Family.familyOf(responseContext.getStatus()) == Family.SERVER_ERROR) {
            LOG.warn("Request {} {} failed with error {}", requestContext.getMethod(), requestContext.getUri(),
                responseContext.getStatus());
            throw new InternalServerErrorException();
        }
        if (Family.familyOf(responseContext.getStatus()) == Family.CLIENT_ERROR) {
            LOG.info("Request {} {} failed with error {}", requestContext.getMethod(), requestContext.getUri(),
                responseContext.getStatus());
            throw new ClientErrorException(responseContext.getStatus());
        }
    }
}