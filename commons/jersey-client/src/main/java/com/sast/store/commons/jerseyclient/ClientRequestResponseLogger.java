package com.sast.store.commons.jerseyclient;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Priority;
import jakarta.inject.Inject;
import jakarta.ws.rs.Priorities;
import jakarta.ws.rs.client.ClientRequestContext;
import jakarta.ws.rs.client.ClientResponseContext;
import jakarta.ws.rs.client.ClientResponseFilter;
import jakarta.ws.rs.core.Form;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response.Status.Family;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

@Component
@Priority(Priorities.USER)
public class ClientRequestResponseLogger implements ClientResponseFilter {
    private Logger log = LoggerFactory.getLogger(ClientRequestResponseLogger.class);

    private final ObjectMapper objectMapper;

    @Inject
    public ClientRequestResponseLogger(final ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    public ClientRequestResponseLogger(final Logger logger) {
        this.log = logger;
        this.objectMapper = new ObjectMapper();
    }

    public ClientRequestResponseLogger(final ObjectMapper objectMapper, final Logger logger) {
        this.log = logger;
        this.objectMapper = objectMapper;
    }

    @Override
    public void filter(final ClientRequestContext requestContext, final ClientResponseContext responseContext) {
        if (!log.isInfoEnabled() && !log.isTraceEnabled()) {
            return;
        }
        if (Family.familyOf(responseContext.getStatus()) == Family.SUCCESSFUL && !log.isTraceEnabled()) {
            return;
        }
        try {
            final StringBuilder stringBuilder = new StringBuilder();
            buildLogRequest(requestContext, stringBuilder);
            buildLogResponse(responseContext, stringBuilder);
            log.info(stringBuilder.toString());
        } catch (final Exception ex) {
            log.error("unhandled exception during logging", ex);
        }
    }

    private void buildLogResponse(final ClientResponseContext responseContext, final StringBuilder stringBuilder) throws IOException {
        stringBuilder.append(" Response received: ")
            .append("Response Status: ")
            .append(responseContext.getStatus())
            .append(", Response Header: ")
            .append(responseContext.getHeaders());

        if (hasBody(responseContext) && readableResponse(responseContext)) {
            stringBuilder.append(", Response Body: ");
            final InputStream stream = logEntityStream(stringBuilder, responseContext.getEntityStream(), StandardCharsets.UTF_8);
            responseContext.setEntityStream(stream);
        }
    }

    private boolean hasBody(final ClientResponseContext responseContext) throws IOException {
        return responseContext.getEntityStream() != null && responseContext.getEntityStream().available() > 0
            && responseContext.hasEntity();
    }

    private void buildLogRequest(final ClientRequestContext requestContext, final StringBuilder stringBuilder)
        throws JsonProcessingException {
        stringBuilder.append("Request send:")
            .append(" Request Url: ")
            .append(requestContext.getMethod())
            .append(" ")
            .append(requestContext.getUri())
            .append(" Request Header: ")
            .append(requestContext.getHeaders());

        if (requestContext.hasEntity() && requestContext.getEntity() instanceof Form) {
            stringBuilder.append(", Request Body: ")
                .append(((Form)(requestContext.getEntity())).asMap());
        } else if (requestContext.hasEntity()) {
            stringBuilder.append(", Request Body: ")
                .append(objectMapper.writeValueAsString(requestContext.getEntity()));
        }
    }

    private boolean readableResponse(final ClientResponseContext responseContext) {
        return MediaType.APPLICATION_JSON_TYPE.isCompatible(responseContext.getMediaType())
            || MediaType.APPLICATION_XML_TYPE.isCompatible(responseContext.getMediaType())
            || new MediaType("text", "*").isCompatible(responseContext.getMediaType());
    }

    private InputStream logEntityStream(final StringBuilder stringBuilder, final InputStream stream, final Charset charset)
        throws IOException {
        final byte[] byteArray = StreamUtils.copyToByteArray(stream);
        stream.close();
        stringBuilder.append(new String(byteArray, charset));
        return new ByteArrayInputStream(byteArray);
    }

}
