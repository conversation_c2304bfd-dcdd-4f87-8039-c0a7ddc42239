package com.sast.store.commons.jerseyclient;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import jakarta.inject.Inject;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.client.ClientRequestContext;
import jakarta.ws.rs.client.ClientResponseContext;
import org.glassfish.jersey.client.spi.PostInvocationInterceptor;
import org.glassfish.jersey.client.spi.PreInvocationInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * expose jersey client request statistics on the prometheus endpoint
 */
@Component
public class RestMetricsJerseyClientInterceptor implements PreInvocationInterceptor, PostInvocationInterceptor {
    private static final Logger LOG = LoggerFactory.getLogger(RestMetricsJerseyClientInterceptor.class);

    private static final String CONTEXT_PROPERTY_START_TIME = RestMetricsJerseyClientInterceptor.class.getName() + "-startTime";
    private static final String CONTEXT_PROPERTY_INTERFACE = RestMetricsJerseyClientInterceptor.class.getName() + "-interface";
    private static final String METRIC_NAME = "http_rest_client_requests";
    private final Map<String, List<String>> knownPartsMap = new HashMap<>();

    @Inject
    private MeterRegistry meterRegistry;

    /**
     * load the interface class and extract jax-rs paths
     *
     * a property with the name CONTEXT_PROPERTY_INTERFACE and the return value of this function should be registered on
     * the webtarget so we can later match it to the request
     *
     * @return the value of the property to be registered
     */
    public String load(final Class<?> proxyInterface, final URI target) {
        // unfortunately we don't have the url template in the ClientRequestContext
        // anymore and the real url path might contain path-parameters that we want to
        // mask, so we split the url templates into tokens and later mask everything else
        final Path root = proxyInterface.getAnnotation(Path.class);
        final String rootPath = root == null ? "" : root.value();
        final String uri = target.getPath() + rootPath;

        final List<String> knownParts = Arrays
            .asList(proxyInterface.getDeclaredMethods()).stream()
            .filter(Objects::nonNull)
            .map(method -> method.getAnnotation(Path.class))
            .filter(Objects::nonNull)
            .map(Path::value)
            .map(path -> uri + path)
            .flatMap(value -> Arrays.stream(value.split("/")))
            .flatMap(value -> Arrays.stream(value.split(":")))
            .filter(value -> !value.startsWith("{"))
            .distinct()
            .sorted()
            .collect(Collectors.toList());

        final String interfaceName = proxyInterface.toString();
        knownPartsMap.put(interfaceName, knownParts);
        LOG.info("extracted url tokens {} from jax-rs interface {}", knownParts, interfaceName);
        return interfaceName;
    }

    public String load(final String pathTemplate, final URI target) {
        final List<String> knownParts = List.of(target.getPath() + pathTemplate)
            .stream()
            .flatMap(value -> Arrays.stream(value.split("/")))
            .flatMap(value -> Arrays.stream(value.split(":")))
            .filter(value -> !value.startsWith("{"))
            .distinct()
            .sorted()
            .collect(Collectors.toList());
        knownPartsMap.put(pathTemplate, knownParts);
        LOG.info("extracted url tokens {} from jax-rs interface {}", knownParts, pathTemplate);
        return pathTemplate;
    }

    public String getName() {
        return CONTEXT_PROPERTY_INTERFACE;
    }

    @Override
    public void beforeRequest(final ClientRequestContext requestContext) {
        // add time of request to requestcontext so we can use it later
        requestContext.setProperty(CONTEXT_PROPERTY_START_TIME, System.nanoTime());
    }

    @Override
    public void afterRequest(final ClientRequestContext requestContext, final ClientResponseContext responseContext) {
        final Long runtime = getRuntime(requestContext);
        if (runtime == null) {
            return;
        }

        // see also
        // org.springframework.boot.actuate.metrics.web.client.DefaultRestTemplateExchangeTagsProvider
        // for similar implementation
        Timer.builder(METRIC_NAME)
            .tag("method", requestContext.getMethod())
            .tag("outcome", responseContext.getStatusInfo().getFamily().toString())
            .tag("status", String.valueOf(responseContext.getStatus()))
            .tag("uri", getMaskedUri(requestContext))
            .tag("clientName", requestContext.getUri().getHost())
            .register(meterRegistry)
            .record(runtime, TimeUnit.NANOSECONDS);
    }

    @Override
    public void onException(final ClientRequestContext requestContext, final ExceptionContext exceptionContext) {
        final Long runtime = getRuntime(requestContext);
        if (runtime == null) {
            return;
        }

        Timer.builder(METRIC_NAME)
            .tag("method", requestContext.getMethod())
            .tag("outcome", "ERROR")
            .tag("status", "unknown")
            .tag("uri", getMaskedUri(requestContext))
            .tag("clientName", requestContext.getUri().getHost())
            .register(meterRegistry)
            .record(runtime, TimeUnit.NANOSECONDS);
    }

    private Long getRuntime(final ClientRequestContext requestContext) {
        final Object startTime = requestContext.getProperty(CONTEXT_PROPERTY_START_TIME);
        if (startTime == null) {
            return null;
        }
        return System.nanoTime() - (long) startTime;
    }

    private String getMaskedUri(final ClientRequestContext requestContext) {
        final List<String> knownParts = knownPartsMap
            .getOrDefault(requestContext.getConfiguration().getProperty(getName()), List.of());
        return maskPath(requestContext.getUri().getPath(), knownParts) + maskQuery(requestContext.getUri().getQuery());
    }

    private String maskQuery(final String query) {
        return query == null ? "" : "?" + query.replaceAll("=[^&]*", "={var}");
    }

    private String maskPath(final String path, final List<String> knownParts) {
        if (path == null) {
            return "";
        }
        return Arrays.stream(path.split("/"))
            .flatMap(value -> Arrays.stream(value.split(":")))
            .map(value -> knownParts.contains(value) ? value : "{var}")
            .collect(Collectors.joining("/"));
    }

}
