package com.sast.store.commons.jerseyclient;

import jakarta.ws.rs.client.ClientRequestContext;
import jakarta.ws.rs.client.ClientRequestFilter;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

@Component
public class AppTracingIdFilter implements ClientRequestFilter {

    /**
     * Constant used in http header to recognize id used in logging to trace requests between microservices. Designed
     * NOT to be set by external application - represents single request propagating through platform. If not provided
     * will be set by filter. Expected to be displayed local logs.
     */
    public static final String REQ_TRACING_ID = "req-tracing-id";

    @Override
    public void filter(final ClientRequestContext requestContext) {
        mdcToHeader(requestContext, REQ_TRACING_ID);
    }

    private void mdcToHeader(final ClientRequestContext requestContext, final String key) {
        final String value = MDC.get(key);
        if (value != null) {
            requestContext.getHeaders().add(key, value);
        }
    }
}
