package com.sast.store.commons.jerseyclient;

import jakarta.ws.rs.client.ClientRequestContext;
import jakarta.ws.rs.client.ClientRequestFilter;
import jakarta.ws.rs.client.ClientResponseContext;
import jakarta.ws.rs.client.ClientResponseFilter;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
@ConditionalOnProperty(prefix = "bossstore.internalauthentication", name = "url")
public class InternalAuthenticationFilter implements ClientRequestFilter, ClientResponseFilter {
    private static final Logger LOG = LoggerFactory.getLogger(InternalAuthenticationFilter.class);

    private static final String BEARER_PREFIX = "Bearer ";
    private final InternalAuthenticationTokenManager tokenManager;

    public InternalAuthenticationFilter(final InternalAuthenticationTokenManager tokenManager) {
        this.tokenManager = tokenManager;
    }

    @Override
    public void filter(final ClientRequestContext request) throws IOException {
        if (request.getHeaders().containsKey(HttpHeaders.AUTHORIZATION)) {
            return;
        }
        final String authHeader = BEARER_PREFIX + tokenManager.getAccessTokenString();
        request.getHeaders().putSingle(HttpHeaders.AUTHORIZATION, authHeader);
    }

    @Override
    public void filter(final ClientRequestContext request, final ClientResponseContext response) throws IOException {
        if (response.getStatus() != Response.Status.UNAUTHORIZED.getStatusCode()) {
            return;
        }
        if (request.getHeaders().containsKey(HttpHeaders.AUTHORIZATION)) {
            return;
        }
        LOG.warn("unexpected 401 from {} {} is url/clientid/clientsecret correct?", request.getMethod(), request.getUri());
        final String headerValue = request.getHeaderString(HttpHeaders.AUTHORIZATION);
        if (headerValue.startsWith(BEARER_PREFIX)) {
            // if we get 401 we assume the token was invalid, however this should actually never happen as the token
            // manager should have already refreshed the token
            final String token = headerValue.substring(BEARER_PREFIX.length());
            tokenManager.expireToken(token);
        }
    }
}