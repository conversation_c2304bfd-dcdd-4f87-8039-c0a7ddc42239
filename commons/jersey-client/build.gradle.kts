plugins {
    id("bossstore.subproject-conventions")
    `java-library`
    id("org.springframework.boot") version "3.4.3" apply false
}

dependencies {
    implementation(platform(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES))
    implementation("org.springframework:spring-context")
    implementation("org.springframework.boot:spring-boot-autoconfigure")

    implementation("org.slf4j:slf4j-api")
    implementation("com.fasterxml.jackson.core:jackson-databind")
    implementation("org.glassfish.jersey.core:jersey-client")
    implementation("org.glassfish.jersey.ext:jersey-proxy-client")
    implementation("io.micrometer:micrometer-registry-prometheus")
}
