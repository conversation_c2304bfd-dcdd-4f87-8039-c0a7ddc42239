#!/bin/bash
set -eu

TEMPORAL_MIN_CONFIG="temporal-minimal.yml"
TEMPORAL_CONFIG="temporal-psql.yml"

function usage {
	echo "Docker compose wrapper script"
	echo "Options:"
	echo " -d	Start in detached mode"
	echo " -m	Start temporal minimal setup (no postgresql)"
}	

while getopts "dm" opt; do
	case "$opt" in
		d)	ADD_ARGS=" -d"; ;;	
		m)	TEMPORAL_CONFIG=${TEMPORAL_MIN_CONFIG}; ;;
		h|*)	usage; exit 2; ;;
	esac
done


docker compose -f ${TEMPORAL_CONFIG} up --force-recreate ${ADD_ARGS:-}
