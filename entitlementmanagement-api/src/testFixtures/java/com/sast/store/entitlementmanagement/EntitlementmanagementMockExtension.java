package com.sast.store.entitlementmanagement;

import com.github.tomakehurst.wiremock.common.ClasspathFileSource;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.github.tomakehurst.wiremock.junit5.WireMockExtension;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.delete;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.matching;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathTemplate;

public class EntitlementmanagementMockExtension extends WireMockExtension {

    public static final int PORT = 34644;

    private static EntitlementmanagementMockExtension instance;

    public EntitlementmanagementMockExtension() {
        super(WireMockExtension
            .extensionOptions()
            .options(WireMockConfiguration.options()
                .fileSource(new ClasspathFileSource("entitlementmanagement"))
                .port(PORT))
            .configureStaticDsl(true));
        instance = this;
    }

    public static EntitlementmanagementMockExtension instance() {
        return instance;
    }

    public static EntitlementmanagementMockExtension withDefaultResponse() {
        withAssignLicensesResponse();
        withGetLicensesResponse();
        withUnassignLicenseResponse();
        return instance;
    }

    public static EntitlementmanagementMockExtension withAssignLicensesResponse() {
        instance.stubFor(post(urlPathEqualTo("/rest/licenses/"))
            .willReturn(aResponse()
                .withStatus(204)
                .withHeader("content-type", "application/json")));

        return instance;
    }

    public static EntitlementmanagementMockExtension withGetLicensesResponse() {
        instance.stubFor(get(urlPathEqualTo("/rest/licenses/"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json")
                .withBodyFile("get-licenses-response.json")));

        return instance;
    }

    public static EntitlementmanagementMockExtension withUnassignLicenseResponse() {
        instance.stubFor(delete(urlPathTemplate("/rest/licenses/{licenseId}"))
            .willReturn(aResponse()
                .withStatus(204)
                .withHeader("content-type", "application/json")));

        return instance;
    }

    public static EntitlementmanagementMockExtension withUnassignLicenseNotFoundResponse() {
        instance.stubFor(delete(urlPathTemplate("/rest/licenses/{licenseId}"))
            .willReturn(aResponse()
                .withStatus(404)
                .withHeader("content-type", "application/json")));

        return instance;
    }

    public static EntitlementmanagementMockExtension withGetDefaultsResponse() {
        instance.stubFor(get(urlPathTemplate("/rest/licenses/defaults"))
            .withQueryParam("contractIds", matching(".*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json")
                .withBodyFile("get-defaults-response.json")));

        return instance;
    }
}
