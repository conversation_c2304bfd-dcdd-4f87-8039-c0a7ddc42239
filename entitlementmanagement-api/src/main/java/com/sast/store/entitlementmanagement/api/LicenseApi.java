package com.sast.store.entitlementmanagement.api;

import com.sast.store.commons.tenant.api.Tenant;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;

import java.util.List;

@Path("/licenses")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public interface LicenseApi {

    @GET
    @Path("/")
    List<LicenseDto> getLicenses(@NotNull @HeaderParam("X-Tenant") Tenant tenantId);

    @GET
    @Path("/defaults")
    List<AssignLicenseDto> getDefaults(@NotNull @HeaderParam("X-Tenant") Tenant tenantId,
        @NotNull @QueryParam("contractIds") List<String> contractIds);

    @POST
    @Path("/")
    void assignLicenses(@NotNull @HeaderParam("X-Tenant") Tenant tenantId, @NotNull List<@Valid AssignLicenseDto> assignLicenseDtos);

    @DELETE
    @Path("/{licenseId}")
    void unassignLicense(@NotNull @HeaderParam("X-Tenant") Tenant tenantId, @NotNull @PathParam("licenseId") String licenseId);
}
