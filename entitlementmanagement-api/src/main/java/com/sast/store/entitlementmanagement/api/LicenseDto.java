package com.sast.store.entitlementmanagement.api;

import jakarta.validation.constraints.NotNull;

import java.util.List;

public record LicenseDto(
    @NotNull String licenseId,
    @NotNull LicenseModel licenseModel,
    @NotNull String contractId,
    // remove once ui is migrated
    String email,
    String firstname,
    String lastname,
    //
    LitmosLicenseDto litmos,
    DcKeycloakLicenseDto dcKeycloak,
    BcCentralLicenseDto bcCentral) {

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {
        private String licenseId;
        private String contractId;
        private String email;
        private String firstname;
        private String lastname;
        private LicenseModel licenseModel;
        private LitmosLicenseDto litmos;
        private DcKeycloakLicenseDto dcKeycloak;
        private BcCentralLicenseDto bcCentral;

        public Builder licenseId(final String licenseId) {
            this.licenseId = licenseId;
            return this;
        }

        public Builder contractId(final String contractId) {
            this.contractId = contractId;
            return this;
        }

        public Builder email(final String email) {
            this.email = email;
            return this;
        }

        public Builder firstname(final String firstname) {
            this.firstname = firstname;
            return this;
        }

        public Builder lastname(final String lastname) {
            this.lastname = lastname;
            return this;
        }

        public Builder licenseModel(final LicenseModel licenseModel) {
            this.licenseModel = licenseModel;
            return this;
        }

        public Builder litmos(final LitmosLicenseDto litmos) {
            this.litmos = litmos;
            return this;
        }

        public Builder dcKeycloak(final DcKeycloakLicenseDto dcKeycloak) {
            this.dcKeycloak = dcKeycloak;
            return this;
        }

        public Builder bcCentral(final BcCentralLicenseDto bcCentral) {
            this.bcCentral = bcCentral;
            return this;
        }

        public LicenseDto build() {
            return new LicenseDto(licenseId, licenseModel, contractId, email, firstname, lastname, litmos, dcKeycloak, bcCentral);
        }
    }

    public record LitmosLicenseDto(
        String firstname,
        String lastname,
        String email) {

        public static Builder builder() {
            return new Builder();
        }

        public static final class Builder {
            private String firstname;
            private String lastname;
            private String email;

            public Builder firstname(final String firstname) {
                this.firstname = firstname;
                return this;
            }

            public Builder lastname(final String lastname) {
                this.lastname = lastname;
                return this;
            }

            public Builder email(final String email) {
                this.email = email;
                return this;
            }

            public LitmosLicenseDto build() {
                return new LitmosLicenseDto(firstname, lastname, email);
            }
        }

    }

    public record DcKeycloakLicenseDto(
        String firstname,
        String lastname,
        String email) {

        public static Builder builder() {
            return new Builder();
        }

        public static final class Builder {
            private String firstname;
            private String lastname;
            private String email;

            public Builder firstname(final String firstname) {
                this.firstname = firstname;
                return this;
            }

            public Builder lastname(final String lastname) {
                this.lastname = lastname;
                return this;
            }

            public Builder email(final String email) {
                this.email = email;
                return this;
            }

            public DcKeycloakLicenseDto build() {
                return new DcKeycloakLicenseDto(firstname, lastname, email);
            }
        }

    }

    public record BcCentralLicenseDto(
        String name,
        List<String> emails) {

        public static Builder builder() {
            return new Builder();
        }

        public static final class Builder {
            private String name;
            private List<String> emails;

            public Builder name(final String name) {
                this.name = name;
                return this;
            }

            public Builder emails(final List<String> emails) {
                this.emails = emails;
                return this;
            }

            public BcCentralLicenseDto build() {
                return new BcCentralLicenseDto(name, emails);
            }
        }

    }

}
