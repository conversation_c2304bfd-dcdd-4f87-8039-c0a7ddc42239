package com.sast.store.entitlementmanagement.api;

import jakarta.validation.Valid;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Builder;

import java.util.List;

@Builder
public record AssignLicenseDto(
    // remove once ui is migrated
    String firstname,
    String lastname,
    String email,
    String contractId,
    //
    LicenseModel licenseModel,
    @Valid LitmosAssignLicenseDto litmos,
    @Valid DcKeycloakAssignLicenseDto dcKeycloak,
    @Valid BcCentralAssignLicenseDto bcCentral) {

    @AssertTrue(message = "invalid payload")
    public boolean isValid() {
        // CHECKSTYLE OFF: BooleanExpressionComplexity
        return licenseModel == null && firstname != null && lastname != null && email != null && contractId != null
            || licenseModel == LicenseModel.LITMOS && litmos != null
            || licenseModel == LicenseModel.BCCENTRAL && bcCentral != null
            || licenseModel == LicenseModel.DCKEYCLOAK && dcKeycloak != null;
    }

    public interface AssignLicenseData {
        String contractId();
    }

    @Builder
    public record LitmosAssignLicenseDto(
        @NotBlank String firstname,
        @NotBlank String lastname,
        @NotBlank String email,
        @NotBlank String contractId) implements AssignLicenseData {
    }

    @Builder
    public record DcKeycloakAssignLicenseDto(
        @NotBlank String firstname,
        @NotBlank String lastname,
        @NotBlank String email,
        @NotBlank String contractId) implements AssignLicenseData {
    }

    @Builder
    public record BcCentralAssignLicenseDto(
        @NotBlank String contractId,
        @NotBlank String name,
        @NotNull @Size(min = 1, max = 10) List<String> emails) implements AssignLicenseData {
    }

}
