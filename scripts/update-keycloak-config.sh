#!/usr/bin/env bash
set -eu

<PERSON><PERSON><PERSON>CL<PERSON><PERSON>_CONTAINER_NAME="boss-store-keycloak"

KEYCLOAK_CONTAINER_ID=$(docker ps -q -f "name=${<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_CONTAINER_NAME}")

if [[ -z "${<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_CONTAINER_ID}" ]] ; then
  echo "Could not find running container with name '${<PERSON><PERSON><PERSON><PERSON><PERSON>K_CONTAINER_NAME}'. Did you start keycloak?"
  exit 1
fi

echo
echo "Using keycloak container with id=${KEYCLOAK_CONTAINER_ID}."
echo
echo "Exporting keycloak configuration."
docker compose -f ../docker-compose.yml exec keycloak /opt/keycloak/bin/export-data.sh

echo
echo "Copying keycloak configuration to docker_compose_config/keycloak/realm-and-users/"
docker cp ${KEYCLOAK_CONTAINER_NAME}:/tmp/realms-and-users/ ../keycloak/

echo
echo "Keycloak configuration exported. Don't forget to commit your changes."
