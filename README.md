# BOSS Store

## Setup project
configure ~/.gradle/gradle.properties:

```sh
nexusUsername=
nexusPassword=
nexusUrl=https://nexus.ci.sastdev.net
```

run `./gradlew build`

## Changelog
automatically generated after each live deployment:
[Gitlab:Releases](https://gitlab.sastdev.net/SAST/store/replatforming/boss-store/-/releases)

## Configuration

see https://gitlab.sastdev.net/SAST/store/replatforming/boss-store-config

## Structure

![](gitlab-ci/architecture.svg)

## Local development

### Frontend
- in `frontend/ui`
  - run `yarn dev` to startup ui locally, connecting to dev keycloak and dev backend services
  - run `yarn local` to startup ui locally, connecting to dev keycloak and local backend services

### Local services
The backend services can be run locally, either though IntelliJ or Gradle.

#### IntelliJ
This repository contains preconfigured IntelliJ run configurations for starting
the backend services locally. To start all applications at once run the
`All Local Services` configuration.

#### Gradle
The backend services can be executed using a local configuration by running 
the `bootTestRun` task. To start all applications at once execute
`./gradlew --parallel bootTestRun`.

### Temporal
To be able to run the `entitlementmanagement` locally, the temporal server
should be running. The `local-infra/start-temporal.sh` starts the `docker
compose` configuration containing of the Postgresql Server and necessary
Temporal containers (server, admin tools and web-ui). The `default` workflow
namespace is available.

The web UI is available on [localhost:18080](http://localhost:18080)
The Temporal server is forwarded to the localhost on the default port 7233
