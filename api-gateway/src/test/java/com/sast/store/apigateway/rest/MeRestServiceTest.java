package com.sast.store.apigateway.rest;

import com.sast.store.apigateway.AbstractComponentTest;
import com.sast.store.ordermanagement.test.OrdermanagementMockExtension;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import org.junit.jupiter.api.Test;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching;
import static org.hamcrest.Matchers.equalTo;

class MeRestServiceTest extends AbstractComponentTest {

    @Test
    void getCompany_withoutAuthentication_returns401() {
        RestAssured
            .given()
            .accept(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when()
            .get(host + "/rest/me/company")
            .then()
            .statusCode(401);
    }

    @Test
    void getCompany_wrongAuthentication_returns403() {
        setupKeycloakToken("OTHER_ROLE");
        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when()
            .get(host + "/rest/me/company")
            .then()
            .statusCode(403);
    }

    @Test
    void getCompany_withAuthentication_returnsCompany() {
        setupKeycloakToken("DEFAULT");
        OrdermanagementMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when()
            .get(host + "/rest/me/company")
            .then()
            .statusCode(200)
            .body("name", equalTo("UPM Tester"))
            .body("country", equalTo("AT"));
    }

    @Test
    void getCompany_withAuthenticationAndServerError_returnsServerError() {
        setupKeycloakToken("DEFAULT");
        OrdermanagementMockExtension.instance().stubFor(get(urlPathMatching("/rest/me/company"))
            .willReturn(aResponse()
                .withStatus(503)));

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when()
            .get(host + "/rest/me/company")
            .then()
            .statusCode(500);
    }
}
