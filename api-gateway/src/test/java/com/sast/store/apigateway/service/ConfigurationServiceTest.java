package com.sast.store.apigateway.service;

import com.sast.store.apigateway.AbstractComponentTest;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.external.countriesservice.api.CountriesServiceClient;
import com.sast.store.external.countriesservice.api.CountryDto;
import com.sast.store.external.countriesservice.api.CountryDto.TenantConfigurationDto;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

class ConfigurationServiceTest extends AbstractComponentTest {

    @Mock
    private CountriesServiceClient countriesServiceClient;

    @InjectMocks
    private ConfigurationService configurationService;

    @Test
    void getConfiguration_onlyReturnsEnabledCountries() {
        when(countriesServiceClient.listAllCountries(com.sast.store.external.countriesservice.api.Tenant.rexroth))
            .thenReturn(List.of(
                new CountryDto.Builder()
                    .activeInStore(true)
                    .isoCode("DE")
                    .tenantConfigurations(List.of(new TenantConfigurationDto.Builder()
                        .tenant("REXROTH")
                        .storefrontEnabled(true)
                        .build()))
                    .build(),
                new CountryDto.Builder()
                    .activeInStore(false)
                    .isoCode("US")
                    .build()
            ));

        final var result = configurationService.getConfiguration(Tenant.REXROTH);

        assertThat(result.countries())
            .flatExtracting(com.sast.store.apigateway.api.CountryDto::code)
            .containsExactly("DE");
    }

    @Test
    void getConfiguration_throwsWhenTenantConfigurationIsMissing() {
        when(countriesServiceClient.listAllCountries(com.sast.store.external.countriesservice.api.Tenant.rexroth))
            .thenReturn(List.of(
                new CountryDto.Builder()
                    .activeInStore(true)
                    .isoCode("DE")
                    .tenantConfigurations(List.of(new TenantConfigurationDto.Builder()
                        .tenant("AZENA")
                        .build()))
                    .build()
            ));

        assertThatThrownBy(() -> configurationService.getConfiguration(Tenant.REXROTH))
            .isInstanceOf(IllegalStateException.class);
    }
}
