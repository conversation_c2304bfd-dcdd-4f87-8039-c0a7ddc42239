package com.sast.store.apigateway.rest;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.sast.store.apigateway.AbstractComponentTest;
import com.sast.store.ordermanagement.test.OrdermanagementMockExtension;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import org.junit.jupiter.api.Test;

import static org.hamcrest.Matchers.equalTo;

class CommercetoolsExtensionRestServiceTest extends AbstractComponentTest {

    @Test
    public void testUnauthorized() {
        RestAssured
            .given()
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .body("{}")
            .when().post(host + "/rest/commercetools/cartvalidation")
            .then().statusCode(401);
    }

    @Test
    public void testWrongPassword() {
        RestAssured
            .given()
            .contentType(ContentType.JSON)
            .auth().basic("user", "wrongpassword")
            .header("X-Tenant", "rexroth")
            .body("{}")
            .when().post(host + "/rest/commercetools/cartvalidation")
            .then().statusCode(401);
    }

    @Test
    public void testPayload() {
        OrdermanagementMockExtension.withDefaultResponse();

        RestAssured
            .given()
            .contentType(ContentType.JSON)
            .auth().basic("user", "password")
            .header("X-Tenant", "rexroth")
            .body("{\"resource\": \"whatever\"}")
            .when().post(host + "/rest/commercetools/cartvalidation")
            .then().statusCode(200);

        OrdermanagementMockExtension.instance().verify(1, WireMock.postRequestedFor(WireMock
            .urlEqualTo("/rest/commercetools/cartvalidation"))
            .withHeader("Authorization", WireMock.containing("Bearer ")));
    }

    @Test
    public void testValidationFailed() {
        OrdermanagementMockExtension.withCartValidationFailedResponse();

        RestAssured
            .given()
            .contentType(ContentType.JSON)
            .auth().basic("user", "password")
            .header("X-Tenant", "rexroth")
            .body("{\"resource\": \"whatever\"}")
            .when().post(host + "/rest/commercetools/cartvalidation")
            .then().statusCode(400)
            .body("errors[0].code", equalTo("InvalidInput"))
            .body("errors[0].message", equalTo("cartvalidation failed"));

    }

    @Test
    public void testPing() {
        RestAssured
            .given()
            .contentType(ContentType.JSON)
            .when().get(host + "/rest/commercetools/ping")
            .then().statusCode(200);
    }
}
