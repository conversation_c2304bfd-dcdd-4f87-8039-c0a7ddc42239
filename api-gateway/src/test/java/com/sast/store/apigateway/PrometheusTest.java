package com.sast.store.apigateway;

import io.restassured.RestAssured;
import org.junit.jupiter.api.Test;

import static org.hamcrest.Matchers.containsString;

class PrometheusTest extends AbstractComponentTest {

    @Test
    void testPrometheusCheck() {
        RestAssured
            .given()
            .when().get(host + "/actuator/prometheus")
            .then().statusCode(200)
            .body(containsString("health 1.0"))
            .body(containsString(
                """
                    http_rest_client_requests_seconds_max{clientName="localhost",method="POST",\
                    outcome="SUCCESSFUL",status="200",uri="/auth/realms/{var}/protocol/openid-connect/token"}"""));

    }
}