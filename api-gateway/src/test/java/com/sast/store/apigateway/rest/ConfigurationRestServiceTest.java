package com.sast.store.apigateway.rest;

import com.sast.store.apigateway.AbstractComponentTest;
import com.sast.store.external.countriesservice.util.CountriesServiceMockExtension;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import org.junit.jupiter.api.Test;

import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasSize;

class ConfigurationRestServiceTest extends AbstractComponentTest {

    @Test
    void getConfiguration_withoutAuthentication_returnsConfiguration() {
        CountriesServiceMockExtension.withDefaultResponse();

        RestAssured
            .given()
            .accept(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when()
            .get(host + "/rest/configuration")
            .then()
            .statusCode(200)
            .body("tenant", equalTo("rexroth"))
            .body("countries", hasSize(2))
            .body("countries[0].code", equalTo("DE"))
            .body("countries[0].languages", containsInAnyOrder("de", "en"))
            .body("countries[0].defaultLanguage", equalTo("de"))
            .body("countries[1].code", equalTo("AT"))
            .body("countries[1].languages", containsInAnyOrder("de", "en"))
            .body("countries[1].defaultLanguage", equalTo("de"));
    }
}
