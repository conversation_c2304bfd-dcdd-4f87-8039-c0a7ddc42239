package com.sast.store.apigateway.rest;

import com.sast.store.apigateway.AbstractComponentTest;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import org.junit.jupiter.api.Test;

class InvoiceRestServiceTest extends AbstractComponentTest {

    @Test
    public void testGetInvoiceUnauthorized() {
        RestAssured
            .given()
            .contentType(ContentType.JSON)
            .accept("application/octet-stream")
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/invoices/123456")
            .then().statusCode(401);
    }

    @Test
    public void testGetInvoiceForbidden() {
        setupKeycloakToken("OTHER_ROLE");
        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .accept("application/octet-stream")
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/invoices/123456")
            .then().statusCode(403);
    }

    @Test
    public void testPing() {
        RestAssured
            .given()
            .contentType(ContentType.JSON)
            .when().get(host + "/rest/invoices/ping")
            .then().statusCode(200);
    }
}
