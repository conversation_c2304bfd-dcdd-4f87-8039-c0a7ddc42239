package com.sast.store.apigateway.config;

import jakarta.validation.constraints.NotNull;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

@ConfigurationProperties(prefix = "bossstore.cors")
@Validated
public record CorsConfiguration(
    @NotNull String origin,
    @NotNull String methods,
    @NotNull String headers) {
}
