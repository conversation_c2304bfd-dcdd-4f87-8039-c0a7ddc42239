package com.sast.store.apigateway.config;

import jakarta.validation.constraints.NotNull;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.net.URI;

@ConfigurationProperties(prefix = "bossstore")
@Validated
public record AppConfiguration(
    @NotNull URI ordermanagementUrl,
    @NotNull Commercetools commercetools) {

    public record Commercetools(@NotNull String username, @NotNull String password) {
    }
}
