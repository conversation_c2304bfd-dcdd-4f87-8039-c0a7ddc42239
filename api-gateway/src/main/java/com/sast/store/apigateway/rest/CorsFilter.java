package com.sast.store.apigateway.rest;

import com.sast.store.apigateway.config.CorsConfiguration;
import jakarta.ws.rs.container.ContainerRequestContext;
import jakarta.ws.rs.container.ContainerResponseContext;
import jakarta.ws.rs.container.ContainerResponseFilter;
import jakarta.ws.rs.ext.Provider;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

@Component
@Provider
@RequiredArgsConstructor
public class CorsFilter implements ContainerResponseFilter {

    private final CorsConfiguration corsConfiguration;

    @Override
    public void filter(final ContainerRequestContext requestContext, final ContainerResponseContext responseContext) {
        final var headers = responseContext.getHeaders();
        headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, corsConfiguration.origin());
        headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, corsConfiguration.headers());
        headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, corsConfiguration.methods());
    }
}
