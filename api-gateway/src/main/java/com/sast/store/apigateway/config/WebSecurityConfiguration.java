package com.sast.store.apigateway.config;

import com.sast.store.commons.basewebapp.security.BaseWebSecurityConfiguration;
import jakarta.inject.Inject;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;

import static org.springframework.security.web.util.matcher.AntPathRequestMatcher.antMatcher;

@Configuration
public class WebSecurityConfiguration {

    /**
     * also see {@link BaseWebSecurityConfiguration}
     */
    @Order(1)
    @Bean
    public SecurityFilterChain commercetoolsFilterChain(final HttpSecurity http) throws Exception {
        return http
            .securityMatchers(s -> s
                .requestMatchers(antMatcher("/rest/commercetools/**")))
            .csrf(csrf -> csrf.disable())
            .sessionManagement(c -> c.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(r -> r
                .requestMatchers(antMatcher("/rest/commercetools/ping")).anonymous()
                .anyRequest().authenticated())
            .httpBasic(Customizer.withDefaults())
            .build();
    }

    @Inject
    public void configureCommercetoolsUser(final AuthenticationManagerBuilder auth, final AppConfiguration appConfiguration,
        final PasswordEncoder passwordEncoder)
        throws Exception {
        auth
            .inMemoryAuthentication()
            .withUser(appConfiguration.commercetools().username())
            .password(passwordEncoder.encode(appConfiguration.commercetools().password()))
            .authorities("ROLE_COMMERCETOOLS");
    }
}