package com.sast.store.apigateway;

import com.sast.store.commons.EnableCommonsAutoconfiguration;
import com.sast.store.external.EnableExternalClientsAutoconfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.cache.annotation.EnableCaching;

@SpringBootApplication
@EnableCommonsAutoconfiguration
@EnableExternalClientsAutoconfiguration
@ConfigurationPropertiesScan
@EnableCaching
public class ApigatewayApplication {

    public static void main(final String[] args) {
        SpringApplication.run(ApigatewayApplication.class, args);
    }

}
