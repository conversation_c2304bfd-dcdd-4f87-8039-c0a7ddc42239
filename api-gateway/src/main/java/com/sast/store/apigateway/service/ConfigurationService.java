package com.sast.store.apigateway.service;

import com.sast.store.apigateway.api.ConfigurationDto;
import com.sast.store.apigateway.api.CountryDto;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.external.countriesservice.api.CountriesServiceClient;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class ConfigurationService {

    private final CountriesServiceClient countriesServiceClient;

    public ConfigurationDto getConfiguration(@NonNull final Tenant tenant) {
        final var apiTenant = com.sast.store.external.countriesservice.api.Tenant.valueOf(tenant.id());
        final var countries = countriesServiceClient.listAllCountries(apiTenant);

        return ConfigurationDto.builder()
            .tenant(tenant)
            .countries(countries.stream()
                .filter(com.sast.store.external.countriesservice.api.CountryDto::isActiveInStore)
                .map(country -> toCountryDto(apiTenant, country))
                .flatMap(Optional::stream)
                .toList())
            .defaultCountry(toDefaultCountry(tenant))
            .build();
    }

    private Optional<CountryDto> toCountryDto(final com.sast.store.external.countriesservice.api.Tenant tenant,
        final com.sast.store.external.countriesservice.api.CountryDto countryDto) {
        final var tenantConfigurationDto = countryDto.getTenantConfigurations().stream()
            .filter(dto -> Objects.equals(dto.getTenant(), CountriesServiceClient.toTenant(tenant).name()))
            .findFirst()
            .orElseThrow(() -> new IllegalStateException(
                "Expected country tenant configuration is missing for tenant %s in country %s".formatted(tenant, countryDto.getIsoCode())));

        if (Objects.equals(tenantConfigurationDto.isStorefrontEnabled(), Boolean.TRUE)) {
            return Optional.of(CountryDto.builder()
                .code(countryDto.getIsoCode())
                .languages(tenantConfigurationDto.getLanguages())
                .defaultLanguage(tenantConfigurationDto.getDefaultLanguage())
                .build());
        } else {
            return Optional.empty();
        }
    }

    private String toDefaultCountry(final Tenant tenant) {
        // TODO this mapping should come from a tenant configuration service we'll introduce in the future
        return switch (tenant) {
            case AZENA, REXROTH -> "DE";
            case BAAM -> "AT";
        };
    }
}
