package com.sast.store.apigateway.rest;

import com.sast.store.apigateway.api.ConfigurationDto;
import com.sast.store.apigateway.service.ConfigurationService;
import com.sast.store.commons.basewebapp.security.Unprotected;
import com.sast.store.commons.tenant.api.Tenant;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.springframework.stereotype.Component;

@Component
@Path("/configuration")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class ConfigurationRestService {

    private final ConfigurationService configurationService;

    public ConfigurationRestService(final ConfigurationService configurationService) {
        this.configurationService = configurationService;
    }

    @GET
    @Unprotected
    public ConfigurationDto getConfiguration(@NotNull @HeaderParam("X-Tenant") final Tenant tenant) {
        return configurationService.getConfiguration(tenant);
    }
}
