package com.sast.store.apigateway.rest;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.ordermanagement.api.CompanyDto;
import com.sast.store.ordermanagement.api.MeApi;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;

@Component
@Path("/me")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class MeRestService {

    private final MeApi meApi;

    public MeRestService(final MeApi meApi) {
        this.meApi = meApi;
    }

    @GET
    @Path("/company")
    @PreAuthorize("hasRole('DEFAULT')")
    public CompanyDto getCompany(@NotNull @HeaderParam("X-Tenant") final Tenant tenant) {
        return meApi.getCompany(tenant);
    }
}
