package com.sast.store.apigateway.config;

import com.sast.store.commons.basewebapp.keycloak.KeycloakTokenClientRequestFilter;
import com.sast.store.commons.jerseyclient.InternalAuthenticationFilter;
import com.sast.store.ordermanagement.api.CommercetoolsExtensionApi;
import com.sast.store.ordermanagement.api.InvoiceApi;
import com.sast.store.ordermanagement.api.MeApi;
import jakarta.inject.Inject;
import jakarta.ws.rs.Priorities;
import jakarta.ws.rs.client.Client;
import org.glassfish.jersey.client.proxy.WebResourceFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.URI;

@Configuration
public class RestServiceProducer {

    @Inject
    private Client restClient;

    @Inject
    private AppConfiguration appConfiguration;

    @Inject
    private KeycloakTokenClientRequestFilter keycloakTokenClientRequestFilter;

    @Inject
    private InternalAuthenticationFilter internalAuthenticationFilter;

    @Bean
    public InvoiceApi getInvoiceRestService() {
        return getKeycloakTokenProxy(appConfiguration.ordermanagementUrl(), InvoiceApi.class);
    }

    @Bean
    public MeApi getMeApiService() {
        return getKeycloakTokenProxy(appConfiguration.ordermanagementUrl(), MeApi.class);
    }

    @Bean
    public CommercetoolsExtensionApi getExtensionApiService() {
        return getProxy(appConfiguration.ordermanagementUrl(), CommercetoolsExtensionApi.class);
    }

    private <T> T getProxy(final URI url, final Class<T> proxyInterface) {
        return WebResourceFactory.newResource(proxyInterface, restClient.target(url)
            .register(keycloakTokenClientRequestFilter, Priorities.AUTHORIZATION)
            .register(internalAuthenticationFilter, Priorities.AUTHORIZATION));
    }

    private <T> T getKeycloakTokenProxy(final URI url, final Class<T> proxyInterface) {
        return WebResourceFactory.newResource(proxyInterface, restClient.target(url)
            .register(keycloakTokenClientRequestFilter, Priorities.AUTHORIZATION));
    }
}
