package com.sast.store.apigateway.rest;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.ordermanagement.api.InvoiceApi;
import jakarta.inject.Inject;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;

@Component
@Path("/invoices")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class InvoiceRestService {

    @Inject
    private InvoiceApi invoiceService;

    @GET
    @Path("/{documentNumber}")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    @PreAuthorize("hasRole('DEFAULT')")
    public Response download(@NotNull @HeaderParam("X-Tenant") final Tenant tenantId,
        @NotNull @PathParam("documentNumber") final String documentNumber) {
        return invoiceService.download(tenantId, documentNumber);
    }

    @GET
    @Path("/ping")
    public String ping() {
        // used by blackbox probe
        return "pong";
    }
}
