package com.sast.store.apigateway.config;

import com.sast.store.apigateway.rest.CommercetoolsExtensionRestService;
import com.sast.store.apigateway.rest.ConfigurationRestService;
import com.sast.store.apigateway.rest.CorsFilter;
import com.sast.store.apigateway.rest.InvoiceRestService;
import com.sast.store.apigateway.rest.MeRestService;
import com.sast.store.commons.basewebapp.rest.CommonJerseyServerConfig;
import jakarta.annotation.PostConstruct;
import jakarta.ws.rs.ApplicationPath;
import org.glassfish.jersey.server.ResourceConfig;
import org.springframework.context.annotation.Configuration;

@Configuration
@ApplicationPath("/rest")
public class JerseyServerConfig extends ResourceConfig {

    @PostConstruct
    public void init() {
        register(CommercetoolsExtensionRestService.class);
        register(ConfigurationRestService.class);
        register(InvoiceRestService.class);
        register(MeRestService.class);
        register(CorsFilter.class);

        CommonJerseyServerConfig.gatewayRegistrationsAndProperties(clazz -> register(clazz), (prop, val) -> property(prop, val));
    }
}
