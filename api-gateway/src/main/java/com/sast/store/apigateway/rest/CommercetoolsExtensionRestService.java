package com.sast.store.apigateway.rest;

import com.sast.store.ordermanagement.api.CommercetoolsExtensionApi;
import jakarta.inject.Inject;
import jakarta.ws.rs.ClientErrorException;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;

@Component
@Path("/commercetools")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class CommercetoolsExtensionRestService {
    private static final Logger LOG = LoggerFactory.getLogger(CommercetoolsExtensionRestService.class);

    @Inject
    private CommercetoolsExtensionApi commercetoolsExtensionApi;

    @POST
    @Path("/cartvalidation")
    @PreAuthorize("hasRole('ROLE_COMMERCETOOLS')")
    public Response validateCart(final String extensionInput) {
        LOG.info("Received cart validation request: {}", extensionInput);
        try {
            commercetoolsExtensionApi.validateCart(extensionInput);
        } catch (final ClientErrorException e) {
            LOG.info("Cart validation failed");
            return Response
                .status(Response.Status.BAD_REQUEST)
                .entity(CommercetoolsErrors.withError("InvalidInput", "cartvalidation failed"))
                .build();
        }
        LOG.info("Cart validation successful");
        // commercetools expects 200 and empty body as response
        return Response.status(Response.Status.OK).entity("").build();
    }

    @GET
    @Path("/ping")
    public String ping() {
        // used by blackbox probe
        return "pong";
    }
}
