package com.sast.store.apigateway.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

@Configuration
@EnableScheduling
@ConditionalOnProperty(prefix = "bossstore.scheduling", name = "enabled", havingValue = "true", matchIfMissing = true)
public class SchedulingConfig {
    // enable scheduling unless explicitly disabled (e.g. in tests)
}
