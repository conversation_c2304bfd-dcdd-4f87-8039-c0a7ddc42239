logging:
    level:
        com.sast: DEBUG

management:
    endpoint:
        health:
            show-details: always
            probes.enabled: true
    endpoints.web.exposure.include: health, info, prometheus
    prometheus.metrics.export.enabled: true

spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://localhost:8000/auth/realms/baam

bossstore:
  cors:
    headers: "*"
    methods: GET, POST, DELETE, PUT, OPTIONS
    origin: "*"
