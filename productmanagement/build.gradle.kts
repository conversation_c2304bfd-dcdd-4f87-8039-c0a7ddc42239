plugins {
    id("bossstore.subproject-conventions")
    id("org.springframework.boot") version "3.4.3"
    id("com.google.cloud.tools.jib") version "3.4.4"
}

dependencies {
    implementation(project(":productmanagement-api"))
    implementation(project(":commons"))
    implementation(project(":commons:base-webapp"))
    implementation(project(":commons:aws"))
    implementation(project(":commons:tenant"))
    implementation(project(":external-clients"))
    implementation(project(":external-clients:commercetools"))
    implementation(project(":external-clients:ump"))
    implementation(project(":external-clients:brim-service"))
    implementation(project(":external-clients:countries-service"))
    implementation(project(":commons:hazelcast"))
    implementation(project(":commons:shedlock"))

    implementation(enforcedPlatform(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES))
    implementation("org.springframework.boot:spring-boot-starter")
    implementation("org.springframework.boot:spring-boot-starter-jersey")
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation("org.springframework.boot:spring-boot-starter-data-jpa")
    implementation("io.awspring.cloud:spring-cloud-aws-starter-sqs")
    implementation("com.google.guava:guava:33.4.0-jre")
    implementation("org.liquibase:liquibase-core")
    runtimeOnly("org.glassfish.jersey.core:jersey-common") {
        because("Otherwise it can't find a converter for the plaintext output in case of errors")
    }
    runtimeOnly("org.postgresql:postgresql")

    testImplementation(testFixtures(project(":testing-awsmockup")))
    testImplementation(testFixtures(project(":testing-commons")))
    testImplementation(testFixtures(project(":external-clients:countries-service")))
    testImplementation(testFixtures(project(":external-clients:ump")))
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.springframework.security:spring-security-test")
    testImplementation("org.springframework.boot:spring-boot-testcontainers")
    testImplementation("org.springframework.boot:spring-boot-docker-compose")
    testImplementation("org.testcontainers:postgresql:1.21.0")
    testImplementation("io.rest-assured:rest-assured")
    testImplementation("org.wiremock.integrations:wiremock-spring-boot:3.9.0")
    testImplementation("com.tngtech.keycloakmock:mock-junit5:0.17.0")
    testImplementation("org.mockito:mockito-core")
    testImplementation("org.mockito:mockito-junit-jupiter")
}

jib {
    from.image = "gcr.io/distroless/java21-debian12"
    to.image = "${property("ecrEndpoint")}/bossstore-${project.name}:${project.version}"
}

tasks.bootTestRun {
    args = listOf("-Daws.maxAttempts=20", "--spring.profiles.active=local")
}
