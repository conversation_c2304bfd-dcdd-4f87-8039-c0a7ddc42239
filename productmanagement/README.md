# Productmanagement

## Connecting to databases on dev/demo/live

For debugging purposes, a db forwarder is deployed on every environment to connect to a `product-db` database from a
local machine.

This requires a working kubectl setup for our kubernetes infrastructure.

```shell
# Example env: dev, change values accordingly
# switch to appropriate k8s context
kubectx sast-pilcrow-dev_default
 
# retrieve password for product-db
credstash -p sast-pilcrow-dev_Developer get stack_bossstore/product_db_password stack=bossstore
 
# setup port forwarding via kubectl
kubectl port-forward service/product-db-forwarder -n bossstore 15432:15432
 
# EXAMPLE: Connect via postgres CLI
psql -h localhost -p 15432 -U productmanagement -d product_db
```

Note: Connecting to the live database requires live access via the big red button (`sast-live-acquire-access.sh`).

## Schema changes
This application uses [liquibase](https://docs.liquibase.com/start/home.html). New changesets are executed on spring context initialization.

To extend the schema, changesets can be added to 
`productmanagement/src/main/resources/db/changelog/db.changelog-master.yaml`:

```yaml
databaseChangeLog:
  - changeSet:
      id: 00001_baseline
      author: <EMAIL>
      comment: Create a test table for test entity, to illustrate liquibase integration
      changes:
        - sqlFile:
            path: changesets/00001_baseline.sql
            relativeToChangelogFile: true
      rollback:
        - sqlFile:
            path: rollbacks/00001_baseline_rollback.sql
            relativeToChangelogFile: true
```

By convention, and consistent with other projects, `sqlFile` changes are preferred.
It is encouraged to always provide rollback changes.

Note on types: Unless a length limitation is required, prefer `TEXT` over `VARCHAR` in postgres. 

## Managing test data
In tests, the `@Sql` annotation should be used to create test data a test may assume to be present:

```java
public class TestEntityPersistenceTest extends AbstractComponentTest {
    @Inject
    private TestEntityRepository testEntityRepository;

    @Test
    @Sql(value = "/sql/test/create-test-entities.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    @Sql(value = "/sql/test/truncate-tables.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
    public void testExistingEntitiesAreFound() {
        final Optional<TestEntity> existingEntity = testEntityRepository
                .findById(UUID.fromString("13423608-f69c-4b3b-9f2e-3d3edc981e3b"));

        assertThat(existingEntity).isNotEmpty()
                .hasValueSatisfying(entity -> assertThat(entity.getName()).isEqualTo("Herbert"));
    }
}
```

In this example, the sql script `/sql/test/create-test-entities.sql` is executed prior to
the execution of `testExistiongEntitiesAreFound()`. For cleanup, the `"/sql/test/truncate-tables.sql"` script is
executed after test case execution. This script is also executed after every test class that is a subclass of
`AbstractComponentTest`. Each test must set up their test data itself to avoid hard to debug
interaction between test classes.

See also the full `TestEntityPersistenceTest` for the full context.

Note that the database container used in tests is *recreated* for every test execution. No
data is retained between test executions.


## Local application database
The local application database is managed via spring boot docker compose integration, and therefore
starts automatically on application startup. See `productmanagement/docker-compose.yml` for its
configuration.

### Recreating the local database
The postgres container used for the local application (not for the tests!) is retained between
application executions. In order to remove the database, the service has to be removed via
docker compose - on the next startup, a fresh database will be created.

```shell
docker-compose -f docker-compose.yml down
```

### Sample data

The local productmanagement application can be provisioned with sample data. For this purpose
the local application executes `productmanagement/src/test/resources/sql/local/local-sampledata.sql` on startup.

Please note that this script is executed on *every* start of the application. The upsert statement in
Postgres (`INSERT ... ON CONFLICT`) is useful here:

```postgresql
INSERT INTO testentity(id,name) 
VALUES ('d1b819e7-da30-48ac-8497-84ee96a4d031', 'Hans Dampf')
ON CONFLICT DO NOTHING;
```

The above example instructs postgres to ignore any conflicts, it is also possible to update or to
use the `MERGE` statement (see
[INSERT](https://www.postgresql.org/docs/current/sql-insert.html),
[MERGE](https://www.postgresql.org/docs/current/sql-merge.html) for further details).




