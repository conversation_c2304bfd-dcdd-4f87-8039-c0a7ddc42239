bossstore:
  tenants:
    rexroth:
      ump:
        tenant: baam
  commercetools:
    clientId: gp83Agv3Z8RMX0tmNtljlFbo
    clientSecret: nED0ZFVUhUQq0QxzvsrIO5yJLH9USTXO
    projectKey: glorious-new-store

logging:
  level:
    com.sast: DEBUG

management:
  endpoint:
    health:
      show-details: always
      probes.enabled: true
  endpoints.web.exposure.include: health, info, prometheus
  prometheus.metrics.export.enabled: true

spring:
  cache:
    type: caffeine
    cache-names: companies,companyDetails,users,user,companyManagers,subCompanies,companyInformation,listAllCountries,getCountry,storeProvider.findByKey,storeProvider.findByTenant,productSelectionProvider.findByKey, productSelectionProvider.findById
    caffeine.spec: maximumSize=100000,expireAfterWrite=300s
  security.oauth2.resourceserver.jwt.issuer-uri: http://localhost:8000/auth/realms/baam
  datasource:
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: validate
    generate-ddl: false
