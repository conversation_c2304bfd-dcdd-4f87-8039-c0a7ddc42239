package com.sast.store.productmanagement.service;

import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.models.customer_group.CustomerGroup;
import com.commercetools.api.models.product.Product;
import com.commercetools.api.models.product.ProductDraftBuilder;
import com.commercetools.api.models.product.ProductProjection;
import com.commercetools.api.models.product.ProductVariant;
import com.commercetools.api.models.product.ProductVariantDraftBuilder;
import com.commercetools.api.models.product_selection.ProductSelection;
import com.commercetools.api.models.product_type.ProductTypeResourceIdentifierBuilder;
import com.commercetools.api.models.store.ProductSelectionSetting;
import com.commercetools.api.models.store.Store;
import com.commercetools.api.models.tax_category.TaxCategoryResourceIdentifierBuilder;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.external.commercetools.service.CustomerGroupProvider;
import com.sast.store.external.commercetools.service.ProductSelectionProvider;
import com.sast.store.external.commercetools.service.StoreProvider;
import com.sast.store.external.commercetools.util.CustomAttributeProvider;
import com.sast.store.external.countriesservice.api.CountriesServiceClient;
import com.sast.store.external.ump.UmpClient;
import com.sast.store.gen.client.ump.api.UmpExternalCompanyDto;
import com.sast.store.gen.client.ump.api.UmpInternalCustomerGroupConfigurationDto;
import com.sast.store.productmanagement.config.AppConfiguration;
import lombok.Builder;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Comparator;
import java.util.Currency;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
import java.util.stream.Stream;

import static com.sast.store.external.commercetools.util.CustomFieldProvider.getProductSelectionCountry;

@Component
@Slf4j
@RequiredArgsConstructor
public class ProductService {
    private final ProjectApiRoot commercetoolsClient;
    private final UmpClient umpClient;
    private final ImportExportService importExportService;
    private final AppConfiguration appConfiguration;
    private final CountriesServiceClient countriesServiceClient;
    private final CustomerGroupProvider customerGroupProvider;
    private final ProductSearchService productSearchService;
    private final StoreProvider storeProvider;
    private final ProductSelectionProvider productSelectionProvider;

    public List<ProductProjection> fetchProducts(final Tenant tenant, final String companyId, final Locale locale) {
        final Store store = storeProvider.findByKey(tenant.id(), true)
            .orElseThrow(() -> new IllegalStateException("Store not found for key " + tenant.id()));
        final String productSelectionId = getProductSelectionId(store, locale);
        final ProductProjectionContext productProjectionContext = getProductProjectionContext(tenant, locale, companyId);

        return productSearchService.search(store.getId(), productSelectionId).stream()
            .map(searchResult -> this.fetchProductInternal(searchResult.getId(), productProjectionContext))
            .filter(Optional::isPresent)
            .map(Optional::get)
            .toList();
    }

    private String getProductSelectionId(final Store store, final Locale locale) {
        return getProductSelections(store).stream()
            .filter(ps -> isProductSelectionCountry(ps, locale))
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("No product selection found for country " + locale.getCountry()))
            .getId();
    }

    private boolean isProductSelectionCountry(final ProductSelection productSelection, final Locale locale) {
        return getProductSelectionCountry(productSelection)
            .map(country -> country.equalsIgnoreCase(locale.getCountry()))
            .orElse(false);
    }

    private List<ProductSelection> getProductSelections(final Store store) {
        return store.getProductSelections().stream()
            .filter(ProductSelectionSetting::getActive)
            .map(ps -> ps.getProductSelection().getId())
            .map(id -> productSelectionProvider.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Product selection not found for id " + id)))
            .toList();
    }

    public Optional<ProductProjection> fetchProduct(final Tenant tenant, final String productId,
        final String companyId, final Locale locale) {
        final ProductProjectionContext productProjectionContext = getProductProjectionContext(tenant, locale, companyId);

        return fetchProductInternal(productId, productProjectionContext);
    }

    private Optional<ProductProjection> fetchProductInternal(
        @NonNull final String productId,
        @NonNull final ProductService.ProductProjectionContext productProjectionContext) {
        return Optional.ofNullable(commercetoolsClient.productProjections().withId(productId).get()
            .withPriceCustomerGroup(Optional.ofNullable(productProjectionContext.customerGroup())
                .map(CustomerGroup::getId).stream().toList())
            .withPriceCurrency(productProjectionContext.currency().getCurrencyCode())
            .withPriceCountry(productProjectionContext.locale().getCountry())
            .withLocaleProjection(productProjectionContext.locale().getLanguage())
            .addExpand("masterVariant.attributes[*].value[*]")
            .addExpand("variants[*].attributes[*].value[*]")
            .executeBlocking()
            .getBody());
    }

    private ProductProjectionContext getProductProjectionContext(@NonNull final Tenant tenant,
        @NonNull final Locale locale, final String companyId) {
        final String umpTenant = appConfiguration.tenant(tenant).ump().tenant();
        final Optional<UmpExternalCompanyDto> companyDetails = Optional.ofNullable(companyId)
            .map(cId -> umpClient.getCompanyDetails(umpTenant, cId));
        final Optional<CustomerGroup> customerGroup = companyDetails
            .map(UmpExternalCompanyDto::getCustomerGroup)
            .map(UmpInternalCustomerGroupConfigurationDto::getCustomizationId)
            .flatMap(customerGroupProvider::findByKey);
        final String country = companyDetails.map(UmpExternalCompanyDto::getCompanyCountry)
            .orElse(locale.getCountry());

        return ProductProjectionContext.builder()
            .customerGroup(customerGroup.orElse(null))
            .currency(Currency.getInstance(getCurrencyOfCountry(tenant, country)))
            .locale(Locale.of(locale.getLanguage(), country))
            .build();
    }

    public Optional<Product> fetchProductBySku(final String sku) {
        return commercetoolsClient.products().get()
            // variants are split into masterVariant and variants, so we need to query both
            .withWhere("masterData(current(variants(sku=:sku))) or masterData(current(masterVariant(sku=:sku)))")
            .addPredicateVar("sku", sku)
            .addLimit(1)
            .addWithTotal(false)
            .executeBlocking()
            .getBody()
            .getResults().stream()
            .findAny();
    }

    public Optional<Tenant> resolveTenant(@NonNull final String sku) {
        final Product product = fetchProductBySku(sku)
            .orElseThrow(() -> new IllegalStateException("Could not fetch product by SKU %s".formatted(sku)));
        final List<ProductVariant> list = product.getMasterData().getCurrent().getAllVariants().stream()
            .filter(variant -> variant.getSku().equals(sku))
            .toList();

        if (list.size() != 1) {
            throw new IllegalStateException("Could not find exactly one variant with SKU %s".formatted(sku));
        }

        return CustomAttributeProvider.getTenant(list.getFirst());
    }

    private String getCurrencyOfCountry(final Tenant tenant, final String countryCode) {
        return countriesServiceClient
            .getCountry(com.sast.store.external.countriesservice.api.Tenant.valueOf(tenant.id()), countryCode)
            .getTenantConfigurations().stream().findAny().orElseThrow().getCurrency();
    }

    public Stream<String> exportAllProducts() {
        return importExportService.getAllPaginated(commercetoolsClient.products().get()).stream()
            .map(p -> {
                // these fields cannot be imported
                p.setId(null);
                p.setVersion(null);
                p.setLastModifiedBy(null);
                p.setCreatedBy(null);
                p.setLastModifiedAt(null);
                p.setCreatedAt(null);
                return p;
            })
            .sorted(Comparator.comparing(Product::getKey))
            .map(importExportService::toString);
    }

    public void exportProducts(final Path path) throws IOException {
        final Stream<String> products = exportAllProducts();
        final Iterable<String> productIterable = products::iterator;
        Files.write(path, productIterable);
        LOG.info("updated file: {}", path);
    }

    public void importProducts(final Path path) throws IOException {
        importProducts(Files.readAllLines(path).stream());
    }

    // import stream of Product json objects as exported by exportProducts
    public void importProducts(final Stream<String> stream) {
        stream
            .map(p -> importExportService.fromString(p, Product.class))
            .filter(p -> {
                final boolean exists = exists(p);
                if (exists) {
                    LOG.info("skipping product because it already exists {}", p);
                }
                return !exists;
            })
            .forEach(p -> {
                try {
                    LOG.info("creating {}", p);
                    commercetoolsClient.products().create(ProductDraftBuilder.of()
                            .productType(ProductTypeResourceIdentifierBuilder.of().id(p.getProductType().getId()).build())
                            .name(p.getMasterData().getCurrent().getName())
                            .slug(p.getMasterData().getCurrent().getSlug())
                            .key(p.getKey())
                            .description(p.getMasterData().getCurrent().getDescription())
                            .masterVariant(ProductVariantDraftBuilder.of()
                                .key(p.getMasterData().getCurrent().getMasterVariant().getKey())
                                .sku(p.getMasterData().getCurrent().getMasterVariant().getSku())
                                .attributes(p.getMasterData().getCurrent().getMasterVariant().getAttributes())
                                .images(p.getMasterData().getCurrent().getMasterVariant().getImages())
                                .build())
                            .variants(p.getMasterData().getCurrent().getVariants().stream()
                                .map(v -> ProductVariantDraftBuilder.of()
                                    .key(v.getKey())
                                    .sku(v.getSku())
                                    .attributes(v.getAttributes())
                                    .images(v.getImages())
                                    .build())
                                .toList())
                            .taxCategory(TaxCategoryResourceIdentifierBuilder.of().id(p.getTaxCategory().getId()).build())
                            .priceMode(p.getPriceMode())
                            .publish(true)
                            .build())
                        .executeBlocking();
                } catch (final RuntimeException e) {
                    LOG.warn("Error: " + e.getMessage());
                }
            });
    }

    private boolean exists(final Product s) {
        if (s.getKey() == null) {
            return false;
        }
        try {
            return commercetoolsClient.products().withKey(s.getKey()).head().executeBlocking().getStatusCode() == 200;
        } catch (final RuntimeException e) {
            return false;
        }
    }

    @Builder
    private record ProductProjectionContext(
        CustomerGroup customerGroup,
        Currency currency,
        Locale locale
    ) {
    }

}
