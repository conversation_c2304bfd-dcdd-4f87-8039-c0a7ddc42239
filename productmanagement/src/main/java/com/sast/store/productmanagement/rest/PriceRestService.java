package com.sast.store.productmanagement.rest;

import com.sast.store.commons.basewebapp.util.ExceptionHelper;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.productmanagement.api.PriceApi;
import com.sast.store.productmanagement.service.PriceService;
import jakarta.inject.Inject;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.StreamingOutput;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

@Component
public class PriceRestService implements PriceApi {
    private static final Logger LOG = LoggerFactory.getLogger(PriceRestService.class);

    @Inject
    private PriceService priceService;

    @Override
    @PreAuthorize("hasRole('MANAGE_BACKOFFICE')")
    public Response exportPrices(@NotNull final Tenant tenantId) {
        LOG.info("exportPrices");
        return Response.ok((StreamingOutput) out -> {
            priceService.exportAllPrices()
                .map(s -> s + "\n")
                .map(String::getBytes)
                .forEach(ExceptionHelper.wrapIoException(out::write));
        }).build();
    }

    @Override
    @PreAuthorize("hasRole('MANAGE_BACKOFFICE')")
    public void importPrices(@NotNull final Tenant tenantId, final InputStream inputStream) {
        LOG.info("importPrices");
        final BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
        priceService.importPrices(reader.lines());
    }

}
