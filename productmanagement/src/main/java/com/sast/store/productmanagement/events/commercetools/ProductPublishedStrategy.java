package com.sast.store.productmanagement.events.commercetools;

import com.commercetools.api.models.message.ProductPublishedMessage;
import com.sast.store.brimtegration.apimodel.events.egress.product.data.PlatformProductExportable;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.external.brim.BrimMessagePublisher;
import com.sast.store.external.commercetools.util.CustomAttributeProvider;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProductPublishedStrategy {
    private final BrimMessagePublisher brimMessagePublisher;

    public void process(@NonNull final ProductPublishedMessage variantPublishedMessage) {
        LOG.info("Processing product published message for product with id={}", variantPublishedMessage.getProductProjection().getId());
        variantPublishedMessage.getProductProjection().getAllVariants().stream()
            .map(variant -> {
                final Tenant tenant = CustomAttributeProvider.getTenant(variant)
                        .orElseThrow(() -> new IllegalStateException(
                            "Given product variant (sku=%s) is missing custom attribute 'tenant'".formatted(variant.getSku())));
                final PlatformProductExportable eventData = PlatformProductExportable.builder()
                    .productId(variant.getSku())
                    .build();
                return Pair.of(tenant, eventData);

            }).toList()
            // publish if all events could be constructed
            .forEach(pair -> {
                brimMessagePublisher.publishPlatformProductEvent(pair.getLeft().id(), pair.getRight());
            });
    }
}
