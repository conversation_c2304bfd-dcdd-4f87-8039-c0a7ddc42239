package com.sast.store.productmanagement;

import com.sast.store.commons.EnableCommonsAutoconfiguration;
import com.sast.store.external.EnableExternalClientsAutoconfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;

@SpringBootApplication
@EnableExternalClientsAutoconfiguration
@EnableCommonsAutoconfiguration
@EnableCaching
public class ProductmanagementApplication {

    public static void main(final String[] args) {
        SpringApplication.run(ProductmanagementApplication.class, args);
    }

}
