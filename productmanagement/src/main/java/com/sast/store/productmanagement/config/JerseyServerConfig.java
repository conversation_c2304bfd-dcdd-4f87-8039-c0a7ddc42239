package com.sast.store.productmanagement.config;

import com.sast.store.commons.basewebapp.rest.CommonJerseyServerConfig;
import com.sast.store.productmanagement.rest.CategoryRestService;
import com.sast.store.productmanagement.rest.PriceRestService;
import com.sast.store.productmanagement.rest.ProductRestService;
import jakarta.annotation.PostConstruct;
import jakarta.ws.rs.ApplicationPath;
import org.glassfish.jersey.server.ResourceConfig;
import org.springframework.context.annotation.Configuration;

@Configuration
@ApplicationPath("/rest")
public class JerseyServerConfig extends ResourceConfig {

    @PostConstruct
    public void init() {
        register(ProductRestService.class);
        register(PriceRestService.class);
        register(CategoryRestService.class);

        CommonJerseyServerConfig.defaultRegistrationsAndProperties(clazz -> register(clazz), (prop, val) -> property(prop, val));
    }
}
