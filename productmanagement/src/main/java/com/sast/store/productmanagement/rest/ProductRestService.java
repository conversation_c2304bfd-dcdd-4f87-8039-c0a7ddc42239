package com.sast.store.productmanagement.rest;

import com.commercetools.api.models.category.CategoryReference;
import com.commercetools.api.models.common.Image;
import com.commercetools.api.models.common.LocalizedString;
import com.commercetools.api.models.common.Price;
import com.commercetools.api.models.product.AttributeAccess;
import com.commercetools.api.models.product.ProductProjection;
import com.commercetools.api.models.product.ProductVariant;
import com.sast.store.commons.basewebapp.keycloak.AuthenticationService;
import com.sast.store.commons.basewebapp.security.Unprotected;
import com.sast.store.commons.basewebapp.util.ExceptionHelper;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.external.commercetools.util.CustomAttributeProvider;
import com.sast.store.external.commercetools.util.LocalizedFieldProvider;
import com.sast.store.external.ump.UmpClient;
import com.sast.store.gen.client.ump.api.UmpCompanyInfoDto;
import com.sast.store.productmanagement.api.ProductApi;
import com.sast.store.productmanagement.api.ProductDto;
import com.sast.store.productmanagement.api.ProductDto.Addon;
import com.sast.store.productmanagement.api.ProductDto.AddonVariant;
import com.sast.store.productmanagement.api.ProductDto.Company;
import com.sast.store.productmanagement.api.ProductDto.LinkType;
import com.sast.store.productmanagement.api.ProductDto.LocalizedLink;
import com.sast.store.productmanagement.api.ProductDto.Money;
import com.sast.store.productmanagement.api.ProductDto.Variant;
import com.sast.store.productmanagement.service.ProductService;
import com.sast.store.productmanagement.service.SellerService;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.NotFoundException;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.StreamingOutput;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
import java.util.function.Function;

@Component
public class ProductRestService implements ProductApi {
    private static final Logger LOG = LoggerFactory.getLogger(ProductRestService.class);

    private final ProductService productService;

    private final AuthenticationService authenticationService;

    private final SellerService sellerService;

    private final UmpClient umpClient;

    public ProductRestService(final ProductService productService, final AuthenticationService authenticationService,
        final SellerService sellerService, final UmpClient umpClient) {
        this.productService = productService;
        this.authenticationService = authenticationService;
        this.sellerService = sellerService;
        this.umpClient = umpClient;
    }

    @Override
    @Unprotected
    public List<ProductDto> getAllProducts(@NotNull final Tenant tenantId, final String languageCode, final String countryCode,
        final Integer limit) {
        try {
            final String language = authenticationService.getCommunicationLanguage().orElse(languageCode);
            final String companyId = authenticationService.getCompanyIdIfExists().orElse(null);
            return productService.fetchProducts(tenantId, companyId, Locale.of(language, countryCode)).stream()
                .filter(p -> p.getAllVariants().stream().anyMatch(v -> v.getPrice() != null))
                .map(buildProductDto(tenantId, language))
                .limit(limit)
                .toList();
        } catch (IllegalArgumentException e) {
            LOG.error("Error occurred during product fetch: {}", e.getMessage());
            throw new NotFoundException();
        }
    }

    private Function<ProductProjection, ProductDto> buildProductDto(final Tenant tenantId, final String language) {
        return p -> ProductDto.builder()
            .id(p.getId())
            .name(extractLanguage(language).apply(p.getName()))
            .description(Optional.ofNullable(p.getDescription()).map(extractLanguage(language)).orElse(null))
            .productType(p.getProductType().getId())
            .images(p.getMasterVariant()
                .getImages().stream().map(Image::getUrl).map(URI::create)
                .toList())
            .externalDocuments(buildExternalDocuments(p, language))
            .variants(p.getAllVariants().stream()
                .filter(v -> v.getPrice() != null)
                .map(v -> buildVariant(v, language))
                .toList())
            .sellerCompany(buildSellerCompany(tenantId, p))
            .categories(p.getCategories().stream().map(CategoryReference::getId).toList())
            .build();
    }

    @Override
    @Unprotected
    public Optional<ProductDto> getProduct(@NotNull final Tenant tenantId, final String productId, final String languageCode,
        final String countryCode) {
        final String language = authenticationService.getCommunicationLanguage().orElse(languageCode);
        final String companyId = authenticationService.getCompanyIdIfExists().orElse(null);

        return productService.fetchProduct(tenantId, productId, companyId, Locale.of(language, countryCode))
            .map(buildProductDto(tenantId, language));
    }

    private List<LocalizedLink> buildExternalDocuments(final ProductProjection product, final String language) {
        final Locale locale = Locale.forLanguageTag(language);
        return CustomAttributeProvider.getExternalDocuments(product).stream()
            .map(attribute -> LocalizedLink.builder()
                .name(CustomAttributeProvider.getAgreementName(attribute, locale))
                .url(CustomAttributeProvider.getAgreementLink(attribute, locale))
                .build())
            .toList();
    }

    private List<LocalizedLink> buildAgreements(final ProductVariant variant, final Locale locale) {
        return CustomAttributeProvider.getAgreements(variant).stream()
            .filter(attribute -> CustomAttributeProvider.getAgreementLink(attribute, locale) != null)
            .map(attribute -> LocalizedLink.builder()
                .name(CustomAttributeProvider.getAgreementName(attribute, locale))
                .url(CustomAttributeProvider.getAgreementLink(attribute, locale))
                .linkType(LinkType.valueOf(CustomAttributeProvider.getAgreementCategory(attribute)
                    .orElse(LinkType.SOFTWARE_LICENSE.name())))
                .build())
            .toList();
    }

    private Variant buildVariant(final ProductVariant productVariant, final String language) {
        final Locale userLocale = Locale.forLanguageTag(language);
        return ProductDto.Variant.builder()
            .sku(productVariant.getSku())
            .externalProductId(CustomAttributeProvider.getExternalProductId(productVariant).orElse(null))
            .price(buildPrice(productVariant.getPrice()).orElse(null))
            .bundleAmount(CustomAttributeProvider.getBundleAmount(productVariant).orElse(null))
            .licenseType(CustomAttributeProvider.getLicenseType(productVariant).orElse(null))
            .runtime(CustomAttributeProvider.getRuntime(productVariant).orElse(null))
            .noticePeriod(CustomAttributeProvider.getNoticePeriod(productVariant).orElse(null))
            .name(CustomAttributeProvider.getVariantName(productVariant, userLocale).orElse(null))
            .description(CustomAttributeProvider.getDescription(productVariant, userLocale).orElse(null))
            .features(CustomAttributeProvider.getFeatures(productVariant, userLocale).orElse(null))
            .agreements(buildAgreements(productVariant, userLocale))
            .priceList(CustomAttributeProvider.getPriceList(productVariant, userLocale))
            .entitlements(getEntitlements(productVariant))
            .addons(CustomAttributeProvider.getAddons(productVariant).stream()
                .map(a -> a.getMasterData().getCurrent())
                .map(a -> Addon.builder()
                    .name(LocalizedFieldProvider.getWithFallback(a.getName(), userLocale).orElse(null))
                    .description(LocalizedFieldProvider.getWithFallback(a.getDescription(), userLocale).orElse(null))
                    .addonVariants(a.getAllVariants().stream().map(v -> AddonVariant.builder()
                        .sku(v.getSku())
                        .price(buildPrice(v.getPrice()).orElse(null))
                        .name(CustomAttributeProvider.getVariantName(v, userLocale).orElse(null))
                        .description(CustomAttributeProvider.getDescription(v, userLocale).orElse(null))
                        .build()).toList())
                    .build())
                .toList())
            .build();
    }

    private Company buildSellerCompany(final Tenant tenant, final ProductProjection product) {
        return sellerService.getSellerCompanyId(product)
            .map(companyId -> getCompanyInformationOrNull(tenant, companyId))
            .map(UmpCompanyInfoDto::getCompanyName)
            .map(name -> Company.builder().name(name).build())
            .orElse(null);
    }

    private UmpCompanyInfoDto getCompanyInformationOrNull(final Tenant tenant, final String companyId) {
        try {
            return umpClient.getCompanyInformation(tenant.id(), companyId);
        } catch (final Exception e) {
            LOG.warn("Cannot get company for tenant={}, companyId={}: {}", tenant, companyId, e.getMessage(), e);
            return null;
        }
    }

    private static List<String> getEntitlements(final ProductVariant variant) {
        return variant.findAttributeByName("entitlements")
            .map(AttributeAccess::asSetString).orElse(List.of());
    }

    private Optional<Money> buildPrice(final Price price) {
        if (authenticationService.getUserIdIfExists().isEmpty()) {
            // hide price for anonymous users
            return Optional.empty();
        }
        if (price == null) {
            return Optional.empty();
        }
        return Optional.of(ProductDto.Money.builder()
            .value(price.getValue().getNumber().doubleValue())
            .currencyCode(price.getValue().getCurrencyCode())
            .build());
    }

    @Override
    @PreAuthorize("hasRole('MANAGE_BACKOFFICE')")
    public Response exportProducts(@NotNull final Tenant tenantId) {
        LOG.info("exportProducts");
        return Response.ok((StreamingOutput) out -> {
            productService.exportAllProducts()
                .map(s -> s + "\n")
                .map(String::getBytes)
                .forEach(ExceptionHelper.wrapIoException(out::write));
        }).build();
    }

    @Override
    @PreAuthorize("hasRole('MANAGE_BACKOFFICE')")
    public void importProducts(@NotNull final Tenant tenantId, final InputStream inputStream) {
        LOG.info("importProducts");
        final BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
        productService.importProducts(reader.lines());
    }

    private Function<? super LocalizedString, ? extends String> extractLanguage(final String language) {
        return localized -> Optional.ofNullable(localized.get(language))
            // fallback to whatever language is defined as fallback in commercetools
            .orElseGet(() -> localized.values().values().stream().findAny().orElse(null));
    }

    @Override
    @Unprotected
    public Optional<ProductDto> getProductVariant(@NotNull final Tenant tenant, @NotNull final String sku, final String languageCode) {
        final String language = authenticationService.getCommunicationLanguage().orElse(languageCode);
        final Optional<ProductDto> result = productService.fetchProductBySku(sku)
            .map(p -> ProductDto.builder()
                .id(p.getId())
                .name(extractLanguage(language).apply(p.getMasterData().getCurrent().getName()))
                .description(Optional.ofNullable(p.getMasterData().getCurrent().getDescription())
                    .map(extractLanguage(language)).orElse(null))
                .productType(p.getProductType().getId())
                .images(p.getMasterData().getCurrent().getMasterVariant()
                    .getImages().stream().map(Image::getUrl).map(URI::create)
                    .toList())
                .variants(p.getMasterData().getCurrent().getAllVariants().stream()
                    .filter(v -> v.getSku().equals(sku))
                    .map(v -> buildVariant(v, language))
                    .toList())
                .build());
        if (result.isPresent() && !result.get().variants().isEmpty()) {
            return result;
        }
        return Optional.empty();
    }
}
