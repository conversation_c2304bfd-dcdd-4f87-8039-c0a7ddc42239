package com.sast.store.productmanagement.service;

import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.models.common.CentPrecisionMoney;
import com.commercetools.api.models.common.TypedMoney;
import com.commercetools.api.models.customer_group.CustomerGroupResourceIdentifierBuilder;
import com.commercetools.api.models.standalone_price.StandalonePrice;
import com.commercetools.api.models.standalone_price.StandalonePriceChangeActiveAction;
import com.commercetools.api.models.standalone_price.StandalonePriceChangeValueAction;
import com.commercetools.api.models.standalone_price.StandalonePriceDraftBuilder;
import com.commercetools.api.models.standalone_price.StandalonePriceSetValidFromAction;
import com.commercetools.api.models.standalone_price.StandalonePriceSetValidUntilAction;
import com.commercetools.api.models.standalone_price.StandalonePriceUpdate;
import com.sast.store.brimtegration.apimodel.common.product.GroupPrice;
import com.sast.store.brimtegration.apimodel.common.product.GroupPriceEntry;
import com.sast.store.brimtegration.apimodel.common.product.ListPrice;
import com.sast.store.brimtegration.apimodel.common.product.ListPriceEntry;
import com.sast.store.brimtegration.apimodel.events.ingress.product.data.BillingProductData;
import io.vrap.rmf.base.client.ApiHttpResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Comparator;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

@Component
@Slf4j
@RequiredArgsConstructor
public class PriceService {
    private final ProjectApiRoot commercetoolsClient;
    private final ImportExportService importExportService;

    public Stream<String> exportAllPrices() {
        return importExportService.getAllPaginated(commercetoolsClient.standalonePrices().get()).stream()
            .map(p -> {
                // these fields cannot be imported
                p.setId(null);
                p.setVersion(null);
                p.setLastModifiedBy(null);
                p.setCreatedBy(null);
                p.setLastModifiedAt(null);
                p.setCreatedAt(null);
                return p;
            })
            .sorted(Comparator.comparing(StandalonePrice::getKey))
            .map(importExportService::toString);
    }

    public void exportPrices(final Path path) throws IOException {
        final Stream<String> prices = exportAllPrices();
        final Iterable<String> priceIterable = prices::iterator;
        Files.write(path, priceIterable);
        LOG.info("updated file: {}", path);
    }

    public void importPrices(final Path path) throws IOException {
        importPrices(Files.readAllLines(path).stream());
    }

    // import stream of StandalonePrice json objects as exported by exportPrices
    public void importPrices(final Stream<String> stream) {
        stream
            .map(p -> importExportService.fromString(p, StandalonePrice.class))
            .forEach(p -> {
                try {
                    if (getStandalonePrice(p.getKey()).isPresent()) {
                        LOG.info("Deleting: {}", p);
                        final Long version = commercetoolsClient.standalonePrices().withKey(p.getKey()).get().executeBlocking().getBody()
                            .getVersion();
                        commercetoolsClient.standalonePrices().withKey(p.getKey()).delete().withVersion(version).executeBlocking();
                    }

                    LOG.info("Creating: {}", p);
                    commercetoolsClient.standalonePrices().create(StandalonePriceDraftBuilder.of()
                        .key(p.getKey() == null ? UUID.randomUUID().toString() : p.getKey())
                        .sku(p.getSku())
                        .country(p.getCountry() == null ? null : p.getCountry())
                        .customerGroup(p.getCustomerGroup() == null ? null
                            : CustomerGroupResourceIdentifierBuilder.of().id(p.getCustomerGroup().getId()).build())
                        .value(p.getValue())
                        .active(p.getActive())
                        .build())
                        .executeBlocking();
                } catch (final RuntimeException e) {
                    LOG.warn("Error: " + e.getMessage());
                }
            });
    }

    private Optional<StandalonePrice> getStandalonePrice(final String key) {
        if (key == null) {
            return Optional.empty();
        }
        try {
            final ApiHttpResponse<StandalonePrice> response = commercetoolsClient.standalonePrices().withKey(key).get()
                .executeBlocking();
            if (response.getStatusCode() == 200) {
                return Optional.of(response.getBody());
            }
            return Optional.empty();
        } catch (final RuntimeException e) {
            return Optional.empty();
        }
    }

    public void updatePrices(final BillingProductData data) {
        for (final ListPrice listPrice : data.listPrices()) {
            for (final ListPriceEntry listPriceEntry : listPrice.listPriceEntries()) {
                updateStandalonePricePrice(listPriceEntry.storefrontId(), listPriceEntry.amount(),
                        listPrice.currency().getCurrencyCode(), listPriceEntry.validFrom(), listPriceEntry.validTo());
            }
        }

        for (final GroupPrice groupPrice : data.groupPrices()) {
            for (final GroupPriceEntry groupPriceEntry : groupPrice.entries()) {
                updateStandalonePricePrice(groupPriceEntry.storefrontId(), groupPriceEntry.amount(),
                        groupPrice.currency().getCurrencyCode(), groupPriceEntry.validFrom(), groupPriceEntry.validTo());
            }
        }
    }

    private void updateStandalonePricePrice(final String priceKey, final BigDecimal amount, final String currency,
                                            final LocalDate validFrom, final LocalDate validTo) {
        LOG.info("Updating price: {}", priceKey);
        final Optional<StandalonePrice> standalonePrice = getStandalonePrice(priceKey);
        if (standalonePrice.isEmpty()) {
            LOG.warn("Price not found: {}", priceKey);
            return;
        }

        try {
            final TypedMoney value = standalonePrice.get().getValue();
            final Long version = standalonePrice.get().getVersion();
            final StandalonePrice response = commercetoolsClient.standalonePrices().withKey(priceKey)
                    .post(StandalonePriceUpdate.builder()
                            .version(version)
                            .plusActions(StandalonePriceChangeActiveAction.builder().active(true).build())
                            .plusActions(StandalonePriceSetValidFromAction.builder().validFrom(getValidFrom(validFrom)).build())
                            .plusActions(StandalonePriceSetValidUntilAction.builder().validUntil(getValidUntil(validTo)).build())
                            .plusActions(
                                    StandalonePriceChangeValueAction.builder().value(getAmount(amount, currency, value)).build())
                            .build())
                    .executeBlocking().getBody();
            LOG.info("Updated price, old price: {} new price: {}", standalonePrice.get(), response);
            if (!standalonePrice.get().getValue().getCentAmount().equals(response.getValue().getCentAmount())) {
                LOG.warn("Updated amount, old amount: {} new amount: {}", standalonePrice.get().getValue(), response.getValue());
            }
        } catch (final IllegalArgumentException e) {
            LOG.warn("Skipping price: {}", priceKey);
        }
    }

    private CentPrecisionMoney getAmount(final BigDecimal amount, final String currency, final TypedMoney oldAmount) {
        if (!oldAmount.getCurrencyCode().equals(currency)) {
            LOG.warn("Currency mismatch: {} != {}", oldAmount.getCurrencyCode(), currency);
            throw new IllegalArgumentException("Currency mismatch");
        }
        return CentPrecisionMoney.builder()
            .centAmount(amount.movePointRight(oldAmount.getFractionDigits()).longValueExact())
            .currencyCode(oldAmount.getCurrencyCode())
            .fractionDigits(oldAmount.getFractionDigits())
            .build();
    }

    private ZonedDateTime getValidFrom(final LocalDate from) {
        return from == null ? null : from.atStartOfDay(ZoneOffset.UTC);
    }

    private ZonedDateTime getValidUntil(final LocalDate to) {
        if (to == null || to.isEqual(LocalDate.parse("9999-12-31"))) {
            return null;
        }
        return to.atTime(LocalTime.MAX).atZone(ZoneOffset.UTC);
    }

}
