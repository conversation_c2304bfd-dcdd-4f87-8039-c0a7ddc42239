package com.sast.store.productmanagement.service;

import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.models.category.Category;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.productmanagement.rest.CategoryRestService;
import jakarta.inject.Inject;
import jakarta.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.stream.Stream;

@Component
public class CategoryService {
    private static final Logger LOG = LoggerFactory.getLogger(CategoryRestService.class);

    @Inject
    private ProjectApiRoot commercetoolsClient;

    public Stream<Category> getAllCategories(@NotNull final Tenant tenantId, final Integer limit) {
        return commercetoolsClient.categories().get().addLimit(limit)
            .executeBlocking().getBody().getResults().stream();
    }

    public Optional<Category> getCategory(@NotNull final Tenant tenantId, @NotNull final String categoryId) {
        try {
            return Optional.of(commercetoolsClient.categories().withId(categoryId).get().executeBlocking().getBody());
        } catch (final io.vrap.rmf.base.client.error.NotFoundException e) {
            LOG.info("Category not found: {}", categoryId);
            return Optional.empty();
        }
    }
}
