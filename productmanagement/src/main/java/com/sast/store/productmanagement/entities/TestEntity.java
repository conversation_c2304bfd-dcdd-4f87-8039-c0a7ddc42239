package com.sast.store.productmanagement.entities;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.UUID;

@Entity
@Getter
@ToString
@Table(name = "testentity")
public class TestEntity {
    @Id
    @GeneratedValue
    @NotNull
    private UUID id;

    @Setter
    @NotBlank
    private String name;
}
