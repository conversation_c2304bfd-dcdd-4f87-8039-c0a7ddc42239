package com.sast.store.productmanagement.events.commercetools;

import com.commercetools.api.models.customer_group.CustomerGroup;
import com.commercetools.api.models.message.StandalonePriceCreatedMessage;
import com.commercetools.api.models.standalone_price.StandalonePrice;
import com.google.common.base.Preconditions;
import com.sast.store.brimtegration.apimodel.common.product.GroupPriceEntry;
import com.sast.store.brimtegration.apimodel.common.product.ListPriceEntry;
import com.sast.store.brimtegration.apimodel.events.egress.product.EgressProductEventData;
import com.sast.store.brimtegration.apimodel.events.egress.product.data.PlatformGroupPriceAdded;
import com.sast.store.brimtegration.apimodel.events.egress.product.data.PlatformListPriceAdded;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.external.brim.BrimMessagePublisher;
import com.sast.store.external.commercetools.service.CustomerGroupProvider;
import com.sast.store.external.commercetools.util.CustomAttributeProvider;
import com.sast.store.productmanagement.service.ProductService;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Currency;


@Service
@Slf4j
@RequiredArgsConstructor
public class StandalonePriceCreatedStrategy {
    private final ProductService productService;
    private final BrimMessagePublisher brimMessagePublisher;
    private final CustomerGroupProvider customerGroupProvider;

    public void process(@NonNull final StandalonePriceCreatedMessage standalonePriceCreatedMessage) {
        final StandalonePrice standalonePrice = standalonePriceCreatedMessage.getStandalonePrice();
        Preconditions.checkState(!Boolean.TRUE.equals(standalonePrice.getActive()),
                "Standalone price (id=%s) is active, although new prices may not be created in active state",
                standalonePrice.getId());
        // Fallback to rexroth tenant since unpublished variants are not returned by commercetools
        final Tenant tenant = productService.resolveTenant(standalonePrice.getSku())
                .orElse(Tenant.REXROTH);

        final EgressProductEventData priceEvent = handleStandalonePrice(standalonePrice);
        brimMessagePublisher.publishPlatformProductEvent(tenant.id(), priceEvent);
    }

    private EgressProductEventData handleStandalonePrice(@NonNull final StandalonePrice standalonePrice) {
        if (ObjectUtils.anyNotNull(standalonePrice.getCountry(), standalonePrice.getChannel())) {
            throw new NotImplementedException("Standalone prices with country or channel are not supported yet.");
        }

        LocalDate validFromDate = null;
        if (standalonePrice.getValidFrom() != null) {
            validFromDate = standalonePrice.getValidFrom().toInstant().atZone(ZoneOffset.UTC).toLocalDate();
        }

        LocalDate validToDate = null;
        if (standalonePrice.getValidUntil() != null) {
            validToDate = standalonePrice.getValidUntil().toInstant().atZone(ZoneOffset.UTC).toLocalDate();
        }

        if (standalonePrice.getCustomerGroup() != null) {
            LOG.info("Processing standalone price {} as group price as it contains a customer group, but no country or channel",
                    standalonePrice.getId());

            final CustomerGroup customerGroup = customerGroupProvider.findById(standalonePrice.getCustomerGroup().getId())
                    .orElseThrow(() -> new IllegalStateException("CustomerGroup %s could not be retrieved from commercetools"
                            .formatted(standalonePrice.getCustomerGroup().getId())));

            return PlatformGroupPriceAdded.builder()
                    .productId(standalonePrice.getSku())
                    .currency(Currency.getInstance(standalonePrice.getValue().getCurrencyCode()))
                    .priceGroup(customerGroup.getKey())
                    .newEntry(GroupPriceEntry.builder()
                            .storefrontId(standalonePrice.getKey())
                            .amount(CustomAttributeProvider.moneyToBigDecimal(standalonePrice.getValue()))
                            .validFrom(validFromDate)
                            .validTo(validToDate)
                            .build())
                    .build();
        } else {
            LOG.info("Processing standalone price {} as list price as it does not contain customer group, country or channel",
                    standalonePrice.getId());
            return PlatformListPriceAdded.builder()
                    .productId(standalonePrice.getSku())
                    .currency(Currency.getInstance(standalonePrice.getValue().getCurrencyCode()))
                    .newListPriceEntry(ListPriceEntry.builder()
                            .storefrontId(standalonePrice.getKey())
                            .amount(CustomAttributeProvider.moneyToBigDecimal(standalonePrice.getValue()))
                            .validFrom(validFromDate)
                            .validTo(validToDate)
                            .build())
                    .build();
        }
    }
}
