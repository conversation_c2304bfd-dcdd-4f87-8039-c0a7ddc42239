package com.sast.store.productmanagement.events.brim;

import com.sast.store.brimtegration.apimodel.events.ingress.product.IngressProductEvent;
import com.sast.store.brimtegration.apimodel.events.ingress.product.IngressProductEventData;
import com.sast.store.brimtegration.apimodel.events.ingress.product.data.BillingProductData;
import com.sast.store.brimtegration.apimodel.events.ingress.product.data.ProductIdOnlyData;
import com.sast.store.productmanagement.service.PriceService;
import io.awspring.cloud.sqs.annotation.SqsListener;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class BrimProductEventSubscriber {
    private static final Logger LOG = LoggerFactory.getLogger(BrimProductEventSubscriber.class);

    @Inject
    private PriceService priceService;

    @SqsListener(value = "bossstore-brim-product-events.fifo")
    public void onMessage(final @Valid IngressProductEvent<? extends IngressProductEventData> message) {
        LOG.info("Received brim message: {}", message);
        if (!message.errors().isEmpty()) {
            LOG.warn("Received errors: {}", message.errors());
        }

        if (message.data() instanceof final BillingProductData data) {
            priceService.updatePrices(data);
        } else if (message.data() instanceof final ProductIdOnlyData data) {
            LOG.info("Ignoring brim message for product: {}", data);
        } else {
            LOG.warn("Received brim message with unknown data: {}", message.data());
        }
    }
}
