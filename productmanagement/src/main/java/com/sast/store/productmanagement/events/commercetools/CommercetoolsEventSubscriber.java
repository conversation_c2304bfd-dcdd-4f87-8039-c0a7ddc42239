package com.sast.store.productmanagement.events.commercetools;

import com.commercetools.api.models.message.Message;
import com.commercetools.api.models.message.ProductPublishedMessage;
import com.commercetools.api.models.message.ProductVariantAddedMessage;
import com.commercetools.api.models.message.StandalonePriceCreatedMessage;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.awspring.cloud.sqs.annotation.SqsListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CommercetoolsEventSubscriber {
    private final ObjectMapper commercetoolsObjectMapper;
    private final ProductVariantAddedStrategy productVariantAddedStrategy;
    private final StandalonePriceCreatedStrategy standalonePriceCreatedStrategy;
    private final ProductPublishedStrategy productPublishedStrategy;

    public CommercetoolsEventSubscriber(
            @Qualifier("commercetoolsObjectMapper") final ObjectMapper commercetoolsObjectMapper,
            final ProductVariantAddedStrategy productVariantAddedStrategy,
            final StandalonePriceCreatedStrategy standalonePriceCreatedStrategy,
            final ProductPublishedStrategy productPublishedStrategy) {
        this.commercetoolsObjectMapper = commercetoolsObjectMapper;
        this.productVariantAddedStrategy = productVariantAddedStrategy;
        this.standalonePriceCreatedStrategy = standalonePriceCreatedStrategy;
        this.productPublishedStrategy = productPublishedStrategy;
    }

    @SqsListener(value = "bossstore-productmanagement-commercetools-product-events")
    public void onProductMessage(final String payload) {
        processPayload(payload);
    }

    @SqsListener(value = "bossstore-productmanagement-commercetools-price-events")
    public void onPriceMessage(final String payload) {
        processPayload(payload);
    }

    private void processPayload(final String payload) {
        LOG.debug("Received commercetools message: {}", payload);
        final Message message = readMessage(payload);
        switch (message) {
            case final ProductVariantAddedMessage productVariantAddedMessage ->
                    productVariantAddedStrategy.process(productVariantAddedMessage);
            case final ProductPublishedMessage productPublishedMessage ->
                    productPublishedStrategy.process(productPublishedMessage);
            case final StandalonePriceCreatedMessage standalonePriceCreatedMessage ->
                    standalonePriceCreatedStrategy.process(standalonePriceCreatedMessage);
            default -> LOG.info("Ignoring unhandled message type {}", message.getClass().getSimpleName());
        }
    }

    private Message readMessage(final String payload) {
        try {
            return commercetoolsObjectMapper.readValue(payload, Message.class);
        } catch (final JsonProcessingException e) {
            throw new RuntimeException("Json processing error when mapping commercetools message", e);
        }
    }
}
