package com.sast.store.productmanagement.events.commercetools;

import com.commercetools.api.models.message.ProductVariantAddedMessage;
import com.commercetools.api.models.product.ProductVariant;
import com.google.common.base.Preconditions;
import com.sast.store.brimtegration.apimodel.common.product.ContractTypeConfiguration;
import com.sast.store.brimtegration.apimodel.common.product.terms.ConsumptionContractTypeConfiguration;
import com.sast.store.brimtegration.apimodel.common.product.terms.ContractPeriod;
import com.sast.store.brimtegration.apimodel.common.product.terms.FreeContractTypeConfiguration;
import com.sast.store.brimtegration.apimodel.common.product.terms.SubscriptionContractTypeConfiguration;
import com.sast.store.brimtegration.apimodel.events.egress.product.data.PlatformProductCreated;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.external.brim.BrimMessagePublisher;
import com.sast.store.external.commercetools.util.CustomAttributeProvider;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Period;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProductVariantAddedStrategy {
    private final BrimMessagePublisher brimMessagePublisher;

    public void process(@NonNull final ProductVariantAddedMessage variantAddedMessage) {
        final ProductVariant variant = variantAddedMessage.getVariant();
        final Optional<String> externalProductIdAttribute = CustomAttributeProvider.getExternalProductId(variant);
        final Tenant tenant = CustomAttributeProvider.getTenant(variant)
                .orElseThrow(() -> new IllegalStateException(
                        "Given product variant (sku=%s) is missing custom attribute 'tenant'".formatted(variant.getSku())));
        final String sellerId = CustomAttributeProvider.getSellerId(variant)
                .orElseThrow(() -> new IllegalStateException(
                        "Given product variant (sku=%s) is missing custom attribute 'sellerId'".formatted(variant.getSku())));
        final Map<Locale, String> billingName = CustomAttributeProvider.getBillingName(variant);
        Preconditions.checkState(!billingName.isEmpty(), "Given variant %s has no billing name", variant.getSku());

        final PlatformProductCreated.PlatformProductCreatedBuilder platformProductCreatedBuilder = PlatformProductCreated.builder()
                .productId(variant.getSku())
                .name(billingName)
                .contractType(getContractTypeConfiguration(variant))
                .sellerId(sellerId);

        externalProductIdAttribute.ifPresent(platformProductCreatedBuilder::externalProductId);

        brimMessagePublisher.publishPlatformProductEvent(tenant.id(), platformProductCreatedBuilder.build());
    }

    private ContractTypeConfiguration getContractTypeConfiguration(@NonNull final ProductVariant productVariant) {
        final String licenseType = CustomAttributeProvider.getLicenseType(productVariant)
                .orElseThrow(() -> new IllegalStateException(
                        "Given product variant %s is missing custom attribute 'licenseType'".formatted(productVariant.getSku())));

        return switch (licenseType) {
            case "SUBSCRIPTION" -> getSubscriptionConfiguration(productVariant);
            case "TRIAL", "FREE" -> getFreeContractConfiguration(productVariant);
            case "CONSUMPTION" -> getConsumptionConfiguration(productVariant);
            default -> throw new IllegalStateException("Unsupported license type: %s".formatted(licenseType));
        };
    }

    private SubscriptionContractTypeConfiguration getSubscriptionConfiguration(@NonNull final ProductVariant productVariant) {
        final ContractPeriod contractPeriod = CustomAttributeProvider.getRuntime(productVariant)
                .map(ContractPeriod::forEquivalentPeriod)
                .orElseThrow(() -> new IllegalStateException(
                        "Given product variant (sku=%s) is missing custom attribute 'runtime'".formatted(productVariant.getSku())));

        final Period noticePeriod = CustomAttributeProvider.getNoticePeriod(productVariant)
                .orElseThrow(() -> new IllegalStateException(
                        "Given product variant (sku=%s) is missing custom attribute 'noticePeriod'".formatted(productVariant.getSku())));

        return SubscriptionContractTypeConfiguration.builder()
                .contractPeriod(contractPeriod)
                .noticePeriod(noticePeriod)
                .build();
    }

    private FreeContractTypeConfiguration getFreeContractConfiguration(@NonNull final ProductVariant productVariant) {
        final Period contractPeriod = CustomAttributeProvider.getRuntime(productVariant)
                .orElseThrow(() -> new IllegalStateException(
                        "Given product variant (sku=%s) is missing custom attribute 'runtime'".formatted(productVariant.getSku())));

        final Period noticePeriod = CustomAttributeProvider.getNoticePeriod(productVariant)
                .orElseThrow(() -> new IllegalStateException(
                        "Given product variant (sku=%s) is missing custom attribute 'noticePeriod'".formatted(productVariant.getSku())));

        return FreeContractTypeConfiguration.builder()
                .contractPeriod(contractPeriod)
                .noticePeriod(noticePeriod)
                .build();
    }

    private ConsumptionContractTypeConfiguration getConsumptionConfiguration(@NonNull final ProductVariant productVariant) {
        final ContractPeriod contractPeriod = CustomAttributeProvider.getRuntime(productVariant)
                .map(ContractPeriod::forEquivalentPeriod)
                .orElseThrow(() -> new IllegalStateException(
                        "Given product variant (sku=%s) is missing custom attribute 'runtime'".formatted(productVariant.getSku())));

        final Period noticePeriod = CustomAttributeProvider.getNoticePeriod(productVariant)
                .orElseThrow(() -> new IllegalStateException(
                        "Given product variant (sku=%s) is missing custom attribute 'noticePeriod'".formatted(productVariant.getSku())));

        return ConsumptionContractTypeConfiguration.builder()
                .contractPeriod(contractPeriod)
                .noticePeriod(noticePeriod)
                .build();
    }
}
