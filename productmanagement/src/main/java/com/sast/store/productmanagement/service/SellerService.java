package com.sast.store.productmanagement.service;

import com.commercetools.api.models.product.ProductProjection;
import com.sast.store.external.commercetools.util.CustomAttributeProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
public class SellerService {
    private static final Logger LOG = LoggerFactory.getLogger(SellerService.class);

    public Optional<String> getSellerCompanyId(final ProductProjection product) {
        final List<String> list = product.getAllVariants().stream()
            .map(CustomAttributeProvider::getSellerCompanyId)
            .filter(Optional::isPresent)
            .map(Optional::get)
            .distinct()
            .toList();
        if (list.size() != 1) {
            LOG.info("product projection {} has multiple or no sellers: {}", product.getId(), list);
            return Optional.empty();
        }
        return Optional.of(list.getFirst());
    }
}
