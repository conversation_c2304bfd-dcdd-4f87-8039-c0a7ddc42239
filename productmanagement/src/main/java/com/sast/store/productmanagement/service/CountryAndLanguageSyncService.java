package com.sast.store.productmanagement.service;


import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.models.store.Store;
import com.commercetools.api.models.store.StoreSetCountriesAction;
import com.commercetools.api.models.store.StoreSetLanguagesAction;
import com.commercetools.api.models.store.StoreUpdate;
import com.commercetools.api.models.store.StoreUpdateAction;
import com.commercetools.api.models.store_country.StoreCountry;
import com.sast.store.external.commercetools.service.StoreProvider;
import com.sast.store.external.countriesservice.api.CountriesServiceClient;
import com.sast.store.external.countriesservice.api.CountryDto;
import com.sast.store.external.countriesservice.api.Tenant;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class CountryAndLanguageSyncService {
    private static final String COUNTRIES_LABEL = "Countries";
    private static final String LANGUAGES_LABEL = "Languages";

    private final ProjectApiRoot commercetoolsClient;
    private final CountriesServiceClient countriesServiceClient;
    private final StoreProvider storeProvider;

    public void syncCountriesAndLanguages() {
        // synchronize for all known tenants
        for (final Tenant tenant : Tenant.values()) {
            LOG.info("Synchronizing countries and languages for tenant: {}", tenant);
            try {
                final Optional<Store> storeOpt = storeProvider.findByKey(tenant.name(), false);

                storeOpt.ifPresentOrElse(
                        store -> validateCountryAndLanguageData(store, tenant),
                        () -> LOG.warn("No store found for tenant: {}", tenant)
                );
            } catch (final Exception e) {
                LOG.error("Failed to synchronize countries and languages for tenant: {}", tenant, e);
            }
        }
    }

    /**
     * Validates whether the store's configured countries and languages match the source of truth (the country service)
     * If discrepancies are found, it logs the differences and updates the store with the correct countries and languages
     */
    void validateCountryAndLanguageData(@NonNull final Store store, @NonNull final Tenant tenant) {
        final Collection<CountryDto> sourceCountries = countriesServiceClient.listAllCountries(tenant);
        final Collection<CountryDto> filteredCountries = filterActiveCountriesWithStorefront(sourceCountries);

        final List<String> storeLanguages = store.getLanguages();
        final List<String> storeCountries = store.getCountries().stream()
                .map(StoreCountry::getCode)
                .toList();

        final Set<String> sourceLanguages = extractAllLanguages(filteredCountries);
        final Set<String> sourceIsoCodes = extractAllIsoCodes(filteredCountries);

        updateCommerceToolsStoreWithDifferences(storeLanguages, storeCountries,
                sourceLanguages, sourceIsoCodes, store);
    }

    private void updateCommerceToolsStoreWithDifferences(final List<String> storeLanguages, final List<String> storeIsoCodes,
                                                         final Set<String> sourceLanguages, final Set<String> sourceIsoCodes,
                                                         final Store store) {
        final var updates = List.of(
                new StorePropertyUpdate(
                        LANGUAGES_LABEL,
                        Set.copyOf(storeLanguages),
                        sourceLanguages,
                        langs -> StoreSetLanguagesAction.builder()
                                .languages(new ArrayList<>(langs))
                                .build()
                ),
                new StorePropertyUpdate(
                        COUNTRIES_LABEL,
                        Set.copyOf(storeIsoCodes),
                        sourceIsoCodes,
                        countries -> StoreSetCountriesAction.builder()
                                // convert the countries to a list of StoreCountry objects
                                .countries(convertToCountryStore(new ArrayList<>(countries)))
                                .build()
                )
        );

        final List<StoreUpdateAction> modifyingUpdateActions = updates.stream()
                .filter(update -> !update.storeValues().equals(update.sourceValues()))
                .map(update -> {
                    logPendingChanges(update, store);
                    final StoreUpdateAction action = update.actionFactory().apply(update.sourceValues());
                    LOG.info("[Tenant: {}] Updating store {} with new {}: {}",
                            store.getKey(), store.getName(), update.label(), update.sourceValues());
                    return action;
                })
                .toList();

        updateStoreWithActions(store, modifyingUpdateActions);
    }

    private void updateStoreWithActions(@NonNull final Store store, @NonNull final List<StoreUpdateAction> actions) {
        if (actions.isEmpty()) {
            LOG.info("[Tenant: {}] No store update actions to apply found", store.getKey());
            return;
        }

        final StoreUpdate storeUpdate = StoreUpdate.builder()
                .version(store.getVersion())
                .actions(actions)
                .build();
        try {
            commercetoolsClient
                    .stores()
                    .withKey(store.getKey())
                    .post(storeUpdate)
                    .executeBlocking()
                    .getBody();

            LOG.info("[Tenant: {}] Store updated successfully with actions: {}",
                    store.getKey(),
                    actions.stream().map(StoreUpdateAction::getAction).toList());

        } catch (final Exception e) {
            LOG.error("[Tenant: {}] Failed to update store with actions: {}",
                    store.getKey(),
                    actions.stream().map(StoreUpdateAction::getAction).toList(), e);
        }
    }

    private void logPendingChanges(final StorePropertyUpdate update, final Store store) {
        final Set<String> toAdd = new HashSet<>(update.sourceValues());
        toAdd.removeAll(update.storeValues());

        final Set<String> toRemove = new HashSet<>(update.storeValues());
        toRemove.removeAll(update.sourceValues());

        if (!toAdd.isEmpty()) {
            LOG.info("[Tenant: {}] {} will be added to store: {}", store.getKey(), update.label(), toAdd);
        }

        if (!toRemove.isEmpty()) {
            LOG.info("[Tenant: {}] {} will be removed from store: {}", store.getKey(), update.label(), toRemove);
        }
    }

    private Collection<CountryDto> filterActiveCountriesWithStorefront(final Collection<CountryDto> countries) {
        if (countries == null || countries.isEmpty()) {
            return Collections.emptyList();
        }

        return countries.stream()
                .filter(CountryDto::isActiveInStore)
                .filter(country -> hasAnyStorefrontEnabledTenant(country.getTenantConfigurations()))
                .collect(Collectors.toList());
    }

    private boolean hasAnyStorefrontEnabledTenant(final List<CountryDto.TenantConfigurationDto> tenantConfigs) {
        if (tenantConfigs == null || tenantConfigs.isEmpty()) {
            return false;
        }

        return tenantConfigs.stream()
                .anyMatch(config -> Boolean.TRUE.equals(config.isStorefrontEnabled()));
    }

    private List<StoreCountry> convertToCountryStore(final List<String> countries) {
        return countries.stream()
                .map(countryCode -> StoreCountry.builder().code(countryCode).build())
                .toList();
    }

    // return a set of unique ISO codes
    private Set<String> extractAllIsoCodes(final Collection<CountryDto> countries) {
        return countries.stream()
                .map(CountryDto::getIsoCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    // return a set of unique languages
    private Set<String> extractAllLanguages(final Collection<CountryDto> countries) {
        return countries.stream()
                .map(CountryDto::getTenantConfigurations)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .map(CountryDto.TenantConfigurationDto::getLanguages)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    private record StorePropertyUpdate(
            String label,
            Set<String> storeValues,
            Set<String> sourceValues,
            Function<Set<String>, StoreUpdateAction> actionFactory) {
    }

}
