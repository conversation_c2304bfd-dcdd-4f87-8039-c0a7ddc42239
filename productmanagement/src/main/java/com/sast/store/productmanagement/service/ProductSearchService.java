package com.sast.store.productmanagement.service;

import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.models.product_search.ProductSearchRequestBuilder;
import com.commercetools.api.models.product_search.ProductSearchResult;
import com.commercetools.api.models.search.SearchAndExpression;
import com.commercetools.api.models.search.SearchExactExpression;
import com.commercetools.api.models.search.SearchExactValue;
import com.commercetools.api.models.search.SearchSortOrder;
import com.commercetools.api.models.search.SearchSortingBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Currency;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
@RequiredArgsConstructor
public class ProductSearchService {
    private final ProjectApiRoot commercetoolsClient;

    /**
     * {
     *   "query": {
     *     "and": [
     *       {
     *         "exact": {
     *           "field": "productSelections",
     *           "language": "en",
     *           "value": "f938bf47-cae1-40b0-a579-9ad1ef7f3a67"
     *         }
     *       },
     *       {
     *         "exact": {
     *           "field": "stores",
     *           "language": "en",
     *           "value": "f9d314d3-dfd2-4831-bebc-1fc43f3d1c55"
     *         }
     *       }
     *     ]
     *   },
     *   "limit": 10,
     *   "offset": 0
     * }
     */
    public List<ProductSearchResult> search(final String storeId, final String productSelectionId) {
        return commercetoolsClient.products().search()
            .post(
                ProductSearchRequestBuilder.of()
                    .query(SearchAndExpression.builder()
                        .and(List.of(SearchExactExpression.builder()
                                .exact(SearchExactValue.builder()
                                    .field("productSelections")
                                    .value(productSelectionId)
                                    .build())
                                .build(),
                            SearchExactExpression.builder()
                                .exact(SearchExactValue.builder()
                                    .field("stores")
                                    .value(storeId)
                                    .build())
                                .build()))
                        .build())
                    .limit(50)
                    .offset(0)
                    .sort(SearchSortingBuilder.of().field("variants.sku").order(SearchSortOrder.ASC).build())
                    .build())
            .executeBlocking()
            .getBody()
            .getResults();
    }

    public List<ProductSearchResult> search(final Currency currency) {
        final List<ProductSearchResult> searchResults = commercetoolsClient.products().search()
            .post(ProductSearchRequestBuilder.of()
                .query(SearchExactExpression.builder()
                    .exact(SearchExactValue.builder()
                        .field("variants.prices.currencyCode")
                        .value(currency.getCurrencyCode())
                        .build())
                    .build())
                .limit(50)
                .offset(0)
                .sort(SearchSortingBuilder.of().field("variants.sku").order(SearchSortOrder.ASC).build())
                .build())
            .executeBlocking().getBody().getResults();

        return deduplicateById(searchResults);
    }

    // Product search seems to return duplicate results sometimes, so we may deduplicate by ID here.
    private List<ProductSearchResult> deduplicateById(final List<ProductSearchResult> originalResults) {
        final Map<Object, Boolean> takenProductIds = new ConcurrentHashMap<>();
        return originalResults.stream()
            .filter(productSearchResult -> takenProductIds
                .putIfAbsent(productSearchResult.getId(), Boolean.TRUE) == null)
            .toList();
    }
}
