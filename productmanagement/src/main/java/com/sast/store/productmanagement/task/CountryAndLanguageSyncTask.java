package com.sast.store.productmanagement.task;

import com.sast.store.productmanagement.service.CountryAndLanguageSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class CountryAndLanguageSyncTask {

    private final CountryAndLanguageSyncService countryAndLanguageSyncService;

    @Scheduled(fixedRateString = "PT60M", initialDelayString = "PT${random.int[5,25]}M")
    @SchedulerLock(name = "productmanagement.countryAndLanguageSyncTask",
            lockAtMostFor = "PT60M",
            lockAtLeastFor = "PT60M"
    )
    public void sync() {
        LOG.info("started CountryAndLanguageSyncTask");
        try {
            countryAndLanguageSyncService.syncCountriesAndLanguages();
        } catch (final RuntimeException e) {
            LOG.warn("CountryAndLanguageSyncTask failed", e);
        }

    }

}
