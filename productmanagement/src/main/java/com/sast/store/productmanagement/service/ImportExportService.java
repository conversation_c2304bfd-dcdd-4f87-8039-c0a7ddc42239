package com.sast.store.productmanagement.service;

import com.commercetools.api.client.PagedQueryResourceRequest;
import com.commercetools.api.models.ResourcePagedQueryResponse;
import com.commercetools.api.models.common.BaseResource;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.inject.Inject;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class ImportExportService {

    @Inject
    private ObjectMapper objectMapper;

    public <T extends BaseResource> List<T> getAllPaginated(
        final PagedQueryResourceRequest<?, ? extends ResourcePagedQueryResponse<T>, ?> query) {
        String lastId = null;
        final int limit = 20;
        final List<T> result = new ArrayList<>();
        ResourcePagedQueryResponse<T> response = null;
        while (response == null || response.getResults().size() == limit) {
            // Keyset Pagination: fetch page by page always starting after the last id of the previous page
            if (lastId == null) {
                response = query.addWithTotal(false).addLimit(limit)
                    .addSort("id asc")
                    .executeBlocking().getBody();
            } else {
                response = query.addWithTotal(false).addLimit(limit)
                    .addSort("id asc")
                    .addWhere("id > \"" + lastId + "\"")
                    .executeBlocking().getBody();
            }

            if (!response.getResults().isEmpty()) {
                final List<T> list = response.getResults().stream().toList();
                lastId = list.get(list.size() - 1).getId();
                result.addAll(list);
            }
        }

        return result;
    }

    public String toString(final Object p) {
        try {
            return objectMapper.writeValueAsString(p);
        } catch (final JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public <T> T fromString(final String p, final Class<T> clazz) {
        try {
            return objectMapper.readValue(p, clazz);
        } catch (final JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }
}
