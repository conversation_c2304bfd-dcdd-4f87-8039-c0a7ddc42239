package com.sast.store.productmanagement.config;

import jakarta.validation.constraints.NotNull;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.util.Map;

@ConfigurationProperties(prefix = "bossstore")
@Validated
public record AppConfiguration(
    Map<com.sast.store.commons.tenant.api.Tenant, Tenant> tenants
) {
    public Tenant tenant(final com.sast.store.commons.tenant.api.Tenant tenant) {
        return tenants.get(tenant);
    }

    public record Tenant(
        @NotNull Ump ump
    ) { }

    public record Ump(
        String tenant
    ) { }
}
