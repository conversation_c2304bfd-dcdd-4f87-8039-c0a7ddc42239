package com.sast.store.productmanagement.rest;

import com.commercetools.api.models.category.Category;
import com.commercetools.api.models.category.CategoryReference;
import com.sast.store.commons.basewebapp.keycloak.AuthenticationService;
import com.sast.store.commons.basewebapp.security.Unprotected;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.external.commercetools.util.LocalizedFieldProvider;
import com.sast.store.productmanagement.api.CategoryApi;
import com.sast.store.productmanagement.api.CategoryDto;
import com.sast.store.productmanagement.service.CategoryService;
import jakarta.inject.Inject;
import jakarta.validation.constraints.NotNull;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Locale;
import java.util.Optional;

@Component
public class CategoryRestService implements CategoryApi {

    @Inject
    private CategoryService categoryService;

    @Inject
    private AuthenticationService authenticationService;

    @Override
    @Unprotected
    public List<CategoryDto> getAllCategories(@NotNull final Tenant tenantId, final Optional<String> languageCode, final Integer limit) {
        final Locale language = authenticationService.getCommunicationLanguage().or(() -> languageCode)
            .map(Locale::forLanguageTag).orElse(null);
        return categoryService.getAllCategories(tenantId, limit)
            .map(c -> toCategoryDto(c, language))
            .toList();
    }

    private CategoryDto toCategoryDto(final Category category, final Locale language) {
        return CategoryDto.builder()
            .categoryId(category.getId())
            .name(LocalizedFieldProvider.getWithFallback(category.getName(), language).orElse(null))
            .description(LocalizedFieldProvider.getWithFallback(category.getDescription(), language).orElse(null))
            .parentCategoryId(Optional.ofNullable(category.getParent()).map(CategoryReference::getId).orElse(null))
            .parentCategories(category.getAncestors().stream().map(CategoryReference::getId).toList())
            .order(new BigDecimal(category.getOrderHint()))
            .build();
    }

    @Override
    @Unprotected
    public Optional<CategoryDto> getCategory(@NotNull final Tenant tenantId, @NotNull final String categoryId,
        final Optional<String> languageCode) {
        final Locale language = authenticationService.getCommunicationLanguage().or(() -> languageCode)
            .map(Locale::forLanguageTag).orElse(null);

        return categoryService.getCategory(tenantId, categoryId)
            .map(c -> toCategoryDto(c, language));
    }

}
