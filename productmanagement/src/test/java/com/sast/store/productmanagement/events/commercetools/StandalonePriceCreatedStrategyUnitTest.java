package com.sast.store.productmanagement.events.commercetools;

import com.commercetools.api.models.channel.ChannelReferenceBuilder;
import com.commercetools.api.models.common.TypedMoneyBuilder;
import com.commercetools.api.models.customer_group.CustomerGroupBuilder;
import com.commercetools.api.models.customer_group.CustomerGroupReferenceBuilder;
import com.commercetools.api.models.message.StandalonePriceCreatedMessage;
import com.commercetools.api.models.standalone_price.StandalonePriceBuilder;
import com.sast.store.brimtegration.apimodel.common.product.GroupPriceEntry;
import com.sast.store.brimtegration.apimodel.common.product.ListPriceEntry;
import com.sast.store.brimtegration.apimodel.events.egress.product.data.PlatformGroupPriceAdded;
import com.sast.store.brimtegration.apimodel.events.egress.product.data.PlatformListPriceAdded;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.external.brim.BrimMessagePublisher;
import com.sast.store.external.commercetools.service.CustomerGroupProvider;
import com.sast.store.productmanagement.service.ProductService;
import org.apache.commons.lang3.NotImplementedException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.Currency;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class StandalonePriceCreatedStrategyUnitTest {
    private static final String SKU = "ABC12354";
    private static final String PRICE_KEY = "DEF545";
    private static final Tenant TENANT = Tenant.REXROTH;
    private static final String EUR = "EUR";
    private static final String CUSTOMER_GROUP_ID = "fac2f789-e21a-4d8b-a0a5-65a92e87f5b0";
    private static final String CUSTOMER_GROUP_KEY = "IDW001";

    @Mock
    private BrimMessagePublisher brimMessagePublisher;

    @Mock
    private ProductService productService;

    @Mock
    private CustomerGroupProvider customerGroupProvider;

    @InjectMocks
    private StandalonePriceCreatedStrategy standalonePriceCreatedStrategy;


    @Test
    public void testFullValidPriceCreationMessage() {
        final PlatformListPriceAdded expectedBrimEvent = PlatformListPriceAdded.builder()
                .productId(SKU)
                .currency(Currency.getInstance(EUR))
                .newListPriceEntry(ListPriceEntry.builder()
                        .storefrontId(PRICE_KEY)
                        .validFrom(LocalDate.parse("2024-12-20"))
                        .validTo(LocalDate.parse("2024-12-31"))
                        .amount(new BigDecimal("12.34"))
                        .build())
                .build();

        final StandalonePriceCreatedMessage givenCommercetoolsMessage = createMinimalValidMessage();
        givenCommercetoolsMessage.getStandalonePrice()
                .setValidFrom(ZonedDateTime.parse("2024-12-21T01:00:00+02:00"));
        givenCommercetoolsMessage.getStandalonePrice()
                .setValidUntil(ZonedDateTime.parse("2024-12-31T23:59:59+02:00"));

        when(productService.resolveTenant(SKU)).thenReturn(Optional.of(TENANT));

        standalonePriceCreatedStrategy.process(givenCommercetoolsMessage);

        verify(brimMessagePublisher, times(1))
                .publishPlatformProductEvent(eq(TENANT.id()), eq(expectedBrimEvent));
    }

    @Test
    public void testTimeframeIsOptional() {
        final PlatformListPriceAdded expectedBrimEvent = PlatformListPriceAdded.builder()
                .productId(SKU)
                .currency(Currency.getInstance(EUR))
                .newListPriceEntry(ListPriceEntry.builder()
                        .storefrontId(PRICE_KEY)
                        .amount(new BigDecimal("12.34"))
                        .build())
                .build();
        final StandalonePriceCreatedMessage givenCommercetoolsMessage = createMinimalValidMessage();

        when(productService.resolveTenant(SKU)).thenReturn(Optional.of(TENANT));

        standalonePriceCreatedStrategy.process(givenCommercetoolsMessage);

        verify(brimMessagePublisher, times(1))
                .publishPlatformProductEvent(eq(TENANT.id()), eq(expectedBrimEvent));
    }

    @Test
    public void testPriceWithCustomerGroupIsProcessedAsGroupPrice() {
        final PlatformGroupPriceAdded expectedBrimEvent = PlatformGroupPriceAdded.builder()
                .productId(SKU)
                .currency(Currency.getInstance(EUR))
                .priceGroup(CUSTOMER_GROUP_KEY)
                .newEntry(GroupPriceEntry.builder()
                        .storefrontId(PRICE_KEY)
                        .amount(new BigDecimal("12.34"))
                        .build())
                .build();

        final StandalonePriceCreatedMessage givenCommercetoolsMessage = createMinimalValidMessage();
        givenCommercetoolsMessage.getStandalonePrice()
                .setCustomerGroup(CustomerGroupReferenceBuilder.of().id(CUSTOMER_GROUP_ID).buildUnchecked());

        when(productService.resolveTenant(SKU)).thenReturn(Optional.of(TENANT));
        when(customerGroupProvider.findById(CUSTOMER_GROUP_ID))
                .thenReturn(Optional.of(CustomerGroupBuilder.of().id(CUSTOMER_GROUP_ID).key(CUSTOMER_GROUP_KEY).buildUnchecked()));

        standalonePriceCreatedStrategy.process(givenCommercetoolsMessage);

        verify(brimMessagePublisher, times(1))
                .publishPlatformProductEvent(eq(TENANT.id()), eq(expectedBrimEvent));
    }

    @Test
    public void testPriceWithCustomerGroupIsNotProcessedIfGroupCouldNotBeFetched() {
        final StandalonePriceCreatedMessage givenCommercetoolsMessage = createMinimalValidMessage();
        givenCommercetoolsMessage.getStandalonePrice()
                .setCustomerGroup(CustomerGroupReferenceBuilder.of().id(CUSTOMER_GROUP_ID).buildUnchecked());

        when(productService.resolveTenant(SKU)).thenReturn(Optional.of(TENANT));
        when(customerGroupProvider.findById(CUSTOMER_GROUP_ID))
                .thenReturn(Optional.empty());

        assertThatThrownBy(() -> standalonePriceCreatedStrategy.process(givenCommercetoolsMessage))
                .isInstanceOf(IllegalStateException.class);

        verifyNoInteractions(brimMessagePublisher);
    }

    @Test
    public void testPriceWithCountryIsIgnored() {
        final StandalonePriceCreatedMessage givenCommercetoolsMessage = createMinimalValidMessage();
        givenCommercetoolsMessage.getStandalonePrice().setCountry("elbonia");

        when(productService.resolveTenant(SKU)).thenReturn(Optional.of(TENANT));

        assertThatThrownBy(() -> standalonePriceCreatedStrategy.process(givenCommercetoolsMessage))
                .isInstanceOf(NotImplementedException.class);

        verifyNoInteractions(brimMessagePublisher);
    }

    @Test
    public void testPriceWithChannelIsIgnored() {
        final StandalonePriceCreatedMessage givenCommercetoolsMessage = createMinimalValidMessage();
        givenCommercetoolsMessage.getStandalonePrice()
                .setChannel(ChannelReferenceBuilder.of().id("doll").buildUnchecked());

        when(productService.resolveTenant(SKU)).thenReturn(Optional.of(TENANT));

        assertThatThrownBy(() -> standalonePriceCreatedStrategy.process(givenCommercetoolsMessage))
                .isInstanceOf(NotImplementedException.class);

        verifyNoInteractions(brimMessagePublisher);
    }

    @Test
    public void testActivePriceIsIgnored() {
        final StandalonePriceCreatedMessage givenCommercetoolsMessage = createMinimalValidMessage();
        givenCommercetoolsMessage.getStandalonePrice().setActive(true);

        assertThatThrownBy(() -> standalonePriceCreatedStrategy.process(givenCommercetoolsMessage))
                .isInstanceOf(IllegalStateException.class);

        verifyNoInteractions(brimMessagePublisher);
    }

    @Test
    public void testPriceWithUnresolvableTenantDefaultsToRexroth() {
        final PlatformListPriceAdded expectedBrimEvent = PlatformListPriceAdded.builder()
                .productId(SKU)
                .currency(Currency.getInstance(EUR))
                .newListPriceEntry(ListPriceEntry.builder()
                        .storefrontId(PRICE_KEY)
                        .amount(new BigDecimal("12.34"))
                        .build())
                .build();

        final StandalonePriceCreatedMessage givenCommercetoolsMessage = createMinimalValidMessage();

        when(productService.resolveTenant(SKU)).thenReturn(Optional.empty());

        standalonePriceCreatedStrategy.process(givenCommercetoolsMessage);

        verify(brimMessagePublisher, times(1))
                .publishPlatformProductEvent(eq(TENANT.id()), eq(expectedBrimEvent));
    }

    private StandalonePriceCreatedMessage createMinimalValidMessage() {
        return StandalonePriceCreatedMessage.builder()
                .standalonePrice(StandalonePriceBuilder.of()
                        .active(false)
                        .sku(SKU)
                        .key(PRICE_KEY)
                        .value(TypedMoneyBuilder.of().centPrecisionBuilder()
                                .centAmount(1234L)
                                .fractionDigits(2)
                                .currencyCode(EUR)
                                .buildUnchecked())
                        .buildUnchecked())
                .buildUnchecked();
    }
}
