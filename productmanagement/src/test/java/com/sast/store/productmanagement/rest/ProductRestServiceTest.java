package com.sast.store.productmanagement.rest;

import com.sast.store.external.countriesservice.util.CountriesServiceMockExtension;
import com.sast.store.external.ump.test.UmpMockExtension;
import com.sast.store.productmanagement.AbstractComponentTest;
import com.sast.store.productmanagement.util.CommercetoolsMockExtension;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Test;

import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;

class ProductRestServiceTest extends AbstractComponentTest {

    @Test
    void testGetProducts() {
        CommercetoolsMockExtension.withDefaultResponse();
        UmpMockExtension.withDefaultResponse();
        CountriesServiceMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .accept(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/products")
            .then().statusCode(200)
            .body("[0].id", equalTo("6393b78f-f6f7-4d2f-a6a1-5ddb59dca45e"))
            .body("[0].name", equalTo("Hydraulic Hub"))
            .body("[0].description", equalTo("Hydraulic Hub description"))
            .body("[0].productType", equalTo("a46f1d35-7553-4813-8a20-993f1e84b9f4"))
            .body("[0].images", hasSize(2))
            .body("[0].images[1]", equalTo("https://images.cdn.europe-west1.gcp.commercetools.com/"
                + "b4800138-a539-4ad0-9511-9260c02d29bb/Marketplace%20Products-rUAZGHEo.png"))
            .body("[0].variants", hasSize(3))
            .body("[0].variants[0].sku", equalTo("DRX_PR_HUB_DE_FREE"))
            .body("[0].variants[0].price.value", equalTo(0.0f))
            .body("[0].variants[0].price.currencyCode", equalTo("EUR"))
            .body("[0].variants[0].bundleAmount", equalTo(1))
            .body("[0].variants[0].licenseType", equalTo("TRIAL"))
            .body("[0].variants[0].runtime", equalTo("P1D"))
            .body("[0].variants[0].noticePeriod", equalTo("P0D"))
            .body("[0].variants[0].name", equalTo("Free trial"))
            .body("[0].variants[0].description", equalTo("this is the free trial"))
            .body("[0].variants[0].features", equalTo("feature 1\nfeature 2a"))
            .body("[0].variants[0].agreements", hasSize(2))
            .body("[0].variants[0].agreements[0].name", equalTo("Data Management T&C"))
            .body("[0].variants[0].agreements[0].url",
                equalTo(
                    "https://dc-mkt-prod.cloud.bosch.tech/xrm/media/global/documents/legal_notice/tc_for_data_management_services.pdf"))
            .body("[0].variants[0].agreements[0].linkType", equalTo("SOFTWARE_LICENSE"))
            .body("[0].variants[0].agreements[1].name", equalTo("Device Management T&C"))
            .body("[0].variants[0].agreements[1].url",
                equalTo("https://dc-mkt-prod.cloud.bosch.tech/xrm/media/global/documents/legal_notice/tc_devicemanagement_saas.pdf"))
            .body("[0].variants[0].agreements[1].linkType", equalTo("SOFTWARE_LICENSE"))
            .body("[0].variants[0].priceList", equalTo("https://assets.mp-dc-d.com/prices/PRICE_LIST-EUR_%20DE_2025_f07dc26708f2.pdf"))
            .body("[0].variants[0].entitlements", hasSize(1))
            .body("[0].variants[0].entitlements[0]", equalTo("DCKEYCLOAK:FREEMIUM"))
            .body("[0].variants[0].addons", hasSize(0));

    }

    @Test
    void testGetProductsAnonymousUser() {
        CommercetoolsMockExtension.withDefaultResponse();
        CountriesServiceMockExtension.withDefaultResponse();

        RestAssured
            .given()
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/products")
            .then().statusCode(200)
            .body("[0].id", notNullValue())
            .body("[0].name", notNullValue())
            .body("[0].description", notNullValue())
            .body("[0].productType", notNullValue())
            .body("[0].images[0]", notNullValue())
            .body("[0].variants[0].price.value", nullValue())
            .body("[0].variants[0].price.currencyCode", nullValue())
            .body("[0].variants[0].name", notNullValue())
            .body("[0].variants[0].description", notNullValue())
            .body("[0].variants[0].features", notNullValue())
            .body("[0].externalDocuments[0]", notNullValue())
            .body("[0].externalDocuments[0].name", notNullValue())
            .body("[0].externalDocuments[0].url", notNullValue())
            .body("[0].variants[0].agreements[0]", notNullValue())
            .body("[0].variants[0].agreements[0].name", notNullValue())
            .body("[0].variants[0].agreements[0].url", notNullValue());
    }

    @Test
    void testGetProductsGroupNotFound() {
        CommercetoolsMockExtension.withDefaultResponse();
        CommercetoolsMockExtension.withCustomerGroupNotFound();
        UmpMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/products")
            .then().statusCode(200)
            .body(".", Matchers.hasSize(2));
    }

    @Test
    void testGetProductsWithAddons() {
        CommercetoolsMockExtension.withDefaultResponse();
        UmpMockExtension.withDefaultResponse();
        CountriesServiceMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .accept(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/products")
            .then().statusCode(200)
            .body("[1].id", equalTo("5abd3673-2110-4aa0-bbf9-c97cd1e24c89"))
            .body("[1].name", equalTo("BODAS Connect for RCU Series 10 & 20"))
            .body("[1].variants", hasSize(2))
            .body("[1].variants[0].sku", equalTo("DRX_PR_BODAS_COMPACT"))
            .body("[1].variants[0].price.value", equalTo(249.0f))
            .body("[1].variants[0].price.currencyCode", equalTo("EUR"))
            .body("[1].variants[0].bundleAmount", equalTo(1))
            .body("[1].variants[0].licenseType", equalTo("SUBSCRIPTION_POSTPAID"))
            .body("[1].variants[0].runtime", equalTo("P1M"))
            .body("[1].variants[0].noticePeriod", equalTo("P28D"))
            .body("[1].variants[0].name", equalTo("Device Management Compact"))
            .body("[1].variants[0].description", equalTo("SOTA, Custom Snaps, Cybersecurity"))
            .body("[1].variants[0].features", equalTo("SOTA\nCustom Snaps\nCybersecurity"))
            .body("[1].variants[0].agreements", hasSize(0))
            .body("[1].variants[0].entitlements", hasSize(0))
            .body("[1].variants[0].addons", hasSize(1))
            .body("[1].variants[0].addons[0].name", equalTo("Cellular Connection"))
            .body("[1].variants[0].addons[0].description", equalTo("Cellular Services for RCU Series 10 & 20"))
            .body("[1].variants[0].addons[0].addonVariants", hasSize(2))
            .body("[1].variants[0].addons[0].addonVariants[0].sku", equalTo("DRX_PR_BODAS_CONNECTION_50MB"))
            .body("[1].variants[0].addons[0].addonVariants[0].name", equalTo("50 MB"))
            .body("[1].variants[0].addons[0].addonVariants[0].description", equalTo("50MB of data"))
            .body("[1].variants[0].addons[0].addonVariants[1].sku", equalTo("DRX_PR_BODAS_CONNECTION_100MB"))
            .body("[1].variants[0].addons[0].addonVariants[1].name", equalTo("100 MB"))
            .body("[1].variants[0].addons[0].addonVariants[1].description", equalTo("100 MB of data"))
            .body("[1].externalDocuments", hasSize(0))
            .body("[1].sellerCompany.name", equalTo("UPM Tester"))
            .body("[1].categories", hasSize(0));

    }

}
