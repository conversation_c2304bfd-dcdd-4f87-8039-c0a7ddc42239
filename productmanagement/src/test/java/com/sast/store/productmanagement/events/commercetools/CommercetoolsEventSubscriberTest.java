package com.sast.store.productmanagement.events.commercetools;

import com.sast.store.productmanagement.AbstractComponentTest;
import com.sast.store.productmanagement.util.CommercetoolsMockExtension;
import com.sast.store.testing.awsmockup.junit.SnsMockExtension;
import com.sast.store.testing.awsmockup.junit.SqsMockExtension;
import org.awaitility.Awaitility;
import org.json.JSONException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.RegularExpressionValueMatcher;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.skyscreamer.jsonassert.comparator.JSONComparator;

import java.time.Duration;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class CommercetoolsEventSubscriberTest extends AbstractComponentTest {
    @AfterEach
    public void tearDown() {
        SnsMockExtension.resetSnsRequests();
    }

    @Test
    public void testProductVariantAddedMessage() throws JSONException {
        CommercetoolsMockExtension.withDefaultResponse();

        SqsMockExtension.sendMessage("/queue/bossstore-productmanagement-commercetools-product-events",
                loadResourceFile("/events/commercetools/product-variant-added.json"));

        final List<String> sentMessages = Awaitility.await().atMost(Duration.ofSeconds(5))
                .until(SnsMockExtension::getAllPublishedMessages, list -> !list.isEmpty());

        JSONAssert.assertEquals(loadResourceFile("/events/platform/egress/product-created.json"),
                sentMessages.getFirst(), platformMessageComparator());
    }

    @Test
    public void testPriceAddedMessage() throws JSONException {
        CommercetoolsMockExtension.withDefaultResponse();

        SqsMockExtension.sendMessage("/queue/bossstore-productmanagement-commercetools-price-events",
                loadResourceFile("/events/commercetools/price-added.json"));

        final List<String> sentMessages = Awaitility.await().atMost(Duration.ofSeconds(5))
                .until(SnsMockExtension::getAllPublishedMessages, list -> !list.isEmpty());

        JSONAssert.assertEquals(loadResourceFile("/events/platform/egress/list-price-changed.json"),
                sentMessages.getFirst(), platformMessageComparator());
    }

    @Test
    public void testGroupPriceAddedMessage() throws JSONException {
        CommercetoolsMockExtension.withDefaultResponse();

        SqsMockExtension.sendMessage("/queue/bossstore-productmanagement-commercetools-price-events",
                loadResourceFile("/events/commercetools/group-price-added.json"));

        final List<String> sentMessages = Awaitility.await().atMost(Duration.ofSeconds(5))
                .until(SnsMockExtension::getAllPublishedMessages, list -> !list.isEmpty());

        JSONAssert.assertEquals(loadResourceFile("/events/platform/egress/group-price-added.json"),
                sentMessages.getFirst(), platformMessageComparator());
    }

    @Test
    public void testPriceWithChannelIsIgnored() {
        CommercetoolsMockExtension.withDefaultResponse();

        SqsMockExtension.sendMessage("/queue/bossstore-productmanagement-commercetools-price-events",
                loadResourceFile("/events/commercetools/channel-price-added.json"));

        final List<String> sentMessages = Awaitility.await().during(Duration.ofSeconds(3)).atMost(Duration.ofSeconds(5))
                .until(SnsMockExtension::getAllPublishedMessages, List::isEmpty);

        assertThat(sentMessages).isEmpty();
    }

    @Test
    public void testPriceWithCountryIsIgnored() {
        CommercetoolsMockExtension.withDefaultResponse();

        SqsMockExtension.sendMessage("/queue/bossstore-productmanagement-commercetools-price-events",
                loadResourceFile("/events/commercetools/country-price-added.json"));

        final List<String> sentMessages = Awaitility.await().during(Duration.ofSeconds(3)).atMost(Duration.ofSeconds(5))
                .until(SnsMockExtension::getAllPublishedMessages, List::isEmpty);

        assertThat(sentMessages).isEmpty();
    }

    @Test
    public void testActivePriceIsIgnored() {
        CommercetoolsMockExtension.withDefaultResponse();

        SqsMockExtension.sendMessage("/queue/bossstore-productmanagement-commercetools-price-events",
                loadResourceFile("/events/commercetools/active-price-added.json"));

        final List<String> sentMessages = Awaitility.await().during(Duration.ofSeconds(3)).atMost(Duration.ofSeconds(5))
                .until(SnsMockExtension::getAllPublishedMessages, List::isEmpty);

        assertThat(sentMessages).isEmpty();
    }

    @Test
    public void testProductPublishedMessage() throws JSONException {
        CommercetoolsMockExtension.withDefaultResponse();

        SqsMockExtension.sendMessage("/queue/bossstore-productmanagement-commercetools-product-events",
                loadResourceFile("/events/commercetools/product-published.json"));

        final List<String> sentMessages = Awaitility.await().atMost(Duration.ofSeconds(5))
                .until(SnsMockExtension::getAllPublishedMessages, list -> !list.isEmpty());

        JSONAssert.assertEquals(loadResourceFile("/events/platform/egress/product-exportable.json"),
                sentMessages.getFirst(), platformMessageComparator());
    }

    private static JSONComparator platformMessageComparator() {
        return new CustomComparator(JSONCompareMode.NON_EXTENSIBLE,
                new Customization("header.timestamp", new RegularExpressionValueMatcher<>("\\S+")),
                new Customization("header.id.eventId", new RegularExpressionValueMatcher<>("\\S+")),
                new Customization("header.cause.eventId", new RegularExpressionValueMatcher<>("\\S+")));
    }

}