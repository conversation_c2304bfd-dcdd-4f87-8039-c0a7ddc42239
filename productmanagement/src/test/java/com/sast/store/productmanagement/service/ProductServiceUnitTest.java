package com.sast.store.productmanagement.service;

import com.commercetools.api.client.ByProjectKeyProductProjectionsByIDGet;
import com.commercetools.api.client.ByProjectKeyProductProjectionsByIDRequestBuilder;
import com.commercetools.api.client.ByProjectKeyProductProjectionsRequestBuilder;
import com.commercetools.api.client.ByProjectKeyStoresKeyByKeyRequestBuilder;
import com.commercetools.api.client.ByProjectKeyStoresRequestBuilder;
import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.models.product.ProductProjection;
import com.commercetools.api.models.product_search.ProductSearchResult;
import com.commercetools.api.models.product_selection.ProductSelection;
import com.commercetools.api.models.product_selection.ProductSelectionReference;
import com.commercetools.api.models.store.ProductSelectionSetting;
import com.commercetools.api.models.store.Store;
import com.commercetools.api.models.type.CustomFields;
import com.commercetools.api.models.type.FieldContainer;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.external.commercetools.service.CustomerGroupProvider;
import com.sast.store.external.commercetools.service.ProductSelectionProvider;
import com.sast.store.external.commercetools.service.StoreProvider;
import com.sast.store.external.countriesservice.api.CountriesServiceClient;
import com.sast.store.external.countriesservice.api.CountryDto;
import com.sast.store.external.ump.UmpClient;
import com.sast.store.productmanagement.config.AppConfiguration;
import io.vrap.rmf.base.client.ApiHttpResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ProductServiceUnitTest {
    private static final String PRODUCT_SELECTION_ID_DE = "dummytenant_productselection_de";
    private static final String PRODUCT_SELECTION_ID_US = "dummytenant_productselection_us";
    private static final String EUR = "EUR";
    private static final String COMPANY_ID = "d25ebf9b-55d1-44d2-a72d-35243effd9bf";
    private static final String STORE_ID = "dummy_store_id";

    @Mock
    private ProjectApiRoot commercetoolsClient;

    @Mock
    private UmpClient umpClient;

    @Mock
    private ImportExportService importExportService;

    @Mock
    private AppConfiguration appConfiguration;

    @Mock
    private CountriesServiceClient countriesServiceClient;

    @Mock
    private CustomerGroupProvider customerGroupProvider;

    @Mock
    private ProductSearchService productSearchService;

    @Mock
    private StoreProvider storeProvider;

    @Mock
    private ProductSelectionProvider productSelectionProvider;

    @Mock
    private ByProjectKeyProductProjectionsRequestBuilder productProjectionsRequestBuilder;

    @Mock
    private ByProjectKeyStoresRequestBuilder requestBuilder;

    @Mock
    private ByProjectKeyStoresKeyByKeyRequestBuilder byKeyRequestBuilder;

    @Mock
    private ByProjectKeyProductProjectionsByIDRequestBuilder byIDRequestBuilder;

    @Mock
    private ByProjectKeyProductProjectionsByIDGet byIDGet;

    @Mock
    private ApiHttpResponse<ProductProjection> projectionApiHttpResponse;

    @Mock
    private ApiHttpResponse<Store> apiHttpResponse;

    @Mock
    private ProductSelectionSetting productSelectionSetting;

    @Mock
    private ProductSelectionReference productSelectionReference;

    @Mock
    private ProductSelection productSelection_de;

    @Mock
    private ProductSelection productSelection_us;

    @Mock
    private CustomFields customFields;

    @Mock
    private FieldContainer fieldContainer;

    @Mock
    private ProductSearchResult productSearchResult;

    @Mock
    private Store store;

    @Mock
    private ProductProjection productProjection;

    @InjectMocks
    private ProductService productService;

    @BeforeEach
    public void setup() {
        appConfiguration = new AppConfiguration(Map.of(Tenant.REXROTH, new AppConfiguration.Tenant(new AppConfiguration.Ump("rexroth"))));
        when(storeProvider.findByKey(anyString(), anyBoolean())).thenReturn(Optional.of(store));
        lenient().when(store.getProductSelections()).thenReturn(List.of(productSelectionSetting));
        lenient().when(productSelectionSetting.getActive()).thenReturn(true);
        lenient().when(productSearchService.search(anyString(), anyString())).thenReturn(List.of(productSearchResult));
        lenient().when(productSearchResult.getId()).thenReturn("dummyProductId");
        lenient().when(store.getId()).thenReturn(STORE_ID);
        productService = new ProductService(commercetoolsClient, umpClient, importExportService, appConfiguration, countriesServiceClient,
            customerGroupProvider, productSearchService, storeProvider, productSelectionProvider);
    }

    @Test
    void fetchProducts_for_de_should_return_theResponseBody() {
        mockProductSelection(Locale.GERMANY.getCountry().toLowerCase());
        mockCountriesService(Locale.GERMANY.getCountry());
        mockProjectionsRequestBuilder();
        final List<ProductProjection> actualResult = productService.fetchProducts(Tenant.REXROTH, COMPANY_ID, Locale.GERMANY);
        assertThat(storeProvider.findByKey("key", false)).contains(store);
        assertThat(actualResult).isNotEmpty();
    }

    @Test
    void fetchProducts_for_us_should_return_theResponseBody() {
        mockProductSelection(Locale.US.getCountry().toLowerCase());
        mockCountriesService(Locale.US.getCountry());
        mockProjectionsRequestBuilder();
        final List<ProductProjection> actualResult = productService.fetchProducts(Tenant.REXROTH, COMPANY_ID, Locale.US);
        assertThat(storeProvider.findByKey("key", false)).contains(store);
        assertThat(actualResult).isNotEmpty();
    }

    @Test
    void fetchProducts_with_unknown_store_should_throw_exception() {
        when(storeProvider.findByKey(anyString(), anyBoolean())).thenThrow(new IllegalStateException("Simulated exception"));
        assertThrows(IllegalStateException.class, () -> productService.fetchProducts(Tenant.fromString("baam"), COMPANY_ID, Locale.US));
    }

    private void mockCountriesService(final String countrCode) {
        when(countriesServiceClient.getCountry(com.sast.store.external.countriesservice.api.Tenant.rexroth, countrCode))
            .thenReturn(new CountryDto.Builder()
                .tenantConfigurations(List.of(new CountryDto.TenantConfigurationDto.Builder()
                    .currency(EUR)
                    .build()))
                .build());
    }

    private void mockProjectionsRequestBuilder() {
        doReturn(productProjectionsRequestBuilder).when(commercetoolsClient).productProjections();
        doReturn(byIDRequestBuilder).when(productProjectionsRequestBuilder).withId(anyString());
        doReturn(byIDGet).when(byIDRequestBuilder).get();
        doReturn(byIDGet).when(byIDGet).withPriceCustomerGroup(anyList());
        doReturn(byIDGet).when(byIDGet).withPriceCurrency(anyString());
        doReturn(byIDGet).when(byIDGet).withPriceCountry(anyString());
        doReturn(byIDGet).when(byIDGet).withLocaleProjection(anyString());
        doReturn(byIDGet).when(byIDGet).addExpand(anyString());
        doReturn(projectionApiHttpResponse).when(byIDGet).executeBlocking();
        doReturn(productProjection).when(projectionApiHttpResponse).getBody();
    }

    private void mockProductSelection(final String countrCode) {
        ProductSelection productSelection = null;
        String productSelectionId = null;
        switch (countrCode) {
            case "de" -> {
                productSelection = productSelection_de;
                productSelectionId = PRODUCT_SELECTION_ID_DE;
            }
            case "us" -> {
                productSelection = productSelection_us;
                productSelectionId = PRODUCT_SELECTION_ID_US;
            }
            default -> throw new IllegalStateException("Unexpected value: " + countrCode);
        }
        when(productSelectionSetting.getProductSelection()).thenReturn(productSelectionReference);
        when(productSelectionReference.getId()).thenReturn("productSelectionId");
        when(productSelectionProvider.findById(anyString())).thenReturn(Optional.of(productSelection));
        when(productSelection.getCustom()).thenReturn(customFields);
        when(customFields.getFields()).thenReturn(fieldContainer);
        when(fieldContainer.values()).thenReturn(Map.of("product-selection-country", countrCode));
        when(productSelection.getId()).thenReturn(productSelectionId);
    }
}