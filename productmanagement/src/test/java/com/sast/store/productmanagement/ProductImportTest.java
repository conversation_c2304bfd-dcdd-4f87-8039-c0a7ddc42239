package com.sast.store.productmanagement;

import com.sast.store.productmanagement.service.PriceService;
import com.sast.store.productmanagement.service.ProductService;
import jakarta.inject.Inject;
import org.junit.jupiter.api.Test;

import java.nio.file.Path;
import java.nio.file.Paths;

class ProductImportTest extends AbstractIntegrationTest {

    @Inject
    private ProductService productService;

    @Inject
    private PriceService priceService;

    @Test
    void testExportPrices() throws Exception {
        final Path path = Paths.get(ProductImportTest.class.getResource("/prices.json").toURI());
        priceService.exportPrices(path);
    }

    // this will overwrite all prices, so be careful
    // @Test
    void testImportPrices() throws Exception {
        final Path path = Paths.get(ProductImportTest.class.getResource("/prices.json").toURI());
        priceService.importPrices(path);
    }

    @Test
    void testExportProducts() throws Exception {
        final Path path = Paths.get(ProductImportTest.class.getResource("/products.json").toURI());
        productService.exportProducts(path);

    }

    // this will overwrite all products, so be careful
    // @Test
    void testImportProducts() throws Exception {
        final Path path = Paths.get(ProductImportTest.class.getResource("/products.json").toURI());
        productService.importProducts(path);

    }

}
