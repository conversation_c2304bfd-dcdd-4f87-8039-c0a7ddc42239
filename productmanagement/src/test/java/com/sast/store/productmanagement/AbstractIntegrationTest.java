package com.sast.store.productmanagement;

import com.sast.store.external.countriesservice.util.CountriesServiceMockExtension;
import com.sast.store.external.ump.test.UmpMockExtension;
import com.sast.store.productmanagement.util.PostgresExtension;
import com.sast.store.testing.awsmockup.junit.SqsMockExtension;
import com.tngtech.keycloakmock.api.ServerConfig;
import com.tngtech.keycloakmock.junit5.KeycloakMockExtension;
import io.restassured.RestAssured;
import io.restassured.filter.log.RequestLoggingFilter;
import io.restassured.filter.log.ResponseLoggingFilter;
import io.restassured.specification.RequestSpecification;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;

import java.util.Arrays;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;

import static com.tngtech.keycloakmock.api.TokenConfig.aTokenConfig;

@ExtendWith(UmpMockExtension.class)
@ExtendWith(SqsMockExtension.class)
@ExtendWith(CountriesServiceMockExtension.class)
@SpringBootTest(classes = ProductmanagementApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, properties = {
    // disable caching of health endpoints
    "endpoints.health.time-to-live=0",
    "bossstore.hazelcast.enabled=local",
    "bossstore.commercetools.clientId=SSAIdVhBpKt80ChCMjZwxi4c",
    "bossstore.commercetools.clientSecret=XtXHlio2S_z0UbW4KSfFniBO3dTklMfW",
    "bossstore.commercetools.projectKey=sast-dev",
    "spring.cloud.aws.region.static=eu-central-1",
    "spring.cloud.aws.credentials.access-key=fakekey",
    "spring.cloud.aws.credentials.secret-key=fakekey",
    "logging.level.commercetools=DEBUG",
    "bossstore.countriesservice.url=http://localhost:40002",
    "bossstore.ump.url=http://localhost:40101",
    "bossstore.ump.username=admin",
    "bossstore.ump.password=admin"

})
@Tag("integrationTest")
public abstract class AbstractIntegrationTest {
    protected static final String COMPANY_ID = UUID.randomUUID().toString();

    // CHECKSTYLE OFF: StaticVariableNameCheck
    protected static RequestSpecification VALID_KEYCLOAK_TOKEN;

    @RegisterExtension
    protected static final PostgresExtension POSTGRES = PostgresExtension.createAndStart();

    @RegisterExtension
    protected static final KeycloakMockExtension KEYCLOAK_MOCK = new KeycloakMockExtension(
        ServerConfig.aServerConfig()
            .withPort(8000)
            .withDefaultRealm("baam")
            .build());

    @Value("http://localhost:${local.server.port}")
    protected String host;

    @DynamicPropertySource
    private static void overrideProperties(final DynamicPropertyRegistry registry) {
        registry.add("spring.cloud.aws.sqs.endpoint", () -> "http://localhost:" + SqsMockExtension.RANDOM_PORT);
        POSTGRES.registerPropertySources(registry);
    }

    @BeforeAll
    public static void setUpUrl() {
        Locale.setDefault(Locale.US);
        RestAssured.enableLoggingOfRequestAndResponseIfValidationFails();
        RestAssured.filters(new RequestLoggingFilter(), new ResponseLoggingFilter());
    }

    @BeforeEach
    public void setUpKeycloakToken() {
        setupKeycloakToken("VIEW_ORDER_DETAILS", "EDIT_PAYMENT_DETAILS", "VIEW_PRICE", "PLACE_ORDER", "MANAGE_BACKOFFICE");
    }

    protected static void setupKeycloakToken(final String... roles) {
        final String accessToken = KEYCLOAK_MOCK.getAccessToken(aTokenConfig()
            .withSubject("user")
            .withAuthorizedParty("bossstore-frontend")
            .withClaims(Map.of(
                "resource_access", Map.of("bossstore-backend", Map.of("roles", Arrays.asList(roles))),
                "name", "John Doe",
                "preferred_username", "7cb0fdd4-9503-41e0-a279-84ad098c8a00",
                "given_name", "John",
                "family_name", "Doe",
                "email", "<EMAIL>",
                "company_name", "THE Company",
                "communication_language", "de",
                "company_id", COMPANY_ID))
            .build());
        VALID_KEYCLOAK_TOKEN = RestAssured.given().auth().preemptive()
            .oauth2(accessToken);
    }
}
