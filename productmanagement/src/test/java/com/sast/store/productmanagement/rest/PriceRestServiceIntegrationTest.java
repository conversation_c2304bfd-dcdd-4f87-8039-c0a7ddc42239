package com.sast.store.productmanagement.rest;

import com.sast.store.productmanagement.AbstractIntegrationTest;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Test;

class PriceRestServiceIntegrationTest extends AbstractIntegrationTest {

    @Test
    void testExportPrices() {
        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.fromContentType("application/octet-stream"))
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/prices")
            .then().statusCode(200)
            .body(Matchers.startsWith("{\"key\":\""));
    }
}
