package com.sast.store.productmanagement.util;

import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.defaultconfig.ApiRootBuilder;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.github.tomakehurst.wiremock.junit5.WireMockExtension;
import com.sast.store.external.commercetools.CommercetoolsConfiguration;
import com.sast.store.external.commercetools.CommercetoolsRestMetricsJerseyClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.matching;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching;

public class CommercetoolsMockExtension extends WireMockExtension {

    private static CommercetoolsMockExtension instance;

    public CommercetoolsMockExtension() {
        super(WireMockExtension
            .extensionOptions()
            .options(WireMockConfiguration.options()
                .port(40000))
            .configureStaticDsl(true));
        instance = this;
    }

    public static CommercetoolsMockExtension get() {
        return instance;
    }

    public static CommercetoolsMockExtension withDefaultResponse() {
        withProductProjectionsSearchResponse();
        withCustomerGroupResponse();
        withStandalonePriceResponse();
        withUpdateStandalonePriceResponse();
        withGetCategoriesResponse();
        withGetCategoryResponse();
        withGetStoreResponse();
        withGetProductSelectionResponse();
        withGetProductResponse();
        withProductSearchResponse();
        withProductProjectionWithAddonsResponse();
        withProductProjectionWithoutAddonsResponse();

        return instance;
    }

    public static CommercetoolsMockExtension withCustomerGroupResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/customer-groups/.*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody(
                    """
                        {
                          "id": "aef9cf41-94ad-4794-8122-62d308900430",
                          "version": 2,
                          "key": "WD0001",
                          "name": "Wholesaler/Distributor 1",
                          "createdAt": "2017-01-10T06:51:25.896Z",
                          "lastModifiedAt": "2017-01-10T06:51:25.946Z"
                        }""")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withCustomerGroupNotFound() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/customer-groups/.*"))
            .willReturn(aResponse()
                .withStatus(404)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody(
                    """
                        {
                          "statusCode" : 404,
                          "message" : "The Resource with key 'KASDF0' was not found.",
                          "errors" : [ {
                            "code" : "ResourceNotFound",
                            "message" : "The Resource with key 'KASDF0' was not found."
                          } ]
                        }""")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withProductProjectionsSearchResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/product-projections/search"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("commercetools/product-projection-search-response.json")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withStandalonePriceResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/standalone-prices/key=.*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("commercetools/get-standalone-price-response.json")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withUpdateStandalonePriceResponse() {
        instance.stubFor(WireMock.post(urlPathMatching("/.*/standalone-prices/key=.*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("commercetools/update-standalone-price-response.json")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withGetProductResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/products"))
                .withQueryParam("where", matching(".*"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("content-type", "application/json;charset=UTF-8")
                        .withBodyFile("commercetools/get-product-response.json")
                        .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withGetStoreResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/stores/key=.*"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("content-type", "application/json;charset=UTF-8")
                        .withBodyFile("commercetools/get-store-response.json")
                        .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withGetProductSelectionResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/product-selections/.*"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("content-type", "application/json;charset=UTF-8")
                        .withBodyFile("commercetools/get-product-selection-response.json")
                        .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withGetCategoriesResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/categories"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody(
                    """
                        {
                            "limit": 100,
                            "offset": 0,
                            "count": 2,
                            "total": 2,
                            "results": [
                                {
                                    "id": "1e6c38eb-1895-4d0c-a885-67d2ed5613cd",
                                    "version": 2,
                                    "versionModifiedAt": "2024-11-21T09:53:18.165Z",
                                    "lastMessageSequenceNumber": 1,
                                    "createdAt": "2024-11-13T14:23:33.916Z",
                                    "lastModifiedAt": "2024-11-21T09:53:18.165Z",
                                    "lastModifiedBy": {
                                        "isPlatformClient": true,
                                        "user": {
                                            "typeId": "user",
                                            "id": "bd8dec93-e3e2-498c-8641-6329b89f1862"
                                        }
                                    },
                                    "createdBy": {
                                        "isPlatformClient": true,
                                        "user": {
                                            "typeId": "user",
                                            "id": "f4ce7654-6df3-4f08-b774-c56172cc4ea6"
                                        }
                                    },
                                    "key": "CAT_FLORP",
                                    "name": {
                                        "en": "Florp"
                                    },
                                    "slug": {
                                        "en": "florp"
                                    },
                                    "description": {
                                        "en": "Florpige Kategorie"
                                    },
                                    "ancestors": [
                                        {
                                            "typeId": "category",
                                            "id": "3fee4907-2ea1-48b1-8c07-6c9da3c73e2f"
                                        }
                                    ],
                                    "parent": {
                                        "typeId": "category",
                                        "id": "3fee4907-2ea1-48b1-8c07-6c9da3c73e2f"
                                    },
                                    "orderHint": "0",
                                    "externalId": "FLORP_EXTERNAL",
                                    "assets": []
                                },
                                {
                                    "id": "3fee4907-2ea1-48b1-8c07-6c9da3c73e2f",
                                    "version": 3,
                                    "versionModifiedAt": "2024-11-21T10:18:42.026Z",
                                    "lastMessageSequenceNumber": 1,
                                    "createdAt": "2024-11-21T09:52:55.283Z",
                                    "lastModifiedAt": "2024-11-21T10:18:42.026Z",
                                    "lastModifiedBy": {
                                        "isPlatformClient": true,
                                        "user": {
                                            "typeId": "user",
                                            "id": "bd8dec93-e3e2-498c-8641-6329b89f1862"
                                        }
                                    },
                                    "createdBy": {
                                        "isPlatformClient": true,
                                        "user": {
                                            "typeId": "user",
                                            "id": "bd8dec93-e3e2-498c-8641-6329b89f1862"
                                        }
                                    },
                                    "name": {
                                        "en": "BODAS Connect"
                                    },
                                    "slug": {
                                        "en": "bodas-connect"
                                    },
                                    "description": {
                                        "en": "BODAS Connect description"
                                    },
                                    "ancestors": [],
                                    "orderHint": "0.1",
                                    "assets": []
                                }
                            ]
                        }""")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withGetCategoryResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/categories/.*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody(
                    """
                        {
                            "id": "{{request.path.2}}",
                            "version": 2,
                            "versionModifiedAt": "2024-11-21T09:53:18.165Z",
                            "lastMessageSequenceNumber": 1,
                            "createdAt": "2024-11-13T14:23:33.916Z",
                            "lastModifiedAt": "2024-11-21T09:53:18.165Z",
                            "lastModifiedBy": {
                                "isPlatformClient": true,
                                "user": {
                                    "typeId": "user",
                                    "id": "bd8dec93-e3e2-498c-8641-6329b89f1862"
                                }
                            },
                            "createdBy": {
                                "isPlatformClient": true,
                                "user": {
                                    "typeId": "user",
                                    "id": "f4ce7654-6df3-4f08-b774-c56172cc4ea6"
                                }
                            },
                            "key": "CAT_FLORP",
                            "name": {
                                "en": "Florp"
                            },
                            "slug": {
                                "en": "florp"
                            },
                            "description": {
                                "en": "Florpige Kategorie"
                            },
                            "ancestors": [
                                {
                                    "typeId": "category",
                                    "id": "3fee4907-2ea1-48b1-8c07-6c9da3c73e2f"
                                }
                            ],
                            "parent": {
                                "typeId": "category",
                                "id": "3fee4907-2ea1-48b1-8c07-6c9da3c73e2f"
                            },
                            "orderHint": "0",
                            "externalId": "FLORP_EXTERNAL",
                            "assets": []
                        }""")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withGetCategoryNotFoundResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/categories/.*"))
            .willReturn(aResponse()
                .withStatus(404)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody(
                    """
                        {
                            "statusCode": 404,
                            "message": "The Resource with ID '{{request.path.2}}' was not found.",
                            "errors": [
                                {
                                    "code": "ResourceNotFound",
                                    "message": "The Resource with ID '{{request.path.2}}' was not found."
                                }
                            ]
                        }""")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withProductSearchResponse() {
        instance.stubFor(WireMock.post(urlPathMatching("/.*/products/search"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("content-type", "application/json;charset=UTF-8")
                        .withBodyFile("commercetools/product-search-response.json")
                        .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withProductSearchResponseWithDuplicates() {
        instance.stubFor(WireMock.post(urlPathMatching("/.*/products/search"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("content-type", "application/json;charset=UTF-8")
                        .withBodyFile("commercetools/product-search-response-with-duplicates.json")
                        .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withProductProjectionWithoutAddonsResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/product-projections/6393b78f-f6f7-4d2f-a6a1-5ddb59dca45e"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("content-type", "application/json;charset=UTF-8")
                        .withBodyFile("commercetools/get-product-projection-without-addons.json")
                        .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withProductProjectionWithAddonsResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/product-projections/5abd3673-2110-4aa0-bbf9-c97cd1e24c89"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("content-type", "application/json;charset=UTF-8")
                        .withBodyFile("commercetools/get-product-projection-with-addons.json")
                        .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withGetProjectResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/glorious-new-store"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("content-type", "application/json;charset=UTF-8")
                        .withBodyFile("commercetools/get-project-response.json")
                        .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withProjectUpdateResponse() {
        instance.stubFor(WireMock.post(urlPathMatching("/glorious-new-store"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("content-type", "application/json;charset=UTF-8")
                        .withBodyFile("commercetools/update-project-response.json")
                        .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withStoreUpdateResponse() {
        instance.stubFor(WireMock.post(urlPathMatching("/glorious-new-store/stores/.*"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("content-type", "application/json;charset=UTF-8")
                        .withBody("""
                            {
                              "id": "{{request.path.2}}",
                              "version": 43,
                              "key": "{{request.path.2}}",
                              "name": {
                                "en": "{{request.path.2}} Store"
                              },
                              "languages": ["de", "en", "ua"],
                              "countries": [
                                { "code": "DE" },
                                { "code": "PL" }
                              ],
                              "createdAt": "2020-01-02T10:00:00.000Z",
                              "lastModifiedAt": "2020-01-02T10:00:00.000Z"
                            }""")
                        .withTransformers("response-template")));

        instance.stubFor(WireMock.get(WireMock.urlPathMatching("/glorious-new-store/stores/key=rexroth"))
                .willReturn(WireMock.aResponse()
                        .withStatus(200)
                        .withHeader("content-type", "application/json;charset=UTF-8")
                        .withBody("""
                            {
                              "id": "rexroth-id",
                              "version": 42,
                              "key": "rexroth",
                              "name": {
                                "en": "Rexroth Store"
                              },
                              "languages": ["de", "en", "fr", "gb"],
                              "countries": [
                                { "code": "DE" },
                                { "code": "US" },
                                { "code": "FR" },
                                { "code": "GB" }
                              ],
                              "createdAt": "2020-01-02T10:00:00.000Z",
                              "lastModifiedAt": "2020-01-02T10:00:00.000Z"
                            }""")
                        .withTransformers("response-template")));

        return instance;
    }

    @TestConfiguration
    public static class CommercetoolsMockClientConfig {
        private static final Logger LOG = LoggerFactory.getLogger(CommercetoolsMockClientConfig.class);

        private static final String MOCK_ENDPOINT = "http://localhost:40000";

        @Bean
        @Primary
        public ProjectApiRoot commercetoolsMockClient(final CommercetoolsConfiguration appConfiguration,
            final CommercetoolsRestMetricsJerseyClientInterceptor metricsInterceptor) {
            LOG.warn("using {} as commercetools api endpoint", MOCK_ENDPOINT);
            return ApiRootBuilder.of().defaultClient(MOCK_ENDPOINT)
                .addMiddleware(metricsInterceptor)
                .withApiBaseUrl(MOCK_ENDPOINT)
                .build(appConfiguration.projectKey());
        }
    }
}
