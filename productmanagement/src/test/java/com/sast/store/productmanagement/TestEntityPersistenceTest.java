package com.sast.store.productmanagement;

import com.sast.store.productmanagement.entities.TestEntity;
import com.sast.store.productmanagement.entities.TestEntityRepository;
import jakarta.inject.Inject;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.jdbc.Sql;

import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

public class TestEntityPersistenceTest extends AbstractComponentTest {
    @Inject
    private TestEntityRepository testEntityRepository;

    @Test
    public void testEntityCanBeSaved() {
        final TestEntity savedEntity = testEntityRepository.save(new TestEntity().setName("Herbert"));

        assertThat(savedEntity.getId()).isNotNull();
    }

    @Test
    public void testEntityIsValidated() {
        assertThatThrownBy(() -> testEntityRepository.save(new TestEntity().setName("")));
    }

    @Test
    @Sql(value = "/sql/test/create-test-entities.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    @Sql(value = "/sql/test/truncate-tables.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
    public void testExistingEntitiesAreFound() {
        final Optional<TestEntity> existingEntity = testEntityRepository
                .findById(UUID.fromString("13423608-f69c-4b3b-9f2e-3d3edc981e3b"));

        assertThat(existingEntity).isNotEmpty()
                .hasValueSatisfying(entity -> assertThat(entity.getName()).isEqualTo("Herbert"));
    }
}
