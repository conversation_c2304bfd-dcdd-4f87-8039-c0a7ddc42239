package com.sast.store.productmanagement.service;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.sast.store.external.countriesservice.util.CountriesServiceMockExtension;
import com.sast.store.external.ump.test.UmpMockExtension;
import com.sast.store.productmanagement.AbstractComponentTest;
import com.sast.store.productmanagement.util.CommercetoolsMockExtension;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Duration;

import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;
import static org.awaitility.Awaitility.await;


class CountryAndLanguageSyncServiceTest extends AbstractComponentTest {

    private static final String STORE_KEY = "/glorious-new-store/stores/key=rexroth";

    @Autowired
    private CountryAndLanguageSyncService syncService;

    @AfterEach
    void tearDown() {
        CommercetoolsMockExtension.get().resetAll();
    }

    @Test
    void testSync() {
        // preparing stubs
        UmpMockExtension.withDefaultResponse();
        CountriesServiceMockExtension.withDefaultResponse();
        CommercetoolsMockExtension.withStoreUpdateResponse();

        // test
        syncService.syncCountriesAndLanguages();

        // verify the first request with "setLanguages" action
        await()
                .atMost(Duration.ofSeconds(10))
                .pollInterval(Duration.ofMillis(200))
                .untilAsserted(() -> {
                    CommercetoolsMockExtension.get().verify(1, postRequestedFor(urlMatching(STORE_KEY))
                            .withRequestBody(WireMock.equalToJson("""
                                    {
                                      "version" : 42,
                                      "actions" : [ {
                                        "languages" : [ "de", "en" ],
                                        "action" : "setLanguages"
                                      }, {
                                        "countries" : [
                                          { "code": "DE" },
                                          { "code": "PL" }
                                        ],
                                        "action" : "setCountries"
                                      } ]
                                    }""", true, true)));
                });

        // verify there are only 2 post requests to the commercetools store
        await()
                .atMost(Duration.ofSeconds(10))
                .pollInterval(Duration.ofMillis(200))
                .untilAsserted(() -> {
                    CommercetoolsMockExtension.get().verify(1, postRequestedFor(urlMatching(STORE_KEY)));
                });
    }

}