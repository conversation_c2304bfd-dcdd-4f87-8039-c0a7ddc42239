package com.sast.store.productmanagement.rest;

import com.sast.store.external.countriesservice.util.CountriesServiceMockExtension;
import com.sast.store.external.ump.test.UmpMockExtension;
import com.sast.store.productmanagement.AbstractIntegrationTest;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import io.restassured.specification.RequestSpecification;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Map;

import static com.tngtech.keycloakmock.api.TokenConfig.aTokenConfig;

class ProductRestServiceIntegrationTest extends AbstractIntegrationTest {

    @Test
    void testGetProducts() {
        UmpMockExtension.withDefaultResponse();
        CountriesServiceMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/products")
            .then().statusCode(200)
            .body("[0].id", Matchers.notNullValue())
            .body("[0].name", Matchers.notNullValue())
            .body("[0].description", Matchers.notNullValue())
            .body("[0].productType", Matchers.notNullValue())
            .body("[0].images[0]", Matchers.notNullValue())
            .body("[0].variants[0].price.value", Matchers.notNullValue())
            .body("[0].variants[0].name", Matchers.notNullValue())
            .body("[0].variants[0].description", Matchers.notNullValue())
            .body("[0].variants[0].features", Matchers.notNullValue())
            .body("[0].variants[0].agreements[0]", Matchers.notNullValue())
            .body("[0].variants[0].agreements[0].name", Matchers.notNullValue())
            .body("[0].variants[0].agreements[0].url", Matchers.notNullValue())
            .body("[0].externalDocuments[0]", Matchers.notNullValue())
            .body("[0].externalDocuments[0].name", Matchers.notNullValue())
            .body("[0].externalDocuments[0].url", Matchers.notNullValue());

    }

    @Test
    void testGetProductsDE() {
        UmpMockExtension.withDefaultResponse();
        CountriesServiceMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/products?language=de&country=DE&limit=1")
            .then().statusCode(200)
            .body(".", Matchers.hasSize(1))
            .body("[0].variants[0].price.currencyCode", Matchers.equalTo("EUR"));
    }

    @Test
    void testGetProductsFR() {
        UmpMockExtension.withDefaultResponse();
        CountriesServiceMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/products?language=fr&country=FR&limit=1")
            .then().statusCode(404);
    }

    @Test
    void testGetProductsAnonymous() {
        CountriesServiceMockExtension.withDefaultResponse();

        RestAssured
            .given()
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .params(Map.of("languageCode", "de", "countryCode", "DE"))
            .when().get(host + "/rest/products")
            .then().statusCode(200)
            .body("[0].id", Matchers.notNullValue())
            .body("[0].name", Matchers.notNullValue())
            .body("[0].description", Matchers.notNullValue())
            .body("[0].productType", Matchers.notNullValue())
            .body("[0].images[0]", Matchers.notNullValue())
            .body("[0].variants[0].price", Matchers.nullValue())
            .body("[0].variants[0].name", Matchers.notNullValue())
            .body("[0].variants[0].description", Matchers.notNullValue())
            .body("[0].variants[0].features", Matchers.notNullValue())
            .body("[0].variants[0].agreements[0]", Matchers.notNullValue())
            .body("[0].variants[0].agreements[0].name", Matchers.notNullValue())
            .body("[0].variants[0].agreements[0].url", Matchers.notNullValue())
            .body("[0].externalDocuments[0]", Matchers.notNullValue())
            .body("[0].externalDocuments[0].name", Matchers.notNullValue())
            .body("[0].externalDocuments[0].url", Matchers.notNullValue());

    }

    @Test
    void testGetProductsUnknownLanguage() {
        UmpMockExtension.withDefaultResponse();
        CountriesServiceMockExtension.withDefaultResponse();

        RestAssured
            .given(setupKeycloakTokenWithLanguage("in"))
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/products?language=in&country=FR&limit=1")
            .then().statusCode(404);
    }

    @Test
    void testExportProducts() {
        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.fromContentType("application/octet-stream"))
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/products")
            .then().statusCode(200)
            .body(Matchers.startsWith("{\"key\":\""));
    }

    private RequestSpecification setupKeycloakTokenWithLanguage(final String communicationLanguage) {
        final String accessToken = KEYCLOAK_MOCK.getAccessToken(aTokenConfig()
            .withSubject("user")
            .withAuthorizedParty("bossstore-frontend")
            .withClaims(Map.of(
                "resource_access", Map.of("bossstore-backend", Map.of("roles", Arrays.asList("VIEW_ORDER_DETAILS"))),
                "name", "John Doe",
                "preferred_username", "7cb0fdd4-9503-41e0-a279-84ad098c8a00",
                "given_name", "John",
                "family_name", "Doe",
                "email", "<EMAIL>",
                "company_name", "THE Company",
                "communication_language", communicationLanguage,
                "company_id", COMPANY_ID))
            .build());
        return RestAssured.given().auth().preemptive().oauth2(accessToken);
    }

    @Test
    void testGetProductVariant() {
        UmpMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/products/variants/DDCIH_hydraulic_hub_quarterly")
            .then().statusCode(200)
            .body("id", Matchers.notNullValue())
            .body("name", Matchers.notNullValue())
            .body("description", Matchers.notNullValue())
            .body("productType", Matchers.notNullValue())
            .body("images[0]", Matchers.notNullValue())
            .body("variants[0].price.value", Matchers.nullValue())
            .body("variants[0].name", Matchers.notNullValue())
            .body("variants[0].description", Matchers.notNullValue())
            .body("variants[0].features", Matchers.notNullValue())
            .body("variants[0].agreements[0]", Matchers.notNullValue())
            .body("variants[0].agreements[0].name", Matchers.notNullValue())
            .body("variants[0].agreements[0].url", Matchers.notNullValue())
            .body("variants[0].entitlements[0]", Matchers.notNullValue());

    }

}
