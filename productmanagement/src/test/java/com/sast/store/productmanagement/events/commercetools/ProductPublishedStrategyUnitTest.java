package com.sast.store.productmanagement.events.commercetools;

import com.commercetools.api.models.message.ProductPublishedMessage;
import com.commercetools.api.models.product.AttributeBuilder;
import com.commercetools.api.models.product.ProductProjectionBuilder;
import com.commercetools.api.models.product.ProductVariant;
import com.commercetools.api.models.product.ProductVariantBuilder;
import com.commercetools.api.models.product_type.AttributePlainEnumValueBuilder;
import com.sast.store.brimtegration.apimodel.events.egress.product.data.PlatformProductExportable;
import com.sast.store.external.brim.BrimMessagePublisher;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

@ExtendWith(MockitoExtension.class)
class ProductPublishedStrategyUnitTest {
    @Mock
    private BrimMessagePublisher brimMessagePublisher;

    @InjectMocks
    private ProductPublishedStrategy productPublishedStrategy;

    @Captor
    private ArgumentCaptor<PlatformProductExportable> productExportableArgumentCaptor;

    @Test
    public void testProcessAllVariants() {
        final ProductVariant masterVariant = createVariant("sku1");

        final ProductPublishedMessage productPublishedMessage = ProductPublishedMessage.builder()
                .productProjection(ProductProjectionBuilder.of()
                        .masterVariant(masterVariant)
                        .variants(createVariant("sku2"),
                                createVariant("sku3"))
                        .buildUnchecked())
                .buildUnchecked();

        productPublishedStrategy.process(productPublishedMessage);

        verify(brimMessagePublisher, times(3))
                .publishPlatformProductEvent(eq("rexroth"), productExportableArgumentCaptor.capture());

        assertThat(productExportableArgumentCaptor.getAllValues())
                .hasSize(3)
                .containsExactly(
                        PlatformProductExportable.builder().productId("sku1").build(),
                        PlatformProductExportable.builder().productId("sku2").build(),
                        PlatformProductExportable.builder().productId("sku3").build()
                );
    }

    @Test
    public void testOneVariantHasNoTenantThenNoVariantsAreExported() {
        final ProductVariant masterVariant = createVariant("sku1");

        final ProductPublishedMessage productPublishedMessage = ProductPublishedMessage.builder()
                .productProjection(ProductProjectionBuilder.of()
                        .masterVariant(masterVariant)
                        .variants(createVariant("sku2"),
                                ProductVariantBuilder.of()
                                        .attributes(List.of())
                                        .sku("sku3").buildUnchecked())
                        .buildUnchecked())
                .buildUnchecked();

        assertThatThrownBy(() -> productPublishedStrategy.process(productPublishedMessage))
                .isInstanceOf(IllegalStateException.class);

        verifyNoInteractions(brimMessagePublisher);
    }

    private ProductVariant createVariant(final String sku) {
        return ProductVariantBuilder.of()
                .sku(sku)
                .attributes(AttributeBuilder.of().name("tenant")
                        .value(AttributePlainEnumValueBuilder.of()
                                .key("rexroth").buildUnchecked()).buildUnchecked())
                .buildUnchecked();
    }
}