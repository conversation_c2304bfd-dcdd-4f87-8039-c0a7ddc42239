package com.sast.store.productmanagement.service;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.sast.store.productmanagement.AbstractComponentTest;
import com.sast.store.productmanagement.util.CommercetoolsMockExtension;
import com.sast.store.testing.awsmockup.junit.SqsMockExtension;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.Test;

public class BrimProductEventSubscriberTest extends AbstractComponentTest {

    @Test
    void testBillingProductDataMessage() throws Exception {
        CommercetoolsMockExtension.withDefaultResponse();

        SqsMockExtension.sendMessage("/queue/bossstore-brim-product-events.fifo",
                loadResourceFile("/events/platform/ingress/product-event-with-everything.json"));

        Awaitility.await().untilAsserted(() -> CommercetoolsMockExtension.get()
            .verify(WireMock.postRequestedFor(WireMock.urlEqualTo("/glorious-new-store/standalone-prices/key=foo2WD0001"))));

        CommercetoolsMockExtension.get().verify(1,
            WireMock.postRequestedFor(WireMock.urlEqualTo("/glorious-new-store/standalone-prices/key=bloop1eur")));
        CommercetoolsMockExtension.get().verify(1,
            WireMock.postRequestedFor(WireMock.urlEqualTo("/glorious-new-store/standalone-prices/key=bloop2eur")));
        CommercetoolsMockExtension.get().verify(1,
            WireMock.postRequestedFor(WireMock.urlEqualTo("/glorious-new-store/standalone-prices/key=foo1"))
                .withRequestBody(WireMock.equalToJson("""
                    {
                      "version": 4,
                      "actions": [
                        {
                          "action": "changeActive",
                          "active": true
                        },
                        {
                          "action": "setValidFrom",
                          "validFrom": "2028-04-01T00:00:00.000Z"
                        },
                        {
                          "action": "setValidUntil"
                        },
                        {
                          "action": "changeValue",
                          "value": {
                            "type": "centPrecision",
                            "currencyCode": "EUR",
                            "centAmount": 1999,
                            "fractionDigits": 2
                          }
                        }
                      ]
                    }""")));
        CommercetoolsMockExtension.get().verify(1,
                WireMock.postRequestedFor(WireMock.urlEqualTo("/glorious-new-store/standalone-prices/key=foo1WD0001"))
                        .withRequestBody(WireMock.equalToJson("""
                    {
                      "version": 4,
                      "actions": [
                        {
                          "action": "changeActive",
                          "active": true
                        },
                        {
                          "action": "setValidFrom",
                          "validFrom": "2028-10-25T00:00:00.000Z"
                        },
                        {
                          "action": "setValidUntil",
                          "validUntil" : "2028-10-27T23:59:59.999Z"
                        },
                        {
                          "action": "changeValue",
                          "value": {
                            "type": "centPrecision",
                            "currencyCode": "EUR",
                            "centAmount": 2999,
                            "fractionDigits": 2
                          }
                        }
                      ]
                    }""")));
        CommercetoolsMockExtension.get().verify(1,
                WireMock.postRequestedFor(WireMock.urlEqualTo("/glorious-new-store/standalone-prices/key=foo2WD0001"))
                        .withRequestBody(WireMock.equalToJson("""
                    {
                      "version": 4,
                      "actions": [
                        {
                          "action": "changeActive",
                          "active": true
                        },
                        {
                          "action": "setValidFrom",
                          "validFrom": "2028-10-28T00:00:00.000Z"
                        },
                        {
                          "action": "setValidUntil"
                        },
                        {
                          "action": "changeValue",
                          "value": {
                            "type": "centPrecision",
                            "currencyCode": "EUR",
                            "centAmount": 3999,
                            "fractionDigits": 2
                          }
                        }
                      ]
                    }""")));
    }
}
