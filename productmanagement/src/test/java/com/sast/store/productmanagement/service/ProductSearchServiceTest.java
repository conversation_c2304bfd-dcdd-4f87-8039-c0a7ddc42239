package com.sast.store.productmanagement.service;

import com.commercetools.api.models.product_search.ProductSearchResult;
import com.sast.store.productmanagement.AbstractComponentTest;
import com.sast.store.productmanagement.util.CommercetoolsMockExtension;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Currency;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class ProductSearchServiceTest extends AbstractComponentTest {
    @Autowired
    private ProductSearchService productSearchService;

    @Test
    void searchResultIsDeduplicatedByIdPreservingOrder() {
        CommercetoolsMockExtension.withProductSearchResponseWithDuplicates();

        final List<ProductSearchResult> actualSearchResults = productSearchService
                .search(Currency.getInstance("EUR"));

        assertThat(actualSearchResults).hasSize(2)
                .flatExtracting(ProductSearchResult::getId)
                .containsExactly("6393b78f-f6f7-4d2f-a6a1-5ddb59dca45e", "5abd3673-2110-4aa0-bbf9-c97cd1e24c89");

    }
}