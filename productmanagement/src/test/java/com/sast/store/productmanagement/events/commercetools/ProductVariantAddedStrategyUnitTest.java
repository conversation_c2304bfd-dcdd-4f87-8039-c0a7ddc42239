package com.sast.store.productmanagement.events.commercetools;

import com.commercetools.api.models.common.LocalizedStringBuilder;
import com.commercetools.api.models.message.ProductVariantAddedMessage;
import com.commercetools.api.models.product.Attribute;
import com.commercetools.api.models.product.AttributeBuilder;
import com.commercetools.api.models.product.ProductVariantBuilder;
import com.commercetools.api.models.product_type.AttributePlainEnumValueBuilder;
import com.sast.store.brimtegration.apimodel.common.product.terms.ConsumptionContractTypeConfiguration;
import com.sast.store.brimtegration.apimodel.common.product.terms.ContractPeriod;
import com.sast.store.brimtegration.apimodel.common.product.terms.FreeContractTypeConfiguration;
import com.sast.store.brimtegration.apimodel.common.product.terms.SubscriptionContractTypeConfiguration;
import com.sast.store.brimtegration.apimodel.events.egress.product.data.PlatformProductCreated;
import com.sast.store.external.brim.BrimMessagePublisher;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Period;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

@ExtendWith(MockitoExtension.class)
class ProductVariantAddedStrategyUnitTest {
    private static final String SKU = "skuValue";
    private static final String EXTERNAL_ID = "externalIdValue";
    private static final String TENANT = "rexroth";
    private static final String SELLER_ID = "sellerIdValue";
    private static final Map<String, String> NAME = Map.of("de", "toll", "en", "amazing");


    @Mock
    private BrimMessagePublisher brimMessagePublisher;

    @InjectMocks
    private ProductVariantAddedStrategy productVariantAddedStrategy;

    @Test
    public void testValidSubscriptionCreation() {
        final PlatformProductCreated expectedBrimEvent = PlatformProductCreated.builder()
                .productId(SKU)
                .name(Map.of(Locale.GERMAN, "toll", Locale.ENGLISH, "amazing"))
                .sellerId(SELLER_ID)
                .contractType(SubscriptionContractTypeConfiguration.builder()
                        .contractPeriod(ContractPeriod.MONTHLY)
                        .noticePeriod(Period.ofDays(7))
                        .build())
                .build();

        final ProductVariantAddedMessage givenCommercetoolsEvent = createMinimalSubscription();
        givenCommercetoolsEvent.getVariant().getAttributes().addAll(subscriptionAttributes());

        productVariantAddedStrategy.process(givenCommercetoolsEvent);

        verify(brimMessagePublisher, times(1)).publishPlatformProductEvent(TENANT, expectedBrimEvent);
    }

    @Test
    public void testExternalProductIdIsOptional() {
        final PlatformProductCreated expectedBrimEvent = PlatformProductCreated.builder()
                .productId(SKU)
                .name(Map.of(Locale.GERMAN, "toll", Locale.ENGLISH, "amazing"))
                .sellerId(SELLER_ID)
                .externalProductId(EXTERNAL_ID)
                .contractType(SubscriptionContractTypeConfiguration.builder()
                        .contractPeriod(ContractPeriod.MONTHLY)
                        .noticePeriod(Period.ofDays(7))
                        .build())
                .build();

        final ProductVariantAddedMessage givenCommercetoolsEvent = createMinimalSubscription();
        givenCommercetoolsEvent.getVariant().getAttributes().addAll(subscriptionAttributes());
        givenCommercetoolsEvent.getVariant().getAttributes()
                .add(AttributeBuilder.of().name("externalProductId").value(EXTERNAL_ID).buildUnchecked());

        productVariantAddedStrategy.process(givenCommercetoolsEvent);

        verify(brimMessagePublisher, times(1)).publishPlatformProductEvent(TENANT, expectedBrimEvent);
    }

    @Test
    public void tenantIsMandatory() {
        final ProductVariantAddedMessage givenCommercetoolsEvent = createMinimalSubscription();
        givenCommercetoolsEvent.getVariant().getAttributes().addAll(subscriptionAttributes());
        givenCommercetoolsEvent.getVariant().getAttributes().removeIf(attribute -> attribute.getName().equals("tenant"));

        assertThatThrownBy(() -> productVariantAddedStrategy.process(givenCommercetoolsEvent))
                .isInstanceOf(IllegalStateException.class);

        verifyNoInteractions(brimMessagePublisher);
    }

    @Test
    public void sellerIdIsMandatory() {
        final ProductVariantAddedMessage givenCommercetoolsEvent = createMinimalSubscription();
        givenCommercetoolsEvent.getVariant().getAttributes().addAll(subscriptionAttributes());
        givenCommercetoolsEvent.getVariant().getAttributes().removeIf(attribute -> attribute.getName().equals("sellerId"));

        assertThatThrownBy(() -> productVariantAddedStrategy.process(givenCommercetoolsEvent))
                .isInstanceOf(IllegalStateException.class);

        verifyNoInteractions(brimMessagePublisher);
    }

    @Test
    public void billingNameIsMandatory() {
        final ProductVariantAddedMessage givenCommercetoolsEvent = createMinimalSubscription();
        givenCommercetoolsEvent.getVariant().getAttributes().addAll(subscriptionAttributes());
        givenCommercetoolsEvent.getVariant().getAttributes().removeIf(attribute -> attribute.getName().equals("billingName"));

        assertThatThrownBy(() -> productVariantAddedStrategy.process(givenCommercetoolsEvent))
                .isInstanceOf(IllegalStateException.class);

        verifyNoInteractions(brimMessagePublisher);
    }

    @Test
    public void subscriptionMustHaveRuntime() {
        final ProductVariantAddedMessage givenCommercetoolsEvent = createMinimalSubscription();
        givenCommercetoolsEvent.getVariant().getAttributes().addAll(subscriptionAttributes());
        givenCommercetoolsEvent.getVariant().getAttributes().removeIf(attribute -> attribute.getName().equals("runtime"));

        assertThatThrownBy(() -> productVariantAddedStrategy.process(givenCommercetoolsEvent))
                .isInstanceOf(IllegalStateException.class);

        verifyNoInteractions(brimMessagePublisher);
    }

    @Test
    public void subscriptionMustHaveNoticePeriod() {
        final ProductVariantAddedMessage givenCommercetoolsEvent = createMinimalSubscription();
        givenCommercetoolsEvent.getVariant().getAttributes().addAll(subscriptionAttributes());
        givenCommercetoolsEvent.getVariant().getAttributes().removeIf(attribute -> attribute.getName().equals("noticePeriod"));

        assertThatThrownBy(() -> productVariantAddedStrategy.process(givenCommercetoolsEvent))
                .isInstanceOf(IllegalStateException.class);

        verifyNoInteractions(brimMessagePublisher);
    }

    @Test
    public void testValidFreeCreation() {
        final PlatformProductCreated expectedBrimEvent = PlatformProductCreated.builder()
                .productId(SKU)
                .name(Map.of(Locale.GERMAN, "toll", Locale.ENGLISH, "amazing"))
                .sellerId(SELLER_ID)
                .contractType(FreeContractTypeConfiguration.builder()
                        .contractPeriod(Period.ofDays(1))
                        .noticePeriod(Period.ZERO)
                        .build())
                .build();

        final ProductVariantAddedMessage givenCommercetoolsEvent = createMinimalSubscription();
        givenCommercetoolsEvent.getVariant().getAttributes().addAll(trialAttributes());

        productVariantAddedStrategy.process(givenCommercetoolsEvent);

        verify(brimMessagePublisher, times(1)).publishPlatformProductEvent(TENANT, expectedBrimEvent);
    }

    @Test
    public void freeMustHaveContractPeriod() {
        final ProductVariantAddedMessage givenCommercetoolsEvent = createMinimalSubscription();
        givenCommercetoolsEvent.getVariant().getAttributes().addAll(trialAttributes());
        givenCommercetoolsEvent.getVariant().getAttributes().removeIf(attribute -> attribute.getName().equals("runtime"));

        assertThatThrownBy(() -> productVariantAddedStrategy.process(givenCommercetoolsEvent))
                .isInstanceOf(IllegalStateException.class);

        verifyNoInteractions(brimMessagePublisher);
    }

    @Test
    public void freeMustHaveNoticePeriod() {
        final ProductVariantAddedMessage givenCommercetoolsEvent = createMinimalSubscription();
        givenCommercetoolsEvent.getVariant().getAttributes().addAll(trialAttributes());
        givenCommercetoolsEvent.getVariant().getAttributes().removeIf(attribute -> attribute.getName().equals("noticePeriod"));

        assertThatThrownBy(() -> productVariantAddedStrategy.process(givenCommercetoolsEvent))
                .isInstanceOf(IllegalStateException.class);

        verifyNoInteractions(brimMessagePublisher);
    }

    @Test
    public void testValidConsumptionCreation() {
        final PlatformProductCreated expectedBrimEvent = PlatformProductCreated.builder()
                .productId(SKU)
                .name(Map.of(Locale.GERMAN, "toll", Locale.ENGLISH, "amazing"))
                .sellerId(SELLER_ID)
                .contractType(ConsumptionContractTypeConfiguration.builder()
                        .contractPeriod(ContractPeriod.YEARLY)
                        .noticePeriod(Period.ofDays(14))
                        .build())
                .build();

        final ProductVariantAddedMessage givenCommercetoolsEvent = createMinimalSubscription();
        givenCommercetoolsEvent.getVariant().getAttributes().addAll(consumptionAttributes());

        productVariantAddedStrategy.process(givenCommercetoolsEvent);

        verify(brimMessagePublisher, times(1)).publishPlatformProductEvent(TENANT, expectedBrimEvent);
    }

    @Test
    public void consumptionMustHaveContractPeriod() {
        final ProductVariantAddedMessage givenCommercetoolsEvent = createMinimalSubscription();
        givenCommercetoolsEvent.getVariant().getAttributes().addAll(consumptionAttributes());
        givenCommercetoolsEvent.getVariant().getAttributes().removeIf(attribute -> attribute.getName().equals("runtime"));

        assertThatThrownBy(() -> productVariantAddedStrategy.process(givenCommercetoolsEvent))
                .isInstanceOf(IllegalStateException.class);

        verifyNoInteractions(brimMessagePublisher);
    }

    @Test
    public void consumptionMustHaveNoticePeriod() {
        final ProductVariantAddedMessage givenCommercetoolsEvent = createMinimalSubscription();
        givenCommercetoolsEvent.getVariant().getAttributes().addAll(consumptionAttributes());
        givenCommercetoolsEvent.getVariant().getAttributes().removeIf(attribute -> attribute.getName().equals("noticePeriod"));

        assertThatThrownBy(() -> productVariantAddedStrategy.process(givenCommercetoolsEvent))
                .isInstanceOf(IllegalStateException.class);

        verifyNoInteractions(brimMessagePublisher);
    }

    @Test
    public void licenseTypeMustBeValid() {
        final ProductVariantAddedMessage givenCommercetoolsEvent = createMinimalSubscription();
        givenCommercetoolsEvent.getVariant().getAttributes().add(AttributeBuilder.of().name("licenseType")
                .value(AttributePlainEnumValueBuilder.of()
                        .key("FOOBARBAZ").buildUnchecked()).buildUnchecked());

        assertThatThrownBy(() -> productVariantAddedStrategy.process(givenCommercetoolsEvent))
                .isInstanceOf(IllegalStateException.class);

        verifyNoInteractions(brimMessagePublisher);
    }


    private ProductVariantAddedMessage createMinimalSubscription() {
        return ProductVariantAddedMessage.builder()
                .variant(ProductVariantBuilder.of()
                        .sku(SKU)
                        .attributes(
                                AttributeBuilder.of().name("tenant").value(AttributePlainEnumValueBuilder.of()
                                        .key(TENANT).buildUnchecked()).buildUnchecked(),
                                AttributeBuilder.of().name("sellerId").value(SELLER_ID).buildUnchecked(),
                                AttributeBuilder.of().name("billingName").value(LocalizedStringBuilder.of()
                                        .values(NAME).buildUnchecked()).buildUnchecked()
                        )
                        .buildUnchecked())
                .buildUnchecked();
    }

    private List<Attribute> trialAttributes() {
        return List.of(
                AttributeBuilder.of().name("licenseType").value(AttributePlainEnumValueBuilder.of()
                        .key("FREE").buildUnchecked()).buildUnchecked(),
                AttributeBuilder.of().name("runtime").value("P1D").buildUnchecked(),
                AttributeBuilder.of().name("noticePeriod").value("P0D").buildUnchecked()
        );
    }

    private List<Attribute> subscriptionAttributes() {
        return List.of(
                AttributeBuilder.of().name("licenseType").value(AttributePlainEnumValueBuilder.of()
                        .key("SUBSCRIPTION").buildUnchecked()).buildUnchecked(),
                AttributeBuilder.of().name("runtime").value("P1M").buildUnchecked(),
                AttributeBuilder.of().name("noticePeriod").value("P7D").buildUnchecked()
        );
    }

    private List<Attribute> consumptionAttributes() {
        return List.of(
                AttributeBuilder.of().name("licenseType").value(AttributePlainEnumValueBuilder.of()
                        .key("CONSUMPTION").buildUnchecked()).buildUnchecked(),
                AttributeBuilder.of().name("runtime").value("P1Y").buildUnchecked(),
                AttributeBuilder.of().name("noticePeriod").value("P14D").buildUnchecked()
        );
    }
}