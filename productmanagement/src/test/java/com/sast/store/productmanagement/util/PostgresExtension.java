package com.sast.store.productmanagement.util;

import org.junit.jupiter.api.extension.Extension;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.testcontainers.containers.PostgreSQLContainer;

public final class PostgresExtension implements Extension {
    private final PostgreSQLContainer<?> postgresContainer;

    private PostgresExtension() {
        this.postgresContainer =
                new PostgreSQLContainer<>("postgres:17.3-alpine")
                        .withDatabaseName("product-db")
                        .withUsername("productuser")
                        .withPassword("productpass");
    }

    public static PostgresExtension createAndStart() {
        final var ext = new PostgresExtension();
        ext.postgresContainer.start();
        return ext;
    }

    public void registerPropertySources(final DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgresContainer::getJdbcUrl);
        registry.add("spring.datasource.username", postgresContainer::getUsername);
        registry.add("spring.datasource.password", postgresContainer::getPassword);
    }
}