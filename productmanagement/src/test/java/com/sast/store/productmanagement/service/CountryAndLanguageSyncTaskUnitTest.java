package com.sast.store.productmanagement.service;

import com.commercetools.api.client.ByProjectKeyGet;
import com.commercetools.api.client.ByProjectKeyStoresKeyByKeyPost;
import com.commercetools.api.client.ByProjectKeyStoresKeyByKeyRequestBuilder;
import com.commercetools.api.client.ByProjectKeyStoresRequestBuilder;
import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.models.project.Project;
import com.commercetools.api.models.store.Store;
import com.commercetools.api.models.store.StoreSetCountriesAction;
import com.commercetools.api.models.store.StoreSetLanguagesAction;
import com.commercetools.api.models.store.StoreUpdate;
import com.commercetools.api.models.store_country.StoreCountry;
import com.sast.store.external.commercetools.service.StoreProvider;
import com.sast.store.external.countriesservice.api.CountriesServiceClient;
import com.sast.store.external.countriesservice.api.CountryDto;
import com.sast.store.external.countriesservice.api.Tenant;
import io.vrap.rmf.base.client.ApiHttpResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CountryAndLanguageSyncServiceUnitTest {

    private static final List<String> PROJECT_COUNTRIES = Arrays.asList("DE", "US", "FR", "GB");
    private static final List<String> PROJECT_LANGUAGES = Arrays.asList("en", "de");
    private static final String PROJECT_KEY = "test-project";
    private static final long PROJECT_VERSION = 42L;

    @Mock
    private CountriesServiceClient countriesServiceClient;
    @Mock
    private ProjectApiRoot commercetoolsClient;
    @Mock
    private StoreProvider storeProvider;
    @Mock
    private Project project;
    @Mock
    private Store store;
    @Captor
    private ArgumentCaptor<StoreUpdate> storeUpdateCaptor;
    @Mock
    private ByProjectKeyGet byProjectKeyGet;
    @Mock
    private CompletableFuture<ApiHttpResponse<Project>> projectCompletableFuture;
    @Mock
    private ApiHttpResponse<Project> projectApiHttpResponse;
    @Mock
    private ApiHttpResponse<Store> storeApiHttpResponse;

    private CountryAndLanguageSyncService syncService;

    @BeforeEach
    void setUp() throws ExecutionException, InterruptedException {
        // create lenient mocks for the common setup
        lenient().when(project.getLanguages()).thenReturn(PROJECT_LANGUAGES);
        lenient().when(project.getCountries()).thenReturn(PROJECT_COUNTRIES);
        lenient().when(project.getVersion()).thenReturn(PROJECT_VERSION);
        lenient().when(project.getKey()).thenReturn(PROJECT_KEY);

        // setup store mock
        lenient().when(store.getLanguages()).thenReturn(PROJECT_LANGUAGES);
        final List<StoreCountry> storeCountries = PROJECT_COUNTRIES.stream()
                .map(code -> StoreCountry.builder().code(code).build())
                .toList();
        lenient().when(store.getCountries()).thenReturn(storeCountries);
        lenient().when(store.getKey()).thenReturn("store-" + PROJECT_KEY);
        lenient().when(store.getVersion()).thenReturn(PROJECT_VERSION);

        // setup store provider mock
        lenient().when(storeProvider.findByKey(any(), eq(false))).thenReturn(java.util.Optional.of(store));

        syncService = new CountryAndLanguageSyncService(commercetoolsClient, countriesServiceClient, storeProvider);

        setupLenientMocks();
    }

    @Test
    void testCountriesSync() {
        setUpMocksChain();
        // mock country service to return different countries than the store has
        final List<CountryDto> fewerCountries = createFewerCountries();
        when(countriesServiceClient.listAllCountries(eq(Tenant.rexroth)))
                .thenReturn(fewerCountries);

        // test
        syncService.syncCountriesAndLanguages();

        // verify the update contains the correct action
        final StoreUpdate update = storeUpdateCaptor.getValue();
        assertThat(update.getActions()).hasSize(1);
        assertThat(update.getActions().getFirst()).isInstanceOf(StoreSetCountriesAction.class);

        // verify country list
        final StoreSetCountriesAction action = (StoreSetCountriesAction) update.getActions().getFirst();
        assertThat(action.getCountries()).hasSize(2);
        assertThat(action.getCountries().stream().map(StoreCountry::getCode).toList())
                .containsExactlyInAnyOrder("DE", "US");
    }

    @Test
    void testLanguagesSync() {
        setUpMocksChain();

        // mock country service to return different languages than the store has
        final List<CountryDto> countriesWithDifferentLanguages = createCountriesWithDifferentLanguages();
        when(countriesServiceClient.listAllCountries(eq(Tenant.rexroth)))
                .thenReturn(countriesWithDifferentLanguages);

        // test
        syncService.syncCountriesAndLanguages();

        // verify the update contains the correct action
        final StoreUpdate update = storeUpdateCaptor.getValue();
        assertThat(update.getActions()).hasSize(1);
        assertThat(update.getActions().getFirst()).isInstanceOf(StoreSetLanguagesAction.class);

        // verify language list - this indirectly tests extractAllLanguages method
        final StoreSetLanguagesAction action = (StoreSetLanguagesAction) update.getActions().getFirst();
        assertThat(action.getLanguages()).containsExactlyInAnyOrder("en", "fr", "es");
    }

    @Test
    void testBothCountriesAndLanguagesSync() {
        setUpMocksChain();

        // mock country service to return differences in both countries and languages
        final List<CountryDto> differentCountriesAndLanguages = createDifferentCountriesAndLanguages();
        when(countriesServiceClient.listAllCountries(eq(Tenant.rexroth)))
                .thenReturn(differentCountriesAndLanguages);

        // test
        syncService.syncCountriesAndLanguages();

        final List<StoreUpdate> updates = storeUpdateCaptor.getAllValues();
        assertThat(updates).hasSize(1);
        assertThat(updates.getFirst().getActions())
                .hasSize(2)
                .containsExactlyInAnyOrder(
                        StoreSetLanguagesAction.builder().languages("en", "fr").build(),
                        StoreSetCountriesAction.builder().countries(
                                StoreCountry.builder().code("US").build(),
                                StoreCountry.builder().code("CA").build()
                        ).build()
                );
    }

    @Test
    void testNoChangesNeeded() {
        final List<CountryDto> identicalCountries = createSameCountriesAndLanguages();
        when(countriesServiceClient.listAllCountries(eq(Tenant.rexroth)))
                .thenReturn(identicalCountries);

        // test
        syncService.syncCountriesAndLanguages();

        // verify
        final List<StoreUpdate> capturedUpdates = storeUpdateCaptor.getAllValues();
        assertThat(capturedUpdates).isEmpty();
    }

    @Test
    void testValidateWithNullStoreAndTenant() {
        when(storeProvider.findByKey(anyString(), eq(false))).thenReturn(java.util.Optional.empty());

        // this should not throw an exception but just log a warning
        syncService.syncCountriesAndLanguages();

        // no verification needed - if it doesn't throw, the test passes
    }

    @Test
    void testFilteringOfInactiveAndNonStorefrontCountries() {
        // create a mix of active and inactive countries
        final List<CountryDto> mixedCountries = Arrays.asList(
                createActiveCountryDto("DE", Arrays.asList("de", "en"), true),
                createActiveCountryDto("US", Arrays.asList("en"), true),
                createActiveCountryDto("FR", Arrays.asList("fr"), false),
                createInactiveCountryDto("GB", Arrays.asList("en"))
        );

        setUpMocksChain();

        when(countriesServiceClient.listAllCountries(eq(Tenant.rexroth)))
                .thenReturn(mixedCountries);

        // test
        syncService.syncCountriesAndLanguages();

        // verify
        final StoreUpdate update = storeUpdateCaptor.getValue();
        assertThat(update.getActions().getFirst()).isInstanceOf(StoreSetCountriesAction.class);
        final StoreSetCountriesAction action = (StoreSetCountriesAction) update.getActions().getFirst();
        assertThat(action.getCountries().stream().map(StoreCountry::getCode).toList())
                .containsExactlyInAnyOrder("DE", "US");
    }

    @Test
    void testHasAnyStorefrontEnabledTenant() {
        final CountryDto enabledCountry = createActiveCountryDto("DE", Arrays.asList("de", "en"), true);
        final CountryDto disabledCountry = createActiveCountryDto("FR", Arrays.asList("fr"), false);

        setUpMocksChain();

        // test 1: only the enabled country should be included
        when(countriesServiceClient.listAllCountries(eq(Tenant.rexroth)))
                .thenReturn(List.of(enabledCountry, disabledCountry));

        syncService.syncCountriesAndLanguages();

        StoreUpdate update = storeUpdateCaptor.getValue();
        final StoreSetCountriesAction action = (StoreSetCountriesAction) update.getActions().getFirst();

        assertThat(action.getCountries().stream().map(StoreCountry::getCode).toList())
                .containsExactly("DE");

        // test 2: only one country with disabled storefront
        when(countriesServiceClient.listAllCountries(eq(Tenant.rexroth)))
                .thenReturn(Collections.singletonList(disabledCountry));

        syncService.syncCountriesAndLanguages();

        update = storeUpdateCaptor.getValue();
        assertThat(update.getActions())
                .hasSize(2)
                .containsExactlyInAnyOrder(
                        StoreSetLanguagesAction.builder().languages().build(),
                        StoreSetCountriesAction.builder().countries().build()
                );
    }


    private void setupLenientMocks() throws ExecutionException, InterruptedException {
        // set up mocks for getProject
        lenient().when(commercetoolsClient.get()).thenReturn(byProjectKeyGet);
        lenient().when(byProjectKeyGet.execute()).thenReturn(projectCompletableFuture);
        lenient().when(projectCompletableFuture.toCompletableFuture()).thenReturn(projectCompletableFuture);
        lenient().when(projectCompletableFuture.get()).thenReturn(projectApiHttpResponse);
        lenient().when(projectApiHttpResponse.getBody()).thenReturn(project);

        // set up mocks for store operations
        final ByProjectKeyStoresRequestBuilder storesRequestBuilder = mock(ByProjectKeyStoresRequestBuilder.class);
        final ByProjectKeyStoresKeyByKeyRequestBuilder storeKeyRequestBuilder = mock(ByProjectKeyStoresKeyByKeyRequestBuilder.class);
        final ByProjectKeyStoresKeyByKeyPost storePost = mock(ByProjectKeyStoresKeyByKeyPost.class);

        lenient().when(commercetoolsClient.stores()).thenReturn(storesRequestBuilder);
        lenient().when(storesRequestBuilder.withKey(anyString())).thenReturn(storeKeyRequestBuilder);
        lenient().when(storeKeyRequestBuilder.post(any(StoreUpdate.class))).thenReturn(storePost);
        lenient().when(storePost.executeBlocking()).thenReturn(storeApiHttpResponse);
        lenient().when(storeApiHttpResponse.getBody()).thenReturn(store);
    }

    private void setUpMocksChain() {
        final ByProjectKeyStoresRequestBuilder storesRequestBuilder = mock(ByProjectKeyStoresRequestBuilder.class);
        final ByProjectKeyStoresKeyByKeyRequestBuilder storeKeyRequestBuilder = mock(ByProjectKeyStoresKeyByKeyRequestBuilder.class);
        final ByProjectKeyStoresKeyByKeyPost storePost = mock(ByProjectKeyStoresKeyByKeyPost.class);

        when(commercetoolsClient.stores()).thenReturn(storesRequestBuilder);
        when(storesRequestBuilder.withKey("store-" + PROJECT_KEY)).thenReturn(storeKeyRequestBuilder);
        when(storeKeyRequestBuilder.post(storeUpdateCaptor.capture())).thenReturn(storePost);
    }

    private List<CountryDto> createFewerCountries() {
        return Arrays.asList(
                createCountryDto("DE", Arrays.asList("de", "en")),
                createCountryDto("US", Arrays.asList("en"))
        );
    }

    private List<CountryDto> createCountriesWithDifferentLanguages() {
        return Arrays.asList(
                createCountryDto("DE", Arrays.asList("en", "fr")),
                createCountryDto("US", Arrays.asList("en", "es")),
                createCountryDto("FR", Arrays.asList("fr")),
                createCountryDto("GB", Arrays.asList("en"))
        );
    }

    private List<CountryDto> createDifferentCountriesAndLanguages() {
        return Arrays.asList(
                createCountryDto("US", Arrays.asList("en")),
                createCountryDto("CA", Arrays.asList("en", "fr"))
        );
    }

    private List<CountryDto> createSameCountriesAndLanguages() {
        final List<CountryDto> countryServiceList = Arrays.asList(
                createCountryDto("DE", Arrays.asList("de", "en")),
                createCountryDto("US", Arrays.asList("en")),
                createCountryDto("FR", Arrays.asList("de")),
                createCountryDto("GB", Arrays.asList("en"))
        );

        // check that countryServiceList is the same as store countries
        final List<String> storeCountryCodes = store.getCountries().stream()
                .map(StoreCountry::getCode)
                .toList();

        assertThat(countryServiceList).hasSize(storeCountryCodes.size());
        assertThat(countryServiceList).extracting(CountryDto::getIsoCode)
                .containsExactlyInAnyOrderElementsOf(storeCountryCodes);

        // check that uniqueLanguages is the same as store languages
        final Set<String> uniqueLanguages = countryServiceList.stream()
                .flatMap(country -> country.getTenantConfigurations().stream())
                .flatMap(config -> config.getLanguages().stream())
                .collect(Collectors.toSet());
        assertThat(uniqueLanguages).containsExactlyInAnyOrderElementsOf(store.getLanguages());

        return countryServiceList;
    }

    private CountryDto createCountryDto(final String isoCode, final List<String> languages) {
        return createActiveCountryDto(isoCode, languages, true);
    }

    private CountryDto createActiveCountryDto(final String isoCode, final List<String> languages, final boolean storefrontEnabled) {
        final CountryDto.TenantConfigurationDto tenantConfig = new CountryDto.TenantConfigurationDto.Builder()
                .tenant(Tenant.rexroth.toString())
                .languages(languages)
                .storefrontEnabled(storefrontEnabled)
                .build();

        return new CountryDto.Builder()
                .isoCode(isoCode)
                .name("Country " + isoCode)
                .activeInStore(true)
                .canSell(true)
                .canBuy(true)
                .tenantConfigurations(Collections.singletonList(tenantConfig))
                .build();
    }

    private CountryDto createInactiveCountryDto(final String isoCode, final List<String> languages) {
        final CountryDto.TenantConfigurationDto tenantConfig = new CountryDto.TenantConfigurationDto.Builder()
                .tenant(Tenant.rexroth.toString())
                .languages(languages)
                .storefrontEnabled(true)
                .build();

        return new CountryDto.Builder()
                .isoCode(isoCode)
                .name("Country " + isoCode)
                .activeInStore(false)
                .canSell(true)
                .canBuy(true)
                .tenantConfigurations(Collections.singletonList(tenantConfig))
                .build();
    }
}