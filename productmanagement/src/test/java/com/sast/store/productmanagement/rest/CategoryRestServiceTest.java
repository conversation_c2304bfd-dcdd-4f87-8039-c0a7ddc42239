package com.sast.store.productmanagement.rest;

import com.sast.store.productmanagement.AbstractComponentTest;
import com.sast.store.productmanagement.util.CommercetoolsMockExtension;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Test;

class CategoryRestServiceTest extends AbstractComponentTest {

    @Test
    void testGetCategories() {
        CommercetoolsMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/categories")
            .then().statusCode(200)
            .body("[0].categoryId", Matchers.equalTo("1e6c38eb-1895-4d0c-a885-67d2ed5613cd"))
            .body("[0].name", Matchers.equalTo("Florp"))
            .body("[0].description", Matchers.equalTo("Florpige Kategorie"))
            .body("[0].parentCategoryId", Matchers.equalTo("3fee4907-2ea1-48b1-8c07-6c9da3c73e2f"))
            .body("[0].parentCategories[0]", Matchers.equalTo("3fee4907-2ea1-48b1-8c07-6c9da3c73e2f"))
            .body("[0].order", Matchers.equalTo(0))
            .body("[1].categoryId", Matchers.equalTo("3fee4907-2ea1-48b1-8c07-6c9da3c73e2f"))
            .body("[1].name", Matchers.equalTo("BODAS Connect"))
            .body("[1].description", Matchers.equalTo("BODAS Connect description"))
            .body("[1].parentCategories", Matchers.empty())
            .body("[1].order", Matchers.equalTo(0.1F));
    }

    @Test
    void testGetCategoriesAnonymousUser() {
        CommercetoolsMockExtension.withDefaultResponse();

        RestAssured
            .given()
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/categories")
            .then().statusCode(200)
            .body(".", Matchers.hasSize(2))
            .body("[0].categoryId", Matchers.equalTo("1e6c38eb-1895-4d0c-a885-67d2ed5613cd"))
            .body("[1].categoryId", Matchers.equalTo("3fee4907-2ea1-48b1-8c07-6c9da3c73e2f"));
    }

    @Test
    void testGetCategory() {
        CommercetoolsMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/categories/1e6c38eb-1895-4d0c-a885-67d2ed5613cd")
            .then().statusCode(200)
            .body("categoryId", Matchers.equalTo("1e6c38eb-1895-4d0c-a885-67d2ed5613cd"))
            .body("name", Matchers.equalTo("Florp"))
            .body("description", Matchers.equalTo("Florpige Kategorie"))
            .body("parentCategoryId", Matchers.equalTo("3fee4907-2ea1-48b1-8c07-6c9da3c73e2f"))
            .body("parentCategories[0]", Matchers.equalTo("3fee4907-2ea1-48b1-8c07-6c9da3c73e2f"))
            .body("order", Matchers.equalTo(0));
    }

    @Test
    void testGetCategoryNotFound() {
        CommercetoolsMockExtension.withGetCategoryNotFoundResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/categories/1e6c38eb-1895-4d0c-a885-xxxx")
            .then().statusCode(200)
            .body(Matchers.equalTo("null"));
    }
}
