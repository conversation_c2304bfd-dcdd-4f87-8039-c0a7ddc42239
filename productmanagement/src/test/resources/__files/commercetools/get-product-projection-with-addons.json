{"id": "5abd3673-2110-4aa0-bbf9-c97cd1e24c89", "version": 44, "productType": {"typeId": "product-type", "id": "a46f1d35-7553-4813-8a20-993f1e84b9f4"}, "name": {"en": "BODAS Connect for RCU Series 10 & 20"}, "description": {"en": "The Telematics solution for machine OEMs to gain internal R&D and customer support efficiency & establish external data driven business models. Includes integrated Device Management, Cellular Connectivity and Data Management and offers Over-the-Air functionality for updating ECUs."}, "categories": [], "categoryOrderHints": {}, "slug": {"en": "bodas-connect-for-rcu-series-10-20"}, "metaTitle": {"en": ""}, "metaDescription": {"en": ""}, "variants": [{"attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "bundleAmount", "value": 1}, {"name": "licenseType", "value": {"key": "SUBSCRIPTION_POSTPAID", "label": "subscription product (SUBSCRIPTION_POSTPAID) with postpaid billing"}}, {"name": "externalProductId", "value": "R917014007"}, {"name": "sellerCompanyId", "value": "f01d4e3c-ab09-4cd2-b5ff-312de81ab7bf"}, {"name": "name", "value": {"en": "Device Management Extended"}}, {"name": "description", "value": {"en": "Compact + OTA Services + Universal Flasher"}}, {"name": "features", "value": {"en": "Compact\nOTA Services\nUniversal Flasher"}}, {"name": "runtime", "value": "P1M"}, {"name": "notice<PERSON><PERSON>od", "value": "P28D"}, {"name": "addons", "value": [{"typeId": "product", "id": "990cdc6a-13f5-4832-a7da-ab44df4b2f19", "obj": {"id": "990cdc6a-13f5-4832-a7da-ab44df4b2f19", "version": 2, "versionModifiedAt": "2024-11-19T15:35:33.357Z", "lastMessageSequenceNumber": 2, "createdAt": "2024-11-19T15:35:27.566Z", "lastModifiedAt": "2024-11-19T15:35:33.357Z", "lastModifiedBy": {"isPlatformClient": true, "user": {"typeId": "user", "id": "bd8dec93-e3e2-498c-8641-6329b89f1862"}}, "createdBy": {"isPlatformClient": true, "user": {"typeId": "user", "id": "bd8dec93-e3e2-498c-8641-6329b89f1862"}}, "productType": {"typeId": "product-type", "id": "75a25e43-ec58-4581-90f4-367b4cae1cfc"}, "masterData": {"current": {"name": {"en": "Data Management"}, "description": {"en": "Data Management"}, "categories": [], "categoryOrderHints": {}, "slug": {"en": "data-management"}, "metaTitle": {"en": "", "de": ""}, "metaDescription": {"en": "", "de": ""}, "masterVariant": {"id": 1, "sku": "DRX_PR_BODAS_DATA_600MB", "key": "DRX_PR_BODAS_DATA_600MB", "prices": [], "images": [], "attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "name", "value": {"en": "600MB"}}, {"name": "description", "value": {"en": "600MB of data"}}], "assets": []}, "variants": [{"id": 2, "sku": "DRX_PR_BODAS_DATA_3GB", "key": "DRX_PR_BODAS_DATA_3GB", "prices": [], "images": [], "attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "name", "value": {"en": "3GB"}}, {"name": "description", "value": {"en": "3GB"}}], "assets": []}], "searchKeywords": {}}, "staged": {"name": {"en": "Data Management"}, "description": {"en": "Data Management"}, "categories": [], "categoryOrderHints": {}, "slug": {"en": "data-management"}, "metaTitle": {"en": "", "de": ""}, "metaDescription": {"en": "", "de": ""}, "masterVariant": {"id": 1, "sku": "DRX_PR_BODAS_DATA_600MB", "key": "DRX_PR_BODAS_DATA_600MB", "prices": [], "images": [], "attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "name", "value": {"en": "600MB"}}, {"name": "description", "value": {"en": "600MB of data"}}], "assets": []}, "variants": [{"id": 2, "sku": "DRX_PR_BODAS_DATA_3GB", "key": "DRX_PR_BODAS_DATA_3GB", "prices": [], "images": [], "attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "name", "value": {"en": "3GB"}}, {"name": "description", "value": {"en": "3GB"}}], "assets": []}], "searchKeywords": {}}, "published": true, "hasStagedChanges": false}, "key": "data-management", "priceMode": "Standalone"}}, {"typeId": "product", "id": "d74b86e4-233c-4bec-b137-d7ef4b630b64", "obj": {"id": "d74b86e4-233c-4bec-b137-d7ef4b630b64", "version": 8, "versionModifiedAt": "2024-11-19T12:54:45.682Z", "lastMessageSequenceNumber": 5, "createdAt": "2024-11-18T16:48:15.973Z", "lastModifiedAt": "2024-11-19T12:54:45.682Z", "lastModifiedBy": {"isPlatformClient": true, "user": {"typeId": "user", "id": "bd8dec93-e3e2-498c-8641-6329b89f1862"}}, "createdBy": {"isPlatformClient": true, "user": {"typeId": "user", "id": "bd8dec93-e3e2-498c-8641-6329b89f1862"}}, "productType": {"typeId": "product-type", "id": "75a25e43-ec58-4581-90f4-367b4cae1cfc"}, "masterData": {"current": {"name": {"en": "Cellular Connection"}, "description": {"en": "Cellular Services for RCU Series 10 & 20"}, "categories": [], "categoryOrderHints": {}, "slug": {"en": "cellular-connection"}, "metaTitle": {"en": "", "de": ""}, "metaDescription": {"en": "", "de": ""}, "masterVariant": {"id": 1, "sku": "DRX_PR_BODAS_CONNECTION_50MB", "key": "DRX_PR_BODAS_CONNECTION_50MB", "prices": [], "images": [], "attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "name", "value": {"en": "50 MB"}}, {"name": "description", "value": {"en": "50MB of data"}}], "assets": []}, "variants": [{"id": 2, "sku": "DRX_PR_BODAS_CONNECTION_100MB", "key": "DRX_PR_BODAS_CONNECTION_100MB", "prices": [], "images": [], "attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "name", "value": {"en": "100 MB"}}, {"name": "description", "value": {"en": "100 MB of data"}}], "assets": []}], "searchKeywords": {}}, "staged": {"name": {"en": "Cellular Connection"}, "description": {"en": "Cellular Services for RCU Series 10 & 20"}, "categories": [], "categoryOrderHints": {}, "slug": {"en": "cellular-connection"}, "metaTitle": {"en": "", "de": ""}, "metaDescription": {"en": "", "de": ""}, "masterVariant": {"id": 1, "sku": "DRX_PR_BODAS_CONNECTION_50MB", "key": "DRX_PR_BODAS_CONNECTION_50MB", "prices": [], "images": [], "attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "name", "value": {"en": "50 MB"}}, {"name": "description", "value": {"en": "50MB of data"}}], "assets": []}, "variants": [{"id": 2, "sku": "DRX_PR_BODAS_CONNECTION_100MB", "key": "DRX_PR_BODAS_CONNECTION_100MB", "prices": [], "images": [], "attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "name", "value": {"en": "100 MB"}}, {"name": "description", "value": {"en": "100 MB of data"}}], "assets": []}], "searchKeywords": {}}, "published": true, "hasStagedChanges": false}, "key": "cellular-connection", "taxCategory": {"typeId": "tax-category", "id": "41eda603-b3f2-4b85-9fa6-d55874111b43"}, "priceMode": "Standalone"}}]}], "assets": [], "images": [], "price": {"key": "DRX_PR_BODAS_EXTENDED", "value": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 49900, "fractionDigits": 2}, "id": "7d7a5c04-bc38-4cc6-b571-e4796b4dd890"}, "prices": [], "key": "DRX_PR_BODAS_EXTENDED", "sku": "DRX_PR_BODAS_EXTENDED", "id": 2}], "masterVariant": {"attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "name", "value": {"en": "Device Management Compact"}}, {"name": "bundleAmount", "value": 1}, {"name": "licenseType", "value": {"key": "SUBSCRIPTION_POSTPAID", "label": "subscription product (SUBSCRIPTION_POSTPAID) with postpaid billing"}}, {"name": "externalProductId", "value": "R917014006"}, {"name": "sellerCompanyId", "value": "f01d4e3c-ab09-4cd2-b5ff-312de81ab7bf"}, {"name": "description", "value": {"en": "SOTA, Custom Snaps, Cybersecurity"}}, {"name": "features", "value": {"en": "SOTA\nCustom Snaps\nCybersecurity"}}, {"name": "runtime", "value": "P1M"}, {"name": "notice<PERSON><PERSON>od", "value": "P28D"}, {"name": "addons", "value": [{"typeId": "product", "id": "d74b86e4-233c-4bec-b137-d7ef4b630b64", "obj": {"id": "d74b86e4-233c-4bec-b137-d7ef4b630b64", "version": 8, "versionModifiedAt": "2024-11-19T12:54:45.682Z", "lastMessageSequenceNumber": 5, "createdAt": "2024-11-18T16:48:15.973Z", "lastModifiedAt": "2024-11-19T12:54:45.682Z", "lastModifiedBy": {"isPlatformClient": true, "user": {"typeId": "user", "id": "bd8dec93-e3e2-498c-8641-6329b89f1862"}}, "createdBy": {"isPlatformClient": true, "user": {"typeId": "user", "id": "bd8dec93-e3e2-498c-8641-6329b89f1862"}}, "productType": {"typeId": "product-type", "id": "75a25e43-ec58-4581-90f4-367b4cae1cfc"}, "masterData": {"current": {"name": {"en": "Cellular Connection"}, "description": {"en": "Cellular Services for RCU Series 10 & 20"}, "categories": [], "categoryOrderHints": {}, "slug": {"en": "cellular-connection"}, "metaTitle": {"en": "", "de": ""}, "metaDescription": {"en": "", "de": ""}, "masterVariant": {"id": 1, "sku": "DRX_PR_BODAS_CONNECTION_50MB", "key": "DRX_PR_BODAS_CONNECTION_50MB", "prices": [], "images": [], "attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "name", "value": {"en": "50 MB"}}, {"name": "description", "value": {"en": "50MB of data"}}], "assets": []}, "variants": [{"id": 2, "sku": "DRX_PR_BODAS_CONNECTION_100MB", "key": "DRX_PR_BODAS_CONNECTION_100MB", "prices": [], "images": [], "attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "name", "value": {"en": "100 MB"}}, {"name": "description", "value": {"en": "100 MB of data"}}], "assets": []}], "searchKeywords": {}}, "staged": {"name": {"en": "Cellular Connection"}, "description": {"en": "Cellular Services for RCU Series 10 & 20"}, "categories": [], "categoryOrderHints": {}, "slug": {"en": "cellular-connection"}, "metaTitle": {"en": "", "de": ""}, "metaDescription": {"en": "", "de": ""}, "masterVariant": {"id": 1, "sku": "DRX_PR_BODAS_CONNECTION_50MB", "key": "DRX_PR_BODAS_CONNECTION_50MB", "prices": [], "images": [], "attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "name", "value": {"en": "50 MB"}}, {"name": "description", "value": {"en": "50MB of data"}}], "assets": []}, "variants": [{"id": 2, "sku": "DRX_PR_BODAS_CONNECTION_100MB", "key": "DRX_PR_BODAS_CONNECTION_100MB", "prices": [], "images": [], "attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "name", "value": {"en": "100 MB"}}, {"name": "description", "value": {"en": "100 MB of data"}}], "assets": []}], "searchKeywords": {}}, "published": true, "hasStagedChanges": false}, "key": "cellular-connection", "taxCategory": {"typeId": "tax-category", "id": "41eda603-b3f2-4b85-9fa6-d55874111b43"}, "priceMode": "Standalone"}}]}], "assets": [], "images": [{"url": "https://images.cdn.europe-west1.gcp.commercetools.com/b4800138-a539-4ad0-9511-9260c02d29bb/bodasconnect-ooNblHOt.jpeg", "dimensions": {"w": 1600, "h": 1600}}, {"url": "https://images.cdn.europe-west1.gcp.commercetools.com/b4800138-a539-4ad0-9511-9260c02d29bb/bodasconnect_icon-dbWuJ_-R.jpeg", "dimensions": {"w": 224, "h": 224}}], "price": {"key": "DRX_PR_BODAS_COMPACT", "value": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 24900, "fractionDigits": 2}, "id": "9c50dda8-7bca-43a8-9b22-63057ea8eab6"}, "prices": [], "key": "DRX_PR_BODAS_COMPACT", "sku": "DRX_PR_BODAS_COMPACT", "id": 1}, "searchKeywords": {}, "hasStagedChanges": false, "published": true, "key": "bodas_connect_10", "taxCategory": {"typeId": "tax-category", "id": "41eda603-b3f2-4b85-9fa6-d55874111b43"}, "priceMode": "Standalone", "createdAt": "2024-11-15T15:57:17.165Z", "lastModifiedAt": "2024-11-19T15:37:04.389Z"}