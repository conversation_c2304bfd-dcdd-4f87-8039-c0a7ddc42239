{"header": {"version": "V1", "id": {"eventId": "ignored", "senderName": "brim-service"}, "tenant": "rex<PERSON>", "timestamp": "2024-07-09T06:19:44.194Z", "cause": {"eventId": "ignored", "senderName": "brim"}}, "data": {"@type": "PRODUCT", "productId": "DC_12345678Z100", "billingSyncStatus": "IN_SYNC", "externalProductId": "12345678Z100", "name": {"de": "Tolles Produkt", "en": "Great Product"}, "contractType": {"@type": "SUBSCRIPTION", "contractPeriod": "QUARTERLY", "noticePeriod": "P1M"}, "sellerCompanyId": "2b09205a-4cf6-4079-8577-647b4562a416", "listPrices": [{"currency": "PLN", "listPriceEntries": [{"validFrom": "2023-03-05", "validTo": "2023-12-31", "amount": "69.99", "storefrontId": "bloop1pln"}, {"validFrom": "2024-01-01", "validTo": "9999-12-31", "amount": "76.99", "storefrontId": "bloop2pln"}], "pendingListPriceEntries": []}, {"currency": "EUR", "listPriceEntries": [{"validFrom": "2023-03-05", "validTo": "2023-12-31", "amount": "15.99", "storefrontId": "bloop1eur"}, {"validFrom": "2024-01-01", "validTo": "2028-03-31", "amount": "17.99", "storefrontId": "bloop2eur"}, {"validFrom": "2028-04-01", "validTo": "9999-12-31", "amount": "19.99", "storefrontId": "foo1"}]}], "groupPrices": [{"currency": "EUR", "priceGroup": "WD0001", "billingSyncStatus": "IN_SYNC", "entries": [{"validFrom": "2028-10-25", "validTo": "2028-10-27", "amount": "29.99", "storefrontId": "foo1WD0001"}, {"validFrom": "2028-10-28", "validTo": "9999-12-31", "amount": "39.99", "storefrontId": "foo2WD0001"}]}]}, "errors": []}