server:
  port: 8082

spring:
  cloud:
    aws:
      region:
        static: eu-central-1
      credentials:
        access-key: fakekey
        secret-key: fakekey
      sqs:
        endpoint: http://localhost:40123
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://sso.mp-dc-d.com/auth/realms/rexroth
  datasource:
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: validate
    generate-ddl: false
  docker:
    compose:
      enabled: true
      file: productmanagement/docker-compose.yml
  sql:
    init:
      data-locations: classpath:sql/local/local-sampledata.sql
      mode: always

bossstore:
  commercetools:
    clientId: SSAIdVhBpKt80ChCMjZwxi4c
    clientSecret: XtXHlio2S_z0UbW4KSfFniBO3dTklMfW
    projectKey: sast-dev
  hazelcast:
    enabled: local
  countriesservice:
    url: http://localhost:40003
  ump:
    url: http://localhost:40101
