package com.sast.store.testing.awsmockup.junit;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sast.store.testing.awsmockup.SqsMock;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.extension.AfterEachCallback;
import org.junit.jupiter.api.extension.BeforeAllCallback;
import org.junit.jupiter.api.extension.ExtensionContext;
import software.amazon.awssdk.services.sqs.SqsAsyncClient;
import software.amazon.awssdk.services.sqs.model.DeleteMessageRequest;
import software.amazon.awssdk.services.sqs.model.GetQueueAttributesRequest;
import software.amazon.awssdk.services.sqs.model.Message;
import software.amazon.awssdk.services.sqs.model.PurgeQueueRequest;
import software.amazon.awssdk.services.sqs.model.ReceiveMessageRequest;
import software.amazon.awssdk.services.sqs.model.ReceiveMessageResponse;
import software.amazon.awssdk.services.sqs.model.SendMessageRequest;

import java.io.IOException;
import java.net.ServerSocket;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;
import java.util.concurrent.ExecutionException;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.hasSize;

public class SqsMockExtension extends SqsMock implements BeforeAllCallback, AfterEachCallback {
    public static final int RANDOM_PORT = new Random().nextInt(10_000, 50_000);
    private static SqsAsyncClient defaultSqsClient;

    public SqsMockExtension() {
        this(RANDOM_PORT);
        defaultSqsClient = getSqsClient();
    }

    public SqsMockExtension(final int port) {
        super(port);
    }

    public static SqsAsyncClient getDefaultSqsClient() {
        return defaultSqsClient;
    }

    public static String sendMessage(final String queueName, final String body) {
        if (queueName.endsWith(".fifo")) {
            return sendMessage(queueName, body, "group1");
        }
        try {
            return getDefaultSqsClient().sendMessage(SendMessageRequest.builder()
                    .queueUrl(queueName)
                    .messageBody(body)
                    .build())
                    .get()
                    .messageId();
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        }
    }

    public static String sendMessage(final String queueName, final String body, final String messageGroupId) {
        try {
            return getDefaultSqsClient().sendMessage(SendMessageRequest.builder()
                .queueUrl(queueName)
                .messageBody(body)
                .messageGroupId(messageGroupId)
                .build())
                .get()
                .messageId();
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        }
    }

    public static Optional<Message> receiveMessage(final String queueName) {
        try {
            final ReceiveMessageResponse messages = getDefaultSqsClient()
                    .receiveMessage(ReceiveMessageRequest.builder()
                            .queueUrl("/queue/" + queueName)
                            .waitTimeSeconds(1)
                            .maxNumberOfMessages(1)
                            .build())
                    .get();
            if (!messages.hasMessages() || messages.messages().isEmpty()) {
                return Optional.empty();
            }
            assertThat(messages.messages(), hasSize(1));
            final String receiptHandle = messages.messages().get(0).receiptHandle();
            getDefaultSqsClient().deleteMessage(DeleteMessageRequest.builder().queueUrl("/queue/" + queueName)
                    .receiptHandle(receiptHandle).build())
                    .get();
            return Optional.ofNullable(messages.messages().get(0));
        } catch (InterruptedException | ExecutionException ex) {
            return Optional.empty();
        }
    }

    public static void purgeQueue(final String queueUrl) {
        Awaitility.await()
                .atMost(Duration.ofSeconds(60))
                .until(() -> {
                    getDefaultSqsClient().purgeQueue(PurgeQueueRequest.builder().queueUrl(queueUrl).build()).get();
                    return getDefaultSqsClient().getQueueAttributes(GetQueueAttributesRequest.builder()
                            .queueUrl(queueUrl)
                            .attributeNamesWithStrings(
                                    "ApproximateNumberOfMessages",
                                    "ApproximateNumberOfMessagesNotVisible",
                                    "ApproximateNumberOfMessagesDelayed")
                            .build()).get()
                            .attributes()
                            .values().stream()
                            .map(Integer::valueOf)
                            .allMatch(messagesCount -> messagesCount == 0);
                });
    }

    private static boolean isPortAvailable(final int port) {
        try (final ServerSocket socket = new ServerSocket(port)) {
            socket.setReuseAddress(true);
            return true;
        } catch (final IOException e) {
            return false;
        }
    }

    @Override
    public void beforeAll(final ExtensionContext context) {
        if (isPortAvailable(getPort())) {
            startAndWait();
        }
        withQueue("EmailserviceMail", "EmailserviceMailDlq");
        withFifoQueue("bossstore-brim-product-events.fifo", "bossstore-brim-product-events-dlq.fifo");
        withFifoQueue("bossstore-brim-order-events.fifo", "bossstore-brim-order-events-dlq.fifo");
        withFifoQueue("bossstore-contractmanagement-brim-order-events.fifo", "bossstore-contractmanagement-brim-order-events-dlq.fifo");
        withFifoQueue("bossstore-brim-contract-events.fifo", "bossstore-brim-contract-events-dlq.fifo");
        withFifoQueue("bossstore-businessprocessing-invoice-events.fifo", "bossstore-businessprocessing-invoice-events-dlq.fifo");
        withQueue("bossstore-ordermanagement-account-events", "bossstore-ordermanagement-account-events-dlq");
    }

    @Override
    public void afterEach(final ExtensionContext context) throws InterruptedException, ExecutionException {
        this.getSqsClient().listQueues().get().queueUrls()
                .forEach(SqsMockExtension::purgeQueue);
    }

    private void startAndWait() {
        start();
        Awaitility.await().atMost(Duration.ofMinutes(2)).pollInterval(Duration.ofMillis(500)).ignoreExceptions()
                .until(() -> !isPortAvailable(getPort()));

    }

    public static String snsToSqsMessage(final String message) {
        try {
            return new ObjectMapper().writeValueAsString(Map.of(
                    "Type", "Notification",
                    "Message", message));
        } catch (final JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static List<Message> collectMessages(final String queueName, final List<Message> collector) {
        final Optional<Message> message = receiveMessage(queueName);
        if (message.isPresent()) {
            collector.add(message.get());
            return collectMessages(queueName, collector);
        }
        return collector;
    }
}
