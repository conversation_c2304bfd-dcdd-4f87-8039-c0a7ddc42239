package com.sast.store.testing.awsmockup;

import org.elasticmq.rest.sqs.SQSRestServer;
import org.elasticmq.rest.sqs.SQSRestServerBuilder;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sqs.SqsAsyncClient;
import software.amazon.awssdk.services.sqs.model.CreateQueueRequest;

import java.net.URI;
import java.util.Map;
import java.util.concurrent.ExecutionException;

public class SqsMock {

    private final int port;
    private final SqsAsyncClient sqsClient;
    private SQSRestServer server;

    public SqsMock(final int port) {
        this.port = port;
        sqsClient = getSqsClient(port);
    }

    public SqsMock start() {
        server = SQSRestServerBuilder.withPort(port).withInterface("0.0.0.0").start();
        return this;
    }

    public SqsMock stop() {
        if (server != null) {
            server.stopAndWait();
        }
        return this;
    }

    public SqsMock withQueue(final String queueName) {
        try {
            sqsClient.createQueue(CreateQueueRequest.builder()
                    .queueName(queueName)
                    .attributesWithStrings(Map.of("VisibilityTimeout", "0"))
                    .build())
                    .get();
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        }
        return this;
    }

    public SqsMock withQueue(final String queueName, final String deadLetterQueue) {
        this.withQueue(deadLetterQueue);
        try {
            sqsClient.createQueue(CreateQueueRequest.builder().queueName(queueName)
                    .attributesWithStrings(Map.of(
                            "RedrivePolicy",
                            "{\"maxReceiveCount\":\"1\", \"deadLetterTargetArn\":\"" + deadLetterQueue + "\"}",
                            "VisibilityTimeout", "0"))
                    .build())
                    .get();
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        }
        return this;
    }

    public SqsMock withFifoQueue(final String queueName) {
        try {
            sqsClient.createQueue(CreateQueueRequest.builder().queueName(queueName)
                    .attributesWithStrings(Map.of(
                            "VisibilityTimeout", "0",
                            "FifoQueue", "true",
                            "ContentBasedDeduplication", "true"))
                    .build())
                    .get();
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        }
        return this;
    }

    public SqsMock withFifoQueue(final String queueName, final String deadLetterQueue) {
        this.withFifoQueue(deadLetterQueue);
        try {
            sqsClient.createQueue(CreateQueueRequest.builder().queueName(queueName)
                    .attributesWithStrings(Map.of(
                            "FifoQueue", "true",
                            "ContentBasedDeduplication", "true",
                            "RedrivePolicy",
                            "{\"maxReceiveCount\":\"1\", \"deadLetterTargetArn\":\"" + deadLetterQueue + "\"}"))
                    .build())
                    .get();
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        }
        return this;
    }

    public SqsAsyncClient getSqsClient() {
        return sqsClient;
    }

    public int getPort() {
        return port;
    }

    public static SqsAsyncClient getSqsClient(final int port) {
        return SqsAsyncClient.builder()
                .endpointOverride(URI.create("http://0.0.0.0:" + port))
                .region(Region.EU_CENTRAL_1)
                .credentialsProvider(
                        StaticCredentialsProvider.create(AwsBasicCredentials.create("fakekey", "fakesecret")))
                .build();
    }
}
