package com.sast.store.testing.awsmockup.junit;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.github.tomakehurst.wiremock.verification.LoggedRequest;
import com.sast.store.testing.awsmockup.SnsMock;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URLEncodedUtils;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.extension.AfterAllCallback;
import org.junit.jupiter.api.extension.AfterEachCallback;
import org.junit.jupiter.api.extension.BeforeAllCallback;
import org.junit.jupiter.api.extension.ExtensionContext;

import java.io.IOException;
import java.net.ServerSocket;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.anyUrl;
import static com.github.tomakehurst.wiremock.client.WireMock.containing;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;

public class SnsMockExtension extends SnsMock implements BeforeAllCallback, AfterAllCallback, AfterEachCallback {
    private static final int DEFAULT_PORT = 9311;

    public SnsMockExtension() {
        super(DEFAULT_PORT);
    }

    private static boolean isPortAvailable(final int port) {
        try (final ServerSocket socket = new ServerSocket(port)) {
            socket.setReuseAddress(true);
            return true;
        } catch (final IOException e) {
            return false;
        }
    }

    @Override
    public void beforeAll(final ExtensionContext context) {
        if (isPortAvailable(getPort())) {
            startAndWait();
        } else {
            stopAndWait();
        }
        withTopic("bossstore-order-events.fifo");
        mockAll();
    }

    @Override
    public void afterEach(final ExtensionContext extensionContext) {
        //resetSnsRequests();
    }

    @Override
    public void afterAll(final ExtensionContext extensionContext) {
        stopAndWait();
    }

    public static void resetSnsRequests() {
        getMock().resetRequests();
    }

    public void mockAll() {
        getMock().stubFor(post(anyUrl()).withRequestBody(containing("Action=CreateTopic"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withBody("""
                    <CreateTopicResponse xmlns="http://sns.amazonaws.com/doc/2010-03-31/">
                        <member>
                            <TopicArn>arn:aws:sns:eu-central-1:123456789012:bossstore-order-events.fifo</TopicArn>
                            <TopicArn>arn:aws:sns:eu-central-1:123456789012:bossstore-contract-events.fifo</TopicArn>
                            <TopicArn>arn:aws:sns:eu-central-1:123456789012:bossstore-product-events.fifo</TopicArn>
                        </member>
                    </CreateTopicResponse>
                    """)));

        getMock().stubFor(post(anyUrl()).withRequestBody(containing("Action=ListTopics"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withBody("""
                    <ListTopicsResponse xmlns="http://sns.amazonaws.com/doc/2010-03-31/">
                      <ListTopicsResult>
                        <Topics>
                          %s
                        </Topics>
                      </ListTopicsResult>
                      <ResponseMetadata>
                        <RequestId>e9193898-9470-5e84-96ce-6d37f90a8a2d</RequestId>
                      </ResponseMetadata>
                    </ListTopicsResponse>
                    """.formatted(TOPICS.stream()
                                .map(t -> "<member><TopicArn>arn:aws:sns:eu-central-1:123456789012:" + t + "</TopicArn></member>")
                                .collect(Collectors.joining())))));

        getMock().stubFor(post(anyUrl()).withRequestBody(containing("Action=Publish"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withBody("""
                    <PublishResponse xmlns="http://sns.amazonaws.com/doc/2010-03-31/">
                      <PublishResult>
                        <MessageId>a3f40626-9aac-5da4-aa02-5389f7d6190d</MessageId>
                      </PublishResult>
                      <ResponseMetadata>
                        <RequestId>deee5872-4269-5cce-9935-0ddd2f139867</RequestId>
                      </ResponseMetadata>
                    </PublishResponse>
                    """)));
    }

    public static List<LoggedRequest> getAllPublishedRequests() {
        return getMock()
                .findAll(postRequestedFor(anyUrl()).withRequestBody(containing("Action=Publish")));
    }


    public static <T> List<T> getAllPublishedMessages(final Class<T> clazz) {
        return getAllPublishedRequests().stream()
                .map(LoggedRequest::getBodyAsString)
                .flatMap(b -> URLEncodedUtils.parse(b, StandardCharsets.UTF_8).stream())
                .filter(m -> m.getName().equals("Message"))
                .map(NameValuePair::getValue)
                .map(json -> fromJson(json, clazz))
                .flatMap(Optional::stream)
                .collect(Collectors.toList());
    }

    public static List<String> getAllPublishedMessages() {
        return getAllPublishedRequests().stream()
                .map(LoggedRequest::getBodyAsString)
                .flatMap(b -> URLEncodedUtils.parse(b, StandardCharsets.UTF_8).stream())
                .filter(m -> m.getName().equals("Message"))
                .map(NameValuePair::getValue)
                .collect(Collectors.toList());
    }

    public static int getDefaultPort() {
        return DEFAULT_PORT;
    }

    private void startAndWait() {
        start();
        Awaitility.await().atMost(Duration.ofMinutes(2)).pollInterval(Duration.ofMillis(500)).ignoreExceptions()
                .until(() -> !isPortAvailable(getPort()));
    }

    private void stopAndWait() {
        stop();
        Awaitility.await().atMost(Duration.ofMinutes(2)).pollInterval(Duration.ofMillis(500)).ignoreExceptions()
                .until(() -> isPortAvailable(getPort()));
    }

    private static <T> Optional<T> fromJson(final String json, final Class<T> clazz) {
        try {
            return Optional.of(new ObjectMapper()
                    .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                    .registerModule(new JavaTimeModule())
                    .readValue(json, clazz));
        } catch (final IOException e) {
            throw new RuntimeException(e);
        }
    }


}

