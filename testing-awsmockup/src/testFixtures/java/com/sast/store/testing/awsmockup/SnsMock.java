package com.sast.store.testing.awsmockup;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;

import java.util.ArrayList;
import java.util.List;

public class SnsMock {

    protected static final List<String> TOPICS = new ArrayList<>();
    private static WireMockServer server;
    private final int port;

    public SnsMock(final int port) {
        this.port = port;
    }

    public SnsMock start() {
        server = new WireMockServer(WireMockConfiguration.options().port(port));
        server.start();
        return this;
    }

    public SnsMock stop() {
        if (server != null) {
            server.stop();
        }
        return this;
    }

    public SnsMock withTopic(final String topicName) {
        TOPICS.add(topicName);
        return this;
    }

    protected int getPort() {
        return port;
    }

    protected static WireMockServer getMock() {
        return server;
    }

}

