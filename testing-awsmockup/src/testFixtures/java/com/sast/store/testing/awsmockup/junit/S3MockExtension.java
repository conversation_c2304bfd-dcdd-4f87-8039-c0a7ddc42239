package com.sast.store.testing.awsmockup.junit;

import com.sast.store.testing.awsmockup.S3Mock;
import org.junit.jupiter.api.extension.AfterAllCallback;
import org.junit.jupiter.api.extension.BeforeAllCallback;
import org.junit.jupiter.api.extension.ExtensionContext;

import java.util.UUID;

public class S3MockExtension extends S3Mock implements BeforeAllCallback, AfterAllCallback {

    public static final int PORT = 9327;
    private static S3MockExtension mock;

    public S3MockExtension() {
        super(PORT, "localS3Storage" + UUID.randomUUID());
        mock = this;
    }

    public static S3MockExtension getMock() {
        return mock;
    }

    @Override
    public void beforeAll(final ExtensionContext context) throws Exception {
        start()
            .withBucket("brim-service-documents-dev");
    }

    @Override
    public void afterAll(final ExtensionContext context) throws Exception {
        stop();
    }
}
