package com.sast.store.testing.awsmockup;

import com.almworks.sqlite4java.SQLite;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB;
import com.amazonaws.services.dynamodbv2.AmazonDynamoDBClientBuilder;
import com.amazonaws.services.dynamodbv2.document.DynamoDB;
import com.amazonaws.services.dynamodbv2.document.Item;
import com.amazonaws.services.dynamodbv2.exceptions.DynamoDBLocalServiceException;
import com.amazonaws.services.dynamodbv2.local.main.ServerRunner;
import com.amazonaws.services.dynamodbv2.local.server.DynamoDBProxyServer;
import com.amazonaws.services.dynamodbv2.model.AttributeDefinition;
import com.amazonaws.services.dynamodbv2.model.CreateTableRequest;
import com.amazonaws.services.dynamodbv2.model.GlobalSecondaryIndex;
import com.amazonaws.services.dynamodbv2.model.KeySchemaElement;
import com.amazonaws.services.dynamodbv2.model.KeyType;
import com.amazonaws.services.dynamodbv2.model.Projection;
import com.amazonaws.services.dynamodbv2.model.ProjectionType;
import com.amazonaws.services.dynamodbv2.model.ProvisionedThroughput;
import com.amazonaws.services.dynamodbv2.model.ScalarAttributeType;
import org.apache.commons.cli.ParseException;
import org.apache.commons.lang3.SystemUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URISyntaxException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Stream;

public class DynamoDbMock {
    private static final Logger LOG = LoggerFactory.getLogger(DynamoDbMock.class);

    private DynamoDBProxyServer server;
    private final AmazonDynamoDB dynamoDbclient;
    private final int port;

    public DynamoDbMock(final int port) {
        this.port = port;
        try {
            // Log.setLog(new StdErrLog());

            SQLite.setLibraryPath(findNativeSqliteLibraries());
            LOG.info("loaded dynamodb/sqlite library from: {} ", System.getProperty("sqlite4java.library.path"));
            initializeServer();

            dynamoDbclient = AmazonDynamoDBClientBuilder.standard()
                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration("http://localhost:" + this.port, "eu-central-1"))
                .withCredentials(new AWSStaticCredentialsProvider(new BasicAWSCredentials("fakeaccesskey", "fakesecretkey")))
                .build();
        } catch (final Exception e) {
            throw new RuntimeException(e);
        }

    }

    private void initializeServer() throws DynamoDBLocalServiceException, ParseException {
        server = ServerRunner.createServerFromCommandLineArgs(new String[] { "-inMemory", "-port", this.port + "", "-sharedDb" });
    }

    /**
     * try to resolve native libraries from maven repository
     *
     * sqlite libraries should be in the following folder:
     *
     * m2/repository/com/almworks/sqlite4java/sqlite4java/1.0.392/sqlite4java-1.0.392.jar
     *
     * native libraries should in the following folder:
     *
     * .m2/repository/com/almworks/sqlite4java/sqlite4java-win32-x64/1.0.392/sqlite4java-win32-x64-1.0.392.dll
     * .m2/repository/com/almworks/sqlite4java/libsqlite4java-linux-amd64/1.0.392/sqlite4java-linux-amd64-1.0.392.so
     * .m2/repository/com/almworks/sqlite4java/libsqlite4java-osx/1.0.392/sqlite4java-osx-1.0.392.dylib
     * .m2/repository/io/github/ganadist/sqlite4java/libsqlite4java-osx-aarch64/1.0.392/libsqlite4java-osx-1.0.392.dylib
     *
     */
    private String findNativeSqliteLibraries() throws URISyntaxException {
        final Path sqliteFolder = Paths.get(SQLite.class.getProtectionDomain().getCodeSource().getLocation().toURI());
        final String sqliteVersion = sqliteFolder.subpath(sqliteFolder.getNameCount() - 2, sqliteFolder.getNameCount() - 1).toString();
        final String mavenRepoFolder = Paths.get(sqliteFolder.toUri().resolve("../../../../..")).toString();

        if (SystemUtils.IS_OS_WINDOWS) {
            return Paths.get(mavenRepoFolder, "com/almworks/sqlite4java/sqlite4java-win32-x64", sqliteVersion).toString();
        }
        if (SystemUtils.IS_OS_LINUX) {
            return Paths.get(mavenRepoFolder, "com/almworks/sqlite4java/libsqlite4java-linux-amd64", sqliteVersion).toString();
        }
        if (SystemUtils.IS_OS_MAC_OSX && SystemUtils.OS_ARCH.equals("aarch64")) {
            return Paths.get(mavenRepoFolder, "io/github/ganadist/sqlite4java/libsqlite4java-osx-aarch64", sqliteVersion).toString();
        }
        if (SystemUtils.IS_OS_MAC_OSX) {
            return Paths.get(mavenRepoFolder, "com/almworks/sqlite4java/libsqlite4java-osx", sqliteVersion).toString();
        }
        throw new IllegalStateException(SystemUtils.OS_NAME + " not supported.");
    }

    public DynamoDbMock start() {
        try {
            server.start();
            LOG.debug("started dynamodb");
        } catch (final Exception e) {
            throw new RuntimeException(e);
        }
        return this;
    }

    public AmazonDynamoDB getClient() {
        return dynamoDbclient;
    }

    public DynamoDbMock stop() {
        try {
            server.stop();
            server.join();
            initializeServer();
            LOG.debug("stopped dynamodb");
        } catch (final Exception e) {
            throw new RuntimeException(e);
        }
        return this;
    }

    public DynamoDbMock withTable(final String tableName, final String hashColumn) {
        return withTable(tableName,
            List.of(new KeySchemaElement(hashColumn, KeyType.HASH)),
            List.of());
    }

    public DynamoDbMock withTable(final String tableName, final String hashColumn, final String rangeColumn) {
        return withTable(tableName,
            List.of(new KeySchemaElement(hashColumn, KeyType.HASH), new KeySchemaElement(rangeColumn, KeyType.RANGE)),
            List.of());
    }

    public DynamoDbMock withTable(final String tableName, final String hashColumn, final String rangeColumn,
        final String secondaryHash, final String secondaryRange) {
        return withTable(tableName,
            List.of(new KeySchemaElement(hashColumn, KeyType.HASH), new KeySchemaElement(rangeColumn, KeyType.RANGE)),
            List.of(List.of(new KeySchemaElement(secondaryHash, KeyType.HASH), new KeySchemaElement(secondaryRange, KeyType.RANGE))));
    }

    public DynamoDbMock withTable(final String tableName, final String hashColumn, final String secondaryHash,
        final String secondaryRange) {
        return withTable(tableName,
            List.of(new KeySchemaElement(hashColumn, KeyType.HASH)),
            List.of(List.of(new KeySchemaElement(secondaryHash, KeyType.HASH), new KeySchemaElement(secondaryRange, KeyType.RANGE))));
    }

    public DynamoDbMock withTable(final String tableName, final List<KeySchemaElement> keySchema,
        final List<List<KeySchemaElement>> secondaryIndexes) {
        final List<AttributeDefinition> attributeNames = Stream.concat(
            keySchema.stream().map(KeySchemaElement::getAttributeName),
            secondaryIndexes.stream().flatMap(List::stream).map(KeySchemaElement::getAttributeName))
            .distinct().map(n -> new AttributeDefinition(n, ScalarAttributeType.S)).toList();
        dynamoDbclient.createTable(new CreateTableRequest()
            .withProvisionedThroughput(new ProvisionedThroughput(1000L, 1000L))
            .withTableName(tableName)
            .withAttributeDefinitions(attributeNames)
            .withKeySchema(keySchema)
            .withGlobalSecondaryIndexes(secondaryIndexes.isEmpty() ? null
                : secondaryIndexes.stream().map(i -> new GlobalSecondaryIndex()
                    .withProvisionedThroughput(new ProvisionedThroughput(1000L, 1000L))
                    .withIndexName(tableName + "_secondaryindex0")
                    .withKeySchema(i)
                    .withProjection(new Projection().withProjectionType(ProjectionType.ALL))).toList()));
        LOG.debug("created table {}", tableName);
        return this;
    }

    public DynamoDbMock withItem(final String tableName, final Item item) {
        new DynamoDB(dynamoDbclient)
            .getTable(tableName)
            .putItem(item);
        return this;
    }

    public DynamoDbMock dropTables() {
        for (final String table : dynamoDbclient.listTables().getTableNames()) {
            dynamoDbclient.deleteTable(table);
            LOG.debug("deleted table {}", table);
        }
        return this;
    }
}
