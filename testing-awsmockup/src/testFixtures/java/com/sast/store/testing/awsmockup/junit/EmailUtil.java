package com.sast.store.testing.awsmockup.junit;

import org.awaitility.Awaitility;
import software.amazon.awssdk.services.sqs.model.Message;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

public final class EmailUtil {

    private EmailUtil() {
    }

    public static List<Message> awaitEmails(final int numberOfEmails) {
        final List<Message> collector = new ArrayList<>();
        return Awaitility.await().atMost(Duration.ofSeconds(10))
            .until(() -> SqsMockExtension.collectMessages("EmailserviceMail", collector), m -> m.size() == numberOfEmails);
    }
}
