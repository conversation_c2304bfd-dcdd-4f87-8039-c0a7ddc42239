package com.sast.store.testing.awsmockup.junit;

import com.sast.store.testing.awsmockup.DynamoDbMock;
import org.junit.jupiter.api.extension.AfterAllCallback;
import org.junit.jupiter.api.extension.BeforeAllCallback;
import org.junit.jupiter.api.extension.BeforeEachCallback;
import org.junit.jupiter.api.extension.ExtensionContext;

public class DynamodbMockExtension extends DynamoDbMock
    implements BeforeAllCallback, AfterAllCallback, BeforeEachCallback {

    public static final int PORT = 9324;
    private static DynamodbMockExtension mock;

    public DynamodbMockExtension() {
        super(PORT);
        mock = this;
    }

    public DynamodbMockExtension(final int port) {
        super(port);
        mock = this;
    }

    public static DynamodbMockExtension getMock() {
        return mock;
    }

    @Override
    public void beforeAll(final ExtensionContext context) throws Exception {
        start();
        setupTables();
    }

    @Override
    public void beforeEach(final ExtensionContext context) throws Exception {
        dropTables();
        setupTables();
    }

    private void setupTables() {
        withTable("BossstoreBrimContracts", "companyId", "contractId");
        withTable("BossstoreLicenses", "companyId", "licenseId");
        withTable("bossstore_invoice_entity", "documentNumber", "company", "documentDate");
        withTable("bossstore_hydraulichub_freemium_users", "email");
        withTable("bossstore_litmos_teams", "company");
    }

    @Override
    public void afterAll(final ExtensionContext context) throws Exception {
        stop();
    }
}
