package com.sast.store.testing.awsmockup;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.auth.credentials.AnonymousCredentialsProvider;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;

import java.io.File;
import java.net.URI;
import java.nio.file.Path;

public class S3Mock {
    private static final Logger LOG = LoggerFactory.getLogger(S3Mock.class);
    private final io.findify.s3mock.S3Mock localS3Server;
    private final String tmpDir = System.getProperty("java.io.tmpdir") + File.separator;
    private final int port;
    private S3Client client;
    private String localStorageDir;

    public S3Mock(final int port) {
        this.port = port;
        localS3Server = new io.findify.s3mock.S3Mock.Builder().withPort(port).withInMemoryBackend().build();
        setupClient();
    }

    public S3Mock(final int port, final String path) {
        this.port = port;
        localStorageDir = tmpDir + path;
        localS3Server = new io.findify.s3mock.S3Mock.Builder().withPort(port).withFileBackend(localStorageDir).build();
        LOG.warn("s3 storage set to {}", localStorageDir);
        setupClient();
    }

    private void setupClient() {
        client = S3Client.builder()
                .endpointOverride(URI.create("http://localhost:" + port))
                .forcePathStyle(true)
                .credentialsProvider(AnonymousCredentialsProvider.create())
                .region(Region.EU_CENTRAL_1)
                .build();
    }

    public S3Mock withBucket(final String bucket) {
        client.createBucket(b -> b.bucket(bucket));
        return this;
    }

    public S3Mock putContent(final String bucket, final String key, final String value) {
        client.putObject(b -> b.bucket(bucket).key(key), RequestBody.fromString(value));
        return this;
    }

    public S3Mock putContent(final String bucket, final String key, final Path value) {
        client.putObject(b -> b.bucket(bucket).key(key), RequestBody.fromFile(value));
        return this;
    }

    public S3Mock start() {
        localS3Server.start();
        LOG.warn("local S3 mock started");
        return this;
    }

    public S3Mock stop() {
        localS3Server.stop();
        LOG.warn("local S3 mock stopped");
        return this;
    }

    public S3Client getClient() {
        return client;
    }

    public String getStorageDir() {
        return localStorageDir;
    }
}
