plugins {
    id("bossstore.subproject-conventions")
    `java-test-fixtures`
    id("org.springframework.boot") version "3.4.3" apply false
}

dependencies {
    testFixturesImplementation(platform(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES))
    testFixturesImplementation("org.elasticmq:elasticmq-server_2.13:1.6.11") {
    	because("s3mock depends on an older version of ssl-config-core")
        exclude(group = "com.typesafe", module = "ssl-config-core_2.13")
    }
    testFixturesImplementation("com.amazonaws:DynamoDBLocal:2.5.4") {
    	because("this provides two different clients, only one of which is needed by the project")
        exclude(group = "software.amazon.awssdk", module = "url-connection-client")
    }
    testFixturesImplementation(platform("io.awspring.cloud:spring-cloud-aws-dependencies:3.3.0"))
    testFixturesImplementation("io.awspring.cloud:spring-cloud-aws-starter-dynamodb")
    testFixturesImplementation("io.awspring.cloud:spring-cloud-aws-starter-sqs")
    testFixturesImplementation("io.awspring.cloud:spring-cloud-aws-starter-s3")
    testFixturesImplementation("org.awaitility:awaitility:4.3.0")
    testFixturesImplementation("com.almworks.sqlite4java:sqlite4java:1.0.392")
    testFixturesImplementation("org.junit.jupiter:junit-jupiter-api")
    testFixturesImplementation("org.junit.jupiter:junit-jupiter")
    testFixturesImplementation("org.wiremock.integrations:wiremock-spring-boot:3.9.0")
    testFixturesImplementation("io.findify:s3mock_2.13:0.2.6") {
        exclude(group = "com.amazonaws")
    }
}
