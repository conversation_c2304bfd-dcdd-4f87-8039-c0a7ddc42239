plugins {
    java
    checkstyle
    id("io.freefair.lombok")
}

group = "com.sast.store"

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

checkstyle {
    toolVersion = "10.21.3"
}

tasks.withType<JavaCompile> {
    options.encoding = "UTF-8"
}

tasks.withType<Checkstyle> {
    isIgnoreFailures = true
    isShowViolations = true
    exclude("**/gen/**")
    exclude("**/codegen/**")
}

tasks.register("integrationTest", Test::class.java) {
    group = LifecycleBasePlugin.VERIFICATION_GROUP
    description = "Run integration tests (tests that communicate with 3rd party systems instead of mocks)"
    useJUnitPlatform {
        includeTags("integrationTest")
    }
}

tasks.withType<Test>().findByName("test")?.apply {
    useJUnitPlatform {
        excludeTags("integrationTest")
    }
}

tasks.findByName("check")?.apply {
    dependsOn("integrationTest")
}

configurations {
    create("swaggerPluginDependencies")
}

dependencies {
    "swaggerPluginDependencies"("org.apache.commons:commons-lang3:3.17.0")
    "swaggerPluginDependencies"("io.swagger.core.v3:swagger-jaxrs2-jakarta:2.2.28")
    "swaggerPluginDependencies"("jakarta.ws.rs:jakarta.ws.rs-api:4.0.0")
    "swaggerPluginDependencies"("jakarta.servlet:jakarta.servlet-api:6.1.0")
}
