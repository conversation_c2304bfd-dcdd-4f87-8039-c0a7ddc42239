package com.sast.store.contractmanagement.api;

import com.sast.store.commons.tenant.api.Tenant;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;

import java.time.OffsetDateTime;
import java.time.Period;
import java.util.List;

@Builder
public record ContractDto(
    @NotNull Tenant tenant,
    @NotNull String companyId,
    @NotNull String contractId,
    @NotNull String orderNumber,
    @NotNull String productId,
    @NotNull OffsetDateTime startDate,
    OffsetDateTime projectedEndDate,
    OffsetDateTime endDate,
    @NotNull ContractType contractType,
    @NotNull ContractState contractState,
    String cancelledByUserId,
    String lastModifiedByUserId,
    @Schema(implementation = String.class) Period contractPeriod,
    @Schema(implementation = String.class) Period noticePeriod,
    List<AddonDto> addons
) {
    @Builder
    public record AddonDto(
        @NotNull String contractId,
        @NotNull String productId,
        @NotNull OffsetDateTime startDate,
        OffsetDateTime endDate,
        @NotNull ContractType contractType,
        @Schema(implementation = String.class) Period contractPeriod,
        @Schema(implementation = String.class) Period noticePeriod
    ) { }
}
