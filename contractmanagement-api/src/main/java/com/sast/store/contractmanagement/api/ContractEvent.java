package com.sast.store.contractmanagement.api;

import com.sast.store.commons.tenant.api.Tenant;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;

import java.time.OffsetDateTime;
import java.time.Period;

@Builder
public record ContractEvent(
    // TODO: Remove all of those up to the next todo. add header?
    @Deprecated @NotNull Tenant tenant,
    @Deprecated @NotNull String companyId,
    @Deprecated @NotNull String contractId,
    @Deprecated @NotNull String orderNumber,
    @Deprecated @NotNull String productId,
    @Deprecated @NotNull OffsetDateTime startDate,
    @Deprecated OffsetDateTime endDate,
    @Deprecated @NotNull ContractType contractType,
    @Deprecated @NotNull ContractState cancellationState,
    @Deprecated Period contractPeriod,
    @Deprecated Period noticePeriod,
    @Deprecated String cancelledByUserId,
    // TODO: Mandatory after next deployment
    @NotNull ContractEventHeader header,
    @NotNull ContractDto contractDto
) { }
