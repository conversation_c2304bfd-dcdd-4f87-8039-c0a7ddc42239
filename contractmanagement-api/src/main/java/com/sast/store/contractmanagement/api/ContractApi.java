package com.sast.store.contractmanagement.api;

import com.sast.store.commons.tenant.api.Tenant;
import jakarta.annotation.Nullable;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

import java.util.List;

@Path("/contracts")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public interface ContractApi {

    @GET
    @Path("/")
    List<ContractDto> getContracts(@NotNull @HeaderParam("X-Tenant") Tenant tenantId,
        @Nullable @HeaderParam("X-Company") String companyId);

    @GET
    @Path("/{contractId}")
    ContractDto getContract(@NotNull @HeaderParam("X-Tenant") Tenant tenantId,
        @Nullable @HeaderParam("X-Company") String companyId, @NotNull @PathParam("contractId") String contractId);

    @POST
    @Path("/cancellations")
    void cancelContracts(@NotNull @HeaderParam("X-Tenant") Tenant tenantId,
        @Nullable @HeaderParam("X-Company") String companyId, @NotNull @Valid CancelContractDto cancelContractDto);
}
