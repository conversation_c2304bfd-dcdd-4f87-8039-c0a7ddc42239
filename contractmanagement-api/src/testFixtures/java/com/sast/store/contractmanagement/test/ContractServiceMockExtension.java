package com.sast.store.contractmanagement.test;

import com.github.tomakehurst.wiremock.common.ClasspathFileSource;
import com.github.tomakehurst.wiremock.common.ConsoleNotifier;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.github.tomakehurst.wiremock.junit5.WireMockExtension;
import com.sast.store.contractmanagement.api.ContractDto;
import org.jeasy.random.EasyRandom;
import org.jeasy.random.EasyRandomParameters;

import java.util.function.Function;
import java.util.stream.Stream;

import static com.github.tomakehurst.wiremock.client.ResponseDefinitionBuilder.okForJson;
import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching;

public class ContractServiceMockExtension extends WireMockExtension {

    public static final int PORT = 34643;

    private static final EasyRandom RND = new EasyRandom(new EasyRandomParameters().objectFactory(new RecordFactory()));

    private static ContractServiceMockExtension instance;

    public ContractServiceMockExtension() {
        super(WireMockExtension
            .extensionOptions()
            .options(WireMockConfiguration.options()
                .fileSource(new ClasspathFileSource("contractservice"))
                .port(PORT)
                .notifier(new ConsoleNotifier(true)))
            .configureStaticDsl(true));
        instance = this;
    }

    public static ContractServiceMockExtension instance() {
        return instance;
    }

    public static ContractServiceMockExtension withDefaultResponse() {
        withGetContractResponse();
        return instance;
    }

    public static ContractServiceMockExtension withCancelContractsSuccessfulResponse() {
        instance.stubFor(post(urlPathMatching("/rest/contracts/cancellations"))
            .willReturn(aResponse()
                .withStatus(204)
                .withHeader("content-type", "application/json")));

        return instance;
    }

    @SafeVarargs
    public static ContractServiceMockExtension withGetContractResponse(
        final Function<ContractDto.ContractDtoBuilder, ContractDto.ContractDtoBuilder>... contractBuilders) {

        Stream.of(contractBuilders).forEach(contractBuilder -> {
            final ContractDto.ContractDtoBuilder builder = RND.nextObject(ContractDto.ContractDtoBuilder.class);
            contractBuilder.apply(builder);

            final ContractDto dto = builder.build();
            instance.stubFor(get(urlPathEqualTo("/rest/contracts/" + dto.contractId()))
                .willReturn(okForJson(dto)));
        });

        return instance;
    }

    public static ContractServiceMockExtension withGetContractResponse() {
        withGetContractResponse(Function.identity());
        return instance;
    }

    public static ContractServiceMockExtension withGetContractNotFoundResponse() {
        instance.stubFor(get(urlPathMatching("/rest/contracts/.*"))
            .willReturn(aResponse()
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody("{\"message\":\"Contract not found\"}")
                .withStatus(404)));

        return instance;
    }

    public static ContractServiceMockExtension withGetContractsResponse() {
        instance.stubFor(get(urlPathEqualTo("/rest/contracts/"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json")
                .withBodyFile("get-contracts-response.json")));

        return instance;
    }
}
