package com.sast.store.external.pgw.test;

import com.github.tomakehurst.wiremock.common.ClasspathFileSource;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.github.tomakehurst.wiremock.junit5.WireMockExtension;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching;

public class PgwMockExtension extends WireMockExtension {

    private static PgwMockExtension instance;

    public PgwMockExtension() {
        super(WireMockExtension
            .extensionOptions()
            .options(WireMockConfiguration.options()
                .fileSource(new ClasspathFileSource("pgw"))
                .port(40001))
            .configureStaticDsl(true));
        instance = this;
    }

    public static PgwMockExtension instance() {
        return instance;
    }

    public static PgwMockExtension withDefaultResponse() {
        withHppResponse();
        withGetPaymentResponse();
        return instance;
    }

    public static PgwMockExtension withHppResponse() {
        instance.stubFor(post(urlPathMatching("/v1/payment/.*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody(
                    """
                        {
                            "paymentId": "000000000000000000",
                            "redirectUri": "{{jsonPath request.body '$.storeRedirectTargets.successUri'}}"
                        }
                        """)
                .withTransformers("response-template")));

        return instance;
    }

    public static PgwMockExtension withGetPaymentResponse() {
        instance.stubFor(get(urlPathMatching("/v1/payment/.*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody(
                    """
                        {
                            "status": "OK",
                            "paymentId": "{{request.pathSegments.[4]}}",
                            "paymentDetails": {
                                "@type": "sepaDirectDebitDetails",
                                "mandateId": "QKGAYBQVDOJRRHROXYVLBLCVIXCSPMTCWTF",
                                "iban": "**********************",
                                "accountOwnerName": "Hansjürgen Dampf",
                                "dateOfSignature": "2024-07-22"
                            }
                        }
                        """)
                .withTransformers("response-template")));

        return instance;
    }
}
