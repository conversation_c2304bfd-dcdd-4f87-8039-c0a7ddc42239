package com.sast.store.external.pgw;

import com.sast.pgw.proxyclient.api.MerchantApi;
import com.sast.pgw.proxyclient.api.PaymentApi;
import com.sast.store.commons.jerseyclient.ClientRequestResponseLogger;
import com.sast.store.commons.jerseyclient.RestMetricsJerseyClientInterceptor;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.ws.rs.client.Client;
import jakarta.ws.rs.client.ClientRequestFilter;
import jakarta.ws.rs.client.WebTarget;
import org.glassfish.jersey.client.proxy.WebResourceFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.URI;

@Configuration
public class PgwRestServiceProducer {
    @Inject
    @Named("externalRestClient")
    private Client client;

    @Inject
    private PgwConfiguration pgwConfiguration;

    @Inject
    private ClientRequestResponseLogger clientRequestResponseLogger;

    @Inject
    private RestMetricsJerseyClientInterceptor metricsInterceptor;

    @Bean
    public MerchantApi pgwMerchantRestService() {
        return getProxy(pgwConfiguration.url(), MerchantApi.class);
    }

    @Bean
    public PaymentApi pgwPaymentRestService() {
        return getProxy(pgwConfiguration.url(), PaymentApi.class);
    }

    private <T> T getProxy(final URI url, final Class<T> proxyInterface) {
        final WebTarget target = client.target(url)
            .register(clientRequestResponseLogger)
            .property(metricsInterceptor.getName(), metricsInterceptor.load(proxyInterface, pgwConfiguration.url()))
            .register((ClientRequestFilter) requestContext -> requestContext.getHeaders()
                .putSingle("X-Api-Key", pgwConfiguration.apiKey()));
        return WebResourceFactory.newResource(proxyInterface, target);
    }
}
