plugins {
    id("bossstore.subproject-conventions")
    `java-library`
    `java-test-fixtures`
    id("org.springframework.boot") version "3.4.3" apply false
}

dependencies {
    implementation(project(":commons:jersey-client"))

    implementation(platform(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES))
    implementation("org.springframework.boot:spring-boot-starter-jersey")
    implementation("org.glassfish.jersey.ext:jersey-proxy-client")
    api("com.sast.pgw:pgw-proxy-client:20241104-c695dc52")

    testFixturesImplementation(platform(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES))
    testFixturesImplementation("org.junit.jupiter:junit-jupiter-api")
    testFixturesImplementation("org.wiremock.integrations:wiremock-spring-boot:3.9.0")
}
