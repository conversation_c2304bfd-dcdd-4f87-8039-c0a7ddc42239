openapi: 3.0.1
paths:
  /account/company:
    get:
      operationId: getOwnCompanyData
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExternalCompanyDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /account/company/users:
    get:
      operationId: getUsers
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AccountUserDto'
                uniqueItems: true
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /account/company/users/invitations:
    get:
      operationId: getUserInvitations
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/InvitationDto'
                uniqueItems: true
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /account/company/users/invitations/{invitationId}:
    delete:
      operationId: deleteUserInvitation
      parameters:
      - in: path
        name: invitationId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json: {}
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    put:
      operationId: resendUserInvitation
      parameters:
      - in: path
        name: invitationId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /account/company/users/invite:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: createUserInvitation
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAccountInvitationFormDto'
        required: true
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /account/user:
    delete:
      operationId: deleteUserSelf
      responses:
        default:
          content:
            application/json: {}
          description: default response
    get:
      operationId: getOwnUserData
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    put:
      operationId: updateOwnUserData
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserFormDto'
        required: true
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateUserFormDto'
          description: default response
  /account/users/{userName}:
    delete:
      operationId: deleteUser
      parameters:
      - in: path
        name: userName
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json: {}
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /account/users/{userName}/permissions:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    put:
      operationId: updatePermissions
      parameters:
      - in: path
        name: userName
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AccountPermissionsDto'
        required: true
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /admin/bpmd-log-details/company/{companyId}:
    get:
      operationId: getCompaniesDetailsFromScreening
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json: {}
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /admin/companies:
    get:
      operationId: getCompanies
      parameters:
      - in: query
        name: filter
        schema:
          type: string
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/BasicCompanyDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: createCompany
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminCreateCompanyDto'
        required: true
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BasicCompanyDto'
          description: default response
  /admin/companies/import:
    get:
      operationId: getImportedCompanies
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CompanyImportDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: importCompanies
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                importFile:
                  $ref: '#/components/schemas/FormDataContentDisposition'
                importType:
                  type: string
                separator:
                  type: string
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CompanyImportResultDto'
          description: default response
  /admin/companies/import/start-import:
    get:
      operationId: triggerCompanyImport
      responses:
        default:
          content:
            application/json: {}
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /admin/companies/import/{companyImportId}:
    delete:
      operationId: deleteCompanyImport
      parameters:
      - in: path
        name: companyImportId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json: {}
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /admin/companies/import/{companyImportId}/run:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: runCompanyImport
      parameters:
      - in: path
        name: companyImportId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /admin/companies/screening-duplicates:
    get:
      operationId: getCompanySubmissionsWithDuplicates
      parameters:
      - in: query
        name: status
        schema:
          type: string
          default: DUPLICATED_TAX_ID
          enum:
          - EMAIL_NOT_VERIFIED
          - DUPLICATED_TAX_ID
          - EMAIL_VERIFIED
          - AWAITING_DELETION
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ExternalCompanySubmissionDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /admin/companies/screening-duplicates/{companyId}:
    delete:
      operationId: deleteDuplicateCompanySubmission
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json: {}
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /admin/companies/screening-duplicates/{country}/{taxId}:
    get:
      operationId: getCompanySubmissionsWithDuplicatedTaxId
      parameters:
      - in: path
        name: country
        required: true
        schema:
          type: string
      - in: path
        name: taxId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  type: array
                  items:
                    $ref: '#/components/schemas/DuplicatedCompanySubmissionFormDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /admin/companies/{companyId}:
    delete:
      operationId: deleteCompany
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json: {}
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /admin/companies/{companyId}/bypass-screening:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: bypassScreening
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /admin/companies/{companyId}/distributor-history:
    get:
      operationId: getDistributorHistory
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DistributorHistoryDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /admin/companies/{companyId}/finish-screening:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: finishScreening
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /admin/companies/{companyId}/information:
    get:
      operationId: getCompanyInformation
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyInfoDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /admin/companies/{companyId}/users:
    get:
      operationId: getUsers_1
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/EcosystemUserDto'
                uniqueItems: true
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /admin/companies/{companyId}/users/invitations:
    get:
      operationId: getInvitations
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/InvitationDto'
                uniqueItems: true
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /admin/companies/{companyId}/users/invitations/{invitationId}:
    delete:
      operationId: deleteInvitation
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      - in: path
        name: invitationId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json: {}
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    put:
      operationId: resendInvitation
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      - in: path
        name: invitationId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /admin/companies/{companyId}/users/invite:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: createInvitation
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateBackofficeInvitationFormDto'
        required: true
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /admin/companies/{companyId}/users/{userName}/permissions:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    put:
      operationId: updatePermissions_1
      parameters:
      - in: path
        name: userName
        required: true
        schema:
          type: string
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EcosystemPermissionsDto'
        required: true
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /admin/customer-group/configurations:
    get:
      operationId: getCustomerGroupConfigurations
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CustomerGroupConfigurationDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: createCustomerGroupConfiguration
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminCreateCustomerGroupConfigurationDto'
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerGroupConfigurationDto'
          description: default response
  /admin/distributors:
    get:
      operationId: getDistributorInformation
      parameters:
      - in: query
        name: companyId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DistributorInfoDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /admin/distributors/{distributorId}:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    put:
      operationId: updateDistributorInformation
      parameters:
      - in: path
        name: distributorId
        required: true
        schema:
          type: string
          format: uuid
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminUpdateDistributorDto'
        required: true
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DistributorInfoDto'
          description: default response
  /admin/export/companies:
    get:
      operationId: exportCompaniesData
      responses:
        default:
          content:
            text/csv: {}
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /admin/export/imported-companies:
    get:
      operationId: exportCompaniesImportData
      responses:
        default:
          content:
            text/csv: {}
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /admin/export/logins:
    get:
      operationId: exportLoginsData
      responses:
        default:
          content:
            text/csv: {}
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /admin/privacy-policy:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: resetPrivacyPolicyForAllUsers
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /admin/privacy-policy/{isoCode}:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: resetPrivacyPolicyForCountryUsers
      parameters:
      - in: path
        name: isoCode
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /admin/signup/companies/{companyId}/verify:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: verifyCompany
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /admin/terms-and-conditions:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: resetTermsAndConditionsForAllUsers
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /admin/terms-and-conditions/{isoCode}:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: resetTermsAndConditionsForCountryUsers
      parameters:
      - in: path
        name: isoCode
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /admin/user:
    get:
      operationId: findUserByEmailOrId
      parameters:
      - in: query
        name: userEmailOrId
        schema:
          type: string
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserSearchResultDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /admin/users/{userName}:
    delete:
      operationId: deleteUser_1
      parameters:
      - in: path
        name: userName
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json: {}
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /admin/v2/companies/{companyId}:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    put:
      operationId: update
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminUpdateCombinedCompanyDto'
        required: true
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyInfoDto'
          description: default response
  /companies/{companyId}/company-email:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    patch:
      operationId: updateCompanyEmail
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCompanyEmailDto'
        required: true
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /companies/{companyId}/friendly-name:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    patch:
      operationId: updateFriendlyName
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCompanyFriendlyNameDto'
        required: true
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /companies/{companyId}/phone-number:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    patch:
      operationId: updatePhoneNumber
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCompanyPhoneNumberDto'
        required: true
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /companies/{companyId}/sub-companies:
    get:
      operationId: getAllSubCompanies_1
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SubCompanyDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: createSubCompany
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSubCompanyDto'
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /companies/{companyId}/sub-companies/search:
    get:
      operationId: searchSubCompanies
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      - description: Specify 'country' to filter sub-companies by country
        in: query
        name: country
        schema:
          type: string
      - description: Specify 'taxId' to filter sub-companies by taxId
        in: query
        name: taxId
        schema:
          type: string
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SubCompanyDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /companies/{companyId}/sub-companies/{subCompanyId}:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: linkSubCompany
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      - in: path
        name: subCompanyId
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompanyRelationDataDto'
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubCompanyDto'
          description: default response
  /companies/{companyId}/sub-companies/{subCompanyId}/invitations:
    get:
      operationId: getSubCompanyUserInvitations
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      - in: path
        name: subCompanyId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SubCompanyInvitationDto'
                uniqueItems: true
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /companies/{companyId}/sub-companies/{subCompanyId}/users:
    get:
      operationId: getSubCompanyUsers
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      - in: path
        name: subCompanyId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SubCompanyUserDto'
                uniqueItems: true
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /config/environment:
    get:
      operationId: environment
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnvironmentDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /config/version:
    get:
      operationId: version
      responses:
        default:
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  type: object
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /customer-groups:
    get:
      operationId: getCustomerGroups
      parameters:
      - description: Specify 'subcompany-creation' to show customer groups allowed
          with sub-company creation
        in: query
        name: availability
        schema:
          type: string
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /internal/companies:
    get:
      operationId: getAllCompanies
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/BasicCompanyDto'
                uniqueItems: true
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /internal/companies/awaiting-screening:
    get:
      operationId: getCompaniesAwaitingScreening
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GetCompanyResponseDto'
                uniqueItems: true
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /internal/companies/external-customer-id/{externalCustomerId}:
    get:
      operationId: getCompaniesByExternalCustomerId
      parameters:
      - in: path
        name: externalCustomerId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ExternalCompanyDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /internal/companies/move-to-in-screening:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: moveCompaniesIntoScreening
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/CompanyUUID'
              uniqueItems: true
        required: true
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /internal/companies/screening/approved:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: approveCompanyScreenings
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/ProcessScreeningResultRequestDto'
              uniqueItems: true
        required: true
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /internal/companies/screening/denied:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: denyCompanyScreenings
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/CompanyUUID'
              uniqueItems: true
        required: true
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /internal/companies/update:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    patch:
      operationId: updateCompany
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCompanyRequestDto'
        required: true
      responses:
        default:
          content:
            application/json:
              schema:
                type: boolean
          description: default response
  /internal/companies/update/block:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: blockCompany
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompanyUUID'
        required: true
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /internal/companies/update/unblock:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: unblockCompany
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompanyUUID'
        required: true
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /internal/companies/{companyId}:
    get:
      operationId: getCompanyDetails
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExternalCompanyDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /internal/companies/{companyId}/distributor-history:
    get:
      operationId: getDistributorHistory_1
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DistributorHistoryDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /internal/companies/{companyId}/information:
    get:
      operationId: getCompanyInformation_1
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyInfoDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /internal/companies/{companyId}/managers:
    get:
      operationId: getCompanyManagers
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /internal/companies/{companyId}/sub-companies:
    get:
      operationId: getAllSubCompanies
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/BasicSubCompanyDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /internal/companies/{companyId}/users:
    get:
      operationId: getUsers_2
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserDto'
                uniqueItems: true
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /internal/customer-groups:
    get:
      operationId: getAllCustomerGroups
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/InternalCustomerGroupDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /internal/ldap-user-sync:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: syncLdapUsers
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/LdapUserDto'
        required: true
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /internal/private/activation/ecosystem-manager-company:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: createEcosystemManagerCompany
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminCreateCompanyDto'
        required: true
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /internal/private/companies/{companyId}:
    delete:
      operationId: deleteEcosystemManagerCompany
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json: {}
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /internal/private/cron/trigger/remove-expired-invitations:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: triggerRemoveExpiredInvitationCron
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /internal/users/{userName}:
    get:
      operationId: getUser
      parameters:
      - in: path
        name: userName
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /public/countries/languages:
    get:
      operationId: getLanguages
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/LanguageDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /public/countries/list:
    get:
      operationId: getCountryList
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CountryWithValidationDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /public/countries/registration:
    get:
      operationId: getCountryListForRegistration
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CountryWithValidationDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /public/distributors/{country}:
    get:
      operationId: getDistributors
      parameters:
      - in: path
        name: country
        required: true
        schema:
          type: string
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/BaseDistributorDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
  /public/signup:
    get:
      operationId: getInvitationActivationDetails
      parameters:
      - in: query
        name: invitationToken
        schema:
          type: string
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvitationActivationDetailsDto'
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: signupInvitedUser
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InvitationActivationFormDto'
        required: true
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /public/signup/company:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: signupCompany
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompanySubmissionFormDto'
        required: true
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /public/signup/company/{companyId}/verify:
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
    post:
      operationId: verifyCompany_1
      parameters:
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TokenDto'
        required: true
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /realms/{realmName}/sast/companies/{companyId}/users:
    get:
      operationId: listUsers
      parameters:
      - in: path
        name: realmName
        required: true
        schema:
          type: string
      - in: path
        name: companyId
        required: true
        schema:
          type: string
      - in: query
        name: clientId
        schema:
          type: string
      responses:
        default:
          content:
            application/json: {}
          description: default response
    parameters:
    - in: header
      name: X-Tenant
      required: true
      schema:
        type: string
        enum:
        - baam
        - rexroth
components:
  schemas:
    AccountPermissionsDto:
      type: object
      properties:
        isCompanyAccountManager:
          type: boolean
        isDeveloper:
          type: boolean
        isIntegrator:
          type: boolean
        isLicensePortalManager:
          type: boolean
        isOrderDeskManager:
          type: boolean
        isPaymentManager:
          type: boolean
    AccountUserDto:
      type: object
      properties:
        activated:
          type: boolean
        communicationLanguage:
          type: string
        companyId:
          type: string
        country:
          type: string
        creationDate:
          type: string
          format: date-time
        email:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        permissions:
          $ref: '#/components/schemas/AccountPermissionsDto'
        phoneNumber:
          type: string
        userName:
          type: string
    AddressDto:
      type: object
      properties:
        city:
          type: string
          maxLength: 40
          minLength: 0
        houseNumber:
          type: string
          maxLength: 10
          minLength: 0
        postalCode:
          type: string
          maxLength: 10
          minLength: 0
        region:
          type: string
          maxLength: 32
          minLength: 0
        state:
          type: string
          maxLength: 32
          minLength: 0
        street:
          type: string
          maxLength: 60
          minLength: 0
      required:
      - city
      - houseNumber
      - postalCode
      - street
    AdminCreateCompanyDto:
      type: object
      properties:
        communicationLanguage:
          type: string
          pattern: "^[a-z]{2}$"
        companyAccountManagerEmail:
          type: string
        companyAccountManagerFirstName:
          type: string
          maxLength: 40
          minLength: 0
        companyAccountManagerLastName:
          type: string
          maxLength: 40
          minLength: 0
        companyCountry:
          type: string
          maxLength: 2
          minLength: 2
        companyEmail:
          type: string
          maxLength: 254
          minLength: 0
        companyName:
          type: string
          maxLength: 60
          minLength: 0
        companyTypes:
          type: array
          items:
            type: string
            enum:
            - INTEGRATOR
            - DEVELOPER
      required:
      - communicationLanguage
      - companyAccountManagerEmail
      - companyAccountManagerFirstName
      - companyAccountManagerLastName
      - companyEmail
      - companyName
      - companyTypes
    AdminCreateCustomerGroupConfigurationDto:
      type: object
      properties:
        customerGroup:
          type: string
        customizationId:
          type: string
          maxLength: 6
          minLength: 0
      required:
      - customerGroup
      - customizationId
    AdminUpdateCombinedCompanyDto:
      type: object
      properties:
        customerGroupConfiguration:
          $ref: '#/components/schemas/AdminUpdateCompanyCustomerGroupConfigurationDto'
        details:
          $ref: '#/components/schemas/AdminUpdateCompanyInfoDto'
        distributor:
          $ref: '#/components/schemas/AdminUpdateCompanyDistributorDto'
        licensing:
          $ref: '#/components/schemas/AdminUpdateCompanyLicensingEmailDto'
        limits:
          $ref: '#/components/schemas/AdminUpdateCompanyCreditLimitDto'
        permissions:
          $ref: '#/components/schemas/AdminUpdateCompanyPermissionsDto'
        settings:
          $ref: '#/components/schemas/AdminUpdateCompanySettingsDto'
      required:
      - customerGroupConfiguration
      - details
      - licensing
      - limits
      - permissions
      - settings
    AdminUpdateCompanyCreditLimitDto:
      type: object
      properties:
        creditLimit:
          type: number
          format: double
          exclusiveMinimum: false
          minimum: 0.00
      required:
      - creditLimit
    AdminUpdateCompanyCustomerGroupConfigurationDto:
      type: object
      properties:
        customerGroupConfigurationId:
          type: string
          format: uuid
      required:
      - customerGroupConfigurationId
    AdminUpdateCompanyDistributorDto:
      type: object
      properties:
        distributorId:
          type: string
          format: uuid
    AdminUpdateCompanyInfoDto:
      type: object
      properties:
        billingAddress:
          $ref: '#/components/schemas/AddressDto'
        bpmdId:
          type: string
          maxLength: 10
          minLength: 0
        businessAddress:
          $ref: '#/components/schemas/AddressDto'
        communicationLanguage:
          type: string
          pattern: "^[a-z]{2}$"
        companyEmail:
          type: string
          maxLength: 254
          minLength: 0
        companyName:
          type: string
          maxLength: 60
          minLength: 0
        costCenter:
          type: string
          maxLength: 160
          minLength: 0
        country:
          type: string
          maxLength: 2
          minLength: 2
        externalCustomerId:
          type: string
          maxLength: 15
          minLength: 0
        friendlyName:
          type: string
          maxLength: 160
          minLength: 0
        isImported:
          type: boolean
        isInternal:
          type: boolean
        paymentTerms:
          type: string
          maxLength: 4
          minLength: 0
          pattern: "^[A-Za-z0-9]{4}$"
        startScreening:
          type: boolean
        taxId:
          type: string
          maxLength: 20
          minLength: 0
        website:
          type: string
          maxLength: 255
          minLength: 0
      required:
      - businessAddress
      - communicationLanguage
      - companyEmail
      - companyName
      - country
      - taxId
    AdminUpdateCompanyLicensingEmailDto:
      type: object
      properties:
        licensingEmail:
          type: string
    AdminUpdateCompanyPermissionsDto:
      type: object
      properties:
        isPurchaseEnabled:
          type: boolean
        isSalesEnabled:
          type: boolean
    AdminUpdateCompanySettingsDto:
      type: object
      properties:
        isManualAppApprovalEnabled:
          type: boolean
        isOwnAppsPurchaseEnabled:
          type: boolean
        isSubCompanyManagementEnabled:
          type: boolean
    AdminUpdateDistributorDto:
      type: object
      properties:
        subsidiaryRegions:
          type: array
          items:
            type: string
          uniqueItems: true
      required:
      - subsidiaryRegions
    BaseDistributorDto:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
    BasicCompanyDto:
      type: object
      properties:
        communicationLanguage:
          type: string
        companyCountry:
          type: string
        companyEmail:
          type: string
        companyId:
          type: string
        companyName:
          type: string
          maxLength: 60
          minLength: 0
        companyStatus:
          type: string
          enum:
          - AWAITING_DATA_INPUT
          - AWAITING_SCREENING
          - IN_SCREENING
          - AWAITING_SPL_CHECK
          - PENDING_SPL_CHECK
          - AWAITING_EXTERNAL_SYNC
          - AWAITING_ACTIVATION
          - APPROVED_COMMERCIAL
          - APPROVED_NON_COMMERCIAL
          - SUSPENDED
          - BLOCKED
          - SPL_BLOCKED
          - AWAITING_DELETION
          - DELETED
        imported:
          type: boolean
        operationalStage:
          type: string
          enum:
          - VALIDATING
          - OPERATIONAL
          - RESTRICTED
        tenant:
          type: string
          enum:
          - baam
          - rexroth
      required:
      - companyName
    BasicSubCompanyDto:
      type: object
      properties:
        communicationLanguage:
          type: string
        companyCountry:
          type: string
        companyEmail:
          type: string
        companyId:
          type: string
        companyName:
          type: string
          maxLength: 60
          minLength: 0
        companyStatus:
          type: string
          enum:
          - AWAITING_DATA_INPUT
          - AWAITING_SCREENING
          - IN_SCREENING
          - AWAITING_SPL_CHECK
          - PENDING_SPL_CHECK
          - AWAITING_EXTERNAL_SYNC
          - AWAITING_ACTIVATION
          - APPROVED_COMMERCIAL
          - APPROVED_NON_COMMERCIAL
          - SUSPENDED
          - BLOCKED
          - SPL_BLOCKED
          - AWAITING_DELETION
          - DELETED
        customTag:
          type: string
        imported:
          type: boolean
        operationalStage:
          type: string
          enum:
          - VALIDATING
          - OPERATIONAL
          - RESTRICTED
        tenant:
          type: string
          enum:
          - baam
          - rexroth
      required:
      - companyName
    CompanyDto:
      type: object
      properties:
        companyCountry:
          type: string
        companyId:
          type: string
        companyName:
          type: string
          maxLength: 60
          minLength: 0
      required:
      - companyName
    CompanyImportDto:
      type: object
      properties:
        batchId:
          type: string
        billingAddressCity:
          type: string
        billingAddressHouseNumber:
          type: string
        billingAddressPostalCode:
          type: string
        billingAddressRegion:
          type: string
        billingAddressState:
          type: string
        billingAddressStreet:
          type: string
        businessAddressCity:
          type: string
        businessAddressHouseNumber:
          type: string
        businessAddressPostalCode:
          type: string
        businessAddressRegion:
          type: string
        businessAddressState:
          type: string
        businessAddressStreet:
          type: string
        communicationLanguage:
          type: string
        companyId:
          type: string
          format: uuid
        companyName:
          type: string
        companyStatus:
          type: string
          enum:
          - AWAITING_DATA_INPUT
          - AWAITING_SCREENING
          - IN_SCREENING
          - AWAITING_SPL_CHECK
          - PENDING_SPL_CHECK
          - AWAITING_EXTERNAL_SYNC
          - AWAITING_ACTIVATION
          - APPROVED_COMMERCIAL
          - APPROVED_NON_COMMERCIAL
          - SUSPENDED
          - BLOCKED
          - SPL_BLOCKED
          - AWAITING_DELETION
          - DELETED
        country:
          type: string
        creationDate:
          type: string
          format: date-time
        creditLimit:
          type: number
        customerGroup:
          type: string
        customerId:
          type: string
        customizationId:
          type: string
        direct:
          type: boolean
        distributor:
          type: boolean
        distributorId:
          type: string
        email:
          type: string
        emailToken:
          type: string
        firstName:
          type: string
        importStatus:
          type: string
          enum:
          - PENDING
          - AWAIT_MANUAL_TRIGGER
          - CREATED
          - EMAIL_EXISTS
          - INVALID_TAX_ID
          - INVALID_COUNTRY
          - INVALID_LANGUAGE
          - INVALID_CUSTOMER_GROUP
          - INVALID_CUSTOMIZATION_ID
          - INVALID_DISTRIBUTOR_ID
          - INVALID_PARENT_COMPANY_ID
          - GENERIC_ERROR
        indirect:
          type: boolean
        lastName:
          type: string
        licensingEmail:
          type: string
        parentCompanyId:
          type: string
        paymentTerms:
          type: string
        phone:
          type: string
        taxId:
          type: string
        website:
          type: string
    CompanyImportResultDto:
      type: object
      properties:
        batchId:
          type: string
        company:
          $ref: '#/components/schemas/CompanyImportUploadDto'
        messages:
          type: array
          items:
            type: object
            additionalProperties:
              type: string
        status:
          type: string
          enum:
          - IMPORTED
          - DUPLICATED_CUSTOMER_ID
          - DATA_ERROR
    CompanyImportUploadDto:
      type: object
      properties:
        billingAddressCity:
          type: string
          maxLength: 40
          minLength: 0
        billingAddressHouseNumber:
          type: string
          maxLength: 10
          minLength: 0
        billingAddressPostalCode:
          type: string
          maxLength: 10
          minLength: 0
        billingAddressRegion:
          type: string
          maxLength: 32
          minLength: 0
        billingAddressState:
          type: string
          maxLength: 32
          minLength: 0
        billingAddressStreet:
          type: string
          maxLength: 60
          minLength: 0
        businessAddressCity:
          type: string
          maxLength: 40
          minLength: 0
        businessAddressHouseNumber:
          type: string
          maxLength: 10
          minLength: 0
        businessAddressPostalCode:
          type: string
          maxLength: 10
          minLength: 0
        businessAddressRegion:
          type: string
          maxLength: 32
          minLength: 0
        businessAddressState:
          type: string
          maxLength: 32
          minLength: 0
        businessAddressStreet:
          type: string
          maxLength: 60
          minLength: 0
        communicationLanguage:
          type: string
          pattern: "^[a-zA-Z]{2}$"
        companyName:
          type: string
        country:
          type: string
          maxLength: 2
          minLength: 2
          pattern: "^[A-Z]{2}$"
        creditLimit:
          type: number
          exclusiveMinimum: false
          minimum: 0.0
        customerGroup:
          type: string
        customerId:
          type: string
        customizationId:
          type: string
        email:
          type: string
          maxLength: 254
          minLength: 0
        firstName:
          type: string
          maxLength: 40
          minLength: 0
        lastName:
          type: string
          maxLength: 40
          minLength: 0
        licensingEmail:
          type: string
          maxLength: 254
          minLength: 0
        paymentTerms:
          type: string
        phone:
          type: string
          maxLength: 20
          minLength: 0
        taxId:
          type: string
          maxLength: 20
          minLength: 0
          pattern: "^[A-Z0-9-.\\s]{1,20}$"
        website:
          type: string
          maxLength: 255
          minLength: 0
      required:
      - businessAddressCity
      - businessAddressPostalCode
      - businessAddressStreet
      - communicationLanguage
      - country
      - customerGroup
      - customerId
      - customizationId
      - email
      - firstName
      - lastName
      - licensingEmail
      - paymentTerms
      - taxId
    CompanyInfoDto:
      type: object
      properties:
        appPurchaseEmail:
          type: string
        appSalesEmail:
          type: string
        billingAddress:
          $ref: '#/components/schemas/AddressDto'
        bpmdId:
          type: string
        businessAddress:
          $ref: '#/components/schemas/AddressDto'
        communicationLanguage:
          type: string
        companyCountry:
          type: string
        companyEmail:
          type: string
        companyId:
          type: string
        companyName:
          type: string
          maxLength: 60
          minLength: 0
        companyStatus:
          type: string
          enum:
          - AWAITING_DATA_INPUT
          - AWAITING_SCREENING
          - IN_SCREENING
          - AWAITING_SPL_CHECK
          - PENDING_SPL_CHECK
          - AWAITING_EXTERNAL_SYNC
          - AWAITING_ACTIVATION
          - APPROVED_COMMERCIAL
          - APPROVED_NON_COMMERCIAL
          - SUSPENDED
          - BLOCKED
          - SPL_BLOCKED
          - AWAITING_DELETION
          - DELETED
        companyTypes:
          type: array
          items:
            type: string
            enum:
            - INTEGRATOR
            - DEVELOPER
        costCenter:
          type: string
        creditLimit:
          type: number
          format: double
        customerGroupConfigurationId:
          type: string
          format: uuid
        distributorId:
          type: string
          format: uuid
        emailToken:
          type: string
        externalCustomerId:
          type: string
        friendlyName:
          type: string
        isBankTransferEnabled:
          type: boolean
        isDirect:
          type: boolean
        isDistributor:
          type: boolean
        isImported:
          type: boolean
        isInternal:
          type: boolean
        isManaged:
          type: boolean
        isManualAppApprovalEnabled:
          type: boolean
        isOwnAppsPurchaseEnabled:
          type: boolean
        isSubCompanyManagementEnabled:
          type: boolean
        licensingEmail:
          type: string
        parentCompanies:
          type: array
          items:
            $ref: '#/components/schemas/BasicCompanyDto'
        paymentTerms:
          type: string
        subCompanies:
          type: array
          items:
            $ref: '#/components/schemas/BasicSubCompanyDto'
        taxId:
          type: string
          maxLength: 20
          minLength: 0
          pattern: "^[A-Z0-9-.\\s]{1,20}$"
        website:
          type: string
      required:
      - businessAddress
      - companyName
      - companyStatus
      - companyTypes
      - taxId
    CompanyRelationDataDto:
      type: object
      properties:
        customTag:
          type: string
    CompanySubmissionFormDto:
      type: object
      properties:
        billingAddress:
          $ref: '#/components/schemas/AddressDto'
        businessAddress:
          $ref: '#/components/schemas/AddressDto'
        communicationLanguage:
          type: string
          pattern: "^[a-z]{2}$"
        companyEmail:
          type: string
          maxLength: 254
          minLength: 0
        companyName:
          type: string
          maxLength: 60
          minLength: 0
        companyTypes:
          type: array
          items:
            type: string
            enum:
            - INTEGRATOR
            - DEVELOPER
        country:
          type: string
          maxLength: 2
          minLength: 2
          pattern: "^[A-Z]{2}$"
        distributor:
          type: string
          maxLength: 36
          minLength: 0
        email:
          type: string
          maxLength: 254
          minLength: 0
        firstName:
          type: string
          maxLength: 40
          minLength: 0
        lastName:
          type: string
          maxLength: 40
          minLength: 0
        phone:
          type: string
          maxLength: 20
          minLength: 0
          pattern: "^[0-9+]{1,20}$"
        taxId:
          type: string
          maxLength: 20
          minLength: 0
          pattern: "^[A-Z0-9-.\\s]{1,20}$"
        website:
          type: string
          maxLength: 255
          minLength: 0
      required:
      - businessAddress
      - communicationLanguage
      - companyEmail
      - companyName
      - companyTypes
      - country
      - distributor
      - email
      - firstName
      - lastName
      - taxId
    CompanyUUID:
      type: object
      properties:
        uuid:
          type: string
          format: uuid
    CountryValidationsDto:
      type: object
      properties:
        phoneCode:
          $ref: '#/components/schemas/ValidationDto'
        postalCode:
          $ref: '#/components/schemas/ValidationDto'
        region:
          $ref: '#/components/schemas/ValidationDto'
        state:
          $ref: '#/components/schemas/ValidationDto'
        taxId:
          $ref: '#/components/schemas/ValidationDto'
    CountryWithValidationDto:
      type: object
      properties:
        canBuy:
          type: boolean
        canSell:
          type: boolean
        isoCode:
          type: string
        name:
          type: string
        regions:
          type: array
          items:
            $ref: '#/components/schemas/StateDto'
        states:
          type: array
          items:
            $ref: '#/components/schemas/StateDto'
        tenantConfiguration:
          $ref: '#/components/schemas/TenantConfigurationDto'
        validation:
          $ref: '#/components/schemas/CountryValidationsDto'
    CreateAccountInvitationFormDto:
      type: object
      properties:
        email:
          type: string
        permissions:
          $ref: '#/components/schemas/AccountPermissionsDto'
    CreateBackofficeInvitationFormDto:
      type: object
      properties:
        email:
          type: string
        firstUser:
          type: boolean
        permissions:
          $ref: '#/components/schemas/AccountPermissionsDto'
    CreateSubCompanyDto:
      type: object
      properties:
        businessAddress:
          $ref: '#/components/schemas/AddressDto'
        communicationLanguage:
          type: string
          pattern: "^[a-z]{2}$"
        companyCountry:
          type: string
          maxLength: 2
          minLength: 2
        companyEmail:
          type: string
          maxLength: 254
          minLength: 0
        companyName:
          type: string
          maxLength: 60
          minLength: 0
        companyPhoneNumber:
          type: string
        customTag:
          type: string
          maxLength: 30
          minLength: 0
        customerGroup:
          type: string
        email:
          type: string
          maxLength: 254
          minLength: 0
        firstName:
          type: string
          maxLength: 40
          minLength: 0
        lastName:
          type: string
          maxLength: 40
          minLength: 0
        licensingEmail:
          type: string
          maxLength: 254
          minLength: 0
        taxId:
          type: string
          maxLength: 20
          minLength: 0
        userPhoneNumber:
          type: string
          maxLength: 20
          minLength: 0
      required:
      - businessAddress
      - communicationLanguage
      - companyEmail
      - companyName
      - customerGroup
      - email
      - firstName
      - lastName
      - licensingEmail
      - taxId
    CustomerGroupConfigurationDto:
      type: object
      properties:
        customerGroup:
          type: string
        customizationId:
          type: string
        id:
          type: string
          format: uuid
      required:
      - customerGroup
    DistributorHistoryDto:
      type: object
      properties:
        creationDate:
          type: string
          format: date-time
        distributor:
          $ref: '#/components/schemas/BaseDistributorDto'
        id:
          type: string
          format: uuid
      required:
      - creationDate
      - distributor
      - id
    DistributorInfoDto:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        subsidiaryRegions:
          type: array
          items:
            type: string
          uniqueItems: true
      required:
      - subsidiaryRegions
    DuplicatedCompanySubmissionFormDto:
      type: object
      properties:
        billingAddress:
          $ref: '#/components/schemas/AddressDto'
        businessAddress:
          $ref: '#/components/schemas/AddressDto'
        communicationLanguage:
          type: string
          pattern: "^[a-z]{2}$"
        companyEmail:
          type: string
          maxLength: 254
          minLength: 0
        companyId:
          type: string
        companyName:
          type: string
          maxLength: 60
          minLength: 0
        companyTypes:
          type: array
          items:
            type: string
            enum:
            - INTEGRATOR
            - DEVELOPER
        country:
          type: string
          maxLength: 2
          minLength: 2
          pattern: "^[A-Z]{2}$"
        distributor:
          type: string
          maxLength: 36
          minLength: 0
        email:
          type: string
          maxLength: 254
          minLength: 0
        firstName:
          type: string
          maxLength: 40
          minLength: 0
        lastName:
          type: string
          maxLength: 40
          minLength: 0
        phone:
          type: string
          maxLength: 20
          minLength: 0
          pattern: "^[0-9+]{1,20}$"
        status:
          type: string
          enum:
          - EMAIL_NOT_VERIFIED
          - DUPLICATED_TAX_ID
          - EMAIL_VERIFIED
          - AWAITING_DELETION
        taxId:
          type: string
          maxLength: 20
          minLength: 0
          pattern: "^[A-Z0-9-.\\s]{1,20}$"
        website:
          type: string
          maxLength: 255
          minLength: 0
      required:
      - businessAddress
      - communicationLanguage
      - companyEmail
      - companyName
      - companyTypes
      - country
      - distributor
      - email
      - firstName
      - lastName
      - taxId
    EcosystemPermissionsDto:
      type: object
      properties:
        isCompanyAccountManager:
          type: boolean
        isDeveloper:
          type: boolean
        isEcosystemCompanyManager:
          type: boolean
        isEcosystemCompanyViewer:
          type: boolean
        isEcosystemCountryManager:
          type: boolean
        isEcosystemCountryViewer:
          type: boolean
        isEcosystemLegalManager:
          type: boolean
        isEcosystemLicenseManager:
          type: boolean
        isIntegrator:
          type: boolean
        isLicensePortalManager:
          type: boolean
        isOrderDeskManager:
          type: boolean
        isPaymentManager:
          type: boolean
    EcosystemUserDto:
      type: object
      properties:
        activated:
          type: boolean
        communicationLanguage:
          type: string
        companyId:
          type: string
        country:
          type: string
        creationDate:
          type: string
          format: date-time
        email:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        permissions:
          $ref: '#/components/schemas/EcosystemPermissionsDto'
        phoneNumber:
          type: string
        userName:
          type: string
    EnvironmentDto:
      type: object
      properties:
        frontendTheme:
          type: string
        keycloakClient:
          type: string
        keycloakUrl:
          type: string
        tenant:
          type: string
          enum:
          - baam
          - rexroth
    ExternalCompanyDto:
      type: object
      properties:
        appPurchaseEmail:
          type: string
        appSalesEmail:
          type: string
        billingAddress:
          $ref: '#/components/schemas/AddressDto'
        bpmdId:
          type: string
        businessAddress:
          $ref: '#/components/schemas/AddressDto'
        communicationLanguage:
          type: string
        companyCountry:
          type: string
        companyEmail:
          type: string
        companyId:
          type: string
        companyName:
          type: string
          maxLength: 60
          minLength: 0
        companyStatus:
          type: string
          enum:
          - AWAITING_DATA_INPUT
          - AWAITING_SCREENING
          - IN_SCREENING
          - AWAITING_SPL_CHECK
          - PENDING_SPL_CHECK
          - AWAITING_EXTERNAL_SYNC
          - AWAITING_ACTIVATION
          - APPROVED_COMMERCIAL
          - APPROVED_NON_COMMERCIAL
          - SUSPENDED
          - BLOCKED
          - SPL_BLOCKED
          - AWAITING_DELETION
          - DELETED
        companyTypes:
          type: array
          items:
            type: string
            enum:
            - INTEGRATOR
            - DEVELOPER
        costCenter:
          type: string
        creditLimit:
          type: number
          format: double
        customerGroup:
          $ref: '#/components/schemas/InternalCustomerGroupConfigurationDto'
        distributor:
          $ref: '#/components/schemas/ExternalDistributorDto'
        externalCustomerId:
          type: string
        friendlyName:
          type: string
        imported:
          type: boolean
        isBankTransferEnabled:
          type: boolean
        isDirect:
          type: boolean
        isInternal:
          type: boolean
        isManaged:
          type: boolean
        isManualAppApprovalEnabled:
          type: boolean
        isOwnAppsPurchaseEnabled:
          type: boolean
        isSubCompanyManagementEnabled:
          type: boolean
        licensingEmail:
          type: string
        operationalStage:
          type: string
          enum:
          - VALIDATING
          - OPERATIONAL
          - RESTRICTED
        phoneNumber:
          type: string
        taxId:
          type: string
        website:
          type: string
      required:
      - bpmdId
      - businessAddress
      - companyName
      - companyStatus
      - operationalStage
    ExternalCompanySubmissionDto:
      type: object
      properties:
        companyCountry:
          type: string
        companyId:
          type: string
        companyName:
          type: string
          maxLength: 60
          minLength: 0
        companyTypes:
          type: array
          items:
            type: string
            enum:
            - INTEGRATOR
            - DEVELOPER
        taxId:
          type: string
      required:
      - companyName
    ExternalDistributorDto:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
    FormDataContentDisposition:
      type: object
      properties:
        creationDate:
          type: string
          format: date-time
        fileName:
          type: string
        modificationDate:
          type: string
          format: date-time
        name:
          type: string
        parameters:
          type: object
          additionalProperties:
            type: string
        readDate:
          type: string
          format: date-time
        size:
          type: integer
          format: int64
        type:
          type: string
    GetCompanyResponseDto:
      type: object
      properties:
        billingAddress:
          $ref: '#/components/schemas/AddressDto'
        businessAddress:
          $ref: '#/components/schemas/AddressDto'
        communicationLanguage:
          type: string
        companyId:
          type: string
        companyName:
          type: string
        companyTypes:
          type: array
          items:
            type: string
            enum:
            - INTEGRATOR
            - DEVELOPER
        country:
          type: string
        identificationNumber:
          type: string
        paymentTerms:
          type: string
        taxId:
          type: string
    InternalCustomerGroupConfigurationDto:
      type: object
      properties:
        customizationId:
          type: string
        id:
          type: string
    InternalCustomerGroupDto:
      type: object
      properties:
        customizationIds:
          type: array
          items:
            type: string
        id:
          type: string
    InvitationActivationDetailsDto:
      type: object
      properties:
        communicationLanguage:
          type: string
        companyName:
          type: string
        companyTypes:
          type: array
          items:
            type: string
            enum:
            - INTEGRATOR
            - DEVELOPER
        country:
          $ref: '#/components/schemas/CountryWithValidationDto'
        email:
          type: string
        firstName:
          type: string
        idpAllowed:
          type: boolean
        lastName:
          type: string
        legalAgreements:
          type: array
          items:
            type: string
            enum:
            - TERMS_AND_CONDITIONS
            - RESELLER_TERMS_AND_CONDITIONS
            - PRIVACY_POLICY
        loginWithEmailAllowed:
          type: boolean
    InvitationActivationFormDto:
      type: object
      properties:
        firstName:
          type: string
          maxLength: 40
          minLength: 0
        idpAllowed:
          type: boolean
        invitationToken:
          type: string
        isNewsletterSubscribe:
          type: boolean
        lastName:
          type: string
          maxLength: 40
          minLength: 0
        legalAgreements:
          type: array
          items:
            type: string
            enum:
            - TERMS_AND_CONDITIONS
            - RESELLER_TERMS_AND_CONDITIONS
            - PRIVACY_POLICY
        loginWithEmailAllowed:
          type: boolean
        passwordConfirmDto:
          type: string
        passwordDto:
          type: string
      required:
      - firstName
      - idpAllowed
      - invitationToken
      - isNewsletterSubscribe
      - lastName
      - legalAgreements
      - loginWithEmailAllowed
    InvitationDto:
      type: object
      properties:
        companyId:
          type: string
        email:
          type: string
        expirationDate:
          type: string
          format: date-time
        permissions:
          $ref: '#/components/schemas/AccountPermissionsDto'
        status:
          type: string
          enum:
          - INVITATION_SENT
          - EXPIRED
        type:
          type: string
          enum:
          - PRIMARY
          - REGULAR
          - SUBSIDIARY
        userName:
          type: string
      required:
      - email
    LanguageDto:
      type: object
      properties:
        code:
          type: string
    LdapUserDto:
      type: object
      properties:
        email:
          type: string
        equalityCriterion:
          type: string
        firstName:
          type: string
        lastName:
          type: string
    PasswordDto:
      type: object
      properties:
        password:
          type: string
          maxLength: **********
          minLength: 12
      required:
      - password
    ProcessScreeningResultRequestDto:
      type: object
      properties:
        billingAddress:
          $ref: '#/components/schemas/AddressDto'
        bpmdId:
          type: string
        businessAddress:
          $ref: '#/components/schemas/AddressDto'
        communicationLanguage:
          type: string
        companyId:
          type: string
        companyName:
          type: string
        country:
          type: string
        identificationNumber:
          type: string
        status:
          type: string
          enum:
          - APPROVED
          - DENIED
          - IN_SCREENING
        taxId:
          type: string
    StateDto:
      type: object
      properties:
        abbrev:
          type: string
        name:
          type: string
    SubCompanyDto:
      type: object
      properties:
        businessAddress:
          $ref: '#/components/schemas/AddressDto'
        communicationLanguage:
          type: string
        companyCountry:
          type: string
        companyEmail:
          type: string
        companyId:
          type: string
        companyName:
          type: string
          maxLength: 60
          minLength: 0
        companyStatus:
          type: string
          enum:
          - AWAITING_DATA_INPUT
          - AWAITING_SCREENING
          - IN_SCREENING
          - AWAITING_SPL_CHECK
          - PENDING_SPL_CHECK
          - AWAITING_EXTERNAL_SYNC
          - AWAITING_ACTIVATION
          - APPROVED_COMMERCIAL
          - APPROVED_NON_COMMERCIAL
          - SUSPENDED
          - BLOCKED
          - SPL_BLOCKED
          - AWAITING_DELETION
          - DELETED
        customTag:
          type: string
        customerGroup:
          type: string
        externalCustomerId:
          type: string
        imported:
          type: boolean
        licensingEmail:
          type: string
        operationalStage:
          type: string
          enum:
          - VALIDATING
          - OPERATIONAL
          - RESTRICTED
        phoneNumber:
          type: string
        taxId:
          type: string
        tenant:
          type: string
          enum:
          - baam
          - rexroth
      required:
      - companyName
    SubCompanyInvitationDto:
      type: object
      properties:
        email:
          type: string
        userName:
          type: string
    SubCompanyUserDto:
      type: object
      properties:
        email:
          type: string
        phoneNumber:
          type: string
    TenantConfigurationDto:
      type: object
      properties:
        allowedCrossCountriesSales:
          type: array
          items:
            type: string
          uniqueItems: true
        canRegister:
          type: boolean
        creditLimit:
          type: number
        currency:
          type: string
        defaultLanguage:
          type: string
        isoCode:
          type: string
        languages:
          type: array
          items:
            type: string
          uniqueItems: true
        tenant:
          type: string
    TokenDto:
      type: object
      properties:
        token:
          type: string
      required:
      - token
    UpdateCompanyEmailDto:
      type: object
      properties:
        companyEmail:
          type: string
          maxLength: 254
          minLength: 0
    UpdateCompanyFriendlyNameDto:
      type: object
      properties:
        friendlyName:
          type: string
          maxLength: 160
          minLength: 0
    UpdateCompanyPhoneNumberDto:
      type: object
      properties:
        phoneNumber:
          type: string
          maxLength: 20
          minLength: 0
    UpdateCompanyRequestDto:
      type: object
      properties:
        billingAddress:
          $ref: '#/components/schemas/AddressDto'
        bpmdId:
          type: string
        businessAddress:
          $ref: '#/components/schemas/AddressDto'
        communicationLanguage:
          type: string
        companyId:
          type: string
        companyName:
          type: string
        companyStatus:
          type: string
          enum:
          - AWAITING_DATA_INPUT
          - AWAITING_SCREENING
          - IN_SCREENING
          - AWAITING_SPL_CHECK
          - PENDING_SPL_CHECK
          - AWAITING_EXTERNAL_SYNC
          - AWAITING_ACTIVATION
          - APPROVED_COMMERCIAL
          - APPROVED_NON_COMMERCIAL
          - SUSPENDED
          - BLOCKED
          - SPL_BLOCKED
          - AWAITING_DELETION
          - DELETED
        identificationNumber:
          type: string
        taxId:
          type: string
    UpdateUserFormDto:
      type: object
      properties:
        communicationLanguage:
          type: string
          maxLength: 2
          minLength: 2
        firstName:
          type: string
          maxLength: 40
          minLength: 0
        lastName:
          type: string
          maxLength: 40
          minLength: 0
        phoneNumber:
          type: string
      required:
      - communicationLanguage
      - firstName
      - lastName
    UserDto:
      type: object
      properties:
        activated:
          type: boolean
        communicationLanguage:
          type: string
        companyId:
          type: string
        country:
          type: string
        creationDate:
          type: string
          format: date-time
        email:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        phoneNumber:
          type: string
        userName:
          type: string
    UserSearchResultDto:
      type: object
      properties:
        company:
          $ref: '#/components/schemas/CompanyDto'
        user:
          $ref: '#/components/schemas/EcosystemUserDto'
    UserUUID:
      type: object
      properties:
        uuid:
          type: string
          format: uuid
    ValidationDto:
      type: object
      properties:
        example:
          type: string
        pattern:
          type: string
        required:
          type: boolean
