{"subject": "COMPANY_UPDATE", "tenant": "rex<PERSON>", "companyId": "********-a2db-4228-bc67-8c2a9e8c4b6e", "companyName": "<PERSON><PERSON> Tester", "companyTypes": ["INTEGRATOR"], "distributor": null, "country": "AT", "taxId": null, "taxIdExemption": null, "website": null, "creditLimit": 5000.0, "bankTransferEnabled": true, "manualAppApprovalEnabled": true, "ownAppsPurchaseEnabled": false, "subCompanyManagementEnabled": true, "friendlyName": "<PERSON> <PERSON> <PERSON><PERSON><PERSON>", "phoneNumber": "+4369497", "costCenter": null, "internal": false, "companyStatus": "APPROVED_COMMERCIAL", "operationalStage": "OPERATIONAL", "bpmdId": null, "externalCustomerId": null, "billingAddress": {"city": "Linz", "postalCode": "4020", "street": "Bistdudeppertgassl", "houseNumber": "12", "state": "Oberösterreich", "region": "Linz-Land"}, "businessAddress": {"city": "Dienten am Niederkaiser", "postalCode": "5652", "street": "Berg", "houseNumber": "18", "state": null, "region": null}, "communicationLanguage": "en", "licensingEmail": "<EMAIL>", "customerGroup": {"customizationId": "IDW000", "id": "INDEPENDENT_WORKSHOP"}, "managed": false, "direct": true, "companyEmail": "<EMAIL>", "imported": false}