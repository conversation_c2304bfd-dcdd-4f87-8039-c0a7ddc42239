package com.sast.store.external.ump.test;

import org.springframework.util.StreamUtils;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

public enum UmpMockEvent {
    COMPANY_UPDATE("/events/company-update.json");

    private final String filename;

    UmpMockEvent(final String filename) {
        this.filename = filename;
    }

    public String payload() {
        return loadJson(filename);
    }


    private String loadJson(final String path) {
        try (final InputStream resourceInputStream = getClass().getResourceAsStream(path)) {
            if (resourceInputStream == null) {
                throw new FileNotFoundException(path);
            }
            return StreamUtils.copyToString(resourceInputStream, StandardCharsets.UTF_8);
        } catch (final IOException e) {
            throw new RuntimeException(e);
        }
    }
}
