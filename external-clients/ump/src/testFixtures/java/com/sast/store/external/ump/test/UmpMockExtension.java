package com.sast.store.external.ump.test;

import com.github.tomakehurst.wiremock.common.ClasspathFileSource;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.github.tomakehurst.wiremock.junit5.WireMockExtension;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching;

public class UmpMockExtension extends WireMockExtension {

    private static final String DEFAULT_COMPANY_ID = "1234567";
    private static UmpMockExtension instance;

    public UmpMockExtension() {
        this(40101);
    }

    public UmpMockExtension(final int port) {
        super(WireMockExtension
            .extensionOptions()
            .options(WireMockConfiguration.options()
                .fileSource(new ClasspathFileSource("ump"))
                .port(port))
            .configureStaticDsl(true));
        instance = this;
    }

    public static UmpMockExtension instance() {
        return instance;
    }

    public static UmpMockExtension withDefaultResponse() {
        withCompanyInformationResponse();
        withUsersInformationResponse();
        withCompanyDetailsResponse();
        return instance;
    }

    public static void withUsersInformationResponse() {
        instance.stubFor(get(urlPathMatching("/internal/companies/[^/]*/users"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody("""
                    [
                        {
                            "activated": true,
                            "companyId": "${companyId}",
                            "email": "<EMAIL>",
                            "firstName": "firstFirstname",
                            "isHardwarePartner": false,
                            "lastName": "firstLastName",
                            "userName": "firstUserId",
                            "communicationLanguage": "en"
                        },
                        {
                            "activated": true,
                            "companyId": "${companyId}",
                            "email": "<EMAIL>",
                            "firstName": "secondFirstname",
                            "isHardwarePartner": false,
                            "lastName": "secondLastname",
                            "userName": "secondUserId",
                            "communicationLanguage": "en"
                        },
                        {
                            "activated": true,
                            "companyId": "${companyId}",
                            "email": "<EMAIL>",
                            "firstName": "lastFirstName",
                            "isHardwarePartner": false,
                            "lastName": "lastLastName",
                            "userName": "lastUserId",
                            "communicationLanguage": "de"
                        }
                    ]"""
                    .replace("${company}", DEFAULT_COMPANY_ID))));

        instance.stubFor(get(urlPathMatching("/internal/users/.*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody("""
                        {
                            "activated": true,
                            "companyId": "${companyId}",
                            "email": "<EMAIL>",
                            "firstName": "myFirstname",
                            "isHardwarePartner": false,
                            "lastName": "myLastname",
                            "userName": "myUserName",
                            "communicationLanguage": "de",
                            "phoneNumber": "123 456"
                        }
                    """.replace("${companyId}", DEFAULT_COMPANY_ID))));
    }

    public static UmpMockExtension withCompanyInformationResponse() {
        instance.stubFor(get(urlPathMatching("/internal/companies/.*/information"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody(
                    """
                        {
                            "companyName": "UPM Tester",
                            "companyId": "52967218-7c7e-4471-8391-4962e7bbe537",
                            "companyCountry": "AT",
                            "billingAddress": {
                                "city": "Linz",
                                "postalCode": "4010",
                                "street": "Bistdudeppertgassl",
                                "houseNumber": "12",
                                "state": "Oberösterreich",
                                "region": "Linz-Land"
                            },
                            "businessAddress": {
                                "city": "Dienten am Hochkönig",
                                "postalCode": "5652",
                                "street": "Berg",
                                "houseNumber": "18",
                                "state": null,
                                "region": null
                            },
                            "website": null,
                            "taxId": "ATU73208478",
                            "companyTypes": [
                                "INTEGRATOR"
                            ],
                            "companyStatus": "APPROVED_COMMERCIAL",
                            "emailToken": "tmQGsA4lzby_cblvEs-K4UM6DZ21FUFa",
                            "creditLimit": 5000.0,
                            "friendlyName": null,
                            "appPurchaseEmail": null,
                            "appSalesEmail": null,
                            "licensingEmail": "<EMAIL>",
                            "costCenter": null,
                            "distributorId": null,
                            "bpmdId": "**********",
                            "externalCustomerId": "89003",
                            "customerGroupConfigurationId": "********-c890-11ed-8422-02bf5080765c",
                            "communicationLanguage": "de",
                            "paymentTerms": null,
                            "parentCompanies": [],
                            "subCompanies": [],
                            "isBankTransferEnabled": true,
                            "isManualAppApprovalEnabled": true,
                            "isOwnAppsPurchaseEnabled": false,
                            "isSubCompanyManagementEnabled": true,
                            "isInternal": false,
                            "isDistributor": false,
                            "isManaged": false,
                            "isDirect": true,
                            "companyEmail": "<EMAIL>"
                        }
                        """)));

        return instance;
    }

    public static UmpMockExtension withCompanyDetailsResponse() {
        instance.stubFor(get(urlPathMatching("/internal/companies/[^/]*[/]*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody(
                    """
                        {
                            "companyName": "UPM Tester",
                            "companyId": "52967218-7c7e-4471-8391-4962e7bbe537",
                            "companyTypes": [
                                "INTEGRATOR"
                            ],
                            "taxId": "ATU73208478",
                            "website": null,
                            "creditLimit": 5000.0,
                            "friendlyName": null,
                            "phoneNumber": null,
                            "appPurchaseEmail": null,
                            "appSalesEmail": null,
                            "costCenter": null,
                            "companyStatus": "APPROVED_COMMERCIAL",
                            "bpmdId": "**********",
                            "externalCustomerId": "89003",
                            "billingAddress": {
                                "city": "Linz",
                                "postalCode": "4010",
                                "street": "Bistdudeppertgassl",
                                "houseNumber": "12",
                                "state": "Oberösterreich",
                                "region": "Linz-Land"
                            },
                            "businessAddress": {
                                "city": "Dienten am Hochkönig",
                                "postalCode": "5652",
                                "street": "Berg",
                                "houseNumber": "18",
                                "state": null,
                                "region": null
                            },
                            "distributor": null,
                            "communicationLanguage": "de",
                            "licensingEmail": "<EMAIL>",
                            "customerGroup": {
                                "customizationId": "IDW000",
                                "id": "INDEPENDENT_WORKSHOP"
                            },
                            "companyCountry": "AT",
                            "isBankTransferEnabled": true,
                            "isManualAppApprovalEnabled": true,
                            "isOwnAppsPurchaseEnabled": false,
                            "isSubCompanyManagementEnabled": true,
                            "isInternal": false,
                            "isManaged": false,
                            "isDirect": true,
                            "companyEmail": "<EMAIL>"
                        }
                        """)));

        return instance;
    }
}
