package com.sast.store.external.ump.messaging;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "subject", visible = true)
@JsonSubTypes({
        @JsonSubTypes.Type(value = CompanyUpdateNotification.class, name = "COMPANY_UPDATE"),
        @JsonSubTypes.Type(value = UnhandledNotification.class, name = "COMPANY_DELETION"),
        @JsonSubTypes.Type(value = UnhandledNotification.class, name = "USER_DELETION"),
        @JsonSubTypes.Type(value = UnhandledNotification.class, name = "USER_CREATION"),
        @JsonSubTypes.Type(value = UnhandledNotification.class, name = "COMPANY_BLOCKED"),
        @JsonSubTypes.Type(value = UnhandledNotification.class, name = "COMPANY_UNBLOCKED"),
        @JsonSubTypes.Type(value = UnhandledNotification.class, name = "USER_INVITATION_CREATION"),
})
public interface UmpNotification {
    String tenant();
}
