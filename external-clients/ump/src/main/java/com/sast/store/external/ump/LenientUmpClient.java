package com.sast.store.external.ump;

import com.sast.store.gen.client.ump.api.UmpExternalCompanyDto;
import com.sast.store.gen.client.ump.api.UmpUserDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@Slf4j
@RequiredArgsConstructor
public class LenientUmpClient {
    private final UmpClient umpClient;

    public Optional<UmpExternalCompanyDto> getCompanyDetails(final String tenant, final String companyId) {
        try {
            return Optional.ofNullable(umpClient.getCompanyDetails(tenant, companyId));
        } catch (final Exception e) {
            LOG.error("Failed to query UMP for company with tenant={}, companyId={}", tenant, companyId, e);
            return Optional.empty();
        }
    }

    public Optional<UmpUserDto> getUser(final String tenant, final String userId) {
        try {
            return Optional.ofNullable(umpClient.getUser(tenant, userId));
        } catch (final Exception e) {
            LOG.warn("Failed to query UMP for user with tenant={}, userId={}", tenant, userId, e);
            return Optional.empty();
        }
    }
}
