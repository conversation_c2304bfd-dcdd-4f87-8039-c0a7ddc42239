package com.sast.store.external.ump;

import jakarta.validation.constraints.NotNull;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.net.URI;

@ConfigurationProperties(prefix = "bossstore.ump")
@Validated
public record UmpConfiguration(
    @NotNull URI url,
    String username,
    String password
) { }
