package com.sast.store.external.ump.messaging;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sast.store.gen.client.ump.api.UmpAddressDto;
import com.sast.store.gen.client.ump.api.UmpExternalCompanyDto;
import com.sast.store.gen.client.ump.api.UmpExternalDistributorDto;
import com.sast.store.gen.client.ump.api.UmpInternalCustomerGroupConfigurationDto;
import lombok.Builder;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public record CompanyUpdateNotification(
        @JsonProperty(required = true) String tenant,
        @JsonProperty(required = true) String companyId,
        @JsonProperty(required = true) String companyName,
        List<UmpExternalCompanyDto.CompanyTypesEnum> companyTypes,
        UmpExternalDistributorDto distributor,
        @JsonProperty(value = "country", required = true) String companyCountry,
        String taxId,
        String website,
        Double creditLimit,
        Boolean bankTransferEnabled,
        Boolean manualAppApprovalEnabled,
        Boolean ownAppsPurchaseEnabled,
        Boolean subCompanyManagementEnabled,
        String friendlyName,
        String phoneNumber,
        String costCenter,
        Boolean internal,
        UmpExternalCompanyDto.CompanyStatusEnum companyStatus,
        UmpExternalCompanyDto.OperationalStageEnum operationalStage,
        String bpmdId,
        String externalCustomerId,
        UmpAddressDto billingAddress,
        UmpAddressDto businessAddress,
        String communicationLanguage,
        String licensingEmail,
        UmpInternalCustomerGroupConfigurationDto customerGroup,
        Boolean managed,
        Boolean direct,
        String companyEmail,
        Boolean imported
) implements UmpNotification {
    public UmpExternalCompanyDto asExternalCompanyDto() {
        return new UmpExternalCompanyDto()
                .companyId(companyId)
                .companyName(companyName)
                .companyTypes(companyTypes)
                .distributor(distributor)
                .companyCountry(companyCountry)
                .taxId(taxId)
                .website(website)
                .creditLimit(creditLimit)
                .isBankTransferEnabled(bankTransferEnabled)
                .isManualAppApprovalEnabled(manualAppApprovalEnabled)
                .isOwnAppsPurchaseEnabled(ownAppsPurchaseEnabled)
                .isSubCompanyManagementEnabled(subCompanyManagementEnabled)
                .friendlyName(friendlyName)
                .phoneNumber(phoneNumber)
                .costCenter(costCenter)
                .isInternal(internal)
                .companyStatus(companyStatus)
                .operationalStage(operationalStage)
                .bpmdId(bpmdId)
                .externalCustomerId(externalCustomerId)
                .billingAddress(billingAddress)
                .businessAddress(businessAddress)
                .communicationLanguage(communicationLanguage)
                .licensingEmail(licensingEmail)
                .customerGroup(customerGroup)
                .isManaged(managed)
                .isDirect(direct)
                .companyEmail(companyEmail)
                .imported(imported);
    }
}