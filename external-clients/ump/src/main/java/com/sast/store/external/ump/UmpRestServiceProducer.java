package com.sast.store.external.ump;

import com.sast.store.commons.jerseyclient.ClientRequestResponseLogger;
import com.sast.store.commons.jerseyclient.RestMetricsJerseyClientInterceptor;
import com.sast.store.gen.client.ump.api.DefaultApi;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.ws.rs.client.Client;
import jakarta.ws.rs.client.WebTarget;
import org.glassfish.jersey.client.authentication.HttpAuthenticationFeature;
import org.glassfish.jersey.client.proxy.WebResourceFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class UmpRestServiceProducer {

    @Inject
    @Named("externalRestClient")
    private Client client;

    @Inject
    private UmpConfiguration configuration;

    @Inject
    private ClientRequestResponseLogger requestResponseLogger;

    @Inject
    private RestMetricsJerseyClientInterceptor metricsInterceptor;

    @Bean
    public DefaultApi getUserRestService() {
        return getProxyUmp(DefaultApi.class);
    }

    private <T> T getProxyUmp(final Class<T> proxyInterface) {
        final WebTarget webTarget = client
            .target(configuration.url())
            .register(requestResponseLogger)
            .property(metricsInterceptor.getName(), metricsInterceptor.load(proxyInterface, configuration.url()))
            .register(HttpAuthenticationFeature.basicBuilder()
                .credentials(configuration.username(), configuration.password())
                .build());
        return WebResourceFactory.newResource(proxyInterface, webTarget);
    }
}
