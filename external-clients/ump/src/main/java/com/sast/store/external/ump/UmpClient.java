package com.sast.store.external.ump;

import com.sast.store.gen.client.ump.api.DefaultApi;
import com.sast.store.gen.client.ump.api.UmpCompanyInfoDto;
import com.sast.store.gen.client.ump.api.UmpExternalCompanyDto;
import com.sast.store.gen.client.ump.api.UmpUserDto;
import jakarta.inject.Inject;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

@Component
public class UmpClient {

    @Inject
    private DefaultApi umpApi;

    @Cacheable(cacheNames = "companyDetails", sync = true)
    public UmpExternalCompanyDto getCompanyDetails(final String tenant, final String companyId) {
        return umpApi.getCompanyDetails(tenant, companyId);
    }

    @Cacheable(cacheNames = "user", sync = true)
    public UmpUserDto getUser(final String tenant, final String userName) {
        return umpApi.getUser(tenant, userName);
    }

    @Cacheable(cacheNames = "companyInformation", sync = true)
    public UmpCompanyInfoDto getCompanyInformation(final String tenant, final String companyId) {
        return umpApi.getCompanyInformation1(tenant, companyId);
    }

}
