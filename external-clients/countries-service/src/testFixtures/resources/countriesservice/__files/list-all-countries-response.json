[{"isoCode": "PL", "name": "Poland", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "PL0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["STRIPE"], "paymentMethods": ["SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{2}-[0-9]{3}$", "example": "12-123"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^PL[0-9]{10}$", "example": "*PL***********"}, "phoneCode": {"required": false, "pattern": "^\\\\+48[0-9]+$", "example": "+48"}}, "tenantConfigurations": [{"isoCode": "PL", "tenant": "{{request.query.tenant}}", "canRegister": false, "storefrontEnabled": false, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": ["PL"]}, {"isoCode": "PL", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}]}, {"isoCode": "DE", "name": "Germany", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "DE0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["PGW", "BOSCH_TRANSFER", "DPG"], "paymentMethods": ["SEPA_DIRECTDEBIT", "SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{5}$", "example": "12345"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^DE[0-9]{9}$", "example": "*DE**********"}, "phoneCode": {"required": false, "pattern": "^\\\\+49[0-9]+$", "example": "+49"}}, "tenantConfigurations": [{"isoCode": "DE", "tenant": "{{request.query.tenant}}", "canRegister": true, "storefrontEnabled": true, "currency": "EUR", "languages": ["en", "de"], "defaultLanguage": "de", "allowedCrossCountriesSales": ["DE"]}, {"isoCode": "DE", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}]}, {"isoCode": "AT", "name": "Austria", "activeInStore": true, "canSell": true, "canBuy": true, "blockedForDualUseApps": false, "inEU": true, "taxCategory": "DE0", "blockedCountriesCommercial": [], "blockedCountriesTrial": [], "paymentProviders": ["PGW", "BOSCH_TRANSFER", "DPG"], "paymentMethods": ["SEPA_DIRECTDEBIT", "SEPA", "CREDIT_CARD"], "validation": {"postalCode": {"required": true, "pattern": "^[0-9]{5}$", "example": "12345"}, "state": null, "region": null, "taxId": {"required": true, "pattern": "^DE[0-9]{9}$", "example": "*DE**********"}, "phoneCode": {"required": false, "pattern": "^\\\\+49[0-9]+$", "example": "+49"}}, "tenantConfigurations": [{"isoCode": "AT", "tenant": "{{request.query.tenant}}", "canRegister": true, "storefrontEnabled": true, "currency": "EUR", "languages": ["en", "de"], "defaultLanguage": "de", "allowedCrossCountriesSales": ["DE"]}, {"isoCode": "AT", "tenant": "AZENA", "canRegister": true, "storefrontEnabled": true, "currency": "EUR", "languages": ["en"], "defaultLanguage": "en", "allowedCrossCountriesSales": []}]}]