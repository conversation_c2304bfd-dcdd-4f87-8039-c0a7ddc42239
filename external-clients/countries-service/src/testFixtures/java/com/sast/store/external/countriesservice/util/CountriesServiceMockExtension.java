package com.sast.store.external.countriesservice.util;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.common.ClasspathFileSource;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.github.tomakehurst.wiremock.junit5.WireMockExtension;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.matching;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching;

public class CountriesServiceMockExtension extends WireMockExtension {

    private static CountriesServiceMockExtension instance;

    public CountriesServiceMockExtension() {
        super(WireMockExtension
            .extensionOptions()
            .options(WireMockConfiguration.options()
                .fileSource(new ClasspathFileSource("countriesservice"))
                .port(40002))
            .configureStaticDsl(true));
        instance = this;
    }

    public CountriesServiceMockExtension(final int port) {
        super(WireMockExtension
            .extensionOptions()
            .options(WireMockConfiguration.options()
                .fileSource(new ClasspathFileSource("countriesservice"))
                .port(port))
            .configureStaticDsl(true));
        instance = this;
    }

    public static CountriesServiceMockExtension get() {
        return instance;
    }

    public static CountriesServiceMockExtension withDefaultResponse() {
        withGetCountryResponse();
        withListAllCountriesResponse();
        return instance;
    }

    public static CountriesServiceMockExtension withGetCountryResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/rest/private/v3/countries/.*")).withQueryParam("tenant", matching(".*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("get-country-response.json")));
        return instance;
    }

    public static CountriesServiceMockExtension withListAllCountriesFullResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/rest/private/v3/countries"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("list-all-countries-full-response.json")));
        return instance;
    }

    public static CountriesServiceMockExtension withListAllCountriesResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/rest/private/v3/countries"))
            .withQueryParam("tenant", matching(".*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("list-all-countries-response.json")
                .withTransformers("response-template")));
        return instance;
    }
}
