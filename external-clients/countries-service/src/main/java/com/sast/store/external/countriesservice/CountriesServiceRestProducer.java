package com.sast.store.external.countriesservice;

import com.sast.store.commons.jerseyclient.ClientRequestResponseLogger;
import com.sast.store.commons.jerseyclient.RestMetricsJerseyClientInterceptor;
import com.sast.store.external.countriesservice.api.CountriesServiceApi;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.ws.rs.client.Client;
import jakarta.ws.rs.client.WebTarget;
import org.glassfish.jersey.client.proxy.WebResourceFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.URI;

@Configuration
public class CountriesServiceRestProducer {

    @Inject
    @Named("externalRestClient")
    private Client client;
    @Inject
    private CountriesServiceConfiguration config;
    @Inject
    private ClientRequestResponseLogger clientRequestResponseLogger;
    @Inject
    private RestMetricsJerseyClientInterceptor metricsInterceptor;

    @Bean
    public CountriesServiceApi countriesServiceApi() {
        return getProxy(config.url(), CountriesServiceApi.class);
    }

    private <T> T getProxy(final URI url, final Class<T> proxyInterface) {
        final WebTarget target = client.target(url)
            .register(clientRequestResponseLogger)
            .property(metricsInterceptor.getName(), metricsInterceptor.load(proxyInterface, config.url()));
        return WebResourceFactory.newResource(proxyInterface, target);
    }

}
