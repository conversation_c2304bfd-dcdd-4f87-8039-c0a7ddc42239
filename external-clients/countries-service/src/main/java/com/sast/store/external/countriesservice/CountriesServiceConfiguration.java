package com.sast.store.external.countriesservice;

import jakarta.validation.constraints.NotNull;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.net.URI;

@ConfigurationProperties(prefix = "bossstore.countriesservice")
@Validated
public record CountriesServiceConfiguration(
    @NotNull URI url
) { }
