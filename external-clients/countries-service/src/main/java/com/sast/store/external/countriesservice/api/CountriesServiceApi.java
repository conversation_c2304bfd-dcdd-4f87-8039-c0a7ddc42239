package com.sast.store.external.countriesservice.api;

import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;

import java.util.Collection;

@Consumes({ "application/json" })
@Produces({ "application/json" })
public interface CountriesServiceApi {
    enum Tenant {
        AZENA, AA, REXROTH
    }

    @GET
    @Path("/rest/private/v3/countries")
    Collection<CountryDto> listAllCountries(@QueryParam("tenant") Tenant tenant);

    @GET
    @Path("/rest/private/v3/countries/{countryCode}")
    CountryDto getCountry(@PathParam("countryCode") String countryCode, @QueryParam("tenant") Tenant tenant);
}
