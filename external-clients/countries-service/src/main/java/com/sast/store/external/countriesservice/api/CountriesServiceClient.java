package com.sast.store.external.countriesservice.api;

import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.Collection;

@Component
public class CountriesServiceClient {

    private final CountriesServiceApi countriesServiceApi;

    public CountriesServiceClient(final CountriesServiceApi countriesServiceApi) {
        this.countriesServiceApi = countriesServiceApi;
    }

    @Cacheable(cacheNames = "listAllCountries", sync = true)
    public Collection<CountryDto> listAllCountries(final Tenant tenant) {
        return countriesServiceApi.listAllCountries(toTenant(tenant));
    }

    @Cacheable(cacheNames = "getCountry", sync = true)
    public CountryDto getCountry(final Tenant tenant, final String countryCode) {
        return countriesServiceApi.getCountry(countryCode, toTenant(tenant));

    }

    public static com.sast.store.external.countriesservice.api.CountriesServiceApi.Tenant toTenant(final Tenant tenant) {
        return switch (tenant) {
            case baam -> CountriesServiceApi.Tenant.AA;
            case azena -> CountriesServiceApi.Tenant.AZENA;
            case rexroth -> CountriesServiceApi.Tenant.REXROTH;
        };
    }
}
