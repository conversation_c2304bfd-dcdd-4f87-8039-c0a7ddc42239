package com.sast.store.external.countriesservice.api;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import jakarta.validation.constraints.NotBlank;

import java.math.BigDecimal;
import java.util.List;

@JsonDeserialize(builder = CountryDto.Builder.class)
public final class CountryDto {
    @NotBlank
    private final String isoCode;
    @NotBlank
    private final String name;
    private final Boolean activeInStore;
    private final Boolean canSell;
    private final Boolean canBuy;
    private final Boolean blockedForDualUseApps;
    private final Boolean inEU;
    private final String taxCategory;
    private final List<String> blockedCountriesCommercial;
    private final List<String> blockedCountriesTrial;
    private final List<String> paymentProviders;
    private final List<String> paymentMethods;
    private final List<TenantConfigurationDto> tenantConfigurations;

    private CountryDto(final Builder builder) {
        this.isoCode = builder.isoCode;
        this.name = builder.name;
        this.activeInStore = builder.activeInStore;
        this.canSell = builder.canSell;
        this.canBuy = builder.canBuy;
        this.blockedForDualUseApps = builder.blockedForDualUseApps;
        this.inEU = builder.inEU;
        this.taxCategory = builder.taxCategory;
        this.blockedCountriesCommercial = builder.blockedCountriesCommercial;
        this.blockedCountriesTrial = builder.blockedCountriesTrial;
        this.paymentProviders = builder.paymentProviders;
        this.paymentMethods = builder.paymentMethods;
        this.tenantConfigurations = builder.tenantConfigurations;
    }

    public String getIsoCode() {
        return isoCode;
    }

    public String getName() {
        return name;
    }

    public Boolean isActiveInStore() {
        return activeInStore;
    }

    public Boolean isCanSell() {
        return canSell;
    }

    public Boolean isCanBuy() {
        return canBuy;
    }

    public Boolean isBlockedForDualUseApps() {
        return blockedForDualUseApps;
    }

    public Boolean isInEU() {
        return inEU;
    }

    public String getTaxCategory() {
        return taxCategory;
    }

    public List<String> getBlockedCountriesCommercial() {
        return blockedCountriesCommercial;
    }

    public List<String> getBlockedCountriesTrial() {
        return blockedCountriesTrial;
    }

    public List<String> getPaymentProviders() {
        return paymentProviders;
    }

    public List<String> getPaymentMethods() {
        return paymentMethods;
    }

    public List<TenantConfigurationDto> getTenantConfigurations() {
        return tenantConfigurations;
    }

    @Override
    public String toString() {
        return "CountryDto [isoCode=" + isoCode + ", name=" + name + ", activeInStore=" + activeInStore + ", canSell=" + canSell
            + ", canBuy=" + canBuy + ", blockedForDualUseApps=" + blockedForDualUseApps + ", inEU=" + inEU + ", taxCategory="
            + taxCategory + ", blockedCountriesCommercial=" + blockedCountriesCommercial + ", blockedCountriesTrial="
            + blockedCountriesTrial + ", paymentProviders=" + paymentProviders + ", paymentMethods=" + paymentMethods
            + ", tenantConfigurations=" + tenantConfigurations + "]";
    }

    @JsonPOJOBuilder(withPrefix = "")
    public static class Builder {
        private String isoCode;
        private String name;
        private Boolean activeInStore;
        private Boolean canSell;
        private Boolean canBuy;
        private Boolean blockedForDualUseApps;
        private Boolean inEU;
        private String taxCategory;
        private List<String> blockedCountriesCommercial;
        private List<String> blockedCountriesTrial;
        private List<String> paymentProviders;
        private List<String> paymentMethods;
        private List<TenantConfigurationDto> tenantConfigurations;

        public Builder isoCode(final String isoCode) {
            this.isoCode = isoCode;
            return this;
        }

        public Builder name(final String name) {
            this.name = name;
            return this;
        }

        public Builder activeInStore(final boolean activeInStore) {
            this.activeInStore = activeInStore;
            return this;
        }

        public Builder canSell(final boolean canSell) {
            this.canSell = canSell;
            return this;
        }

        public Builder canBuy(final boolean canBuy) {
            this.canBuy = canBuy;
            return this;
        }

        public Builder blockedForDualUseApps(final boolean blockedForDualUseApps) {
            this.blockedForDualUseApps = blockedForDualUseApps;
            return this;
        }

        public Builder inEU(final boolean inEU) {
            this.inEU = inEU;
            return this;
        }

        public Builder taxCategory(final String taxCategory) {
            this.taxCategory = taxCategory;
            return this;
        }

        public Builder blockedCountriesCommercial(final List<String> blockedCountriesCommercial) {
            this.blockedCountriesCommercial = blockedCountriesCommercial;
            return this;
        }

        public Builder blockedCountriesTrial(final List<String> blockedCountriesTrial) {
            this.blockedCountriesTrial = blockedCountriesTrial;
            return this;
        }

        public Builder paymentProviders(final List<String> paymentProviders) {
            this.paymentProviders = paymentProviders;
            return this;
        }

        public Builder paymentMethods(final List<String> paymentMethods) {
            this.paymentMethods = paymentMethods;
            return this;
        }

        public Builder tenantConfigurations(final List<TenantConfigurationDto> tenantConfigurations) {
            this.tenantConfigurations = tenantConfigurations;
            return this;
        }

        public CountryDto build() {
            return new CountryDto(this);
        }
    }

    @JsonDeserialize(builder = TenantConfigurationDto.Builder.class)
    public static final class TenantConfigurationDto {
        private final String isoCode;
        private final String tenant;
        private final Boolean canRegister;
        private final Boolean storefrontEnabled;
        private final String currency;
        private final List<String> languages;
        private final String defaultLanguage;
        private final List<String> allowedCrossCountriesSales;
        private final BigDecimal creditLimit;
        private final Boolean importedCompaniesCanBuy;

        private TenantConfigurationDto(final Builder builder) {
            this.isoCode = builder.isoCode;
            this.tenant = builder.tenant;
            this.canRegister = builder.canRegister;
            this.storefrontEnabled = builder.storefrontEnabled;
            this.currency = builder.currency;
            this.languages = builder.languages;
            this.defaultLanguage = builder.defaultLanguage;
            this.allowedCrossCountriesSales = builder.allowedCrossCountriesSales;
            this.creditLimit = builder.creditLimit;
            this.importedCompaniesCanBuy = builder.importedCompaniesCanBuy;
        }

        public String getIsoCode() {
            return isoCode;
        }

        public String getTenant() {
            return tenant;
        }

        public Boolean isCanRegister() {
            return canRegister;
        }

        public Boolean isStorefrontEnabled() {
            return storefrontEnabled;
        }

        public String getCurrency() {
            return currency;
        }

        public List<String> getLanguages() {
            return languages;
        }

        public String getDefaultLanguage() {
            return defaultLanguage;
        }

        public List<String> getAllowedCrossCountriesSales() {
            return allowedCrossCountriesSales;
        }

        public BigDecimal getCreditLimit() {
            return creditLimit;
        }

        public Boolean isImportedCompaniesCanBuy() {
            return importedCompaniesCanBuy;
        }

        @Override
        public String toString() {
            return "TenantConfigurationDto [isoCode=" + isoCode + ", tenant=" + tenant + ", canRegister=" + canRegister
                + ", storefrontEnabled=" + storefrontEnabled + ", currency=" + currency + ", languages=" + languages
                + ", defaultLanguage=" + defaultLanguage + ", allowedCrossCountriesSales=" + allowedCrossCountriesSales
                + ", creditLimit=" + creditLimit + ", importedCompaniesCanBuy=" + importedCompaniesCanBuy + "]";
        }

        @JsonPOJOBuilder(withPrefix = "")
        public static class Builder {
            private String isoCode;
            private String tenant;
            private Boolean canRegister;
            private Boolean storefrontEnabled;
            private String currency;
            private List<String> languages;
            private String defaultLanguage;
            private List<String> allowedCrossCountriesSales;
            private BigDecimal creditLimit;
            private Boolean importedCompaniesCanBuy;

            public Builder isoCode(final String isoCode) {
                this.isoCode = isoCode;
                return this;
            }

            public Builder tenant(final String tenant) {
                this.tenant = tenant;
                return this;
            }

            public Builder canRegister(final Boolean canRegister) {
                this.canRegister = canRegister;
                return this;
            }

            public Builder storefrontEnabled(final Boolean storefrontEnabled) {
                this.storefrontEnabled = storefrontEnabled;
                return this;
            }

            public Builder currency(final String currency) {
                this.currency = currency;
                return this;
            }

            public Builder languages(final List<String> languages) {
                this.languages = languages;
                return this;
            }

            public Builder defaultLanguage(final String defaultLanguage) {
                this.defaultLanguage = defaultLanguage;
                return this;
            }

            public Builder allowedCrossCountriesSales(final List<String> allowedCrossCountriesSales) {
                this.allowedCrossCountriesSales = allowedCrossCountriesSales;
                return this;
            }

            public Builder creditLimit(final BigDecimal creditLimit) {
                this.creditLimit = creditLimit;
                return this;
            }

            public Builder importedCompaniesCanBuy(final Boolean importedCompaniesCanBuy) {
                this.importedCompaniesCanBuy = importedCompaniesCanBuy;
                return this;
            }

            public TenantConfigurationDto build() {
                return new TenantConfigurationDto(this);
            }
        }
    }
}
