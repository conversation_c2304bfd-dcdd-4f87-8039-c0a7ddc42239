package com.sast.store.external.mattermost;

import jakarta.validation.constraints.NotNull;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.net.URI;

@ConfigurationProperties(prefix = "bossstore.mattermost")
@Validated
public record MattermostConfiguration(
    @NotNull URI url,
    @NotNull String apikey,
    @NotNull String channelId
) { }
