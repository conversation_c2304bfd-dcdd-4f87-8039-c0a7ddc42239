package com.sast.store.external.mattermost;

import com.sast.store.gen.client.mattermost.api.MattermostCreatePostRequest;
import com.sast.store.gen.client.mattermost.api.PostsApi;
import jakarta.inject.Inject;
import org.springframework.stereotype.Component;

@Component
public class MattermostClient {

    @Inject
    private PostsApi postsApi;

    @Inject
    private MattermostConfiguration configuration;

    public void send(final String text) {
        final MattermostCreatePostRequest message = new MattermostCreatePostRequest()
            .channelId(configuration.channelId())
            .message(text);

        postsApi.createPost(message, false);
    }

}
