package com.sast.store.external.mattermost;

import com.sast.store.commons.jerseyclient.ClientRequestResponseLogger;
import com.sast.store.commons.jerseyclient.RestMetricsJerseyClientInterceptor;
import com.sast.store.gen.client.mattermost.api.PostsApi;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.ws.rs.client.Client;
import jakarta.ws.rs.client.ClientRequestFilter;
import jakarta.ws.rs.client.WebTarget;
import jakarta.ws.rs.core.HttpHeaders;
import org.glassfish.jersey.client.proxy.WebResourceFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MattermostRestServiceProducer {

    @Inject
    @Named("externalRestClient")
    private Client client;

    @Inject
    private MattermostConfiguration configuration;

    @Inject
    private ClientRequestResponseLogger requestResponseLogger;

    @Inject
    private RestMetricsJerseyClientInterceptor metricsInterceptor;

    @Bean
    public PostsApi getMattermostRestService() {
        return getProxy(PostsApi.class);
    }

    private <T> T getProxy(final Class<T> proxyInterface) {
        final WebTarget webTarget = client
            .target(configuration.url())
            .register(requestResponseLogger)
            .register((ClientRequestFilter) requestContext -> requestContext
                .getHeaders().putSingle(HttpHeaders.AUTHORIZATION, "Bearer " + configuration.apikey()))
            .property(metricsInterceptor.getName(), metricsInterceptor.load(proxyInterface, configuration.url()));
        return WebResourceFactory.newResource(proxyInterface, webTarget);
    }
}
