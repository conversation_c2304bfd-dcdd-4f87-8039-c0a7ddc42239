openapi: 3.0.0
paths:
  /hooks/{apiToken}:
    post:
      tags:
        - webhook
      operationId: send
      parameters:
        - in: path
          name: apiToken
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                channel:
                  type: string
                username:
                  type: string
                icon_url:
                  type: string
                icon_emoji:
                  type: string
                text:
                  type: string
        required: true
      responses:
        default:
          content:
            text/plain:
              schema:
                type: string
          description: default response
  /api/v4/posts:
    post:
      tags:
        - posts
      summary: Create a post
      description: |
        Create a new post in a channel. To create the post as a comment on another post, provide `root_id`.
        ##### Permissions
        Must have `create_post` permission for the channel the post is being created in.
      operationId: CreatePost
      parameters:
        - name: set_online
          in: query
          description: Whether to set the user status as online or not.
          schema:
            type: boolean
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - channel_id
                - message
              properties:
                channel_id:
                  type: string
                  description: The channel ID to post in
                message:
                  type: string
                  description: The message contents, can be formatted with Markdown
                root_id:
                  type: string
                  description: The post ID to comment on
                file_ids:
                  type: array
                  description: A list of file IDs to associate with the post. Note that posts are limited to 5 files maximum. Please use additional posts for more files.
                  items:
                    type: string
                props:
                  description: A general JSON property bag to attach to the post
                  type: object
                metadata:
                  description: A JSON object to add post metadata, e.g the post's priority
                  type: object
                  properties:
                    priority:
                      type: object
                      description: An object containing the post's priority properties
                      properties:
                        priority:
                          type: string
                          description: The priority label of the post, could empty, important, or urgent
                        requested_ack:
                          type: boolean
                          description: Set to true to request for acknowledgements
        description: Post object to create
        required: true
      responses:
        default:
          description: Post creation successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                  create_at:
                    description: The time in milliseconds a post was created
                    type: integer
                    format: int64
                  update_at:
                    description: The time in milliseconds a post was last updated
                    type: integer
                    format: int64
                  delete_at:
                    description: The time in milliseconds a post was deleted
                    type: integer
                    format: int64
                  edit_at:
                    type: integer
                    format: int64
                  user_id:
                    type: string
                  channel_id:
                    type: string
                  root_id:
                    type: string
                  original_id:
                    type: string
                  message:
                    type: string
                  type:
                    type: string
                  props:
                    type: object
                  hashtag:
                    type: string
                  file_ids:
                    type: array
                    items:
                      type: string
                  pending_post_id:
                    type: string
