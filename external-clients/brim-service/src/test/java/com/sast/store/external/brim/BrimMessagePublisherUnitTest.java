package com.sast.store.external.brim;

import com.sast.store.brimtegration.apimodel.common.ApiVersion;
import com.sast.store.brimtegration.apimodel.events.egress.EgressEvent;
import com.sast.store.brimtegration.apimodel.events.egress.contract.EgressContractEventData;
import com.sast.store.brimtegration.apimodel.events.egress.order.EgressOrderEventData;
import com.sast.store.brimtegration.apimodel.events.egress.product.EgressProductEventData;
import io.awspring.cloud.sns.core.SnsTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;

import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BrimMessagePublisherUnitTest {
    private static final String RESOLVED_APP_NAME = "excellentApplication";
    private static final String REXROTH = "rexroth";
    private static final String UNKNOWN_TENANT = "unknownTenant";

    private static final String ORDER_EVENTS_TOPIC = "bossstore-order-events.fifo";
    private static final String CONTRACT_EVENTS_TOPIC = "bossstore-contract-events.fifo";
    private static final String PRODUCT_EVENTS_TOPIC = "bossstore-product-events.fifo";

    @Mock
    private SnsTemplate snsTemplate;

    @Mock
    private ApplicationContext applicationContext;


    private BrimMessagePublisher brimMessagePublisher;

    @Captor
    private ArgumentCaptor<EgressEvent<?>> eventCaptor;

    @Captor
    private ArgumentCaptor<Map<String, Object>> snsHeaderCaptor;

    @BeforeEach
    public void setUp() {
        when(applicationContext.getBeansWithAnnotation(eq(SpringBootApplication.class)))
                .thenReturn(Map.of(RESOLVED_APP_NAME, new Object()));

        brimMessagePublisher = new BrimMessagePublisher(snsTemplate, applicationContext);
    }

    @Test
    public void testPublishContractEvent() {
        final EgressContractEventData givenEventData = () -> "";

        brimMessagePublisher.publishPlatformContractEvent(REXROTH, givenEventData);

        verify(snsTemplate, times(1))
                .convertAndSend(eq(CONTRACT_EVENTS_TOPIC), eventCaptor.capture(), snsHeaderCaptor.capture(), any());
        checkEvent(givenEventData);
    }

    @Test
    public void testPublishContractEventWithUnknownTenantThrows() {
        final EgressContractEventData givenEventData = () -> "";

        assertThatThrownBy(() -> brimMessagePublisher.publishPlatformContractEvent(UNKNOWN_TENANT, givenEventData))
                .isInstanceOf(IllegalArgumentException.class);

        verifyNoInteractions(snsTemplate);
    }

    @Test
    public void testPublishProductEvent() {
        final EgressProductEventData givenEventData = () -> "";

        brimMessagePublisher.publishPlatformProductEvent(REXROTH, givenEventData);

        verify(snsTemplate, times(1))
                .convertAndSend(eq(PRODUCT_EVENTS_TOPIC), eventCaptor.capture(), snsHeaderCaptor.capture(), any());
        checkEvent(givenEventData);
    }

    @Test
    public void testPublishProductEventWithUnknownTenantThrows() {
        final EgressProductEventData givenEventData = () -> "";

        assertThatThrownBy(() -> brimMessagePublisher.publishPlatformProductEvent(UNKNOWN_TENANT, givenEventData))
                .isInstanceOf(IllegalArgumentException.class);

        verifyNoInteractions(snsTemplate);
    }

    @Test
    public void testPublishOrderEvent() {
        final EgressOrderEventData givenEventData = () -> "";

        brimMessagePublisher.publishPlatformOrderEvent(REXROTH, givenEventData);

        verify(snsTemplate, times(1))
                .convertAndSend(eq(ORDER_EVENTS_TOPIC), eventCaptor.capture(), snsHeaderCaptor.capture(), any());
        checkEvent(givenEventData);
    }

    @Test
    public void testPublishOrderEventWithUnknownTenantThrows() {
        final EgressOrderEventData givenEventData = () -> "";

        assertThatThrownBy(() -> brimMessagePublisher.publishPlatformOrderEvent(UNKNOWN_TENANT, givenEventData))
                .isInstanceOf(IllegalArgumentException.class);

        verifyNoInteractions(snsTemplate);
    }

    private void checkEvent(final Object expectedPayload) {
        final EgressEvent<?> capturedEvent = eventCaptor.getValue();
        assertThat(capturedEvent.data())
                .isEqualTo(expectedPayload);
        assertThat(capturedEvent.header().id().senderName())
                .isEqualTo(RESOLVED_APP_NAME);
        assertThat(capturedEvent.header().version())
                .isEqualTo(ApiVersion.V1);
        assertThat(snsHeaderCaptor.getValue())
                .containsEntry("message-deduplication-id", capturedEvent.header().id().eventId())
                .containsEntry("message-group-id", RESOLVED_APP_NAME);
    }

}