package com.sast.store.external.brim;

import com.sast.store.brimtegration.apimodel.common.ApiVersion;
import com.sast.store.brimtegration.apimodel.common.Tenant;
import com.sast.store.brimtegration.apimodel.events.EventHeader;
import com.sast.store.brimtegration.apimodel.events.EventIdentity;
import com.sast.store.brimtegration.apimodel.events.egress.EgressEvent;
import com.sast.store.brimtegration.apimodel.events.egress.contract.EgressContractEvent;
import com.sast.store.brimtegration.apimodel.events.egress.contract.EgressContractEventData;
import com.sast.store.brimtegration.apimodel.events.egress.order.EgressOrderEvent;
import com.sast.store.brimtegration.apimodel.events.egress.order.EgressOrderEventData;
import com.sast.store.brimtegration.apimodel.events.egress.product.EgressProductEvent;
import com.sast.store.brimtegration.apimodel.events.egress.product.EgressProductEventData;
import io.awspring.cloud.sns.core.SnsNotification;
import io.awspring.cloud.sns.core.SnsTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.Map;
import java.util.UUID;

@Component
public class BrimMessagePublisher {
    private static final Logger LOG = LoggerFactory.getLogger(BrimMessagePublisher.class);

    private static final String ORDER_EVENTS_TOPIC = "bossstore-order-events.fifo";
    private static final String CONTRACT_EVENTS_TOPIC = "bossstore-contract-events.fifo";
    private static final String PRODUCT_EVENTS_TOPIC = "bossstore-product-events.fifo";

    private final SnsTemplate snsTemplate;
    private final String senderName;

    public BrimMessagePublisher(final SnsTemplate snsTemplate, final ApplicationContext applicationContext) {
        this.snsTemplate = snsTemplate;

        final Map<String, Object> springBootApplicationBean = applicationContext.getBeansWithAnnotation(SpringBootApplication.class);
        final String springBootApplicationName = springBootApplicationBean.keySet().iterator().next();
        LOG.info("resolved spring boot application name: {}", springBootApplicationName);
        senderName = springBootApplicationName;
    }

    public void publishPlatformContractEvent(final String tenant, final EgressContractEventData egressContractEventData) {
        final SnsNotification<? extends EgressEvent<?>> snsNotification = createSnsNotification(EgressContractEvent.builder()
            .header(generateHeader(Tenant.forJsonValue(tenant)))
            .data(egressContractEventData)
            .build());
        sendAndLog(snsNotification, CONTRACT_EVENTS_TOPIC);
    }

    public void publishPlatformOrderEvent(final String tenant, final EgressOrderEventData egressOrderEventData) {
        final SnsNotification<? extends EgressEvent<?>> snsNotification = createSnsNotification(EgressOrderEvent.builder()
            .header(generateHeader(Tenant.forJsonValue(tenant)))
            .data(egressOrderEventData)
            .build());
        sendAndLog(snsNotification, ORDER_EVENTS_TOPIC);
    }

    public void publishPlatformProductEvent(final String tenant, final EgressProductEventData egressProductEventData) {
        final SnsNotification<? extends EgressEvent<?>> snsNotification = createSnsNotification(EgressProductEvent.builder()
                .header(generateHeader(Tenant.forJsonValue(tenant)))
                .data(egressProductEventData)
                .build());
        sendAndLog(snsNotification, PRODUCT_EVENTS_TOPIC);
    }

    private void sendAndLog(final SnsNotification<? extends EgressEvent<?>> notification, final String topicName) {
        snsTemplate.convertAndSend(topicName, notification.getPayload(), notification.getHeaders(), message -> {
            LOG.info("Publishing to SNS topic {} message: {}", topicName, message);
            return message;
        });
    }

    private SnsNotification<? extends EgressEvent<?>> createSnsNotification(final EgressEvent<?> event) {
        return SnsNotification.builder(event)
            .deduplicationId(event.header().id().eventId())
            .groupId(event.header().id().senderName())
            .build();
    }

    private EventHeader generateHeader(final Tenant tenant) {
        return EventHeader.builder()
            .version(ApiVersion.V1)
            .id(EventIdentity.builder()
                .eventId(UUID.randomUUID().toString())
                .senderName(senderName)
                .build())
            .tenant(tenant)
            .timestamp(ZonedDateTime.now())
            .build();
    }
}
