plugins {
    id("bossstore.subproject-conventions")
    `java-library`
    id("org.springframework.boot") version "3.4.3" apply false
}

dependencies {
    implementation(project(":external-clients:ump"))
    implementation(project(":commons:aws"))

    implementation(platform(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES))
    implementation(platform("io.awspring.cloud:spring-cloud-aws-dependencies:3.3.0"))
    implementation("org.springframework:spring-context")
    implementation("io.awspring.cloud:spring-cloud-aws-starter-sqs")
    implementation("com.sast.email:email-service-api:20240808-3a7f3cbe")
    implementation("jakarta.validation:jakarta.validation-api")
    implementation("org.apache.commons:commons-collections4:4.4")

    testImplementation("org.junit.jupiter:junit-jupiter-api")
    testImplementation("org.junit.jupiter:junit-jupiter")
    testImplementation("org.assertj:assertj-core")
    testImplementation("org.mockito:mockito-core")
    testImplementation("org.mockito:mockito-junit-jupiter")

    testRuntimeOnly("ch.qos.logback:logback-classic")

    annotationProcessor("org.projectlombok:lombok:1.18.36")
    compileOnly("org.projectlombok:lombok")
}
