package com.sast.store.external.email;

import com.sast.store.external.email.data.EmailTenant;
import com.sast.store.external.email.data.TemplatedEmail;
import com.sast.store.external.email.data.UmpCompanyEmail;
import com.sast.store.external.email.data.UmpUserEmail;
import com.sast.store.external.ump.UmpClient;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@Slf4j
@RequiredArgsConstructor
public class EmailLocaleResolver {
    private static final String DEFAULT_EMAIL_LOCALE = "en";

    private final UmpClient umpClient;

    public String resolve(@NonNull final TemplatedEmail templatedEmail) {
        if (templatedEmail.localeOverride() != null) {
            return templatedEmail.localeOverride().toLanguageTag();
        }
        final Optional<String> primaryRecipientLanguage = switch(templatedEmail.to()) {
            case final UmpUserEmail umpUserEmail -> getUmpUserLanguage(templatedEmail.tenant(), umpUserEmail);
            case final UmpCompanyEmail umpCompanyEmail -> getUmpCompanyLanguage(templatedEmail.tenant(), umpCompanyEmail);
            default -> Optional.empty();
        };

        return primaryRecipientLanguage.orElse(DEFAULT_EMAIL_LOCALE);
    }

    private Optional<String> getUmpUserLanguage(@NonNull final EmailTenant tenant,
                                                @NonNull final UmpUserEmail umpUserEmail) {
        try {
            return Optional.ofNullable(umpClient
                    .getUser(tenant.name(), umpUserEmail.userId())
                    .getCommunicationLanguage());
        } catch (final Exception e) {
            LOG.error("Cannot resolve user language for tenant={}, uid={}: {}",
                    tenant, umpUserEmail.userId(), e.getMessage(), e);
            return Optional.empty();
        }
    }

    private Optional<String> getUmpCompanyLanguage(final EmailTenant tenant, final UmpCompanyEmail companyEmail) {
        try {
            return Optional.ofNullable(umpClient
                    .getCompanyDetails(tenant.name(), companyEmail.companyId())
                    .getCommunicationLanguage());
        } catch (final Exception e) {
            LOG.error("Cannot resolve company language for tenant={}, companyId={}: {}",
                    tenant, companyEmail.companyId(), e.getMessage(), e);
            return Optional.empty();
        }
    }
}
