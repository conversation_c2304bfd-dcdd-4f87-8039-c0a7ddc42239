package com.sast.store.external.email.data;

import lombok.NonNull;

public interface EmailRecipient {
    static UmpUserEmail forUmpUserId(@NonNull final String umpUserId) {
        return new UmpUserEmail(umpUserId);
    }

    static UmpCompanyEmail forUmpCompanyId(@NonNull final String umpCompanyId) {
        return new UmpCompanyEmail(umpCompanyId);
    }

    static EmailAddress forEmailAddress(@NonNull final String emailAddress) {
        return new EmailAddress(emailAddress);
    }
}
