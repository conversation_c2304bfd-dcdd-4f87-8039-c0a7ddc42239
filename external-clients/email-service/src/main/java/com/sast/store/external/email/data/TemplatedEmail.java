package com.sast.store.external.email.data;

import lombok.Builder;
import lombok.NonNull;

import java.util.List;
import java.util.Locale;
import java.util.Set;

/**
 * @param to primary recipient of the mail. is also used to determine the email locale
 * @param cc secondary recipients in CC
 * @param bcc secondary recipients in BCC
 * @param localeOverride overrides automatic determination of the email locale with the given value
 * @param tenant tenant for which this email is sent
 * @param templateData template type and properties required for the template
 * @param attachments list of attachments. email-service must be able to read the given S3 objects
 */
@Builder
public record TemplatedEmail(
        @NonNull EmailRecipient to,
        Set<EmailRecipient> cc,
        Set<EmailRecipient> bcc,
        @NonNull EmailTenant tenant,
        @NonNull EmailTemplateData templateData,
        Locale localeOverride,
        List<S3Attachment> attachments
) {
}
