package com.sast.store.external.email;

import com.sast.store.external.email.data.EmailName;
import com.sast.store.external.email.data.EmailTenant;
import com.sast.store.external.email.data.TemplatedEmail;
import com.sast.store.external.email.data.UmpUserEmail;
import com.sast.store.external.ump.UmpClient;
import com.sast.store.gen.client.ump.api.UmpUserDto;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@Slf4j
@RequiredArgsConstructor
public class EmailNameResolver {
    private final UmpClient umpClient;

    public Optional<EmailName> resolve(@NonNull final TemplatedEmail templatedEmail) {
        if (templatedEmail.to() instanceof final UmpUserEmail umpUserEmail) {
            return getUmpUser(templatedEmail.tenant(), umpUserEmail)
                    .map(umpUserDto -> EmailName.builder()
                            .firstName(umpUserDto.getFirstName())
                            .lastName(umpUserDto.getLastName())
                            .build());
        }
        return Optional.empty();
    }

    private Optional<UmpUserDto> getUmpUser(@NonNull final EmailTenant tenant,
                                                @NonNull final UmpUserEmail umpUserEmail) {
        try {
            return Optional.ofNullable(umpClient
                    .getUser(tenant.name(), umpUserEmail.userId()));
        } catch (final Exception e) {
            LOG.error("Cannot retrieve ump user for tenant={}, uid={}: {}",
                    tenant, umpUserEmail.userId(), e.getMessage(), e);
            return Optional.empty();
        }
    }
}
