package com.sast.store.external.email;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

@ConfigurationProperties(prefix = "bossstore.emails")
@Validated
public record EmailConfiguration(
    @NotEmpty String queueName,
    // TODO: BOSS-686 Remove when mailjet project for rexroth is set up
    @NotBlank String fromAddress
) { }
