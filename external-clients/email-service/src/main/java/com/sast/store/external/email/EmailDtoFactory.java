package com.sast.store.external.email;

import com.sast.email.client.dto.EmailDto;
import com.sast.email.client.dto.TenantDto;
import com.sast.store.external.email.data.EmailTemplateData;
import com.sast.store.external.email.data.S3Attachment;
import com.sast.store.external.email.data.TemplatedEmail;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Component
@RequiredArgsConstructor
public class EmailDtoFactory {
    private final EmailAddressResolver emailAddressResolver;
    private final EmailLocaleResolver emailLocaleResolver;
    private final EmailConfiguration emailConfiguration;
    private final EmailNameResolver emailNameResolver;

    public EmailDto createEmailDto(@NonNull final TemplatedEmail templatedEmail) {
        return new EmailDto()
                .from(emailConfiguration.fromAddress())
                .to(List.of(emailAddressResolver
                        .resolve(templatedEmail.tenant(), templatedEmail.to())
                        .orElseThrow(() -> new IllegalStateException("Cannot resolve primary recipient %s"
                                .formatted(templatedEmail.to())))
                ))
                .cc(emailAddressResolver.resolve(templatedEmail.tenant(), templatedEmail.cc()))
                .bcc(emailAddressResolver.resolve(templatedEmail.tenant(), templatedEmail.bcc()))
                .locale(emailLocaleResolver.resolve(templatedEmail))
                .tenant(TenantDto.fromValue(templatedEmail.tenant().name()))
                .messageId(UUID.randomUUID().toString())
                .properties(toPropMap(templatedEmail))
                .templateName(templatedEmail.templateData().getTemplateName(templatedEmail.tenant()))
                .attachments(toAttachmentUris(templatedEmail.attachments()));
    }

    private Map<String, Object> toPropMap(final TemplatedEmail templatedEmail) {
        final EmailTemplateData templateData = templatedEmail.templateData();
        final Map<String, Object> propMap = new HashMap<>();

        emailNameResolver.resolve(templatedEmail)
                .ifPresent(emailName -> {
                    propMap.put("firstName", emailName.firstName());
                    propMap.put("lastName", emailName.lastName());
                });

        Arrays.stream(templateData.getClass().getDeclaredFields()).forEach(field -> {
            field.setAccessible(true);
            try {
                if (field.get(templateData) != null) {
                    propMap.put(field.getName(), field.get(templateData));
                }
            } catch (final IllegalAccessException e) {
                throw new IllegalStateException("Cannot convert template data.", e);
            }
        });
        return propMap;
    }

    private List<String> toAttachmentUris(final List<S3Attachment> attachments) {
        return CollectionUtils.emptyIfNull(attachments).stream()
                .map(S3Attachment::getS3Uri)
                .toList();
    }
}
