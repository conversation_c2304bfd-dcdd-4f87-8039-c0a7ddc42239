package com.sast.store.external.email;

import com.sast.email.client.dto.EmailDto;
import com.sast.store.external.email.data.TemplatedEmail;
import io.awspring.cloud.sqs.operations.SqsTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class EmailServiceClient {
    private static final Logger LOG = LoggerFactory.getLogger(EmailServiceClient.class);

    private final EmailConfiguration emailConfiguration;
    private final SqsTemplate queue;
    private final EmailDtoFactory emailDtoFactory;

    public EmailServiceClient(final EmailConfiguration emailConfiguration,
                              final SqsTemplate queue,
                              final EmailDtoFactory emailDtoFactory) {
        this.emailConfiguration = emailConfiguration;
        this.queue = queue;
        this.emailDtoFactory = emailDtoFactory;
    }

    public void sendIgnoringFailures(final TemplatedEmail templatedEmail) {
        try {
            send(templatedEmail);
        } catch (final Exception e) {
            LOG.error("error while sending email [{}] to queue: {}",
                    templatedEmail, emailConfiguration.queueName(), e);
        }
    }

    public void send(final TemplatedEmail templatedEmail) {
        LOG.debug("sending email with these parameters: {}", templatedEmail);
        final EmailDto emailDto = emailDtoFactory.createEmailDto(templatedEmail);
        LOG.info("sending email: {}", emailDto);
        queue.send(emailConfiguration.queueName(), emailDto);
    }
}
