package com.sast.store.external.email;

import com.sast.store.external.email.data.EmailAddress;
import com.sast.store.external.email.data.EmailRecipient;
import com.sast.store.external.email.data.EmailTenant;
import com.sast.store.external.email.data.UmpCompanyEmail;
import com.sast.store.external.email.data.UmpUserEmail;
import com.sast.store.external.ump.UmpClient;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Component
@Slf4j
@RequiredArgsConstructor
public class EmailAddressResolver {
    private final UmpClient umpClient;

    public List<String> resolve(@NonNull final EmailTenant tenant, final Set<EmailRecipient> recipients) {
        if (recipients == null) {
            return null;
        }

        return recipients.stream()
                .map(recipient -> this.resolve(tenant, recipient))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .toList();
    }

    public Optional<String> resolve(@NonNull final EmailTenant tenant, final EmailRecipient recipient) {
        return switch(recipient) {
            case final UmpUserEmail umpUserEmail -> getUmpUserEmail(tenant, umpUserEmail);
            case final UmpCompanyEmail umpCompanyEmail -> getUmpCompanyEmail(tenant, umpCompanyEmail);
            case final EmailAddress emailAddress -> Optional.ofNullable(emailAddress.address());
            case null, default -> {
                LOG.warn("Unhandled recipient type {}", recipient);
                yield Optional.empty();
            }
        };
    }

    private Optional<String> getUmpUserEmail(final EmailTenant tenant, final UmpUserEmail umpUserEmail) {
        try {
            return Optional.ofNullable(umpClient
                    .getUser(tenant.name(), umpUserEmail.userId())
                    .getEmail());
        } catch (final Exception e) {
            LOG.error("Cannot resolve user email address for tenant={}, uid={}: {}",
                    tenant, umpUserEmail.userId(), e.getMessage(), e);
            return Optional.empty();
        }
    }

    private Optional<String> getUmpCompanyEmail(final EmailTenant tenant, final UmpCompanyEmail umpCompanyEmail) {
        try {
            return Optional.ofNullable(umpClient
                    .getCompanyDetails(tenant.name(), umpCompanyEmail.companyId())
                    .getCompanyEmail());
        } catch (final Exception e) {
            LOG.error("Cannot resolve company email address for tenant={}, companyId={}: {}",
                    tenant, umpCompanyEmail.companyId(), e.getMessage(), e);
            return Optional.empty();
        }
    }
}
