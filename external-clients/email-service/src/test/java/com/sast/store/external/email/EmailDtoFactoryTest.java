package com.sast.store.external.email;

import com.sast.email.client.dto.EmailDto;
import com.sast.email.client.dto.TenantDto;
import com.sast.store.external.email.data.EmailAddress;
import com.sast.store.external.email.data.EmailName;
import com.sast.store.external.email.data.EmailRecipient;
import com.sast.store.external.email.data.EmailTemplateData;
import com.sast.store.external.email.data.EmailTenant;
import com.sast.store.external.email.data.TemplatedEmail;
import com.sast.store.external.email.data.UmpCompanyEmail;
import com.sast.store.external.email.data.UmpUserEmail;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
class EmailDtoFactoryTest {
    private static final String TEMPLATE_NAME = "template/dummy_template";
    private static final UmpUserEmail RECIPIENT_USER = EmailRecipient.forUmpUserId("aUser");
    private static final UmpCompanyEmail RECIPIENT_COMPANY = EmailRecipient.forUmpCompanyId("aCompany");
    private static final EmailAddress RECIPIENT_EMAILADDRESS = EmailRecipient.forEmailAddress("aBareEmail");

    private static final String BARE_EMAIL = "<EMAIL>";
    private static final String USER_EMAIL_ADDRESS = "<EMAIL>";
    private static final String COMPANY_EMAIL_ADDRESS = "<EMAIL>";

    private static final String SENDER_ADDRESS = "<EMAIL>";

    private static final String EMAIL_LOCALE = "tlh";

    @Mock
    private EmailAddressResolver emailAddressResolver;

    @Mock
    private EmailLocaleResolver emailLocaleResolver;

    @Mock
    private EmailNameResolver emailNameResolver;

    private EmailDtoFactory emailDtoFactory;

    @BeforeEach
    public void setUp() {
        final EmailConfiguration emailConfiguration = new EmailConfiguration("Herbert", SENDER_ADDRESS);

        emailDtoFactory = new EmailDtoFactory(emailAddressResolver, emailLocaleResolver, emailConfiguration, emailNameResolver);
    }

    @Test
    void givenFullTemplatedEmail_expectedDtoIsCreated() {
        final Object propValue = new Object();
        final TemplatedEmail givenEmail = TemplatedEmail.builder()
                .to(RECIPIENT_USER)
                .cc(Set.of(RECIPIENT_COMPANY))
                .bcc(Set.of(RECIPIENT_EMAILADDRESS))
                .tenant(EmailTenant.rexroth)
                .templateData(new DummyTemplate(propValue, null, null))
                .build();

        when(emailAddressResolver.resolve(EmailTenant.rexroth, RECIPIENT_USER))
                .thenReturn(Optional.of(USER_EMAIL_ADDRESS));
        when(emailAddressResolver.resolve(EmailTenant.rexroth, Set.of(RECIPIENT_COMPANY)))
                .thenReturn(List.of(COMPANY_EMAIL_ADDRESS));
        when(emailAddressResolver.resolve(EmailTenant.rexroth, Set.of(RECIPIENT_EMAILADDRESS)))
                .thenReturn(List.of(BARE_EMAIL));
        when(emailLocaleResolver.resolve(givenEmail))
                .thenReturn(EMAIL_LOCALE);
        when(emailNameResolver.resolve(givenEmail))
                .thenReturn(Optional.of(EmailName.builder().firstName("Heinz").lastName("Tester").build()));

        final EmailDto actualEmailDto = emailDtoFactory.createEmailDto(givenEmail);

        assertThat(actualEmailDto).isNotNull();
        assertThat(actualEmailDto.getTo()).containsExactly(USER_EMAIL_ADDRESS);
        assertThat(actualEmailDto.getCc()).containsExactly(COMPANY_EMAIL_ADDRESS);
        assertThat(actualEmailDto.getBcc()).containsExactly(BARE_EMAIL);
        assertThat(actualEmailDto.getLocale()).isEqualTo(EMAIL_LOCALE);
        assertThat(actualEmailDto.getTenant()).isEqualTo(TenantDto.REXROTH);
        assertThat(actualEmailDto.getMessageId()).isNotBlank();
        assertThat(actualEmailDto.getTemplateName()).isEqualTo(TEMPLATE_NAME);
        assertThat(actualEmailDto.getProperties()).isEqualTo(Map.of(
                "aProperty", propValue,
                "firstName", "Heinz",
                "lastName", "Tester"
        ));
    }

    @Test
    void givenFullTemplatedEmail_nameCanBeOverridden() {
        final Object propValue = new Object();
        final TemplatedEmail givenEmail = TemplatedEmail.builder()
                .to(RECIPIENT_USER)
                .tenant(EmailTenant.rexroth)
                .templateData(new DummyTemplate(propValue, "Herbert", "Testhuber"))
                .build();

        when(emailAddressResolver.resolve(EmailTenant.rexroth, RECIPIENT_USER))
                .thenReturn(Optional.of(USER_EMAIL_ADDRESS));
        when(emailLocaleResolver.resolve(givenEmail))
                .thenReturn(EMAIL_LOCALE);
        when(emailNameResolver.resolve(givenEmail))
                .thenReturn(Optional.of(EmailName.builder().firstName("Heinz").lastName("Tester").build()));

        final EmailDto actualEmailDto = emailDtoFactory.createEmailDto(givenEmail);

        assertThat(actualEmailDto).isNotNull();
        assertThat(actualEmailDto.getTo()).containsExactly(USER_EMAIL_ADDRESS);
        assertThat(actualEmailDto.getLocale()).isEqualTo(EMAIL_LOCALE);
        assertThat(actualEmailDto.getTenant()).isEqualTo(TenantDto.REXROTH);
        assertThat(actualEmailDto.getMessageId()).isNotBlank();
        assertThat(actualEmailDto.getTemplateName()).isEqualTo(TEMPLATE_NAME);
        assertThat(actualEmailDto.getProperties()).isEqualTo(Map.of(
                "aProperty", propValue,
                "firstName", "Herbert",
                "lastName", "Testhuber"
        ));
    }

    @Test
    void givenFullTemplatedEmail_nameIsNotSetIfUserCannotBeResolved() {
        final Object propValue = new Object();
        final TemplatedEmail givenEmail = TemplatedEmail.builder()
                .to(RECIPIENT_USER)
                .tenant(EmailTenant.rexroth)
                .templateData(new DummyTemplate(propValue, null, null))
                .build();

        when(emailAddressResolver.resolve(EmailTenant.rexroth, RECIPIENT_USER))
                .thenReturn(Optional.of(USER_EMAIL_ADDRESS));
        when(emailLocaleResolver.resolve(givenEmail))
                .thenReturn(EMAIL_LOCALE);
        when(emailNameResolver.resolve(givenEmail))
                .thenReturn(Optional.empty());

        final EmailDto actualEmailDto = emailDtoFactory.createEmailDto(givenEmail);

        assertThat(actualEmailDto).isNotNull();
        assertThat(actualEmailDto.getTo()).containsExactly(USER_EMAIL_ADDRESS);
        assertThat(actualEmailDto.getLocale()).isEqualTo(EMAIL_LOCALE);
        assertThat(actualEmailDto.getTenant()).isEqualTo(TenantDto.REXROTH);
        assertThat(actualEmailDto.getMessageId()).isNotBlank();
        assertThat(actualEmailDto.getTemplateName()).isEqualTo(TEMPLATE_NAME);
        assertThat(actualEmailDto.getProperties()).isEqualTo(Map.of(
                "aProperty", propValue
        ));
    }

    @Test
    void givenFullTemplatedEmail_primaryRecipientUnresolvable_exceptionIsThrown() {
        final Object propValue = new Object();
        final TemplatedEmail givenEmail = TemplatedEmail.builder()
                .to(RECIPIENT_USER)
                .tenant(EmailTenant.rexroth)
                .templateData(new DummyTemplate(propValue, null, null))
                .build();

        when(emailAddressResolver.resolve(EmailTenant.rexroth, RECIPIENT_USER))
                .thenReturn(Optional.empty());

        assertThatThrownBy(() -> emailDtoFactory.createEmailDto(givenEmail))
                .isInstanceOf(IllegalStateException.class);
    }

    private record DummyTemplate(
            Object aProperty,
            String firstName,
            String lastName
    ) implements EmailTemplateData {
        @Override
        public String getTemplateName(final EmailTenant emailTenant) {
            return TEMPLATE_NAME;
        }
    }

}
