package com.sast.store.external.email;

import com.sast.store.external.email.data.EmailRecipient;
import com.sast.store.external.email.data.EmailTenant;
import com.sast.store.external.ump.UmpClient;
import com.sast.store.gen.client.ump.api.UmpExternalCompanyDto;
import com.sast.store.gen.client.ump.api.UmpUserDto;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class EmailAddressResolverUnitTest {
    private static final String UID = UUID.randomUUID().toString();
    private static final String COMPANY_ID = UUID.randomUUID().toString();

    private static final String BARE_EMAIL = "<EMAIL>";
    private static final String USER_EMAIL_ADDRESS = "<EMAIL>";
    private static final String COMPANY_EMAIL_ADDRESS = "<EMAIL>";

    @Mock
    private UmpClient umpClient;

    @InjectMocks
    private EmailAddressResolver resolver;

    @Test
    void givenUmpUserEmail_umpQuerySucceeds_emailOfUmpUserIsReturned() {
        when(umpClient.getUser(EmailTenant.baam.name(), UID))
                .thenReturn(new UmpUserDto().email(USER_EMAIL_ADDRESS));

        final Optional<String> actualAddress = resolver
                .resolve(EmailTenant.baam, EmailRecipient.forUmpUserId(UID));

        assertThat(actualAddress)
                .isNotEmpty()
                .contains(USER_EMAIL_ADDRESS);
    }

    @Test
    void givenUmpUserEmail_umpQueryFails_emptyOptionalIsReturned() {
        when(umpClient.getUser(EmailTenant.baam.name(), UID))
                .thenThrow(new RuntimeException("I made a boo boo"));

        final Optional<String> actualAddress = resolver
                .resolve(EmailTenant.baam, EmailRecipient.forUmpUserId(UID));

        assertThat(actualAddress).isEmpty();
    }

    @Test
    void givenUmpCompanyEmail_umpQuerySucceeds_emailOfUmpCompanyIsReturned() {
        when(umpClient.getCompanyDetails(EmailTenant.baam.name(), COMPANY_ID))
                .thenReturn(new UmpExternalCompanyDto().companyEmail(COMPANY_EMAIL_ADDRESS));

        final Optional<String> actualAddress = resolver
                .resolve(EmailTenant.baam, EmailRecipient.forUmpCompanyId(COMPANY_ID));

        assertThat(actualAddress)
                .isNotEmpty()
                .contains(COMPANY_EMAIL_ADDRESS);
    }

    @Test
    void givenUmpCompanyEmail_umpQueryFails_emptyOptionalIsReturned() {
        when(umpClient.getCompanyDetails(EmailTenant.baam.name(), COMPANY_ID))
                .thenThrow(new RuntimeException("I made a boo boo"));

        final Optional<String> actualAddress = resolver
                .resolve(EmailTenant.baam, EmailRecipient.forUmpCompanyId(COMPANY_ID));

        assertThat(actualAddress).isEmpty();
    }

    @Test
    void givenEmailAddress_containedAddressIsReturned() {
        final Optional<String> actualAddress = resolver
                .resolve(EmailTenant.baam, EmailRecipient.forEmailAddress(BARE_EMAIL));

        assertThat(actualAddress)
                .isNotEmpty()
                .contains(BARE_EMAIL);
    }

    @Test
    void givenUnknownRecipientType_emptyOptionalIsReturned() {
        final Optional<String> actualAddress = resolver.resolve(EmailTenant.rexroth, new EmailRecipient() { });

        assertThat(actualAddress).isEmpty();
    }

    @Test
    void givenNullRecipient_emptyOptionalIsReturned() {
        final Optional<String> actualAddress = resolver.resolve(EmailTenant.rexroth, (EmailRecipient) null);

        assertThat(actualAddress).isEmpty();
    }

    @Test
    void givenRecipientCollection_allRecipientsAreResolved() {
        when(umpClient.getUser(EmailTenant.baam.name(), UID))
                .thenReturn(new UmpUserDto().email(USER_EMAIL_ADDRESS));
        when(umpClient.getCompanyDetails(EmailTenant.baam.name(), COMPANY_ID))
                .thenReturn(new UmpExternalCompanyDto().companyEmail(COMPANY_EMAIL_ADDRESS));

        final List<String> actualAddresses = resolver
                .resolve(EmailTenant.baam, Set.of(
                        EmailRecipient.forUmpUserId(UID),
                        EmailRecipient.forUmpCompanyId(COMPANY_ID),
                        EmailRecipient.forEmailAddress(BARE_EMAIL)));

        assertThat(actualAddresses).isNotEmpty()
                .containsExactlyInAnyOrder(USER_EMAIL_ADDRESS, COMPANY_EMAIL_ADDRESS, BARE_EMAIL);
    }

    @Test
    void givenRecipientCollection_failedResolutionsAreFiltered() {
        when(umpClient.getUser(EmailTenant.baam.name(), UID))
                .thenThrow(new RuntimeException("I made a boo boo"));
        when(umpClient.getCompanyDetails(EmailTenant.baam.name(), COMPANY_ID))
                .thenThrow(new RuntimeException("I made a boo boo"));

        final List<String> actualAddresses = resolver
                .resolve(EmailTenant.baam, Set.of(
                        new EmailRecipient() { },
                        EmailRecipient.forUmpUserId(UID),
                        EmailRecipient.forUmpCompanyId(COMPANY_ID),
                        EmailRecipient.forEmailAddress(BARE_EMAIL)));

        assertThat(actualAddresses).isNotEmpty()
                .containsExactlyInAnyOrder(BARE_EMAIL);
    }
}