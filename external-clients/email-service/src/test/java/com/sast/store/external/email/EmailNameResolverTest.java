package com.sast.store.external.email;

import com.sast.store.external.email.data.EmailName;
import com.sast.store.external.email.data.EmailRecipient;
import com.sast.store.external.email.data.EmailTenant;
import com.sast.store.external.email.data.TemplatedEmail;
import com.sast.store.external.ump.UmpClient;
import com.sast.store.gen.client.ump.api.UmpUserDto;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class EmailNameResolverTest {
    @Mock
    private UmpClient umpClient;

    @InjectMocks
    private EmailNameResolver emailNameResolver;

    @Test
    public void testResolveCompanyRecipient() {
        final Optional<EmailName> actualName = emailNameResolver
                .resolve(getTemplatedEmail(EmailRecipient.forUmpCompanyId("foo")));

        assertThat(actualName).isEmpty();
    }


    @Test
    public void testResolveEmailAddressRecipient() {
        final Optional<EmailName> actualName = emailNameResolver
                .resolve(getTemplatedEmail(EmailRecipient.forEmailAddress("foo")));

        assertThat(actualName).isEmpty();
    }

    @Test
    public void testResolveUserRecipient() {
        when(umpClient.getUser("rexroth", "foo"))
                .thenReturn(new UmpUserDto().firstName("Herbert").lastName("Tester"));

        final Optional<EmailName> actualName = emailNameResolver
                .resolve(getTemplatedEmail(EmailRecipient.forUmpUserId("foo")));

        assertThat(actualName)
                .isNotEmpty().contains(EmailName.builder().firstName("Herbert").lastName("Tester").build());
    }

    @Test
    public void testResolveUserRecipientWithUmpError() {
        when(umpClient.getUser("rexroth", "foo"))
                .thenThrow(new RuntimeException());

        final Optional<EmailName> actualName = emailNameResolver
                .resolve(getTemplatedEmail(EmailRecipient.forUmpUserId("foo")));

        assertThat(actualName).isEmpty();
    }

    private TemplatedEmail getTemplatedEmail(final EmailRecipient primaryRecipient) {
        return TemplatedEmail.builder()
                .to(primaryRecipient)
                .tenant(EmailTenant.rexroth)
                .templateData(emailTenant -> "")
                .build();
    }

}