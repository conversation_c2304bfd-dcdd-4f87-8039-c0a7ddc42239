package com.sast.store.external.email;

import com.sast.store.external.email.data.EmailAddress;
import com.sast.store.external.email.data.EmailRecipient;
import com.sast.store.external.email.data.EmailTenant;
import com.sast.store.external.email.data.TemplatedEmail;
import com.sast.store.external.email.data.UmpCompanyEmail;
import com.sast.store.external.email.data.UmpUserEmail;
import com.sast.store.external.ump.UmpClient;
import com.sast.store.gen.client.ump.api.UmpExternalCompanyDto;
import com.sast.store.gen.client.ump.api.UmpUserDto;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Locale;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class EmailLocaleResolverTest {
    private static final String UID = UUID.randomUUID().toString();
    private static final String COMPANY_ID = UUID.randomUUID().toString();
    private static final String BARE_EMAIL = "<EMAIL>";

    private static final String USER_LANGUAGE = "bar";
    private static final String COMPANY_LANGUAGE = "de";
    private static final String OVERRIDE_LANGUAGE = "tlh";
    private static final String DEFAULT_LANGUAGE = "en";

    private static final UmpUserEmail RECIPIENT_USER = EmailRecipient.forUmpUserId(UID);
    private static final UmpCompanyEmail RECIPIENT_COMPANY = EmailRecipient.forUmpCompanyId(COMPANY_ID);
    private static final EmailAddress RECIPIENT_EMAILADDRESS = EmailRecipient.forEmailAddress(BARE_EMAIL);


    @Mock
    private UmpClient umpClient;

    @InjectMocks
    private EmailLocaleResolver emailLocaleResolver;

    @Test
    void givenPrimaryRecipientIsUser_umpQuerySucceeds_umpEmailAddressIsReturned() {
        when(umpClient.getUser(EmailTenant.rexroth.name(), UID))
                .thenReturn(new UmpUserDto().communicationLanguage(USER_LANGUAGE));

        final String actualLocale = emailLocaleResolver.resolve(getTemplatedEmail(RECIPIENT_USER, null));

        assertThat(actualLocale).isEqualTo(USER_LANGUAGE);
    }

    @Test
    void givenPrimaryRecipientIsUser_localeOverrideIsActive_umpEmailAddressIsReturned() {
        final String actualLocale = emailLocaleResolver
                .resolve(getTemplatedEmail(RECIPIENT_USER, Locale.forLanguageTag(OVERRIDE_LANGUAGE)));

        assertThat(actualLocale).isEqualTo(OVERRIDE_LANGUAGE);
    }

    @Test
    void givenPrimaryRecipientIsUser_umpQueryFails_defaultLanguageIsReturned() {
        when(umpClient.getUser(EmailTenant.rexroth.name(), UID))
                .thenThrow(new RuntimeException());

        final String actualLocale = emailLocaleResolver.resolve(getTemplatedEmail(RECIPIENT_USER, null));

        assertThat(actualLocale).isEqualTo(DEFAULT_LANGUAGE);
    }

    @Test
    void givenPrimaryRecipientIsUser_umpUserHasNoLanguage_defaultLanguageIsReturned() {
        when(umpClient.getUser(EmailTenant.rexroth.name(), UID))
                .thenReturn(new UmpUserDto().communicationLanguage(null));

        final String actualLocale = emailLocaleResolver.resolve(getTemplatedEmail(RECIPIENT_USER, null));

        assertThat(actualLocale).isEqualTo(DEFAULT_LANGUAGE);
    }

    @Test
    void givenPrimaryRecipientIsCompany_umpQuerySucceeds_umpEmailAddressIsReturned() {
        when(umpClient.getCompanyDetails(EmailTenant.rexroth.name(), COMPANY_ID))
                .thenReturn(new UmpExternalCompanyDto().communicationLanguage(COMPANY_LANGUAGE));

        final String actualLocale = emailLocaleResolver.resolve(getTemplatedEmail(RECIPIENT_COMPANY, null));

        assertThat(actualLocale).isEqualTo(COMPANY_LANGUAGE);
    }

    @Test
    void givenPrimaryRecipientIsCompany_localeOverrideIsActive_umpEmailAddressIsReturned() {
        final String actualLocale = emailLocaleResolver
                .resolve(getTemplatedEmail(RECIPIENT_COMPANY, Locale.forLanguageTag(OVERRIDE_LANGUAGE)));

        assertThat(actualLocale).isEqualTo(OVERRIDE_LANGUAGE);
    }

    @Test
    void givenPrimaryRecipientIsCompany_umpQueryFails_defaultLanguageIsReturned() {
        when(umpClient.getCompanyDetails(EmailTenant.rexroth.name(), COMPANY_ID))
                .thenThrow(new RuntimeException());

        final String actualLocale = emailLocaleResolver.resolve(getTemplatedEmail(RECIPIENT_COMPANY, null));

        assertThat(actualLocale).isEqualTo(DEFAULT_LANGUAGE);
    }

    @Test
    void givenPrimaryRecipientIsCompany_umpCompanyHasNoLanguage_defaultLanguageIsReturned() {
        when(umpClient.getCompanyDetails(EmailTenant.rexroth.name(), COMPANY_ID))
                .thenReturn(new UmpExternalCompanyDto().communicationLanguage(null));

        final String actualLocale = emailLocaleResolver.resolve(getTemplatedEmail(RECIPIENT_COMPANY, null));

        assertThat(actualLocale).isEqualTo(DEFAULT_LANGUAGE);
    }

    @Test
    void givenPrimaryRecipientIsEmail_defaultLanguageIsReturned() {
        final String actualLocale = emailLocaleResolver
                .resolve(getTemplatedEmail(RECIPIENT_EMAILADDRESS, null));

        assertThat(actualLocale).isEqualTo(DEFAULT_LANGUAGE);
    }

    @Test
    void givenPrimaryRecipientIsEmail_localeOverrideIsActive_defaultLanguageIsReturned() {
        final String actualLocale = emailLocaleResolver
                .resolve(getTemplatedEmail(RECIPIENT_EMAILADDRESS, Locale.forLanguageTag(OVERRIDE_LANGUAGE)));

        assertThat(actualLocale).isEqualTo(OVERRIDE_LANGUAGE);
    }

    private TemplatedEmail getTemplatedEmail(final EmailRecipient primaryRecipient, final Locale localeOverride) {
        return TemplatedEmail.builder()
                .to(primaryRecipient)
                .tenant(EmailTenant.rexroth)
                .templateData(emailTenant -> "")
                .localeOverride(localeOverride)
                .build();
    }

}