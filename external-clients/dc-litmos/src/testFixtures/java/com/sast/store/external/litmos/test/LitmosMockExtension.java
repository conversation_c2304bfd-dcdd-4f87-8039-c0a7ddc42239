package com.sast.store.external.litmos.test;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.common.ClasspathFileSource;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.github.tomakehurst.wiremock.junit5.WireMockExtension;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.delete;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching;

public class LitmosMockExtension extends WireMockExtension {

    public static final int PORT = 34729;

    private static LitmosMockExtension instance;

    public LitmosMockExtension() {
        super(WireMockExtension
            .extensionOptions()
            .options(WireMockConfiguration.options()
                .fileSource(new ClasspathFileSource("litmos"))
                .port(PORT))
            .configureStaticDsl(true));
        instance = this;
    }

    public static LitmosMockExtension instance() {
        return instance;
    }

    public static LitmosMockExtension withDefaultResponse() {
        withCreateUserResponse();
        withSearchUsersResponse();
        withCreateTeamResponse();
        withCreateSubTeamResponse();
        withAssignCoursesToTeamResponse();
        withAssignUsersToTeamResponse();
        withUnassignUsersFromTeamResponse();
        withSearchTeamResponse();
        withGetTeamResponse();
        return instance;
    }

    public static LitmosMockExtension withCreateUserResponse() {
        instance.stubFor(post(urlPathMatching("/v1.svc/users"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody(
                    """
                        {
                            "Id": "WZVU5BFm2hI1",
                            "UserName": "<EMAIL>",
                            "FirstName": "Firstname",
                            "LastName": "Lastname",
                            "FullName": "Firstname Lastname",
                            "Email": "<EMAIL>",
                            "AccessLevel": "Learner",
                            "DisableMessages": false,
                            "Active": true,
                            "Skype": "",
                            "PhoneWork": "",
                            "PhoneMobile": "",
                            "LastLogin": "",
                            "LoginKey": "https:\\/\\/robertboschgmbh-test-30402765.litmoseu.com\\/login.aspx?loginkey=9994c323-b206-4f31-9fd8-6adf05abb97f",
                            "IsCustomUsername": false,
                            "Password": "",
                            "SkipFirstLogin": false,
                            "TimeZone": "UTC",
                            "SalesforceId": null,
                            "OriginalId": 2493696,
                            "Street1": "",
                            "Street2": "",
                            "City": "",
                            "State": "",
                            "PostalCode": "",
                            "Country": "DE",
                            "CompanyName": "Bosch",
                            "JobTitle": "",
                            "CustomField1": "",
                            "CustomField2": "",
                            "CustomField3": "",
                            "CustomField4": "",
                            "CustomField5": "",
                            "CustomField6": "",
                            "CustomField7": "",
                            "CustomField8": "",
                            "CustomField9": "",
                            "CustomField10": "",
                            "Culture": "en-US",
                            "SalesforceContactId": null,
                            "SalesforceAccountId": null,
                            "CreatedDate": "2024-08-14T15:21:11Z",
                            "Points": 0,
                            "Brand": "Default",
                            "ManagerId": "0",
                            "ManagerName": "",
                            "EnableTextNotification": false,
                            "Website": "",
                            "Twitter": "",
                            "ExpirationDate": "",
                            "JobRole": "",
                            "ExternalEmployeeId": null,
                            "ProfileType": "Internal"
                        }""")
                .withTransformers("response-template")));

        return instance;
    }

    public static LitmosMockExtension withSearchUsersResponse() {
        instance.stubFor(get(urlPathMatching("/v1.svc/users")).withQueryParam("search", WireMock.matching(".*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody(
                    """
                        [
                            {
                                "Id": "WZVU5BFm2hI1",
                                "UserName": "<EMAIL>",
                                "FirstName": "firstname",
                                "LastName": "lastname",
                                "Active": true,
                                "Email": "<EMAIL>",
                                "AccessLevel": "Learner",
                                "Brand": "Default"
                            }
                        ]""")
                .withTransformers("response-template")));

        return instance;
    }

    public static LitmosMockExtension withSearchUsersEmptyResponse() {
        instance.stubFor(get(urlPathMatching("/v1.svc/users")).withQueryParam("search", WireMock.matching(".*"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("content-type", "application/json;charset=UTF-8")
                        .withBody("[]")
                        .withTransformers("response-template")));

        return instance;
    }

    public static LitmosMockExtension withSearchTeamResponse() {
        instance.stubFor(get(urlPathMatching("/v1.svc/teams")).withQueryParam("search", WireMock.matching(".*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody(
                    """
                        [{
                            "Id": "fNDDifPRKkM1",
                            "Name": "{{request.query.search.0}}",
                            "ParentTeamId": "02_Rexroth_Partner",
                            "TeamCodeForBulkImport": null
                        }]""")
                .withTransformers("response-template")));

        return instance;
    }

    public static LitmosMockExtension withGetTeamResponse() {
        instance.stubFor(get(urlPathMatching("/v1.svc/teams/.*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody(
                    """
                        {
                            "Id": "fNDDifPRKkM1",
                            "Name": "Teamname",
                            "ParentTeamId": "02_Rexroth_Partner",
                            "TeamCodeForBulkImport": null
                        }""")
                .withTransformers("response-template")));

        return instance;
    }

    public static LitmosMockExtension withCreateSubTeamResponse() {
        instance.stubFor(post(urlPathMatching("/v1.svc/teams/.*/teams"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody(
                    """
                        {
                            "Id": "fNDDifPRKkM1",
                            "Name": "UPM Tester",
                            "Description": "Managed by Boss Marketplace. Do not change!",
                            "ParentTeamId": "02_Rexroth_Partner",
                            "CopyParentCourses": true,
                            "CopyParentCourseLibraries": true,
                            "CopyParentLearningpathLibraries": true,
                            "TeamCodeForBulkImport": null,
                            "RemoveFromParent": false
                        }""")
                .withTransformers("response-template")));

        return instance;
    }

    public static LitmosMockExtension withCreateTeamResponse() {
        instance.stubFor(post(urlPathMatching("/v1.svc/teams"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody(
                    """
                        {
                            "Id": "fNDDifPRKkM1",
                            "Name": "UPM Tester",
                            "Description": "Managed by Boss Marketplace. Do not change!",
                            "ParentTeamId": "",
                            "TeamCodeForBulkImport": null,
                            "RemoveFromParent": false
                        }""")
                .withTransformers("response-template")));

        return instance;
    }

    public static LitmosMockExtension withAssignCoursesToTeamResponse() {
        instance.stubFor(post(urlPathMatching("/v1.svc/teams/.*/courses"))
            .willReturn(aResponse()
                .withStatus(201)
                .withHeader("content-type", "application/json;charset=UTF-8")));

        return instance;
    }

    public static LitmosMockExtension withAssignUsersToTeamResponse() {
        instance.stubFor(post(urlPathMatching("/v1.svc/teams/.*/users"))
            .willReturn(aResponse()
                .withStatus(201)
                .withHeader("content-type", "application/json;charset=UTF-8")));

        return instance;
    }

    public static LitmosMockExtension withUnassignUsersFromTeamResponse() {
        instance.stubFor(delete(urlPathMatching("/v1.svc/teams/.*/users/.*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")));
        return instance;
    }
}
