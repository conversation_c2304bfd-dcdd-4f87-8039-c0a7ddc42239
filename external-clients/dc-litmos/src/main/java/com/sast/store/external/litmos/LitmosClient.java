package com.sast.store.external.litmos;

import com.sast.store.gen.client.litmos.api.LitmosApi;
import com.sast.store.gen.client.litmos.api.LitmosCourseInformation;
import com.sast.store.gen.client.litmos.api.LitmosCreateTeamRequest;
import com.sast.store.gen.client.litmos.api.LitmosCreateUserRequest;
import com.sast.store.gen.client.litmos.api.LitmosGetUsersResponse;
import com.sast.store.gen.client.litmos.api.LitmosId;
import com.sast.store.gen.client.litmos.api.LitmosTeamInformation;
import com.sast.store.gen.client.litmos.api.LitmosUserDetails;
import com.sast.store.gen.client.litmos.api.LitmosUserInformation;
import jakarta.inject.Inject;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;

@Component
public class LitmosClient {

    @Inject
    private LitmosApi litmosApi;

    public List<LitmosUserInformation> getUsers(final String usernameOrEmail) {
        return litmosApi.findUsers(usernameOrEmail);
    }

    public LitmosUserDetails getUserDetails(final String userIdOrUsername) {
        return litmosApi.getUserDetails(userIdOrUsername);
    }

    public LitmosGetUsersResponse getUsers() {
        return litmosApi.getUsers(0, 1000, null);
    }

    public List<LitmosTeamInformation> getTeams(final String teamname) {
        return litmosApi.getTeams(teamname);
    }

    public LitmosTeamInformation getTeam(final String teamId) {
        return litmosApi.getTeamInformation(teamId);
    }

    public LitmosTeamInformation createTeam(final LitmosCreateTeamRequest team) {
        return litmosApi.createTeam(team);
    }

    public LitmosTeamInformation createTeam(final String parentTeamId, final LitmosCreateTeamRequest team) {
        if (parentTeamId == null) {
            return createTeam(team);
        }
        return litmosApi.createSubTeam(parentTeamId, team);
    }

    public void deleteTeam(final String teamId) {
        litmosApi.deleteTeam(teamId);
    }

    public void assignCoursesToTeam(final String teamId, final Collection<String> courseIds) {
        litmosApi.assignCoursesToTeam(teamId, courseIds.stream().map(new LitmosId()::id).toList());
    }

    public LitmosUserDetails createUser(final LitmosCreateUserRequest user) {
        return litmosApi.createUser(user);
    }

    public void deleteUser(final String userId) {
        litmosApi.deleteUser(userId);
    }

    public List<LitmosCourseInformation> getCourses() {
        return litmosApi.getCourses();
    }

    public void assignUsersToTeam(final String teamId, final Collection<String> userIds) {
        litmosApi.assignUsersToTeam(teamId, userIds.stream().map(new LitmosId()::id).toList());
    }

    public void unassignUserFromTeam(final String teamId, final String userId) {
        litmosApi.removeUserFromTeam(teamId, userId);
    }

}
