package com.sast.store.external.litmos;

import com.sast.store.commons.jerseyclient.ClientRequestResponseLogger;
import com.sast.store.commons.jerseyclient.RestMetricsJerseyClientInterceptor;
import com.sast.store.gen.client.litmos.api.LitmosApi;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.ws.rs.client.Client;
import jakarta.ws.rs.client.ClientRequestFilter;
import jakarta.ws.rs.client.WebTarget;
import jakarta.ws.rs.core.UriBuilder;
import org.glassfish.jersey.client.ClientProperties;
import org.glassfish.jersey.client.proxy.WebResourceFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class LitmosRestServiceProducer {

    @Inject
    @Named("externalRestClient")
    private Client client;

    @Inject
    private LitmosConfiguration configuration;

    @Inject
    private ClientRequestResponseLogger requestResponseLogger;

    @Inject
    private RestMetricsJerseyClientInterceptor metricsInterceptor;

    @Bean
    public LitmosApi getLitmosRestService() {
        return getProxyLitmos(LitmosApi.class);
    }

    private <T> T getProxyLitmos(final Class<T> proxyInterface) {
        final WebTarget webTarget = client
            .target(configuration.url())
            .register(requestResponseLogger)
            .property(metricsInterceptor.getName(), metricsInterceptor.load(proxyInterface, configuration.url()))
            .register((ClientRequestFilter) requestContext -> requestContext
                .setUri(UriBuilder.fromUri(requestContext.getUri())
                    .replaceQueryParam("format", "json")
                    .replaceQueryParam("source", "sast-boss-store")
                    .build()))
            .register((ClientRequestFilter) requestContext -> requestContext
                .getHeaders().putSingle("apikey", configuration.apikey()))
            // because litmos api uses DELETE with body
            .property(ClientProperties.SUPPRESS_HTTP_COMPLIANCE_VALIDATION, true);
        return WebResourceFactory.newResource(proxyInterface, webTarget);
    }
}
