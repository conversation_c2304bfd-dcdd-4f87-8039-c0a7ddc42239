plugins {
    id("bossstore.subproject-conventions")
    `java-library`
    `java-test-fixtures`
    id("org.springframework.boot") version "3.4.3" apply false
    id("org.openapi.generator") version "7.11.0"
}

dependencies {
    implementation(project(":commons:jersey-client"))

    implementation(platform(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES))
    implementation("org.springframework.boot:spring-boot-starter-jersey")

    implementation("jakarta.ws.rs:jakarta.ws.rs-api")
    implementation("org.glassfish.jersey.ext:jersey-proxy-client")
    implementation("io.swagger:swagger-annotations:1.6.15")
    implementation("io.swagger:swagger-jaxrs:1.6.15")
    implementation("org.apache.cxf:cxf-rt-frontend-jaxrs:4.1.0")

    testFixturesImplementation(platform(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES))
    testFixturesImplementation("org.junit.jupiter:junit-jupiter-api")
    testFixturesImplementation("org.wiremock.integrations:wiremock-spring-boot:3.9.0")
}

openApiGenerate {
    generatorName.set("jaxrs-cxf-client")
    inputSpec.set(layout.projectDirectory.file("openapi/litmos.yml").asFile.canonicalPath)
    outputDir.set(layout.buildDirectory.dir("generated/").get().toString())
    modelPackage.set("com.sast.store.gen.client.litmos.api")
    apiPackage.set("com.sast.store.gen.client.litmos.api")
    modelNamePrefix.set("Litmos")
    skipValidateSpec.set(true)
    generateApiTests.set(false)
    generateApiDocumentation.set(false)
    generateModelTests.set(false)
    configOptions.set(mapOf(
        "dateLibrary" to "java8",
        "useJakartaEe" to "true",
        "enumUnknownDefaultCase" to "true",
        "useAnnotatedBasePath" to "true"
    ))
}

tasks.compileJava {
    dependsOn("openApiGenerate")
}

sourceSets {
    main {
        java {
            srcDir(layout.buildDirectory.dir("generated/src/gen/java"))
        }
    }
}
