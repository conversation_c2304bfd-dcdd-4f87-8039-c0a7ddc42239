openapi: 3.0.0
info:
  version: 1.0.0
  title: Litmos
tags:
  - name: Litmos
paths:
  /v1.svc/users:
    get:
      tags:
        - Litmos
      operationId: findUsers
      summary: Get Users by User Name, First/Last Name, Email, or Company Name
      parameters:
        - name: search
          in: query
          required: false
          schema:
            type: string
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/UserInformation"
          description: default response
    post:
      tags:
        - Litmos
      operationId: createUser
      summary: Create a new user
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateUserRequest"
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserDetails"
          description: default response          
  /v1.svc/users/paginated:
    get:
      tags:
        - Litmos
      operationId: getUsers
      summary: Get all users paginated
      parameters:
        - name: start
          in: query
          required: false
          schema:
            type: integer
        - name: limit
          in: query
          required: false
          schema:
            type: integer
        - name: showInactive
          in: query
          required: false
          schema:
            type: boolean
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetUsersResponse"
          description: default response
  /v1.svc/users/{userId}:
    get:
      tags:
        - Litmos
      operationId: getUserDetails
      summary: Get User Details by User Id or Username
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
          description: userId or username
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserDetails"
          description: default response
    delete:
      tags:
        - Litmos
      operationId: deleteUser
      summary: Delete user by user id
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
          description: userId
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /v1.svc/courses:
    get:
      tags:
        - Litmos
      operationId: getCourses
      summary: Get all courses
      parameters: []
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetCoursesResponse"
          description: default response
  /v1.svc/courses/{courseId}/users:
    get:
      tags:
        - Litmos
      operationId: getUsersAssignedToCourse
      parameters:
        - name: courseId
          in: path
          required: true
          schema:
            type: string
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetUserAssignedToCourseResponse"
          description: default response
  /v1.svc/teams:
    get:
      tags:
        - Litmos
      operationId: getTeams
      summary: Get teams filtered by teamname
      parameters: 
        - name: search
          in: query
          required: false
          schema:
            type: string      
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetTeamsResponse"
          description: default response
      security: []
    post:
      tags:
        - Litmos
      operationId: createTeam
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateTeamRequest"
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TeamInformation"
          description: default response
  /v1.svc/teams/{teamId}:
    get:
      tags:
        - Litmos
      operationId: getTeamInformation
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: string
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TeamInformation"
          description: default response
    delete:
      tags:
        - Litmos
      operationId: deleteTeam
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: string
      responses:
        default:
          content:
            application/json: {}
          description: default response 
  /v1.svc/teams/{teamId}/teams:
    post:
      tags:
        - Litmos
      operationId: createSubTeam
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: string      
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateTeamRequest"
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TeamInformation"
          description: default response                   
  /v1.svc/teams/{teamId}/courses:
    get:
      tags:
        - Litmos
      operationId: getCoursesAssignedToTeam
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: string
      responses:
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetCoursesAssignedToTeamResponse"
          description: default response
    post:
      tags:
        - Litmos
      operationId: assignCoursesToTeam
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AssignIdRequest"
      responses:
        default:
          content:
            application/json: {}
          description: default response
    delete:
      tags:
        - Litmos
      operationId: removeCoursesFromTeam
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AssignIdRequest"
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /v1.svc/teams/{teamId}/users:
    get:
      tags:
        - Litmos
      operationId: getUsersAssignedToTeam
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: string
      responses:
        default:
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/UserInformation"              
          description: default response
    post:
      tags:
        - Litmos
      operationId: assignUsersToTeam
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AssignIdRequest"
      responses:
        default:
          content:
            application/json: {}
          description: default response
  /v1.svc/teams/{teamId}/users/{userId}:
    delete:
      tags:
        - Litmos
      operationId: removeUserFromTeam
      parameters:
        - name: teamId
          in: path
          required: true
          schema:
            type: string
        - name: userId
          in: path
          required: true
          schema:
            type: string            
      responses:
        default:
          content:
            application/json: {}
          description: default response                    
                 
components:
  schemas:
    GetUsersResponse:
      type: object
      properties:
        Pagination:
          type: object
          properties:
            BatchParam:
              type: string
            BatchSize:
              type: integer
            Start:
              type: integer
            TotalCount:
              type: integer
        Items:
          type: array
          items:
            $ref: "#/components/schemas/UserInformation"
    UserInformation:
      type: object
      properties:
        Id:
          type: string
        UserName:
          type: string
        FirstName:
          type: string
        LastName:
          type: string
        Email:
          type: string
        Active:
          type: boolean
        AccessLevel:
          type: string
        Brand:
          type: string
    UserDetails:
      type: object
      properties:
        Id:
          type: string
        UserName:
          type: string
        FirstName:
          type: string
        LastName:
          type: string
        FullName:
          type: string
        Email:
          type: string
        AccessLevel:
          type: string
        DisableMessages:
          type: boolean
        Active:
          type: boolean
        Skype:
          type: string
        PhoneWork:
          type: string
        PhoneMobile:
          type: string
        LastLogin:
          type: string
        LoginKey:
          type: string
        IsCustomUsername:
          type: boolean
        Password:
          type: string
        SkipFirstLogin:
          type: boolean
        TimeZone:
          type: string
        SalesforceId:
          type: string
        OriginalId:
          type: integer
        Street1:
          type: string
        Street2:
          type: string
        City:
          type: string
        State:
          type: string
        PostalCode:
          type: string
        Country:
          type: string
        CompanyName:
          type: string
        JobTitle:
          type: string
        CustomField1:
          type: string
        CustomField2:
          type: string
        CustomField3:
          type: string
        CustomField4:
          type: string
        CustomField5:
          type: string
        CustomField6:
          type: string
        CustomField7:
          type: string
        CustomField8:
          type: string
        CustomField9:
          type: string
        CustomField10:
          type: string
        Culture:
          type: string
        SalesforceContactId:
          type: string
        SalesforceAccountId:
          type: string
        CreatedDate:
          type: string
          format: date-time
        Points:
          type: integer
        Brand:
          type: string
        ManagerId:
          type: string
        ManagerName:
          type: string
        EnableTextNotification:
          type: boolean
        Website:
          type: string
        Twitter:
          type: string
        ExpirationDate:
          type: string
        JobRole:
          type: string
        ExternalEmployeeId:
          type: string
        ProfileType:
          type: string
    GetCoursesResponse:
      type: array
      items:
        $ref: "#/components/schemas/CourseInformation"
    CourseInformation:
      type: object
      properties:
        Id:
          type: string
        Code:
          type: string
        Name:
          type: string
        Active:
          type: boolean
        ForSale:
          type: boolean
        OriginalId:
          type: integer
        Description:
          type: string
        EcommerceShortDescription:
          type: string
        EcommerceLongDescription:
          type: string
        CourseCodeForBulkImport:
          type: string
        Price:
          type: number
        AccessTillDate:
          type: string
          format: date-time
        AccessTillDays:
          type: integer
        CourseTeamLibrary:
          type: boolean
        CreatedBy:
          type: string
        SeqId:
          type: integer
    GetUserAssignedToCourseResponse:
      type: array
      items:
        $ref: "#/components/schemas/UserAssignedToCourseInformation"
    UserAssignedToCourseInformation:
      type: object
      properties:
        Id:
          type: string
        UserName:
          type: string
        FirstName:
          type: string
        LastName:
          type: string
        Completed:
          type: boolean
        PercentageComplete:
          type: integer
        CompliantTill:
          type: string
          format: date-time
        DueDate:
          type: string
          format: date-time
        AccessTillDate:
          type: string
          format: date-time
    GetTeamsResponse:
      type: array
      items:
        $ref: "#/components/schemas/TeamInformation"
    TeamInformation:
      type: object
      properties:
        Id:
          type: string
        Name:
          type: string
        Description:
          type: string
        TeamCodeForBulkImport:
          type: string
        ParentTeamId:
          type: string
        RemoveFromParent:
          type: boolean
    GetCoursesAssignedToTeamResponse:
      type: array
      items:
        $ref: "#/components/schemas/CourseInformation"
    CreateTeamRequest:
      type: object
      properties:
        Name:
          type: string
        Description:
          type: string
        ParentTeamId:
          type: string
        CopyParentCourses:
          type: boolean
        CopyParentCourseLibraries:
          type: boolean
        CopyParentLearningpathLibraries:
          type: boolean
    AssignIdRequest:
      type: array
      items:
        $ref: "#/components/schemas/Id"
    Id:
      properties:
        Id:
          type: string
    CreateUserRequest:
      type: object
      properties:
        Id:
          type: string
        UserName:
          type: string
        FirstName:
          type: string
        LastName:
          type: string
        Email:
          type: string
        AccessLevel:
          type: string
          enum:
            - Learner
            - Account_Owner
            - Admin
            - Team_Leader
        Active:
          type: boolean
        CompanyName:
          type: string
        Country:
          type: string
        TimeZone:
          type: string
        SkipFirstLogin:
          type: boolean
  requestBodies: {}
  securitySchemes: {}
  links: {}
  callbacks: {}
security: []
servers: []
