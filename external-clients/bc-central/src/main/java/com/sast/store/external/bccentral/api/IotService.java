package com.sast.store.external.bccentral.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;

import java.util.List;

@Builder
public record IotService(
    @NotBlank @JsonProperty("Service name") String serviceName,
    @NotBlank @JsonProperty("Material number") String materialNumber,
    @NotNull List<Measurement> measurements
) { }
