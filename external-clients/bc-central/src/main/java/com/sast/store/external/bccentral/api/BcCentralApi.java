package com.sast.store.external.bccentral.api;

import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

import java.util.List;

@Consumes({ MediaType.APPLICATION_JSON })
@Produces({ MediaType.APPLICATION_JSON })
public interface BcCentralApi {

    @GET
    @Path("/r/pog8306/boss/materialNumbers")
    GetMaterialNumbersResponse getMaterialNumbers();

    @POST
    @Path("/data-recorder-service/v2/pog8306")
    List<CreateContractResponse> createContract(@HeaderParam("X-Metadata") List<String> metadata,
        CreateContractRequest createContractRequest);

    @POST
    @Path("/data-recorder-service/v2/pog8306")
    List<UpdateContractResponse> updateContract(@HeaderParam("X-Metadata") List<String> metadata,
                                                UpdateContractRequest updateContractRequest);

    @GET
    @Path("/r/pog8306/boss/status/contract/{contractId}")
    List<GetContractResponse> getContract(@PathParam("contractId") String contractId);

    @POST
    @Path("/data-recorder-service/v2/pog8306")
    List<DeleteContractResponse> deleteContract(@HeaderParam("X-Metadata") List<String> metadata,
        DeleteContractRequest deleteContractRequest);
}
