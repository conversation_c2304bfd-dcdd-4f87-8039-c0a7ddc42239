package com.sast.store.external.bccentral.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;

import java.util.List;

@Builder
public record GetContractResponse(
    @NotBlank @JsonProperty("_id") String id,
    ContractStatus status,
    @NotBlank String contractId,
    @NotBlank String lastAction,
    String reason,
    @NotNull List<ContractBooking> booking
) { }
