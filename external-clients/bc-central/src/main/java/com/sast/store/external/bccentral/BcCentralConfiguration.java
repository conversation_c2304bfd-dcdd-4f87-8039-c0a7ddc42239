package com.sast.store.external.bccentral;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

import java.net.URI;

@Configuration
@ConfigurationProperties(prefix = "bossstore.bccentral")
@Validated
@Data
public class BcCentralConfiguration {

    @NotNull
    private URI url;

    @NotBlank
    private String username;

    @NotBlank
    private String password;

    @NotNull
    private Stage stage;
}
