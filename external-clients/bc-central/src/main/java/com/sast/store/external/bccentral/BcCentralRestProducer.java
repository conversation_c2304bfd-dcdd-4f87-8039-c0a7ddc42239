package com.sast.store.external.bccentral;

import com.sast.store.commons.jerseyclient.ClientRequestResponseLogger;
import com.sast.store.commons.jerseyclient.RestMetricsJerseyClientInterceptor;
import com.sast.store.external.bccentral.api.BcCentralApi;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.ws.rs.client.Client;
import jakarta.ws.rs.client.WebTarget;
import org.glassfish.jersey.client.authentication.HttpAuthenticationFeature;
import org.glassfish.jersey.client.proxy.WebResourceFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.URI;

@Configuration
public class BcCentralRestProducer {

    @Inject
    @Named("externalRestClient")
    private Client client;

    @Inject
    private BcCentralConfiguration configuration;

    @Inject
    private ClientRequestResponseLogger clientRequestResponseLogger;

    @Inject
    private RestMetricsJerseyClientInterceptor metricsInterceptor;

    @Bean
    public BcCentralApi bcCentralApi() {
        return getProxy(configuration.getUrl(), BcCentralApi.class);
    }

    private <T> T getProxy(final URI url, final Class<T> proxyInterface) {
        final WebTarget target = client
            .target(url)
            .register(clientRequestResponseLogger)
            .property(metricsInterceptor.getName(), metricsInterceptor.load(proxyInterface, configuration.getUrl()))
            .register(HttpAuthenticationFeature.basicBuilder()
                .credentials(configuration.getUsername(), configuration.getPassword())
                .build());
        return WebResourceFactory.newResource(proxyInterface, target);
    }
}
