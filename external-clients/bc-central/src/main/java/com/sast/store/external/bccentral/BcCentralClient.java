package com.sast.store.external.bccentral;

import com.sast.store.external.bccentral.api.BcCentralApi;
import com.sast.store.external.bccentral.api.CreateContractRequest;
import com.sast.store.external.bccentral.api.CreateContractResponse;
import com.sast.store.external.bccentral.api.DeleteContractRequest;
import com.sast.store.external.bccentral.api.DeleteContractResponse;
import com.sast.store.external.bccentral.api.GetContractResponse;
import com.sast.store.external.bccentral.api.GetMaterialNumbersResponse;
import com.sast.store.external.bccentral.api.UpdateContractRequest;
import com.sast.store.external.bccentral.api.UpdateContractResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.List;

@Component
@RequiredArgsConstructor
public class BcCentralClient {

    private final BcCentralApi bcCentralApi;
    private final BcCentralConfiguration bcCentralConfiguration;

    public GetMaterialNumbersResponse getMaterialNumbers() {
        return bcCentralApi.getMaterialNumbers();
    }

    public CreateContractResponse createContract(final CreateContractRequest createContractRequest) {
        return firstResponseOf(bcCentralApi.createContract(metadataFor("boss_contract_creation"), createContractRequest));

    }

    public UpdateContractResponse updateContract(final UpdateContractRequest updateContractRequest) {
        return firstResponseOf(bcCentralApi.updateContract(metadataFor("boss_contract_update"), updateContractRequest));
    }

    public GetContractResponse getContract(final String contractId) {
        return firstResponseOf(bcCentralApi.getContract(contractId));
    }

    public DeleteContractResponse deleteContract(final DeleteContractRequest deleteContractRequest) {
        return firstResponseOf(bcCentralApi.deleteContract(metadataFor("boss_contract_deletion"), deleteContractRequest));
    }

    private List<String> metadataFor(final String type) {
        return List.of(
            "type=%s".formatted(type),
            "timestamp=%d".formatted(Instant.now().toEpochMilli()),
            "sender=BOSS",
            "version=X.X",
            "stage=%s".formatted(bcCentralConfiguration.getStage())
        );
    }

    private <T> T firstResponseOf(final List<T> responses) {
        if (responses.size() != 1) {
            throw new IllegalStateException("Exactly one response expected");
        }
        return responses.getFirst();
    }
}
