package com.sast.store.external.bccentral.api;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;

import java.util.Currency;
import java.util.List;

@Builder
public record UpdateContractRequest(
    @NotBlank String contractId,
    @NotBlank String requestorId,
    @NotBlank String requestId,
    @NotNull Currency customerCurrency,
    @NotNull List<UpdateBooking> booking
) { }
