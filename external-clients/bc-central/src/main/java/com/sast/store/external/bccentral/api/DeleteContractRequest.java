package com.sast.store.external.bccentral.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.util.StdConverter;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;

import java.time.Instant;

@Builder
public record DeleteContractRequest(
    @NotBlank
    String contractId,

    @NotNull
    @JsonProperty("termination_date")
    @JsonSerialize(converter = TerminationDateConverter.class)
    Instant terminationDate,

    @NotBlank
    String requestor
) {
    static class TerminationDateConverter extends StdConverter<Instant, Long> {
        @Override
        public Long convert(final Instant instant) {
            return instant.toEpochMilli();
        }
    }
}
