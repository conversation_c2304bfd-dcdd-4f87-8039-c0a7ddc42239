{"IOTServices": [{"Service name": "Device Management Compact", "Material number": "R917014006", "measurements": [{"measurement_id": "connected_rcu", "measurement": "count", "unit": "PC"}]}, {"Service name": "Device Management Extended", "Material number": "R917014007", "measurements": [{"measurement_id": "connected_rcu", "measurement": "count", "unit": "PC"}]}, {"Service name": "Cellular Services 50MB", "Material number": "R917014016", "measurements": [{"measurement_id": "nr_devices_mob", "measurement": "count", "unit": "PC"}, {"measurement_id": "mobile_mb", "measurement": "amount", "unit": "MB"}, {"measurement_id": "excess_mobile_mb", "measurement": "amount", "unit": "MB"}]}, {"Service name": "Cellular Services 100MB", "Material number": "R917014017", "measurements": [{"measurement_id": "nr_devices_mob", "measurement": "count", "unit": "PC"}, {"measurement_id": "mobile_mb", "measurement": "amount", "unit": "MB"}, {"measurement_id": "excess_mobile_mb", "measurement": "amount", "unit": "MB"}]}, {"Service name": "Cellular Services 150MB", "Material number": "R917014018", "measurements": [{"measurement_id": "nr_devices_mob", "measurement": "count", "unit": "PC"}, {"measurement_id": "mobile_mb", "measurement": "amount", "unit": "MB"}, {"measurement_id": "excess_mobile_mb", "measurement": "amount", "unit": "MB"}]}, {"Service name": "Cellular Services 1024MB", "Material number": "R917014019", "measurements": [{"measurement_id": "nr_devices_mob", "measurement": "count", "unit": "PC"}, {"measurement_id": "mobile_mb", "measurement": "amount", "unit": "MB"}, {"measurement_id": "excess_mobile_mb", "measurement": "amount", "unit": "MB"}]}, {"Service name": "Data Management with 600MB Cloud Storage", "Material number": "R917015757", "measurements": [{"measurement_id": "nr_devices_data", "measurement": "count", "unit": "PC"}, {"measurement_id": "raw_data_mb", "measurement": "amount", "unit": "MB"}, {"measurement_id": "excess_raw_data_mb", "measurement": "amount", "unit": "MB"}, {"measurement_id": "ui_read_tickets", "measurement": "count", "unit": "PC"}, {"measurement_id": "api_read_tickets", "measurement": "count", "unit": "PC"}, {"measurement_id": "unmanaged_domains", "measurement": "count", "unit": "PC"}, {"measurement_id": "managed_domains", "measurement": "count", "unit": "PC"}, {"measurement_id": "pipeline", "measurement": "amount", "unit": "PC"}]}, {"Service name": "Data Management with 3GB Cloud Storage", "Material number": "R917015758", "measurements": [{"measurement_id": "nr_devices_data", "measurement": "count", "unit": "PC"}, {"measurement_id": "raw_data_mb", "measurement": "amount", "unit": "MB"}, {"measurement_id": "excess_raw_data_mb", "measurement": "amount", "unit": "MB"}, {"measurement_id": "ui_read_tickets", "measurement": "count", "unit": "PC"}, {"measurement_id": "api_read_tickets", "measurement": "count", "unit": "PC"}, {"measurement_id": "unmanaged_domains", "measurement": "count", "unit": "PC"}, {"measurement_id": "managed_domains", "measurement": "count", "unit": "PC"}, {"measurement_id": "pipeline", "measurement": "amount", "unit": "PC"}]}, {"Service name": "Data Management with 6GB Cloud Storage", "Material number": "R917015759", "measurements": [{"measurement_id": "nr_devices_data", "measurement": "count", "unit": "PC"}, {"measurement_id": "raw_data_mb", "measurement": "amount", "unit": "MB"}, {"measurement_id": "excess_raw_data_mb", "measurement": "amount", "unit": "MB"}, {"measurement_id": "ui_read_tickets", "measurement": "count", "unit": "PC"}, {"measurement_id": "api_read_tickets", "measurement": "count", "unit": "PC"}, {"measurement_id": "unmanaged_domains", "measurement": "count", "unit": "PC"}, {"measurement_id": "managed_domains", "measurement": "count", "unit": "PC"}, {"measurement_id": "pipeline", "measurement": "amount", "unit": "PC"}]}, {"Service name": "RCU Lite Base Service", "Material number": "R917015756", "measurements": [{"measurement_id": "connected_rcu_lite", "measurement": "count", "unit": "PC "}]}]}