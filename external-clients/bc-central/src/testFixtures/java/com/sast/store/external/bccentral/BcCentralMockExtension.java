package com.sast.store.external.bccentral;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.common.ClasspathFileSource;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.github.tomakehurst.wiremock.junit5.WireMockExtension;

public class BcCentralMockExtension extends WireMockExtension {

    public static final int PORT = 34890;

    private static BcCentralMockExtension instance;

    public BcCentralMockExtension() {
        super(WireMockExtension
            .extensionOptions()
            .options(WireMockConfiguration.options()
                .fileSource(new ClasspathFileSource("bccentral"))
                .port(PORT))
            .configureStaticDsl(true));
        instance = this;
    }

    public static BcCentralMockExtension get() {
        return instance;
    }

    public static BcCentralMockExtension withDefaultResponse() {
        withGetMaterialNumbersResponse();
        withCreateContractResponse();
        withUpdateContractResponse();
        withDeleteContractResponse();
        withGetContractResponse();
        return instance;
    }

    public static BcCentralMockExtension withGetMaterialNumbersResponse() {
        instance.stubFor(WireMock.get(WireMock.urlPathMatching("/r/pog8306/boss/materialNumbers"))
            .withBasicAuth("test", "test")
            .willReturn(WireMock.aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("get-material-numbers-response.json")
                .withTransformers("response-template")));

        return instance;
    }

    public static BcCentralMockExtension withCreateContractResponse() {
        instance.stubFor(WireMock.post(WireMock.urlPathMatching("/data-recorder-service/v2/pog8306"))
            .withBasicAuth("test", "test")
            .withHeader("X-Metadata", WireMock.containing("boss_contract_creation"))
            .willReturn(WireMock.aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("create-contract-response.json")
                .withTransformers("response-template")));

        return instance;
    }

    public static BcCentralMockExtension withUpdateContractResponse() {
        instance.stubFor(WireMock.post(WireMock.urlPathMatching("/data-recorder-service/v2/pog8306"))
                .withBasicAuth("test", "test")
                .withHeader("X-Metadata", WireMock.containing("boss_contract_update"))
                .willReturn(WireMock.aResponse()
                        .withStatus(200)
                        .withHeader("content-type", "application/json;charset=UTF-8")
                        .withBodyFile("update-contract-response.json")
                        .withTransformers("response-template")));

        return instance;
    }

    public static BcCentralMockExtension withDeleteContractResponse() {
        instance.stubFor(WireMock.post(WireMock.urlPathMatching("/data-recorder-service/v2/pog8306"))
            .withBasicAuth("test", "test")
            .withHeader("X-Metadata", WireMock.containing("boss_contract_deletion"))
            .willReturn(WireMock.aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("delete-contract-response.json")
                .withTransformers("response-template")));

        return instance;
    }

    public static BcCentralMockExtension withGetContractResponse() {
        instance.stubFor(WireMock.get(WireMock.urlPathMatching("/r/pog8306/boss/status/contract/.*"))
            .withBasicAuth("test", "test")
            .willReturn(WireMock.aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("get-contract-response.json")
                .withTransformers("response-template")));

        return instance;
    }

    public static BcCentralMockExtension withGetContractNotFoundResponse() {
        instance.stubFor(WireMock.get(WireMock.urlPathMatching("/r/pog8306/boss/status/contract/.*"))
            .withBasicAuth("test", "test")
            .willReturn(WireMock.aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody("[]")));

        return instance;
    }
}
