plugins {
    id("bossstore.subproject-conventions")
    `java-library`
    id("org.springframework.boot") version "3.4.3" apply false
}

dependencies {
    implementation(project(":commons:jersey-client"))
    implementation(project(":commons:tenant"))

    implementation(platform(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES))
    implementation("org.springframework.boot:spring-boot")
    implementation("com.commercetools.sdk:commercetools-apachehttp-client:17.27.0")
    implementation("io.micrometer:micrometer-registry-prometheus")
    implementation("jakarta.ws.rs:jakarta.ws.rs-api")
    api("com.commercetools.sdk:commercetools-sdk-java-api:17.27.0")
    implementation(project(":commons:tenant"))


    testImplementation("org.junit.jupiter:junit-jupiter-api")
    testImplementation("org.junit.jupiter:junit-jupiter")
    testImplementation("org.assertj:assertj-core")
    testImplementation("org.mockito:mockito-core")
    testImplementation("org.mockito:mockito-junit-jupiter")
    testRuntimeOnly("ch.qos.logback:logback-classic")
}

