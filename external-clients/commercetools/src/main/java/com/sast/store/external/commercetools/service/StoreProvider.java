package com.sast.store.external.commercetools.service;

import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.models.store.Store;
import com.sast.store.commons.tenant.api.Tenant;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class StoreProvider {
    private final ProjectApiRoot commercetoolsClient;

    @Cacheable(cacheNames = "storeProvider.findByKey", condition = "#useCache == true", unless = "#result == null")
    public Optional<Store> findByKey(@NonNull final String storeKey, final boolean useCache) {
        try {
            final Store store = commercetoolsClient.stores()
                .withKey(storeKey)
                .get()
                .executeBlocking()
                .getBody();

            return Optional.ofNullable(store);
        } catch (final Exception e) {
            LOG.error("Could not fetch store with key {}: {}", storeKey, e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Cacheable(cacheNames = "storeProvider.findByTenant", unless = "#result == null")
    public Optional<Store> findByTenant(@NonNull final Tenant tenant) {
        try {
            final Store store = commercetoolsClient.stores()
                .withKey(tenant.id())
                .get()
                .executeBlocking()
                .getBody();

            return Optional.ofNullable(store);
        } catch (final Exception e) {
            LOG.error("Could not fetch store with tenant {}: {}", tenant, e.getMessage(), e);
            return Optional.empty();
        }
    }
}
