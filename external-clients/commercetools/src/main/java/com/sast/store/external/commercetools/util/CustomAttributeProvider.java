package com.sast.store.external.commercetools.util;

import com.commercetools.api.models.common.LocalizedString;
import com.commercetools.api.models.common.TypedMoney;
import com.commercetools.api.models.product.AttributeAccess;
import com.commercetools.api.models.product.AttributeContainer;
import com.commercetools.api.models.product.Product;
import com.commercetools.api.models.product.ProductProjection;
import com.commercetools.api.models.product.ProductReference;
import com.commercetools.api.models.product.ProductVariant;
import com.commercetools.api.models.product_type.AttributePlainEnumValue;
import com.sast.store.commons.tenant.api.Tenant;

import java.math.BigDecimal;
import java.net.URI;
import java.time.Period;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

public final class CustomAttributeProvider {

    private CustomAttributeProvider() {
        // noop for util class
    }

    public static Optional<String> getVariantName(final ProductVariant productVariant, final Locale locale) {
        return productVariant.findAttributeByName(Attribute.NAME.attributeName())
                .map(AttributeAccess::asLocalizedString)
                .flatMap(localizedString -> LocalizedFieldProvider.getWithFallback(localizedString, locale));
    }

    public static Optional<Period> getRuntime(final ProductVariant productVariant) {
        return productVariant.findAttributeByName(Attribute.RUNTIME.attributeName())
                .map(AttributeAccess::asString)
                .map(Period::parse);
    }

    public static Optional<Period> getNoticePeriod(final ProductVariant productVariant) {
        return productVariant.findAttributeByName(Attribute.NOTICE_PERIOD.attributeName())
                .map(AttributeAccess::asString)
                .map(Period::parse);
    }

    public static Optional<String> getLicenseType(final ProductVariant productVariant) {
        return productVariant.findAttributeByName(Attribute.LICENSE_TYPE.attributeName())
                .map(AttributeAccess::asEnum)
                .map(AttributePlainEnumValue::getKey);
    }

    public static Optional<String> getSellerCompanyId(final ProductVariant productVariant) {
        return productVariant.findAttributeByName(Attribute.SELLER_COMPANY_ID.attributeName())
                .map(AttributeAccess::asString);
    }

    public static Optional<String> getSellerId(final ProductVariant productVariant) {
        return productVariant.findAttributeByName(Attribute.SELLER_ID.attributeName())
                .map(AttributeAccess::asString);
    }

    public static BigDecimal moneyToBigDecimal(final TypedMoney typedMoney) {
        return BigDecimal.valueOf(typedMoney.getCentAmount())
                .movePointLeft(typedMoney.getFractionDigits());
    }

    public static Optional<Long> getBundleAmount(final ProductVariant productVariant) {
        return productVariant.findAttributeByName(Attribute.BUNDLE_AMOUNT.attributeName())
                .map(AttributeAccess::asLong);
    }

    public static Optional<String> getDescription(final ProductVariant productVariant, final Locale locale) {
        return productVariant.findAttributeByName(Attribute.DESCRIPTION.attributeName())
                .map(AttributeAccess::asLocalizedString)
                .flatMap(localizedString -> LocalizedFieldProvider.getWithFallback(localizedString, locale));
    }

    public static Optional<String> getFeatures(final ProductVariant productVariant, final Locale locale) {
        return productVariant.findAttributeByName(Attribute.FEATURES.attributeName())
                .map(AttributeAccess::asLocalizedString)
                .flatMap(localizedString -> LocalizedFieldProvider.getWithFallback(localizedString, locale));
    }

    public static List<AttributeContainer> getAgreements(final ProductVariant productVariant) {
        return productVariant.findAttributeByName(Attribute.AGREEMENTS.attributeName())
            .map(AttributeAccess::asSetNested)
            .orElse(List.of()).stream()
            .map(l -> (AttributeContainer) () -> l)
            .toList();
    }

    public static List<AttributeContainer> getExternalDocuments(final ProductProjection product) {
        return product.getMasterVariant().findAttributeByName(Attribute.EXTERNAL_DOCUMENTS.attributeName())
                .map(AttributeAccess::asSetNested)
                .orElse(List.of()).stream()
                .map(l -> (AttributeContainer) () -> l)
                .toList();
    }

    public static String getAgreementName(final AttributeContainer attributeContainer, final Locale locale) {
        return attributeContainer.findAttributeByName(Attribute.NAME.attributeName())
                .map(AttributeAccess::asLocalizedString)
                .flatMap(localizedString -> LocalizedFieldProvider.getWithFallback(localizedString, locale))
                .orElse(null);
    }

    public static URI getAgreementLink(final AttributeContainer attributeContainer, final Locale locale) {
        return attributeContainer.findAttributeByName(Attribute.LINK.attributeName())
                .map(AttributeAccess::asLocalizedString)
                .flatMap(localizedString -> LocalizedFieldProvider.getWithFallback(localizedString, locale))
                .map(URI::create)
                .orElse(null);
    }

    public static URI getPriceList(final AttributeContainer attributeContainer, final Locale locale) {
        return attributeContainer.findAttributeByName(Attribute.PRICE_LIST.attributeName())
            .map(AttributeAccess::asLocalizedString)
            .flatMap(localizedString -> LocalizedFieldProvider.getWithFallback(localizedString, locale))
            .map(URI::create)
            .orElse(null);
    }

    public static Optional<String> getAgreementCategory(final AttributeContainer attributeContainer) {
        return attributeContainer.findAttributeByName(Attribute.CATEGORY.attributeName())
            .map(AttributeAccess::asEnum)
            .map(AttributePlainEnumValue::getKey);
    }

    public static List<Product> getAddons(final ProductVariant productVariant) {
        return getAddonsAsReference(productVariant).stream()
            .map(ProductReference::getObj)
            .filter(Objects::nonNull)
            .toList();
    }

    public static List<ProductReference> getAddonsAsReference(final ProductVariant productVariant) {
        return productVariant.findAttributeByName(Attribute.ADDONS.attributeName())
            .map(AttributeAccess::asSetReference)
            .orElse(List.of()).stream()
            .filter(ProductReference.class::isInstance)
            .map(ProductReference.class::cast)
            .toList();
    }

    public static Optional<Tenant> getTenant(final ProductVariant productVariant) {
        return productVariant.findAttributeByName(Attribute.TENANT.attributeName())
                .map(AttributeAccess::asEnum)
                .map(AttributePlainEnumValue::getKey)
                .map(Tenant::fromString);
    }

    public static Map<Locale, String> getBillingName(final ProductVariant productVariant) {
        return productVariant.findAttributeByName(Attribute.BILLING_NAME.attributeName())
                .map(AttributeAccess::asLocalizedString)
                .map(LocalizedString::getTranslations)
                .orElse(Map.of());
    }

    public static Optional<String> getExternalProductId(final ProductVariant productVariant) {
        return productVariant.findAttributeByName(Attribute.EXTERNAL_PRODUCT_ID.attributeName())
                .map(AttributeAccess::asString);
    }


    public static Optional<String> getContractStart(final ProductVariant productVariant) {
        return productVariant.findAttributeByName(Attribute.CONTRACT_START.attributeName())
            .map(AttributeAccess::asEnum)
            .map(AttributePlainEnumValue::getKey);
    }

    private enum Attribute {
        NAME("name"),
        RUNTIME("runtime"),
        NOTICE_PERIOD("noticePeriod"),
        LICENSE_TYPE("licenseType"),
        SELLER_COMPANY_ID("sellerCompanyId"),
        AGREEMENTS("agreements"),
        LINK("link"),
        BUNDLE_AMOUNT("bundleAmount"),
        CATEGORY("category"),
        PRICE_LIST("priceList"),
        DESCRIPTION("description"),
        FEATURES("features"),
        EXTERNAL_DOCUMENTS("externalDocuments"),
        ADDONS("addons"),
        TENANT("tenant"),
        BILLING_NAME("billingName"),
        EXTERNAL_PRODUCT_ID("externalProductId"),
        SELLER_ID("sellerId"),
        CONTRACT_START("contractStart");

        private final String attributeName;

        Attribute(final String attributeName) {
            this.attributeName = attributeName;
        }

        private String attributeName() {
            return attributeName;
        }
    }
}
