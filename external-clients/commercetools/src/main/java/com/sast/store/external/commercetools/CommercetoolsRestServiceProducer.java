package com.sast.store.external.commercetools;

import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.defaultconfig.ApiRootBuilder;
import com.commercetools.api.defaultconfig.ServiceRegion;
import io.vrap.rmf.base.client.oauth2.ClientCredentials;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CommercetoolsRestServiceProducer {
    private final CommercetoolsConfiguration appConfiguration;
    private final CommercetoolsRestMetricsJerseyClientInterceptor metricsInterceptor;

    public CommercetoolsRestServiceProducer(final CommercetoolsConfiguration appConfiguration,
                                            final CommercetoolsRestMetricsJerseyClientInterceptor metricsInterceptor) {
        this.appConfiguration = appConfiguration;
        this.metricsInterceptor = metricsInterceptor;
    }

    @Bean
    public ProjectApiRoot commercetoolsClient() {
        return ApiRootBuilder.of()
            .defaultClient(ClientCredentials.of()
                .withClientId(appConfiguration.clientId())
                .withClientSecret(appConfiguration.clientSecret())
                .build(),
                ServiceRegion.GCP_EUROPE_WEST1)
            .addMiddleware(metricsInterceptor)
            .build(appConfiguration.projectKey());
    }
}
