package com.sast.store.external.commercetools.util;

import com.commercetools.api.models.cart.LineItem;
import com.commercetools.api.models.order.Order;
import com.commercetools.api.models.order.OrderLike;
import com.commercetools.api.models.payment.Payment;
import com.commercetools.api.models.product_selection.ProductSelection;
import com.commercetools.api.models.type.CustomFields;
import com.commercetools.api.models.type.CustomFieldsDraft;
import com.commercetools.api.models.type.CustomFieldsDraftBuilder;
import com.commercetools.api.models.type.FieldContainerBuilder;
import com.commercetools.api.models.type.TypeResourceIdentifierBuilder;
import com.sast.store.commons.tenant.api.Tenant;

import java.util.List;
import java.util.Optional;

public final class CustomFieldProvider {

    private CustomFieldProvider() {
        // noop for util class
    }

    public static Optional<Tenant> getTenant(final OrderLike<?> order) {
        return getString(order.getCustom(), "tenant")
            .map(Tenant::fromString);
    }

    public static Optional<String> getCompanyId(final OrderLike<?> order) {
        return getString(order.getCustom(), "companyId");
    }

    public static Optional<String> getUserId(final OrderLike<?> order) {
        return getString(order.getCustom(), "userId");
    }

    public static Optional<String> getInvoiceNotes1(final Order order) {
        return getString(order.getCustom(), "notes1");
    }

    public static Optional<String> getInvoiceNotes2(final Order order) {
        return getString(order.getCustom(), "notes2");
    }

    public static Optional<String> getSellerCompanyId(final Payment payment) {
        return getString(payment.getCustom(), "sellerCompanyId");
    }

    public static Optional<String> getProductSelectionCountry(final ProductSelection productSelection) {
        return getString(productSelection.getCustom(), "product-selection-country");
    }

    public static Optional<String> getParentLineItemKey(final LineItem item) {
        return getString(item.getCustom(), "parentLineItemKey");
    }

    private static Optional<String> getString(final CustomFields customFields, final String field) {
        if (customFields == null || customFields.getFields() == null || customFields.getFields().values() == null) {
            return Optional.empty();
        }

        return Optional.ofNullable(customFields.getFields().values().get(field))
            .filter(String.class::isInstance)
            .map(String.class::cast);
    }

    public static CustomFieldsDraft defaultLineItem(final List<String> keys) {
        return CustomFieldsDraftBuilder.of()
            .fields(FieldContainerBuilder.of()
                .addValue("addonItemKeys", keys)
                .build())
            .type(TypeResourceIdentifierBuilder.of().key("default-line-item").build())
            .build();
    }

    public static CustomFieldsDraft addonLineItem(final String parentKey) {
        return CustomFieldsDraftBuilder.of()
            .fields(FieldContainerBuilder.of()
                .addValue("parentLineItemKey", parentKey)
                .build())
            .type(TypeResourceIdentifierBuilder.of().key("addon-line-item").build())
            .build();
    }

    public static CustomFieldsDraft defaultOrder(final Tenant tenant, final String companyId, final String userId) {
        return CustomFieldsDraftBuilder.of()
            .fields(FieldContainerBuilder.of()
                .addValue("tenant", tenant.id())
                .addValue("companyId", companyId)
                .addValue("userId", userId)
                .build())
            .type(TypeResourceIdentifierBuilder.of().key("default-order").build())
            .build();
    }

    public static CustomFieldsDraft defaultPayment(final String sellerCompanyId) {
        return CustomFieldsDraftBuilder.of()
            .fields(FieldContainerBuilder.of()
                .addValue("sellerCompanyId", sellerCompanyId)
                .build())
            .type(TypeResourceIdentifierBuilder.of().key("default-payment").build())
            .build();
    }
}
