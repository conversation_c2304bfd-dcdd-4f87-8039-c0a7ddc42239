package com.sast.store.external.commercetools.service;

import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.models.customer_group.CustomerGroup;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerGroupProvider {
    private final ProjectApiRoot commercetoolsClient;

    public Optional<CustomerGroup> findById(@NonNull final String customerGroupId) {
        try {
            final CustomerGroup customerGroup = commercetoolsClient.customerGroups()
                    .withId(customerGroupId)
                    .get()
                    .executeBlocking()
                    .getBody();

            return Optional.ofNullable(customerGroup);
        } catch (final Exception e) {
            LOG.error("Could not fetch customer group with ID {}: {}", customerGroupId, e.getMessage(), e);
            return Optional.empty();
        }
    }

    public Optional<CustomerGroup> findB<PERSON><PERSON><PERSON>(@NonNull final String customerGroupId) {
        try {
            final CustomerGroup customerGroup = commercetoolsClient.customerGroups()
                    .withKey(customerGroupId)
                    .get()
                    .executeBlocking()
                    .getBody();

            return Optional.ofNullable(customerGroup);
        } catch (final Exception e) {
            LOG.error("Could not fetch customer group with key {}: {}", customerGroupId, e.getMessage(), e);
            return Optional.empty();
        }
    }
}
