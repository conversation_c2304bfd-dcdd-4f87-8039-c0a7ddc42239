package com.sast.store.external.commercetools.util;

import com.commercetools.api.models.common.LocalizedString;

import java.util.List;
import java.util.Locale;
import java.util.Optional;

public final class LocalizedFieldProvider {
    private static final Locale DEFAULT_LOCALE = Locale.ENGLISH;

    private LocalizedFieldProvider() {
        // noop for util class
    }

    public static Optional<String> getEnglish(final LocalizedString localizedString) {
        if (localizedString == null) {
            return Optional.empty();
        }

        return Optional.ofNullable(localizedString.getTranslation(List.of(DEFAULT_LOCALE)));
    }

    public static Optional<String> getWithFallback(final LocalizedString localizedString, final Locale locale) {
        if (localizedString == null) {
            return Optional.empty();
        }

        if (locale == null) {
            return Optional.ofNullable(localizedString.getTranslation(List.of(DEFAULT_LOCALE)));
        }
        return Optional.ofNullable(localizedString.getTranslation(List.of(locale, DEFAULT_LOCALE)));
    }
}
