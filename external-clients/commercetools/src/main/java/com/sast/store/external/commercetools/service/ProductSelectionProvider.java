package com.sast.store.external.commercetools.service;

import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.models.product_selection.ProductSelection;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProductSelectionProvider {
    private final ProjectApiRoot commercetoolsClient;

    @Cacheable(cacheNames = "productSelectionProvider.findByKey", unless = "#result == null")
    public Optional<ProductSelection> findByKey(@NonNull final String key) {
        try {
            return Optional.ofNullable(commercetoolsClient.productSelections()
                .withKey(key)
                .get()
                .executeBlocking()
                .getBody());
        } catch (final Exception e) {
            LOG.error("Could not fetch product selection with key {}: {}", key, e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Cacheable(cacheNames = "productSelectionProvider.findById", unless = "#result == null")
    public Optional<ProductSelection> findById(@NonNull final String id) {
        try {
            return Optional.ofNullable(commercetoolsClient.productSelections()
                .withId(id)
                .get()
                .executeBlocking()
                .getBody());
        } catch (final Exception e) {
            LOG.error("Could not fetch product selection with id {}: {}", id, e.getMessage(), e);
            return Optional.empty();
        }
    }
}
