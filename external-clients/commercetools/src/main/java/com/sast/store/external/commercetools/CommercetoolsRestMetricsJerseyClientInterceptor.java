package com.sast.store.external.commercetools;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.vrap.rmf.base.client.ApiHttpRequest;
import io.vrap.rmf.base.client.ApiHttpResponse;
import io.vrap.rmf.base.client.http.Middleware;
import jakarta.ws.rs.core.Response.Status;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

@Component
public class CommercetoolsRestMetricsJerseyClientInterceptor implements Middleware {
    private static final String METRIC_NAME = "http_rest_client_requests";

    private static final String UUID_REGEX = "\\b[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\\b";

    @Autowired(required = false)
    private MeterRegistry meterRegistry;

    @Override
    public CompletableFuture<ApiHttpResponse<byte[]>> invoke(final ApiHttpRequest request,
        final Function<ApiHttpRequest, CompletableFuture<ApiHttpResponse<byte[]>>> next) {
        if (meterRegistry == null) {
            return next.apply(request);
        }
        final long startTime = System.nanoTime();
        return next.apply(request).whenComplete((response, exception) -> {
            final long runtime = System.nanoTime() - startTime;
            if (exception != null) {
                Timer.builder(METRIC_NAME)
                    .tag("method", request.getMethod().toString())
                    .tag("outcome", "ERROR")
                    .tag("status", "ERROR")
                    .tag("uri", mask(request.getUri()))
                    .tag("clientName", request.getUri().getHost())
                    .register(meterRegistry)
                    .record(runtime, TimeUnit.NANOSECONDS);
            } else {
                Timer.builder(METRIC_NAME)
                    .tag("method", request.getMethod().toString())
                    .tag("outcome", Status.fromStatusCode(response.getStatusCode()).getFamily().toString())
                    .tag("status", String.valueOf(response.getStatusCode()))
                    .tag("uri", mask(request.getUri()))
                    .tag("clientName", request.getUri().getHost())
                    .register(meterRegistry)
                    .record(runtime, TimeUnit.NANOSECONDS);
            }
        });
    }

    private String mask(final URI uri) {
        return maskPath(uri.getPath()) + maskQuery(uri.getQuery());
    }

    private String maskQuery(final String query) {
        return query == null ? "" : "?" + query.replaceAll("=[^&]*", "={var}");
    }

    private String maskPath(final String path) {
        return path == null ? "" : path.replaceAll(UUID_REGEX, "{var}");
    }
}
