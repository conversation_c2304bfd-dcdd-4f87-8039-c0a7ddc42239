package com.sast.store.external.commercetools;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.vrap.rmf.base.client.utils.json.JsonUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CommercetoolsObjectMapperConfiguration {
    @Bean
    public ObjectMapper commercetoolsObjectMapper() {
        return JsonUtils.getConfiguredObjectMapper();
    }
}