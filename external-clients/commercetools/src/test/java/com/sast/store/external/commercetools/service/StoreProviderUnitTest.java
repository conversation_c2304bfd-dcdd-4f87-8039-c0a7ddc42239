package com.sast.store.external.commercetools.service;

import com.commercetools.api.client.ByProjectKeyStoresKeyByKeyGet;
import com.commercetools.api.client.ByProjectKeyStoresKeyByKeyRequestBuilder;
import com.commercetools.api.client.ByProjectKeyStoresRequestBuilder;
import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.models.store.Store;
import com.sast.store.commons.tenant.api.Tenant;
import io.vrap.rmf.base.client.ApiHttpResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class StoreProviderUnitTest {
    @Mock
    private ProjectApiRoot commercetoolsClient;

    @Mock
    private ByProjectKeyStoresRequestBuilder requestBuilder;

    @Mock
    private ByProjectKeyStoresKeyByKeyRequestBuilder byKeyRequestBuilder;

    @Mock
    private ByProjectKeyStoresKeyByKeyGet byKeyGet;

    @Mock
    private ApiHttpResponse<Store> apiHttpResponse;

    @Mock
    private Store store;

    private final Tenant tenant = Tenant.REXROTH;

    @InjectMocks
    private StoreProvider storeProvider;

    @BeforeEach
    public void setup() {
        when(commercetoolsClient.stores()).thenReturn(requestBuilder);
        doReturn(byKeyRequestBuilder).when(requestBuilder)
            .withKey(ArgumentMatchers.anyString());
        doReturn(byKeyGet).when(byKeyRequestBuilder).get();
        doReturn(apiHttpResponse).when(byKeyGet).executeBlocking();
    }

    @Test
    void findByKey_should_return_theResponseBody() {
        when(apiHttpResponse.getBody()).thenReturn(store);
        assertThat(storeProvider.findByKey("key", false)).contains(store);
    }

    @Test
    void findByTenant_should_return_theResponseBody() {
        when(apiHttpResponse.getBody()).thenReturn(store);
        assertThat(storeProvider.findByTenant(tenant)).contains(store);
    }

    @Test
    void findByKey_should_return_empty_if_exception() {
        when(byKeyGet.executeBlocking()).thenThrow(new RuntimeException("Simulated exception"));
        assertThat(storeProvider.findByKey("key", false).isPresent()).isFalse();
    }

    @Test
    void findByTenant_should_return_empty_if_exception() {
        when(byKeyGet.executeBlocking()).thenThrow(new RuntimeException("Simulated exception"));
        assertThat(storeProvider.findByTenant(tenant).isPresent()).isFalse();
    }
}