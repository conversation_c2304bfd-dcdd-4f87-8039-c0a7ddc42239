package com.sast.store.external.commercetools.util;

import com.commercetools.api.models.order.Order;
import com.commercetools.api.models.type.CustomFields;
import com.commercetools.api.models.type.FieldContainer;
import org.junit.jupiter.api.Test;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;


class CustomFieldProviderUnitTest {
    @Test
    public void getStringReturnsValueIfFieldValueIsString() {
        final Order order = Order.builder()
                .custom(CustomFields.builder().fields(FieldContainer.builder().addValue("notes1", "bar").build()).buildUnchecked())
                .buildUnchecked();

        final Optional<String> actualValue = CustomFieldProvider.getInvoiceNotes1(order);

        assertThat(actualValue)
                .isNotEmpty()
                .contains("bar");
    }

    @Test
    public void getStringReturnsEmptyIfKeyIsNotPresent() {
        final Order order = Order.builder()
                .custom(CustomFields.builder().fields(FieldContainer.builder().addValue("notes2", "bar").build()).buildUnchecked())
                .buildUnchecked();

        final Optional<String> actualValue = CustomFieldProvider.getInvoiceNotes1(order);

        assertThat(actualValue).isEmpty();
    }

    @Test
    public void getStringReturnsEmptyIfKeyIsNotString() {
        final Order order = Order.builder()
                .custom(CustomFields.builder().fields(FieldContainer.builder().addValue("notes1", 12).build()).buildUnchecked())
                .buildUnchecked();

        final Optional<String> actualValue = CustomFieldProvider.getInvoiceNotes1(order);

        assertThat(actualValue).isEmpty();
    }

    @Test
    public void getStringReturnsEmptyIfOrderHasNoCustomFields() {
        final Order order = Order.builder()
                .buildUnchecked();

        final Optional<String> actualValue = CustomFieldProvider.getInvoiceNotes1(order);

        assertThat(actualValue).isEmpty();
    }

    @Test
    public void getStringReturnsEmptyIfOrderCustomFieldsIsEmpty() {
        final Order order = Order.builder()
                .custom(CustomFields.builder().fields(FieldContainer.builder().build()).buildUnchecked())
                .buildUnchecked();

        final Optional<String> actualValue = CustomFieldProvider.getInvoiceNotes1(order);

        assertThat(actualValue).isEmpty();
    }
}