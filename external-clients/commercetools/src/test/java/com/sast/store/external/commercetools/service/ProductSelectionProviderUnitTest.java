package com.sast.store.external.commercetools.service;


import com.commercetools.api.client.ByProjectKeyProductSelectionsByIDGet;
import com.commercetools.api.client.ByProjectKeyProductSelectionsByIDRequestBuilder;
import com.commercetools.api.client.ByProjectKeyProductSelectionsKeyByKeyGet;
import com.commercetools.api.client.ByProjectKeyProductSelectionsKeyByKeyRequestBuilder;
import com.commercetools.api.client.ByProjectKeyProductSelectionsRequestBuilder;
import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.models.product_selection.ProductSelection;
import io.vrap.rmf.base.client.ApiHttpResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ProductSelectionProviderUnitTest {

    @Mock
    private ProjectApiRoot commercetoolsClient;
    @Mock
    private ByProjectKeyProductSelectionsRequestBuilder requestBuilder;
    @Mock
    private ByProjectKeyProductSelectionsKeyByKeyRequestBuilder byKeyRequestBuilder;
    @Mock
    private ByProjectKeyProductSelectionsByIDRequestBuilder byIDRequestBuilder;
    @Mock
    private ByProjectKeyProductSelectionsKeyByKeyGet byKeyGet;
    @Mock
    private ByProjectKeyProductSelectionsByIDGet byIDGet;
    @Mock
    private ApiHttpResponse<ProductSelection> apiHttpResponse;
    @Mock
    private ProductSelection productSelection;
    @InjectMocks
    private ProductSelectionProvider productSelectionProvider;

    @BeforeEach
    public void setup() {
        when(commercetoolsClient.productSelections()).thenReturn(requestBuilder);
    }

    @Test
    public void findByKeyShouldReturnTheResponseBody() {
        doReturn(byKeyRequestBuilder).when(requestBuilder).withKey(anyString());
        doReturn(byKeyGet).when(byKeyRequestBuilder).get();
        doReturn(apiHttpResponse).when(byKeyGet).executeBlocking();
        when(apiHttpResponse.getBody()).thenReturn(productSelection);

        assertThat(productSelectionProvider.findByKey("key").get()).isEqualTo(productSelection);
    }

    @Test
    public void findByIdShouldReturnTheResponseBody() {
        doReturn(byIDRequestBuilder).when(requestBuilder).withId(anyString());
        doReturn(byIDGet).when(byIDRequestBuilder).get();
        doReturn(apiHttpResponse).when(byIDGet).executeBlocking();
        when(apiHttpResponse.getBody()).thenReturn(productSelection);

        assertThat(productSelectionProvider.findById("id").get()).isEqualTo(productSelection);
    }

    @Test
    public void findByKeyShouldReturnEmptyIfException() {
        doReturn(byKeyRequestBuilder).when(requestBuilder).withKey(anyString());
        doReturn(byKeyGet).when(byKeyRequestBuilder).get();
        doReturn(apiHttpResponse).when(byKeyGet).executeBlocking();
        when(byKeyGet.executeBlocking()).thenThrow(new RuntimeException("Simulated exception"));

        assertThat(productSelectionProvider.findByKey("key").isEmpty()).isTrue();
    }

    @Test
    public void findByIdShouldReturnEmptyIfException() {
        doReturn(byIDRequestBuilder).when(requestBuilder).withId(anyString());
        doReturn(byIDGet).when(byIDRequestBuilder).get();
        doReturn(apiHttpResponse).when(byIDGet).executeBlocking();
        when(byIDGet.executeBlocking()).thenThrow(new RuntimeException("Simulated exception"));

        assertThat(productSelectionProvider.findById("id").isEmpty()).isTrue();
    }

}