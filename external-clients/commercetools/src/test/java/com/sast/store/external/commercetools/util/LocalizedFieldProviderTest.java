package com.sast.store.external.commercetools.util;

import com.commercetools.api.models.common.LocalizedString;
import org.junit.jupiter.api.Test;

import java.util.Locale;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

class LocalizedFieldProviderTest {
    private static final String ENGLISH_TEXT = "an english text";
    private static final String GERMAN_TEXT = "ein deutscher text";

    @Test
    public void testExistingTranslationsCanBeRetrieved() {
        final LocalizedString givenLocalizedString = LocalizedString.of(
                Locale.ENGLISH, ENGLISH_TEXT,
                Locale.GERMAN, GERMAN_TEXT
        );

        final Optional<String> actualEnglishResult = LocalizedFieldProvider.getWithFallback(givenLocalizedString, Locale.ENGLISH);
        final Optional<String> actualGermanResult = LocalizedFieldProvider.getWithFallback(givenLocalizedString, Locale.GERMAN);

        assertThat(actualEnglishResult).hasValue(ENGLISH_TEXT);
        assertThat(actualGermanResult).hasValue(GERMAN_TEXT);
    }

    @Test
    public void testFallbackToEnglishIfGivenLanguageIsNotPresent() {
        final LocalizedString givenLocalizedString = LocalizedString.of(
                Locale.ENGLISH, ENGLISH_TEXT,
                Locale.GERMAN, GERMAN_TEXT
        );

        final Optional<String> actualResult = LocalizedFieldProvider.getWithFallback(givenLocalizedString, Locale.ITALIAN);

        assertThat(actualResult).hasValue(ENGLISH_TEXT);
    }

    @Test
    public void testReturnNullIfGivenLanguageAndEnglishAreNotPresent() {
        final LocalizedString givenLocalizedString = LocalizedString.of(
                Locale.GERMAN, GERMAN_TEXT
        );

        final Optional<String> actualResult = LocalizedFieldProvider.getWithFallback(givenLocalizedString, Locale.ITALIAN);

        assertThat(actualResult).isEmpty();
    }

    @Test
    public void testFallbackToEnglishIfNoLanguageIsGiven() {
        final LocalizedString givenLocalizedString = LocalizedString.of(
                Locale.ENGLISH, ENGLISH_TEXT,
                Locale.GERMAN, GERMAN_TEXT
        );

        final Optional<String> actualResult = LocalizedFieldProvider.getWithFallback(givenLocalizedString, null);

        assertThat(actualResult).hasValue(ENGLISH_TEXT);
    }

    @Test
    public void testReturnEmptyIfGivenLocalizedStringIsNUll() {
        final Optional<String> actualResult = LocalizedFieldProvider.getWithFallback(null, Locale.GERMAN);

        assertThat(actualResult).isEmpty();
    }

    @Test
    public void testEnglish() {
        final LocalizedString givenLocalizedString = LocalizedString.of(
            Locale.ENGLISH, ENGLISH_TEXT,
            Locale.GERMAN, GERMAN_TEXT);

        final Optional<String> actualResult = LocalizedFieldProvider.getEnglish(givenLocalizedString);

        assertThat(actualResult).hasValue(ENGLISH_TEXT);
    }
}