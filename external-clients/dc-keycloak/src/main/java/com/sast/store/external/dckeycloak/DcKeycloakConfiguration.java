package com.sast.store.external.dckeycloak;

import jakarta.validation.constraints.NotNull;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.net.URI;

@ConfigurationProperties(prefix = "bossstore.dckeycloak")
@Validated
public record DcKeycloakConfiguration(
    @NotNull URI url,
    @NotNull String clientId,
    @NotNull String clientSecret,
    @NotNull String realm
) { }
