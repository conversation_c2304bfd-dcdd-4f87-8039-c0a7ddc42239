package com.sast.store.external.dckeycloak;

import jakarta.inject.Inject;
import org.keycloak.admin.client.resource.RealmResource;
import org.keycloak.representations.idm.RoleRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
public class DcKeycloakClient {
    private static final Logger LOG = LoggerFactory.getLogger(DcKeycloakClient.class);

    @Inject
    private RealmResource realm;

    public Optional<UserRepresentation> getUser(final String username) {
        return realm.users().search(username).stream().findAny();
    }

    public void createUser(final String username, final String firstname, final String lastname) {
        final UserRepresentation userRepresentation = new UserRepresentation();
        userRepresentation.setEmailVerified(Boolean.FALSE);
        userRepresentation.setUsername(username);
        userRepresentation.setEmail(username);
        userRepresentation.setFirstName(firstname);
        userRepresentation.setLastName(lastname);
        userRepresentation.setEnabled(Boolean.TRUE);
        realm.users().create(userRepresentation).getEntity();
    }

    public void deleteUser(final String userId) {
        realm.users().delete(userId);
        LOG.info("Deleted user {}", userId);
    }

    public void assignRoles(final String userId, final String clientUUID, final List<String> roleNames) {
        final List<RoleRepresentation> availableRoles = realm.clients().get(clientUUID).roles().list();
        final List<RoleRepresentation> roles = roleNames.stream()
            .map(roleName -> availableRoles.stream().filter(r -> r.getName().equals(roleName)).findAny())
            .filter(Optional::isPresent)
            .map(Optional::get)
            .toList();
        LOG.info("Assigning roles {} to user {}", roles, userId);
        realm.users().get(userId).roles().clientLevel(clientUUID).add(roles);
    }

    public void unassignRoles(final String userId, final String clientUUID, final List<String> roleNames) {
        final List<RoleRepresentation> availableRoles = realm.clients().get(clientUUID).roles().list();
        final List<RoleRepresentation> roles = roleNames.stream()
            .map(roleName -> availableRoles.stream().filter(r -> r.getName().equals(roleName)).findAny())
            .filter(Optional::isPresent)
            .map(Optional::get)
            .toList();
        LOG.info("Unassigning roles {} from user {}", roles, userId);
        realm.users().get(userId).roles().clientLevel(clientUUID).remove(roles);
    }

}
