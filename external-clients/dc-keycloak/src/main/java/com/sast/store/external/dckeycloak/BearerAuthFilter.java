package com.sast.store.external.dckeycloak;

import jakarta.ws.rs.client.ClientRequestContext;
import jakarta.ws.rs.client.ClientRequestFilter;
import jakarta.ws.rs.client.ClientResponseContext;
import jakarta.ws.rs.client.ClientResponseFilter;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

public class BearerAuthFilter implements ClientRequestFilter, ClientResponseFilter {
    private static final Logger LOG = LoggerFactory.getLogger(BearerAuthFilter.class);

    private static final String BEARER_PREFIX = "Bearer ";
    private final ClientCredentialsTokenManager tokenManager;

    public BearerAuthFilter(final ClientCredentialsTokenManager tokenManager) {
        this.tokenManager = tokenManager;
    }

    @Override
    public void filter(final ClientRequestContext requestContext) throws IOException {
        final String authHeader = BEARER_PREFIX + tokenManager.getAccessTokenString();
        requestContext.getHeaders().putSingle(HttpHeaders.AUTHORIZATION, authHeader);
    }

    @Override
    public void filter(final ClientRequestContext requestContext, final ClientResponseContext responseContext) throws IOException {
        if (responseContext.getStatus() != Response.Status.UNAUTHORIZED.getStatusCode()) {
            return;
        }
        final String headerValue = requestContext.getHeaderString(HttpHeaders.AUTHORIZATION);
        if (headerValue == null) {
            return;
        }
        LOG.warn("unexpected 401 from {} {} is url/clientid/clientsecret correct?", requestContext.getMethod(), requestContext.getUri());
        if (headerValue.startsWith(BEARER_PREFIX)) {
            // if we get 401 we assume the token was invalid, however this should actually never happen as the token
            // manager should have already refreshed the token
            final String token = headerValue.substring(BEARER_PREFIX.length());
            tokenManager.expireToken(token);
        }
    }
}
