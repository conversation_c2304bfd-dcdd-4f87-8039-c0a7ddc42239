package com.sast.store.external.dckeycloak;

import jakarta.ws.rs.BadRequestException;
import jakarta.ws.rs.core.Form;
import org.keycloak.OAuth2Constants;
import org.keycloak.admin.client.token.TokenService;
import org.keycloak.representations.AccessTokenResponse;

import java.net.URI;
import java.time.Duration;
import java.time.Instant;

// this class should be one instance per token scope / rest api
public class ClientCredentialsTokenManager {
    // refresh token 60 seconds before it expires, so it doesn't suddenly expire during a request
    private static final Duration MIN_VALIDITY = Duration.ofSeconds(60);

    private AccessTokenResponse currentToken;
    private Instant expirationTime;
    private Instant refreshExpirationTime;
    private final TokenService tokenService;
    private final String realm;
    private final String clientId;
    private final String clientSecret;

    public ClientCredentialsTokenManager(final URI serverUrl, final String realm, final String clientId, final String clientSecret,
        final TokenService tokenService) {
        this.realm = realm;
        this.clientId = clientId;
        this.clientSecret = clientSecret;
        this.tokenService = tokenService;
    }

    public String getAccessTokenString() {
        return getAccessToken().getToken();
    }

    public synchronized AccessTokenResponse getAccessToken() {
        if (currentToken == null) {
            getNewToken();
        } else if (isTokenExpired()) {
            refreshToken();
        }
        return currentToken;
    }

    public AccessTokenResponse getNewToken() {
        final Form form = new Form()
            .param(OAuth2Constants.GRANT_TYPE, OAuth2Constants.CLIENT_CREDENTIALS)
            .param(OAuth2Constants.CLIENT_ID, clientId)
            .param(OAuth2Constants.CLIENT_SECRET, clientSecret);
        final Instant requestTime = Instant.now();
        synchronized (this) {
            currentToken = tokenService.grantToken(realm, form.asMap());
            expirationTime = requestTime.plusSeconds(currentToken.getExpiresIn());
            refreshExpirationTime = requestTime.plusSeconds(currentToken.getRefreshExpiresIn());
        }
        return currentToken;
    }

    public synchronized AccessTokenResponse refreshToken() {
        if (currentToken.getRefreshToken() == null || isRefreshTokenExpired()) {
            return getNewToken();
        }

        final Form form = new Form()
            .param(OAuth2Constants.GRANT_TYPE, OAuth2Constants.REFRESH_TOKEN)
            .param(OAuth2Constants.REFRESH_TOKEN, currentToken.getRefreshToken());

        try {
            final Instant requestTime = Instant.now();
            currentToken = tokenService.refreshToken(realm, form.asMap());
            expirationTime = requestTime.plusSeconds(currentToken.getExpiresIn());
            return currentToken;
        } catch (final BadRequestException e) {
            return getNewToken();
        }
    }

    private synchronized boolean isTokenExpired() {
        return !Instant.now().plus(MIN_VALIDITY).isBefore(expirationTime);
    }

    private synchronized boolean isRefreshTokenExpired() {
        return !Instant.now().plus(MIN_VALIDITY).isBefore(refreshExpirationTime);
    }

    public synchronized void expireToken(final String token) {
        if (currentToken == null) {
            return;
        }
        if (token.equals(currentToken.getToken())) {
            // force new token on next request
            expirationTime = Instant.now();
        }
    }
}
