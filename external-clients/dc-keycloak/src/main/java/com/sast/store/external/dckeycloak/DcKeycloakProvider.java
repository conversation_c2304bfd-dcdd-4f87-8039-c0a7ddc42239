package com.sast.store.external.dckeycloak;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sast.store.commons.jerseyclient.ClientRequestResponseLogger;
import com.sast.store.commons.jerseyclient.RestMetricsJerseyClientInterceptor;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.ws.rs.client.Client;
import jakarta.ws.rs.client.WebTarget;
import org.glassfish.jersey.client.ClientProperties;
import org.glassfish.jersey.client.proxy.WebResourceFactory;
import org.glassfish.jersey.jackson.internal.jackson.jaxrs.json.JacksonJaxbJsonProvider;
import org.keycloak.admin.client.resource.RealmResource;
import org.keycloak.admin.client.resource.RealmsResource;
import org.keycloak.admin.client.token.TokenService;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class DcKeycloakProvider {

    @Inject
    private DcKeycloakConfiguration configuration;

    @Inject
    @Named("externalRestClient")
    private Client client;

    @Inject
    private ClientRequestResponseLogger requestResponseLogger;

    @Inject
    private RestMetricsJerseyClientInterceptor metricsInterceptor;

    @Bean
    public RealmResource dcKeycloak() {
        return getProxy(RealmsResource.class).realm(configuration.realm());
    }

    public TokenService tokenService() {
        final WebTarget webTarget = client.target(configuration.url())
            .register(requestResponseLogger)
            .property(metricsInterceptor.getName(), metricsInterceptor.load(TokenService.class, configuration.url()));
        return WebResourceFactory.newResource(TokenService.class, webTarget);
    }

    private <T> T getProxy(final Class<T> proxyInterface) {
        final JacksonJaxbJsonProvider jsonProvider = new JacksonJaxbJsonProvider();
        jsonProvider.setMapper(new ObjectMapper());
        final WebTarget webTarget = client
            .target(configuration.url())
            .register(jsonProvider)
            .register(requestResponseLogger)
            .property(metricsInterceptor.getName(), metricsInterceptor.load(proxyInterface, configuration.url()))
            .register(new BearerAuthFilter(
                new ClientCredentialsTokenManager(configuration.url(), configuration.realm(), configuration.clientId(),
                    configuration.clientSecret(), tokenService())))
            // because keycloak api uses DELETE with body
            .property(ClientProperties.SUPPRESS_HTTP_COMPLIANCE_VALIDATION, true);
        return WebResourceFactory.newResource(proxyInterface, webTarget);
    }

}
