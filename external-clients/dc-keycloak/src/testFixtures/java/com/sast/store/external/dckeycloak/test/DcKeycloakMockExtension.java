package com.sast.store.external.dckeycloak.test;

import com.github.tomakehurst.wiremock.common.ClasspathFileSource;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.github.tomakehurst.wiremock.junit5.WireMockExtension;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.delete;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching;

public class DcKeycloakMockExtension extends WireMockExtension {

    public static final int PORT = 34561;

    private static DcKeycloakMockExtension instance;

    public DcKeycloakMockExtension() {
        super(WireMockExtension
            .extensionOptions()
            .options(WireMockConfiguration.options()
                .fileSource(new ClasspathFileSource("dckeycloak"))
                .port(PORT))
            .configureStaticDsl(true));
        instance = this;
    }

    public static DcKeycloakMockExtension instance() {
        return instance;
    }

    public static DcKeycloakMockExtension withDefaultResponse() {
        withTokenResponse();
        withGetUsersResponse();
        withGetClientRolesResponse();
        withCreateUserResponse();
        withAssignRolesResponse();
        withUnassignRolesResponse();
        return instance;
    }

    public static DcKeycloakMockExtension withTokenResponse() {
        instance.stubFor(post(urlPathMatching("/realms/realm/protocol/openid-connect/token"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody(
                    """
                        {
                          "access_token": "dummytoken",
                          "expires_in": 300,
                          "token_type": "Bearer",
                          "id_token": "dummyidtoken",
                          "not-before-policy": 1708077955,
                          "scope": ""
                        }""")
                .withTransformers("response-template")));

        return instance;
    }

    public static DcKeycloakMockExtension withGetUsersEmptyResponse() {
        instance.stubFor(get(urlPathMatching("/admin/realms/realm/users"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody("[]")
                .withTransformers("response-template")));

        return instance;
    }

    public static DcKeycloakMockExtension withGetUsersResponse() {
        instance.stubFor(get(urlPathMatching("/admin/realms/realm/users"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody(
                    """
                        [{
                            "id": "d4e22f66-a386-40e2-9e17-b4a5add5dac0",
                            "username": "<EMAIL>",
                            "firstName": "Azena Dev",
                            "lastName": "Tester",
                            "email": "<EMAIL>",
                            "emailVerified": false,
                            "createdTimestamp": 1688642702865,
                            "enabled": true
                        }]""")
                .withTransformers("response-template")));

        return instance;
    }

    public static DcKeycloakMockExtension withCreateUserResponse() {
        instance.stubFor(post(urlPathMatching("/admin/realms/realm/users"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody(
                    "{}")
                .withTransformers("response-template")));

        return instance;
    }

    public static DcKeycloakMockExtension withGetClientRolesResponse() {
        instance.stubFor(get(urlPathMatching("/admin/realms/realm/clients/.*/roles"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody(
                    """
                        [
                            {
                                "id": "bd3d709c-028b-4d94-b065-48f53718f85e",
                                "name": "role1",
                                "description": "",
                                "composite": false,
                                "clientRole": true,
                                "containerId": "b16d2c3d-7e41-4b15-9023-85273d93771d"
                            },
                            {
                                "id": "630d9cd2-762d-43c5-9c7a-81086261fc0e",
                                "name": "role2",
                                "description": "",
                                "composite": false,
                                "clientRole": true,
                                "containerId": "b16d2c3d-7e41-4b15-9023-85273d93771d"
                            },
                            {
                                "id": "054fbff3-6945-42b7-99c3-287821537d16",
                                "name": "role3",
                                "description": "",
                                "composite": true,
                                "clientRole": true,
                                "containerId": "b16d2c3d-7e41-4b15-9023-85273d93771d"
                            }
                        ]""")
                .withTransformers("response-template")));

        return instance;
    }

    public static DcKeycloakMockExtension withAssignRolesResponse() {
        instance.stubFor(post(urlPathMatching("/admin/realms/realm/users/[^/]*/role-mappings/clients/[^/]*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody(
                    "{}")
                .withTransformers("response-template")));

        return instance;
    }

    public static DcKeycloakMockExtension withUnassignRolesResponse() {
        instance.stubFor(delete(urlPathMatching("/admin/realms/realm/users/[^/]*/role-mappings/clients/[^/]*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withTransformers("response-template")));

        return instance;
    }
}
