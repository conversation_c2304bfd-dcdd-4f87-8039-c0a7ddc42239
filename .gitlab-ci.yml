stages:
  - prepare
  - test
  - publish
  - deploy
  - post-deploy
  - maintenance

variables:
  GRADLE_USER_HOME: "$CI_PROJECT_DIR/.gradle"
  ECR_REPO_URL: 836989565586.dkr.ecr.eu-central-1.amazonaws.com
  BRANCH: "${CI_COMMIT_BRANCH}"
  BUILD_TAG: "${CI_COMMIT_SHORT_SHA}-${CI_COMMIT_REF_SLUG}"
  BUILD_TAG_MASTER: "${CI_COMMIT_SHORT_SHA}"
  ORG_GRADLE_PROJECT_nexusUsername: "${NEXUS_MVN_REPO_USER}"
  ORG_GRADLE_PROJECT_nexusPassword: "${NEXUS_MVN_REPO_PWD}"
  ORG_GRADLE_PROJECT_nexusUrl: "${NEXUS_MVN_REPO_URL}"

workflow:
  rules:
    - if: $CI_COMMIT_BRANCH

#default cache is read only, so we don't spend time on zipping and uploading it
cache:
  policy: pull
  paths:
    - $GRADLE_USER_HOME
    - frontend/.yarn/cache
default:
  image: docker.sastdev.net/sast/ci/java-21-docker-builder:latest
  tags:
    - sast-cd

before_script:
  - mkdir -p $GRADLE_USER_HOME
  - echo "127.0.0.1 $(hostname)" >> /etc/hosts # neccessary because we have no DNS mapping in gitlab runner. Tests will fail without

Buildvariables:
  stage: prepare
  script:
    - export COMMIT_TIME=$(echo ${CI_COMMIT_TIMESTAMP:0:11} | sed 's/[-:T]//g')
    - export ECR_TAG=$([ "$BRANCH" == "master" ] && echo "$COMMIT_TIME-$BUILD_TAG_MASTER" || echo "$COMMIT_TIME-$BUILD_TAG")
    - echo "COMMIT_TIME=$COMMIT_TIME"
    - echo "COMMIT_TIME=$COMMIT_TIME" >> build.env
    - echo "ECR_TAG=$ECR_TAG"
    - echo "ECR_TAG=$ECR_TAG" >> build.env
  artifacts:
    reports:
      dotenv: build.env
  cache: {} # disable cache

#rebuild and upload cache
Build cache:
  stage: prepare
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
  script: ./gradlew assemble testClasses testFixturesClasses --console=plain
  cache:
    policy: push
    paths:
      - $GRADLE_USER_HOME
      - frontend/.yarn/cache
  needs: []

Test 1/4:
  extends: .test_template
  script: ./gradlew checkShard violations -PselectedShard=1 -PtotalShards=4 --console=plain

Test 2/4:
  extends: .test_template
  script: ./gradlew checkShard violations -PselectedShard=2 -PtotalShards=4 --console=plain

Test 3/4:
  extends: .test_template
  script: ./gradlew checkShard violations -PselectedShard=3 -PtotalShards=4 --console=plain

Test 4/4:
  extends: .test_template
  script: ./gradlew checkShard violations -PselectedShard=4 -PtotalShards=4 --console=plain

.test_template:
  stage: test
  artifacts:
    when: always
    reports:
      junit:
        - "**/build/test-results/test/*.xml"
        - "**/build/test-results/integrationTest/*.xml"
      codequality: "build/code-climate-file.json"
  needs:
    - Buildvariables

.ui_visual_tests_template:
  stage: test
  image: docker.sastdev.net/sast/store/replatforming/boss-store-playwright-builder:v1.52.0-jammy
  variables:
    PLAYWRIGHT_FLAGS: ""
  script:
    - 'echo -e "npmRegistries:\n  //nexus.ci.sastdev.net/repository/npm-all/:\n    npmAlwaysAuth: true\n    npmAuthToken: ${YARN_NPM_AUTH_TOKEN}" >> ~/.yarnrc.yml'
    - docker-compose up -d --force-recreate --always-recreate-deps --build
    - docker wait boss-store-keycloak-startup
    - cd $CI_PROJECT_DIR/frontend/ui
    - yarn install --immutable
    - yarn build --mode playwright
    - yarn test:ui $PLAYWRIGHT_FLAGS
  after_script:
    - docker-compose logs --no-color > docker-compose.log
    - docker-compose down
    - docker system prune -a --volumes -f
  artifacts:
    when: always
    expire_in: 3 days
    paths:
      - frontend/ui/test-results
      - frontend/ui/playwright-report
      - docker-compose.log
    reports:
      junit: frontend/ui/test-results/rspec.xml

Test UI:
  extends: .ui_visual_tests_template

Test UI (Update):
  extends: .ui_visual_tests_template
  stage: maintenance
  variables:
    PLAYWRIGHT_FLAGS: "--update-snapshots"
  artifacts:
    paths:
      - frontend/ui/test-results
      - frontend/ui/playwright-report
      - docker-compose.log
      - frontend/ui/tests/**/*-snapshots/*
  rules:
    - when: manual
      allow_failure: true
  needs: []

Test UI (Push):
  stage: maintenance
  script:
    - git add frontend/ui/tests/ui
    - git status
    - git -c user.email="$GITLAB_USER_EMAIL" -c user.name="$GITLAB_USER_NAME" commit -m "Update screenshots"
    - git log -1
    - git push "https://gitops:$GITLAB_GITOPS_TOKEN@$CI_SERVER_HOST/$CI_PROJECT_PATH" HEAD:$CI_COMMIT_BRANCH
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: never
    - if: $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH
  allow_failure: true
  needs:
    - Test UI (Update)

Codeanalysis UI:
  stage: test
  image: docker.sastdev.net/sast/ci/node-lts-base-builder:latest
  script:
    - 'echo -e "npmRegistries:\n  //nexus.ci.sastdev.net/repository/npm-all/:\n    npmAlwaysAuth: true\n    npmAuthToken: ${YARN_NPM_AUTH_TOKEN}" >> ~/.yarnrc.yml'
    - cd frontend/ui
    - yarn install --immutable
    - yarn lint-ci
  artifacts:
    reports:
      codequality: gl-codequality.json
  allow_failure: true
  needs:
    - Buildvariables

Codeanalysis UI translations:
  stage: test
  image: docker.sastdev.net/sast/ci/node-lts-base-builder:latest
  script:
    - 'echo -e "npmRegistries:\n  //nexus.ci.sastdev.net/repository/npm-all/:\n    npmAlwaysAuth: true\n    npmAuthToken: ${YARN_NPM_AUTH_TOKEN}" >> ~/.yarnrc.yml'
    - cd frontend/ui
    - yarn install --immutable
    - yarn check-translations
  artifacts:
    reports:
      codequality: frontend/ui/i18n-report-codeclimate.json
  allow_failure: true
  needs:
    - Buildvariables

Publish images:
  stage: publish
  script:
    - >
      ./gradlew jib
      -Pversion=${ECR_TAG}
      -PecrEndpoint=${ECR_REPO_URL}
      -x test
      --console=plain

    - echo "ECR_TAG = $ECR_TAG"
  tags:
    - sast-cd
  needs:
    - Buildvariables

Build UI:
  stage: publish
  image: docker.sastdev.net/sast/ci/node-lts-base-builder:latest
  script:
    - 'echo -e "npmRegistries:\n  //nexus.ci.sastdev.net/repository/npm-all/:\n    npmAlwaysAuth: true\n    npmAuthToken: ${YARN_NPM_AUTH_TOKEN}" >> ~/.yarnrc.yml'
    - cd $CI_PROJECT_DIR/frontend/ui
    - yarn install --immutable
    - yarn build
    - cd $CI_PROJECT_DIR/backoffice-ui
    - yarn install --immutable
    - yarn build
  artifacts:
    paths:
      - frontend/ui/dist
      - backoffice-ui/dist
  needs:
    - Buildvariables

Publish UI:
  stage: publish
  image: docker.sastdev.net/sast/ci/docker-builder:latest
  script:
    - mkdir ~/.docker
    - cp /docker/config.json ~/.docker/config.json
    # copy ui to cli container for deployment to cloudfront

    - cd frontend/ui
    - projectname=frontend-ui
    - docker build -t bossstore-$projectname --target cli .
    - docker tag bossstore-$projectname:latest ${ECR_REPO_URL}/bossstore-$projectname:latest
    - docker tag bossstore-$projectname:latest ${ECR_REPO_URL}/bossstore-$projectname:${ECR_TAG}
    - docker push ${ECR_REPO_URL}/bossstore-$projectname:latest
    - docker push ${ECR_REPO_URL}/bossstore-$projectname:${ECR_TAG}
    - cd ../..
    - cd backoffice-ui
    - projectname=backoffice-ui
    - docker build -t bossstore-$projectname --target cli .
    - docker tag bossstore-$projectname:latest ${ECR_REPO_URL}/bossstore-$projectname:latest
    - docker tag bossstore-$projectname:latest ${ECR_REPO_URL}/bossstore-$projectname:${ECR_TAG}
    - docker push ${ECR_REPO_URL}/bossstore-$projectname:latest
    - docker push ${ECR_REPO_URL}/bossstore-$projectname:${ECR_TAG}
    - cd ..
    - echo "ECR_TAG = $ECR_TAG"
  needs:
    - Buildvariables
    - Build UI

Changelog:
  stage: publish
  image: docker.sastdev.net/sast/ci/rest-tools:latest
  script:
    - export LAST_TAG=$(git describe --abbrev=0 --tag)
    - >
      git log --pretty="* %s (%an) %ai" ${LAST_TAG}..HEAD | ( grep -i "^\* [A-Z]\+-[0-9]\+.*" || echo "" ) > CHANGELOG.md
    - cat CHANGELOG.md
  artifacts:
    paths:
      - CHANGELOG.md
  needs:
    - Buildvariables

include:
  - project: "SAST/template/ci-cd-templates"
    file: "/templates/CrowdinSourcesUpload.gitlab-ci.yml"

Crowdin sources upload: # do not change the name, must match the job name in the template included above
  stage: publish
  variables:
    CROWDIN_PROJECT_ID: "491"
    TRANSLATIONS_BASE_PATH: "frontend/ui/src/locales"
    TRANSLATIONS_SOURCE_FILE: "en.json"
  cache: {}
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
      when: on_success
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: manual
  allow_failure: true
  needs: []

.deploy-template:
  stage: deploy
  variables:
    DOWNSTREAM_PROJECT_ID: 678 #gns-config
    DOWNSTREAM_BRANCH: master # branch to trigger
  needs:
    - Publish images
    - Publish UI
    - Buildvariables

Deploy DEV 🚀:
  extends: .deploy-template
  script:
    - >
      curl --request POST
      --form token=$CI_JOB_TOKEN
      --form ref=$DOWNSTREAM_BRANCH
      --form variables[ECR_TAG]=$ECR_TAG
      --form variables[BRANCH_NAME]=$BRANCH
      --form variables[ENV]='dev'
      https://gitlab.sastdev.net/api/v4/projects/$DOWNSTREAM_PROJECT_ID/trigger/pipeline
  rules:
    - when: manual
      allow_failure: true

Deploy DEMO 💥:
  extends: .deploy-template
  script:
    - >
      curl --request POST
      --form token=$CI_JOB_TOKEN
      --form ref=$DOWNSTREAM_BRANCH
      --form variables[ECR_TAG]=$ECR_TAG
      --form variables[BRANCH_NAME]=$BRANCH
      --form variables[ENV]='demo'
      https://gitlab.sastdev.net/api/v4/projects/$DOWNSTREAM_PROJECT_ID/trigger/pipeline
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
      when: manual
      allow_failure: true

Deploy LIVE 💥:
  extends: .deploy-template
  script:
    - >
      curl --request POST
      --form token=$CI_JOB_TOKEN
      --form ref=$DOWNSTREAM_BRANCH
      --form variables[ECR_TAG]=$ECR_TAG
      --form variables[BRANCH_NAME]=$BRANCH
      --form variables[ENV]='live'
      https://gitlab.sastdev.net/api/v4/projects/$DOWNSTREAM_PROJECT_ID/trigger/pipeline
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
      when: manual
      allow_failure: false

Tag release:
  stage: deploy
  image: registry.gitlab.com/gitlab-org/release-cli:latest
  rules:
    - if: $CI_COMMIT_BRANCH == 'master'
      when: always
  script:
    - >
      release-cli create
      --name "$ECR_TAG"
      --description CHANGELOG.md
      --tag-name "$ECR_TAG"
      --ref "$CI_COMMIT_SHA"
  needs:
    - Buildvariables
    - Changelog
    - Deploy LIVE 💥

.crowdin-publish-template:
  stage: deploy
  image: docker.sastdev.net/sast/ci/crowdin-cli-builder:latest
  before_script: []
  after_script: []
  variables:
    CROWDIN_PROJECT_ID: "491"
  script:
    # release translations as distribution
    - >
      crowdin distribution release ${CROWDIN_DISTRIBUTION_ID}
      --token="${CROWDIN_ACCESS_TOKEN}" 
      --project-id="${CROWDIN_PROJECT_ID}"
      --base-url="https://bosch.api.crowdin.com" 
      --no-progress
  cache: {}
  allow_failure: true
  rules:
    - if: $CI_COMMIT_BRANCH == 'master'
      when: on_success

Crowdin publish DEV:
  extends: .crowdin-publish-template
  variables:
    CROWDIN_DISTRIBUTION_ID: "e-15f5cc7f41ca56013cd73b33hc"
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
      when: on_success
    - if: '$CI_COMMIT_BRANCH != "master"'
      when: on_success
  needs:
    - Deploy DEV 🚀

Crowdin publish DEMO:
  extends: .crowdin-publish-template
  variables:
    CROWDIN_DISTRIBUTION_ID: "e-c40d8e38e14932ea54f23b33hc"
  needs:
    - Deploy DEMO 💥

Crowdin publish LIVE:
  extends: .crowdin-publish-template
  variables:
    CROWDIN_DISTRIBUTION_ID: "e-63782c560152b91376f03b33hc"
  needs:
    - Deploy LIVE 💥

DEV E2E Tests:
  stage: deploy
  image: mcr.microsoft.com/playwright:v1.52.0-noble
  variables:
    PLAYWRIGHT_PROJECT: dev
    SINGLEKEYID_CLIENT_ID: $SINGLEKEYID_CLIENT_ID
    SINGLEKEYID_CLIENT_SECRET: $SINGLEKEYID_CLIENT_SECRET
  script:
    - 'echo -e "npmRegistries:\n  //nexus.ci.sastdev.net/repository/npm-all/:\n    npmAlwaysAuth: true\n    npmAuthToken: ${YARN_NPM_AUTH_TOKEN}" >> ~/.yarnrc.yml'
    - cd $CI_PROJECT_DIR/e2e-testing
    - yarn install --immutable
    - yarn test:dev:e2e
  artifacts:
    when: always
    paths:
      - $CI_PROJECT_DIR/e2e-testing/test-results
      - $CI_PROJECT_DIR/e2e-testing/playwright-report
    expire_in: 3 days
    reports:
      junit: $CI_PROJECT_DIR/e2e-testing/test-results/rspec.xml
  rules:
    - when: manual
      allow_failure: true

DEMO E2E Tests:
  stage: deploy
  image: mcr.microsoft.com/playwright:v1.52.0-noble
  variables:
    PLAYWRIGHT_PROJECT: demo
    SINGLEKEYID_CLIENT_ID: $SINGLEKEYID_CLIENT_ID
    SINGLEKEYID_CLIENT_SECRET: $SINGLEKEYID_CLIENT_SECRET
  script:
    - 'echo -e "npmRegistries:\n  //nexus.ci.sastdev.net/repository/npm-all/:\n    npmAlwaysAuth: true\n    npmAuthToken: ${YARN_NPM_AUTH_TOKEN}" >> ~/.yarnrc.yml'
    - cd $CI_PROJECT_DIR/e2e-testing
    - yarn install --immutable
    - yarn test:demo:e2e
  artifacts:
    when: always
    paths:
      - $CI_PROJECT_DIR/e2e-testing/test-results
      - $CI_PROJECT_DIR/e2e-testing/playwright-report
    expire_in: 3 days
    reports:
      junit: $CI_PROJECT_DIR/e2e-testing/test-results/rspec.xml
  needs:
    - Deploy DEMO 💥
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
      when: manual
      allow_failure: false
