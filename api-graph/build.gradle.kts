plugins {
    id("bossstore.subproject-conventions")
    id("org.springframework.boot") version "3.4.3"
    id("com.google.cloud.tools.jib") version "3.4.4"
    id("com.netflix.dgs.codegen") version "8.0.1"
}

dependencies {
	implementation(project(":commons"))
	implementation(project(":commons:base-webapp"))
	implementation(project(":commons:jersey-client"))
	implementation(project(":commons:tenant"))
	implementation(project(":contractmanagement-api"))
	implementation(project(":entitlementmanagement-api"))
	implementation(project(":ordermanagement-api"))
	implementation(project(":productmanagement-api"))
	implementation(enforcedPlatform(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES))
	implementation(platform("com.netflix.graphql.dgs:graphql-dgs-platform-dependencies:10.0.4"))
	implementation("org.springframework.boot:spring-boot-starter")
    implementation("org.springframework.boot:spring-boot-starter-graphql")
    implementation("org.springframework.boot:spring-boot-starter-jersey")
	implementation("org.springframework.boot:spring-boot-starter-security")
    implementation("org.springframework.boot:spring-boot-starter-validation")
	implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("com.graphql-java:graphql-java-extended-scalars:22.0")
	implementation("org.glassfish.jersey.ext:jersey-proxy-client")
    implementation("org.apache.commons:commons-text:1.13.0")
    implementation("org.apache.commons:commons-collections4:4.4")

    testImplementation(testFixtures(project(":contractmanagement-api")))
    testImplementation(testFixtures(project(":entitlementmanagement-api")))
    testImplementation(testFixtures(project(":ordermanagement-api")))
    testImplementation(testFixtures(project(":productmanagement-api")))
    testImplementation(testFixtures(project(":testing-commons")))
    testImplementation(project(":commons:tenant"))
	testImplementation("org.springframework.boot:spring-boot-starter-test")
	testImplementation("org.springframework.security:spring-security-test")
    testImplementation("org.springframework.graphql:spring-graphql-test")
    testImplementation("org.springframework.boot:spring-boot-starter-webflux")
	testImplementation("io.rest-assured:rest-assured")
	testImplementation("com.tngtech.keycloakmock:mock-junit5:0.16.0")
    testImplementation("org.wiremock.integrations:wiremock-spring-boot:3.9.0")
}

tasks.generateJava {
    schemaPaths.add("${projectDir}/src/main/resources/graphql")
    packageName = "com.sast.store.apigraph.codegen"
    typeMapping.putAll(mapOf(
        "BigDecimal" to "java.math.BigDecimal",
        "Locale" to "java.util.Locale",
        "Period" to "java.time.Period",
        "Url" to "java.net.URL"
    ))
}

jib {
    from.image = "gcr.io/distroless/java21-debian12"
    to.image = "${property("ecrEndpoint")}/bossstore-${project.name}:${project.version}"
}

tasks.bootTestRun {
    args = listOf("--spring.profiles.active=local")
}
