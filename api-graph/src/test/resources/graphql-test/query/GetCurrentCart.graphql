query FullCurrentCart {
  currentCart {
    id
    lineItems {
      lineItemId
      name
      productId
      sku
      variant {
        sku
        externalProductId
        price {
          value
          currencyCode
        }
        bundleAmount
        licenseType
        runtime
        noticePeriod
        name
        description
        features
        agreements {
          name
          url
          linkType
        }
        priceList
        entitlements
        addons {
          id
          name
          description
          addonVariants {
            sku
            name
            description
            price {
              value
              currencyCode
            }
          }
        }
      }
      itemPrice {
        value
        currencyCode
      }
      quantity
      totalPrice {
        value
        currencyCode
      }
      addons {
        addonLineItemId
        parentLineItemId
        name
        quantity
        addonVariant {
          sku
          name
          description
          price {
            value
            currencyCode
          }
        }
        itemPrice {
          value
          currencyCode
        }
        totalPrice {
          value
          currencyCode
        }
      }
    }
    totalPrice {
      value
      currencyCode
    }
    sellerCompany {
      name
      country
    }
    billingAddress {
      city
      postalCode
      streetName
      streetNumber
      state
      region
      country
      email
    }
  }
}
