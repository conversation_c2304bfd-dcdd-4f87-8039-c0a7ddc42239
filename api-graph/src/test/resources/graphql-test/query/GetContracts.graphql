query GetContracts {
  contracts {
    count
    results {
      companyId
      contractId
      orderNumber
      productId
      startDate
      projectedEndDate
      endDate
      contractType
      contractState
      contractPeriod
      noticePeriod
      addons {
        contractId
        productId
        startDate
        endDate
        contractType
        contractPeriod
        noticePeriod
      }
      licenses {
        licenseId
        licenseModel
        contractId
        contract {
          contractId
        }
        ... on BcCentralLicense {
          name
          emails
        }
        ... on DcKeycloakLicense {
          firstname
          lastname
          email
        }
        ... on LitmosLicense {
          firstname
          lastname
          email
        }
      }
    }
  }
}
