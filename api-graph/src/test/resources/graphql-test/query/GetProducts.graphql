query GetProducts($expand: Boolean) {
  productSearch(expand: $expand) {
    count
    results {
      id
      product {
        id
        name
        description
        productType
        images
        variants {
          sku
          externalProductId
          price {
            value
            currencyCode
          }
          bundleAmount
          licenseType
          runtime
          noticePeriod
          name
          description
          features
          agreements {
            name
            url
            linkType
          }
          priceList
          entitlements
          addons {
            id
            name
            description
            addonVariants {
              sku
              name
              description
              price {
                value
                currencyCode
              }
            }
          }
        }
        externalDocuments {
          name
          url
          linkType
        }
        sellerCompany {
          name
          country
        }
        categories
      }
    }
  }
}
