server:
  port: 8086

spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://sso.mp-dc-d.com/auth/realms/rexroth
  graphql:
    graphiql:
      enabled: true
    schema:
      introspection:
        enabled: true
    cors:
      allowed-headers: Authorization, Content-Type, X-Tenant
      allowed-methods: GET, POST
      allowed-origins: http://localhost:5173

bossstore:
  contractmanagementUrl: http://localhost:8084/rest
  entitlementmanagementUrl: http://localhost:8085/rest
  ordermanagementUrl: http://localhost:8083/rest
  productmanagementUrl: http://localhost:8082/rest
  apigraph:
    instrumentation:
      max-query-depth: 15
      max-query-complexity: 80
      max-query-execution-cost: 10000
