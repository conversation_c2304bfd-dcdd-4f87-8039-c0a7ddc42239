package com.sast.store.apigraph.config;

import com.sast.store.apigraph.AbstractComponentTest;
import com.sast.store.commons.tenant.api.Tenant;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import org.junit.jupiter.api.Test;

import static com.sast.store.commons.tenant.TenantHeaderConstants.X_TENANT;

class WebSecurityConfigurationTest extends AbstractComponentTest {

    @Test
    void givenValidKeycloakToken_whenRequestSent_thenResponseIs200() {
        RestAssured.given()
            .header(X_TENANT, Tenant.REXROTH.id())
            .auth()
            .preemptive()
            .oauth2(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .body("""
              {
                "query": "{ __typename }"
              }""")
            .when()
            .post(host + "/graphql")
            .then()
            .statusCode(200);
    }

    @Test
    void givenInvalidKeycloakToken_whenRequestSent_thenResponseIs403() {
        RestAssured.given()
            .header(X_TENANT, Tenant.REXROTH.id())
            .auth()
            .preemptive()
            .oauth2(INVALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .body("""
              {
                "query": "{ __typename }"
              }""")
            .when()
            .post(host + "/graphql")
            .then()
            .statusCode(403);
    }
}
