package com.sast.store.apigraph.instrumentation;

import com.sast.store.apigraph.AbstractComponentTest;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.contractmanagement.test.ContractServiceMockExtension;
import com.sast.store.entitlementmanagement.EntitlementmanagementMockExtension;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.test.tester.HttpGraphQlTester;
import org.springframework.test.context.ActiveProfiles;

import static com.sast.store.commons.tenant.TenantHeaderConstants.X_TENANT;

@ActiveProfiles("complex-query")
class QueryExecutionCostInstrumentationTest extends AbstractComponentTest {

    @Autowired
    private HttpGraphQlTester tester;

    @Test
    void shouldBeTooComplexForExecution() {
        ContractServiceMockExtension.withGetContractsResponse();
        EntitlementmanagementMockExtension.withGetLicensesResponse();

        tester.mutate()
            .headers(headers -> {
                headers.set(X_TENANT, Tenant.REXROTH.id());
                headers.setBearerAuth(VALID_KEYCLOAK_TOKEN);
            })
            .build()
            .documentName("query/GetContracts")
            .execute()
            .errors()
            .expect(e-> e.getMessage().contains("INTERNAL_ERROR"))
            .verify()
            .path("contracts");
    }
}