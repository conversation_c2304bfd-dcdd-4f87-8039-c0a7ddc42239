package com.sast.store.apigraph.data;

import com.sast.store.apigraph.AbstractComponentTest;
import com.sast.store.apigraph.codegen.types.AuthorizationInformation;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.ordermanagement.api.AddressDto;
import com.sast.store.ordermanagement.api.CartDto;
import com.sast.store.ordermanagement.api.CompanyDto;
import com.sast.store.ordermanagement.api.MoneyDto;
import com.sast.store.ordermanagement.test.OrdermanagementMockExtension;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.test.tester.HttpGraphQlTester;

import java.util.List;
import java.util.Map;

import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson;
import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.putRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo;
import static com.sast.store.commons.tenant.TenantHeaderConstants.X_TENANT;
import static org.assertj.core.api.Assertions.assertThat;

class CartDataFetcherTest extends AbstractComponentTest {

    @Autowired
    private HttpGraphQlTester tester;

    @Test
    void givenCartResponse_whenQueryCurrentCart_thenCartIsReturned() {
        OrdermanagementMockExtension.withGetCurrentCartResponse();

        tester.mutate()
                .headers(headers -> {
                    headers.set(X_TENANT, Tenant.REXROTH.id());
                    headers.setBearerAuth(VALID_KEYCLOAK_TOKEN);
                })
                .build()
                .documentName("query/GetCurrentCart")
                .execute()
                .path("currentCart")
                .matchesJsonStrictly(loadJson("/graphql-test/response/GetCurrentCart.json"));
    }

    @Test
    void givenLineItemsIsNull_whenQueryCurrentCart_thenReturnedLineItemsIsEmpty() {
        OrdermanagementMockExtension.withGetCurrentCartResponse(CartDto.builder()
                .id("dummy-cart-id")
                .lineItems(null)
                .addons(null)
                .totalPrice(MoneyDto.builder()
                        .value(0.0)
                        .currencyCode("EUR")
                        .build())
                .sellerCompany(CompanyDto.builder()
                        .name("UMTeter GmbH")
                        .build())
                .billingAddress(AddressDto.builder().build())
                .build());

        tester.mutate()
                .headers(headers -> {
                    headers.set(X_TENANT, Tenant.REXROTH.id());
                    headers.setBearerAuth(VALID_KEYCLOAK_TOKEN);
                })
                .build()
                .documentName("query/GetCurrentCart")
                .execute()
                .path("currentCart")
                .matchesJsonStrictly(loadJson("/graphql-test/response/LineItemsNullCurrentCart.json"));

    }

    @Test
    void givenItemToAdd_whenMutateAddToCurrentCart_thenAddRequestIsSent() {
        OrdermanagementMockExtension.withAddToCurrentCartResponse();

        tester.mutate()
                .headers(headers -> {
                    headers.set(X_TENANT, Tenant.REXROTH.id());
                    headers.setBearerAuth(VALID_KEYCLOAK_TOKEN);
                })
                .build()
                .documentName("mutation/AddToCurrentCart")
                .variables(Map.of(
                        "sku", "test-sku",
                        "quantity", 1))
                .execute()
                .path("currentCart")
                .matchesJsonStrictly(loadJson("/graphql-test/response/AddToCurrentCart.json"));

        OrdermanagementMockExtension.instance()
                .verify(postRequestedFor(urlPathEqualTo("/rest/cart/"))
                        .withRequestBody(equalToJson("""
                                {
                                  "sku": "test-sku",
                                  "quantity": 1,
                                  "addons": []
                                }""")));
    }

    @Test
    void givenItemToUpdate_whenMutateUpdateCurrentCart_thenUpdateRequestIsSent() {
        OrdermanagementMockExtension.withUpdateCurrentCartResponse();

        tester.mutate()
                .headers(headers -> {
                    headers.set(X_TENANT, Tenant.REXROTH.id());
                    headers.setBearerAuth(VALID_KEYCLOAK_TOKEN);
                })
                .build()
                .documentName("mutation/UpdateCurrentCart")
                .variables(Map.of(
                        "lineItemId", "test-id",
                        "quantity", 1))
                .execute()
                .path("currentCart")
                .matchesJsonStrictly(loadJson("/graphql-test/response/UpdateCurrentCart.json"));

        OrdermanagementMockExtension.instance()
                .verify(putRequestedFor(urlPathEqualTo("/rest/cart/"))
                        .withRequestBody(equalToJson("""
                                {
                                  "lineItemId": "test-id",
                                  "quantity": 1
                                }""")));
    }

    @Test
    void givenCheckoutDetails_whenMutateCheckoutCurrentCart_thenCheckoutRequestIsSent() {
        OrdermanagementMockExtension.withCheckoutCartResponse("test-payment-id");

        tester.mutate()
                .headers(headers -> {
                    headers.set(X_TENANT, Tenant.REXROTH.id());
                    headers.setBearerAuth(VALID_KEYCLOAK_TOKEN);
                })
                .build()
                .documentName("mutation/CheckoutCurrentCart")
                .variables(Map.of(
                        "paymentMethodId", "BOSCH_TRANSFER/SEPA_CREDIT",
                        "notes", List.of("Note 1", "Note 2")))
                .execute()
                .path("result")
                .entity(AuthorizationInformation.class)
                .satisfies(authorizationInformation -> {
                    assertThat(authorizationInformation)
                            .isNotNull();
                    assertThat(authorizationInformation.getPaymentId())
                            .isEqualTo("test-payment-id");
                    assertThat(authorizationInformation.getRedirectUrl())
                            .isNull();
                });

        OrdermanagementMockExtension.instance()
                .verify(postRequestedFor(urlPathEqualTo("/rest/cart/checkout"))
                        .withRequestBody(equalToJson("""
                                {
                                  "paymentMethodId": "BOSCH_TRANSFER/SEPA_CREDIT",
                                  "notes": ["Note 1", "Note 2"]
                                }""")));
    }
}
