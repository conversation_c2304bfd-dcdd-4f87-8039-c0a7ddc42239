package com.sast.store.apigraph;

import io.restassured.RestAssured;
import org.junit.jupiter.api.Test;

import static org.hamcrest.Matchers.equalTo;

class HealthIndicatorComponentTest extends AbstractComponentTest {
    @Test
    void testInfoCheck() {
        RestAssured
            .given()
            .when()
            .get(host + "/actuator/info")
            .then()
            .statusCode(200);
    }

    @Test
    void testHealthCheck() {
        RestAssured
            .given()
            .when()
            .get(host + "/actuator/health")
            .then()
            .statusCode(200)
            .body("status", equalTo("UP"));
    }

    @Test
    void testReadynessCheck() {
        RestAssured
            .given()
            .when()
            .get(host + "/actuator/health/readiness")
            .then()
            .statusCode(200);
    }

    @Test
    void testLivenessCheck() {
        RestAssured
            .given()
            .when()
            .get(host + "/actuator/health/liveness")
            .then()
            .statusCode(200);
    }
}
