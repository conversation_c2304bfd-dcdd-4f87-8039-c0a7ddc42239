package com.sast.store.apigraph.data;

import com.sast.store.apigraph.AbstractComponentTest;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.ordermanagement.test.OrdermanagementMockExtension;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.test.tester.HttpGraphQlTester;

import static com.sast.store.commons.tenant.TenantHeaderConstants.X_TENANT;

class PaymentDataFetcherTest extends AbstractComponentTest {

    @Autowired
    private HttpGraphQlTester tester;

    @Test
    void shouldReturnPaymentConfig() {
        OrdermanagementMockExtension.withGetPaymentConfigResponse("BOSCH_TRANSFER/SEPA_CREDIT");

        tester.mutate()
                .headers(headers -> {
                    headers.set(X_TENANT, Tenant.REXROTH.id());
                    headers.setBearerAuth(VALID_KEYCLOAK_TOKEN);
                })
                .build()
                .documentName("query/GetPaymentConfig")
                .execute()
                .path("paymentConfig")
                .matchesJsonStrictly(loadJson("/graphql-test/response/GetPaymentConfig.json"));
    }

    @Test
    void shouldReturnPayment() {
        OrdermanagementMockExtension.withGetPaymentResponse();

        tester.mutate()
                .headers(headers -> {
                    headers.set(X_TENANT, Tenant.REXROTH.id());
                    headers.setBearerAuth(VALID_KEYCLOAK_TOKEN);
                })
                .build()
                .documentName("query/GetPayment")
                .variable("paymentId", "dummy-payment-id")
                .execute()
                .path("payment")
                .matchesJsonStrictly(loadJson("/graphql-test/response/GetPayment.json"));
    }
}
