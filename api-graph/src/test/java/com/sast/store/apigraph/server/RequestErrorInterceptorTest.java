package com.sast.store.apigraph.server;

import com.sast.store.apigraph.AbstractComponentTest;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.productmanagement.test.ProductServiceMockExtension;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.ResponseError;
import org.springframework.graphql.execution.ErrorType;
import org.springframework.graphql.test.tester.HttpGraphQlTester;

import java.util.List;
import java.util.Objects;

import static com.sast.store.commons.tenant.TenantHeaderConstants.X_TENANT;
import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.graphql.execution.ErrorType.FORBIDDEN;
import static org.springframework.graphql.execution.ErrorType.INTERNAL_ERROR;
import static org.springframework.graphql.execution.ErrorType.NOT_FOUND;
import static org.springframework.graphql.execution.ErrorType.UNAUTHORIZED;

class RequestErrorInterceptorTest extends AbstractComponentTest {

    static final List<ErrorType> EXPOSED_ERROR_TYPES = List.of(INTERNAL_ERROR, UNAUTHORIZED, FORBIDDEN, NOT_FOUND);

    @Autowired
    private HttpGraphQlTester tester;

    @Test
    void givenNoAuthentication_whenSendingRequest_thenResponseShouldNotExposeInternalDetails() {
        tester.mutate()
            .headers(headers -> {
                headers.set(X_TENANT, Tenant.REXROTH.id());
            })
            .build()
            .documentName("query/GetCurrentCart")
            .execute()
            .errors()
            .satisfy(errors ->
                assertThat(errors)
                    .isNotEmpty()
                    .allSatisfy(this::doesNotExposeDetails));
    }

    @Test
    void givenNoTenantHeader_whenSendingRequest_thenResponseShouldNotExposeInternalDetails() {
        tester.mutate()
            .headers(headers -> {
                headers.setBearerAuth(VALID_KEYCLOAK_TOKEN);
            })
            .build()
            .documentName("query/GetCurrentCart")
            .execute()
            .errors()
            .satisfy(errors ->
                assertThat(errors)
                    .isNotEmpty()
                    .allSatisfy(this::doesNotExposeDetails));
    }

    @Test
    void givenInvalidQuery_whenSendingRequest_thenResponseShouldNotExposeInternalDetails() {
        tester.mutate()
            .headers(headers -> {
                headers.set(X_TENANT, Tenant.REXROTH.id());
                headers.setBearerAuth(VALID_KEYCLOAK_TOKEN);
            })
            .build()
            .document("{ currentCart { invalidField } }")
            .execute()
            .errors()
            .satisfy(errors ->
                assertThat(errors)
                    .isNotEmpty()
                    .allSatisfy(this::doesNotExposeDetails));
    }

    @Test
    void givenDownstreamServerError_whenSendingRequest_thenResponseShouldNotExposeInternalDetails() {
        ProductServiceMockExtension.withGetProductServerErrorResponse(500);

        tester.mutate()
            .headers(headers -> {
                headers.set(X_TENANT, Tenant.REXROTH.id());
            })
            .build()
            .document("{ product(id: \"some-product-id\") { id } }")
            .execute()
            .errors()
            .satisfy(errors ->
                assertThat(errors)
                    .isNotEmpty()
                    .allSatisfy(this::doesNotExposeDetails));
    }

    @Test
    void givenDownstreamNotFoundError_whenSendingRequest_thenResponseShouldNotExposeInternalDetails() {
        ProductServiceMockExtension.withGetProductServerErrorResponse(404);

        tester.mutate()
            .headers(headers -> {
                headers.set(X_TENANT, Tenant.REXROTH.id());
            })
            .build()
            .document("{ product(id: \"some-product-id\") { id } }")
            .execute()
            .errors()
            .satisfy(errors ->
                assertThat(errors)
                    .isNotEmpty()
                    .allSatisfy(this::doesNotExposeDetails));
    }

    private void doesNotExposeDetails(final ResponseError responseError) {
        assertThat(responseError.getErrorType())
            .isIn(EXPOSED_ERROR_TYPES);
        assertThat(responseError.getLocations())
            .isEmpty();
        assertThat(responseError.getExtensions())
            .containsOnlyKeys("classification");

        if (Objects.equals(responseError.getErrorType(), ErrorType.INTERNAL_ERROR)) {
            assertThat(responseError.getPath())
                .isEmpty();
        }
    }
}
