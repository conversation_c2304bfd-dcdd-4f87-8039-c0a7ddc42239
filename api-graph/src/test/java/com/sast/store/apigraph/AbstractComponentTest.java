package com.sast.store.apigraph;

import com.sast.store.contractmanagement.test.ContractServiceMockExtension;
import com.sast.store.entitlementmanagement.EntitlementmanagementMockExtension;
import com.sast.store.ordermanagement.test.OrdermanagementMockExtension;
import com.sast.store.productmanagement.test.ProductServiceMockExtension;
import com.tngtech.keycloakmock.api.ServerConfig;
import com.tngtech.keycloakmock.junit5.KeycloakMockExtension;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.graphql.tester.AutoConfigureHttpGraphQlTester;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.util.StreamUtils;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Map;
import java.util.UUID;

import static com.tngtech.keycloakmock.api.TokenConfig.aTokenConfig;

@ExtendWith(ContractServiceMockExtension.class)
@ExtendWith(EntitlementmanagementMockExtension.class)
@ExtendWith(OrdermanagementMockExtension.class)
@ExtendWith(ProductServiceMockExtension.class)
@SpringBootTest(classes = ApigraphApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, properties = {
    // disable caching of health endpoints
    "endpoints.health.time-to-live=0",
    "bossstore.contractmanagementUrl=http://localhost:34643/rest",
    "bossstore.entitlementmanagementUrl=http://localhost:34644/rest",
    "bossstore.ordermanagementUrl=http://localhost:8083/rest",
    "bossstore.productmanagementUrl=http://localhost:34642/rest",
})
@AutoConfigureHttpGraphQlTester
@ActiveProfiles("test")
public abstract class AbstractComponentTest {
    protected static final String COMPANY_ID = UUID.randomUUID().toString();

    // CHECKSTYLE OFF: StaticVariableNameCheck
    protected static String VALID_KEYCLOAK_TOKEN;

    protected static String INVALID_KEYCLOAK_TOKEN;

    @RegisterExtension
    private static final KeycloakMockExtension KEYCLOAK_MOCK = new KeycloakMockExtension(
        ServerConfig.aServerConfig()
            .withPort(8000)
            .withDefaultRealm("baam")
            .build());

    @Value("http://localhost:${local.server.port}")
    protected String host;

    @BeforeEach
    public void setUpKeycloakToken() {
        setupKeycloakToken("VIEW_ORDER_DETAILS", "EDIT_PAYMENT_DETAILS", "VIEW_PRICE", "PLACE_ORDER", "MANAGE_BACKOFFICE", "DEFAULT");
    }

    protected static void setupKeycloakToken(final String... roles) {
        VALID_KEYCLOAK_TOKEN = KEYCLOAK_MOCK.getAccessToken(aTokenConfig()
            .withSubject("user")
            .withAuthorizedParty("bossstore-frontend")
            .withClaims(Map.of(
                "resource_access", Map.of("bossstore-backend", Map.of("roles", Arrays.asList(roles))),
                "name", "John Doe",
                "preferred_username", "7cb0fdd4-9503-41e0-a279-84ad098c8a00",
                "given_name", "John",
                "family_name", "Doe",
                "email", "<EMAIL>",
                "company_name", "THE Company",
                "communication_language", "de",
                "company_id", COMPANY_ID))
            .build());

        INVALID_KEYCLOAK_TOKEN = KEYCLOAK_MOCK.getAccessToken(aTokenConfig()
            .withSubject("user")
            .withAuthorizedParty("dronestore-frontend")
            .withClaims(Map.of(
                "resource_access", Map.of("dronestore-backend", Map.of("roles", Arrays.asList(roles))),
                "name", "John Doe",
                "preferred_username", "7cb0fdd4-9503-41e0-a279-84ad098c8a00",
                "given_name", "John",
                "family_name", "Doe",
                "email", "<EMAIL>",
                "company_name", "THE Company",
                "communication_language", "de",
                "company_id", COMPANY_ID))
            .build());
    }

    protected String loadJson(final String path) {
        try (final InputStream resourceInputStream = getClass().getResourceAsStream(path)) {
            if (resourceInputStream == null) {
                throw new FileNotFoundException(path);
            }
            return StreamUtils.copyToString(resourceInputStream, StandardCharsets.UTF_8);
        } catch (final IOException e) {
            throw new RuntimeException(e);
        }
    }
}
