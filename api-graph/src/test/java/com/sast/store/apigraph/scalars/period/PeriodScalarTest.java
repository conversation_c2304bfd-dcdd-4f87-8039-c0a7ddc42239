package com.sast.store.apigraph.scalars.period;

import com.sast.store.apigraph.scalars.CustomScalars;
import graphql.GraphQLContext;
import graphql.execution.CoercedVariables;
import graphql.language.Value;
import graphql.schema.Coercing;
import graphql.schema.CoercingParseLiteralException;
import graphql.schema.CoercingParseValueException;
import graphql.schema.CoercingSerializeException;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Period;
import java.util.Locale;
import java.util.stream.Stream;

import static graphql.language.BooleanValue.newBooleanValue;
import static graphql.language.StringValue.newStringValue;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

@ExtendWith(MockitoExtension.class)
class PeriodScalarTest {

    @Mock
    private CoercedVariables variables;

    @Mock
    private GraphQLContext graphQLContext;

    private final Coercing<?, ?> periodScalarCoercing = CustomScalars.Period.getCoercing();

    @ParameterizedTest
    @MethodSource
    void givenValidInput_whenSerialize_thenReturnExpectedResult(final Object input, final String expectedResult) {
        final var result = periodScalarCoercing.serialize(input, graphQLContext, Locale.ENGLISH);

        assertThat(result)
            .isEqualTo(expectedResult);
    }

    public static Stream<Arguments> givenValidInput_whenSerialize_thenReturnExpectedResult() {
        return Stream.of(
            Arguments.of("P7D", "P7D"),
            Arguments.of("P1Y", "P1Y"),
            Arguments.of(Period.ofDays(7), "P7D"));
    }

    @ParameterizedTest
    @MethodSource
    void givenInvalidInput_whenSerialize_thenThrowException(final Object input) {
        assertThatThrownBy(() -> periodScalarCoercing.serialize(input, graphQLContext, Locale.ENGLISH))
            .isInstanceOfAny(CoercingSerializeException.class, IllegalArgumentException.class);
    }

    public static Stream<Object> givenInvalidInput_whenSerialize_thenThrowException() {
        return Stream.of("", "UMTeter", null, 1);
    }

    @ParameterizedTest
    @MethodSource
    void givenValidInput_whenParseValue_thenReturnExpectedResult(final Object input, final Period expectedResult) {
        final var result = periodScalarCoercing.parseValue(input, graphQLContext, Locale.ENGLISH);

        assertThat(result)
            .isEqualTo(expectedResult);
    }

    public static Stream<Arguments> givenValidInput_whenParseValue_thenReturnExpectedResult() {
        return Stream.of(
            Arguments.of("P7D", Period.ofDays(7)),
            Arguments.of("P1Y", Period.ofYears(1)),
            Arguments.of(Period.ofDays(7), Period.ofDays(7)));
    }

    @ParameterizedTest
    @MethodSource
    void givenInvalidInput_whenParseValue_thenThrowException(final Object input) {
        assertThatThrownBy(() -> periodScalarCoercing.parseValue(input, graphQLContext, Locale.ENGLISH))
            .isInstanceOfAny(CoercingParseValueException.class, IllegalArgumentException.class);
    }

    public static Stream<Object> givenInvalidInput_whenParseValue_thenThrowException() {
        return Stream.of("", "UMTeter", null, 1);
    }

    @ParameterizedTest
    @MethodSource
    void givenValidInput_whenParseLiteral_thenReturnExpectedResult(final Value<?> input, final Period expectedResult) {
        final var result = periodScalarCoercing.parseLiteral(input, variables, graphQLContext, Locale.ENGLISH);

        assertThat(result)
            .isEqualTo(expectedResult);
    }

    public static Stream<Arguments> givenValidInput_whenParseLiteral_thenReturnExpectedResult() {
        return Stream.of(
            Arguments.of(newStringValue("P7D").build(), Period.ofDays(7)),
            Arguments.of(newStringValue("P1Y").build(), Period.ofYears(1)));
    }

    @ParameterizedTest
    @MethodSource
    void givenInvalidInput_whenParseLiteral_thenThrowException(final Value<?> input) {
        assertThatThrownBy(() -> periodScalarCoercing.parseLiteral(input, variables, graphQLContext, Locale.ENGLISH))
            .isInstanceOfAny(CoercingParseLiteralException.class, IllegalArgumentException.class);
    }

    public static Stream<Object> givenInvalidInput_whenParseLiteral_thenThrowException() {
        return Stream.of(
            newStringValue("").build(),
            newStringValue("UMTeter").build(),
            null,
            newBooleanValue(true).build());
    }

    @ParameterizedTest
    @MethodSource
    void givenValidInput_whenValueToLiteral_thenReturnExpectedResult(final Object input, final Value<?> expectedResult) {
        final var result = periodScalarCoercing.valueToLiteral(input, graphQLContext, Locale.ENGLISH);

        assertThat(result.isEqualTo(expectedResult))
            .isTrue();
    }

    public static Stream<Arguments> givenValidInput_whenValueToLiteral_thenReturnExpectedResult() {
        return Stream.of(
            Arguments.of("P7D", newStringValue("P7D").build()),
            Arguments.of(Period.ofYears(1), newStringValue("P1Y").build()));
    }

    @ParameterizedTest
    @MethodSource
    void givenInvalidInput_whenValueToLiteral_thenThrowException(final Value<?> input) {
        assertThatThrownBy(() -> periodScalarCoercing.valueToLiteral(input, graphQLContext, Locale.ENGLISH))
            .isInstanceOfAny(CoercingSerializeException.class, IllegalArgumentException.class);
    }

    public static Stream<Object> givenInvalidInput_whenValueToLiteral_thenThrowException() {
        return Stream.of(
            newStringValue("").build(),
            newStringValue("UMTeter").build(),
            null,
            newBooleanValue(true).build());
    }
}
