package com.sast.store.apigraph.data;

import com.sast.store.apigraph.AbstractComponentTest;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.entitlementmanagement.EntitlementmanagementMockExtension;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.test.tester.HttpGraphQlTester;

import static com.sast.store.commons.tenant.TenantHeaderConstants.X_TENANT;

class DefaultsDataFetcherTest extends AbstractComponentTest {

    @Autowired
    private HttpGraphQlTester tester;

    @Test
    void shouldReturnPaymentConfig() {
        EntitlementmanagementMockExtension.withGetDefaultsResponse();

        tester.mutate()
                .headers(headers -> {
                    headers.set(X_TENANT, Tenant.REXROTH.id());
                    headers.setBearerAuth(VALID_KEYCLOAK_TOKEN);
                })
                .build()
                .documentName("query/Defaults")
                .variable("contractIds", "test-contract-id")
                .execute()
                .path("defaults")
                .matchesJsonStrictly(loadJson("/graphql-test/response/GetDefaults.json"));

    }
}
