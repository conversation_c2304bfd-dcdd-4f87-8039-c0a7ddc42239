package com.sast.store.apigraph.data;

import com.sast.store.apigraph.AbstractComponentTest;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.ordermanagement.test.OrdermanagementMockExtension;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.test.tester.HttpGraphQlTester;

import static com.sast.store.commons.tenant.TenantHeaderConstants.X_TENANT;

class InvoiceDataFetcherTest extends AbstractComponentTest {
    @Autowired
    private HttpGraphQlTester tester;

    @Test
    void shouldReturnInvoices() {
        OrdermanagementMockExtension.withListAllInvoicesResponse();

        tester.mutate()
                .headers(headers -> {
                    headers.set(X_TENANT, Tenant.REXROTH.id());
                    headers.setBearerAuth(VALID_KEYCLOAK_TOKEN);
                })
                .build()
                .documentName("query/AllInvoices")
                .execute()
                .path("invoices")
                .matchesJsonStrictly(loadJson("/graphql-test/response/GetInvoices.json"));
    }
}
