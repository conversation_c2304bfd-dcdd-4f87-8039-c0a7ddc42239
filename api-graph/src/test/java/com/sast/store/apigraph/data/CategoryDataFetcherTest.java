package com.sast.store.apigraph.data;

import com.sast.store.apigraph.AbstractComponentTest;
import com.sast.store.productmanagement.test.ProductServiceMockExtension;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.test.tester.HttpGraphQlTester;

import static com.sast.store.commons.tenant.TenantHeaderConstants.X_TENANT;
import static com.sast.store.commons.tenant.api.Tenant.REXROTH;

class CategoryDataFetcherTest extends AbstractComponentTest {

    @Autowired
    private HttpGraphQlTester tester;

    @Test
    void shouldReturnCategories() {
        ProductServiceMockExtension.withGetAllCategories();

        tester
                .mutate()
                .header(X_TENANT, REXROTH.id())
                .build()
                .documentName("query/GetCategories")
                .execute()
                .path("categories")
                .matchesJsonStrictly(loadJson("/graphql-test/response/GetCategories.json"));
    }
}
