package com.sast.store.apigraph.data;

import com.sast.store.apigraph.AbstractComponentTest;
import com.sast.store.apigraph.codegen.DgsConstants.MUTATION;
import com.sast.store.apigraph.codegen.DgsConstants.QUERY;
import com.sast.store.commons.tenant.api.Tenant;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.execution.ErrorType;
import org.springframework.graphql.execution.GraphQlSource;
import org.springframework.graphql.test.tester.HttpGraphQlTester;
import org.springframework.http.HttpHeaders;

import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Stream;

import static com.sast.store.commons.tenant.TenantHeaderConstants.X_TENANT;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.params.provider.Arguments.argumentSet;

class AccessControlTest extends AbstractComponentTest {

    // language=GraphQL
    private static final Map<String, String> QUERY_DOCUMENTS = Map.of(
        QUERY.Contracts, "{ contracts { __typename } }",
        QUERY.CurrentCart, "{ currentCart { __typename } }",
        QUERY.Defaults, "{ defaults(contractIds: [\"dummy-id\"]) { __typename } }",
        QUERY.Invoices, "{ invoices { __typename } }",
        QUERY.Payment, "{ payment(paymentId: \"dummy-id\") { __typename } }",
        QUERY.PaymentConfig, "{ paymentConfig { __typename } }"
    );

    // language=GraphQL
    private static final Map<String, String> MUTATION_DOCUMENTS = Map.of(
        MUTATION.AssignLicenses, "mutation { assignLicenses(assignments: []) { __typename } }",
        MUTATION.UnassignLicense, "mutation { unassignLicense(licenseId: \"\") }",
        MUTATION.CancelContracts, "mutation { cancelContracts(contractIds: []) }",
        MUTATION.AddToCurrentCart, "mutation { addToCurrentCart(input: { sku: \"\", quantity: 1 }) { __typename } }",
        MUTATION.UpdateCurrentCart, "mutation { updateCurrentCart(input: { lineItemId: \"\", quantity: 1 }) { __typename } }",
        MUTATION.CheckoutCurrentCart, "mutation { checkoutCurrentCart(input: { paymentMethodId: \"\" }) { __typename } }"
    );

    @Autowired
    private HttpGraphQlTester tester;

    @Autowired
    private GraphQlSource graphQlSource;

    @Test
    @Order(1)
    void givenGraphQlSchema_whenQueryFieldIsNonPublic_thenQueryDocumentIsDefined() {
        graphQlSource.schema().getQueryType().getFields().stream()
            .filter(field -> field.getDirectives().stream()
                .noneMatch(directive -> Objects.equals("public", directive.getName())))
            .forEach(queryField ->
                assertThat(QUERY_DOCUMENTS)
                    .containsKey(queryField.getName()));
    }

    @Test
    @Order(1)
    void givenGraphQlSchema_whenMutationFieldIsNonPublic_thenMutationDocumentIsDefined() {
        graphQlSource.schema().getMutationType().getFields().stream()
            .filter(field -> field.getDirectives().stream()
                .noneMatch(directive -> Objects.equals("public", directive.getName())))
            .forEach(queryField ->
                assertThat(MUTATION_DOCUMENTS)
                    .containsKey(queryField.getName()));
    }

    @ParameterizedTest
    @MethodSource("unauthenticatedHeaders")
    void givenGraphQlSchema_whenQueryFieldIsNonPublic_thenUnauthenticatedRequestsShouldFail(final Consumer<HttpHeaders> headersConsumer) {
        graphQlSource.schema().getQueryType().getFields().stream()
            .filter(field -> field.getDirectives().stream()
                .noneMatch(directive -> Objects.equals("public", directive.getName())))
            .forEach(queryField -> {
                final var queryDocument = QUERY_DOCUMENTS.get(queryField.getName());
                tester.mutate()
                    .headers(headersConsumer)
                    .build()
                    .document(queryDocument)
                    .execute()
                    .errors()
                    .expect(responseError -> Objects.equals(ErrorType.UNAUTHORIZED, responseError.getErrorType()))
                    .verify();
            });
    }

    @ParameterizedTest
    @MethodSource("unauthenticatedHeaders")
    void givenGraphQlSchema_whenMutationFieldIsNonPublic_thenUnauthenticatedRequestsShouldFail(
        final Consumer<HttpHeaders> headersConsumer) {
        graphQlSource.schema().getMutationType().getFields().stream()
            .filter(field -> field.getDirectives().stream()
                .noneMatch(directive -> Objects.equals("public", directive.getName())))
            .forEach(queryField -> {
                final var mutationDocument = MUTATION_DOCUMENTS.get(queryField.getName());
                tester.mutate()
                    .headers(headersConsumer)
                    .build()
                    .document(mutationDocument)
                    .execute()
                    .errors()
                    .expect(responseError -> Objects.equals(ErrorType.UNAUTHORIZED, responseError.getErrorType()))
                    .verify();
            });
    }

    public static Stream<Arguments> unauthenticatedHeaders() {
        return Stream.of(
            argumentSet("no auth header", tenantAndNoAuthHeader()),
            argumentSet("basic auth header", tenantAndBasicAuthHeader()));
    }

    private static Consumer<HttpHeaders> tenantAndNoAuthHeader() {
        return headers -> {
            headers.set(X_TENANT, Tenant.REXROTH.id());
        };
    }

    private static Consumer<HttpHeaders> tenantAndBasicAuthHeader() {
        return headers -> {
            headers.set(X_TENANT, Tenant.REXROTH.id());
            headers.setBasicAuth("user", "password1");
        };
    }
}
