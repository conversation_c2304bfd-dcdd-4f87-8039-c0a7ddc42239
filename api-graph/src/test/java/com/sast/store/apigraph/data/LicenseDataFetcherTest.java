package com.sast.store.apigraph.data;

import com.sast.store.apigraph.AbstractComponentTest;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.entitlementmanagement.EntitlementmanagementMockExtension;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.execution.ErrorType;
import org.springframework.graphql.test.tester.HttpGraphQlTester;

import java.util.Map;
import java.util.Objects;

import static com.sast.store.commons.tenant.TenantHeaderConstants.X_TENANT;

class LicenseDataFetcherTest extends AbstractComponentTest {

    @Autowired
    private HttpGraphQlTester tester;

    @Test
    void shouldAssignLicense() {
        EntitlementmanagementMockExtension.withAssignLicensesResponse();

        tester.mutate()
                .headers(headers -> {
                    headers.set(X_TENANT, Tenant.REXROTH.id());
                    headers.setBearerAuth(VALID_KEYCLOAK_TOKEN);
                })
                .build()
                .documentName("mutation/AssignLicense")
                .variables(Map.of(
                        "contractId", "some-contract-id",
                        "firstname", "Justus",
                        "lastname", "Jonas",
                        "email", "<EMAIL>"))
                .execute()
                .path("licenses")
                // FIXME result list is empty until implemented in entitlementmanagement
                .matchesJsonStrictly(loadJson("/graphql-test/response/AssignLicense.json"));
    }

    @Test
    void shouldUnassignLicense() {
        EntitlementmanagementMockExtension.withUnassignLicenseResponse();

        tester.mutate()
                .headers(headers -> {
                    headers.set(X_TENANT, Tenant.REXROTH.id());
                    headers.setBearerAuth(VALID_KEYCLOAK_TOKEN);
                })
                .build()
                .documentName("mutation/UnassignLicense")
                .variable("licenseId", "known-license-id")
                .execute()
                .path("unassignedId")
                .matchesJsonStrictly(loadJson("/graphql-test/response/UnassignLicense.json"));
    }

    @Test
    void shouldHandleUnassignmentWithUnknownLicenseId() {
        EntitlementmanagementMockExtension.withUnassignLicenseNotFoundResponse();

        tester.mutate()
                .headers(headers -> {
                    headers.set(X_TENANT, Tenant.REXROTH.id());
                    headers.setBearerAuth(VALID_KEYCLOAK_TOKEN);
                })
                .build()
                .documentName("mutation/UnassignLicense")
                .variable("licenseId", "unknown-license-id")
                .execute()
                .errors()
                .expect(responseError -> Objects.equals(responseError.getErrorType(), ErrorType.NOT_FOUND))
                .verify()
                .path("unassignedId")
                .valueIsNull();
    }
}
