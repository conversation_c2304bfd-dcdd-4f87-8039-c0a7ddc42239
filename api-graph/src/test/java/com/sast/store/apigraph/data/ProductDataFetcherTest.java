package com.sast.store.apigraph.data;

import com.sast.store.apigraph.AbstractComponentTest;
import com.sast.store.productmanagement.test.ProductServiceMockExtension;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.test.tester.HttpGraphQlTester;

import java.util.Objects;

import static com.sast.store.commons.tenant.TenantHeaderConstants.X_TENANT;
import static com.sast.store.commons.tenant.api.Tenant.REXROTH;
import static org.springframework.graphql.execution.ErrorType.NOT_FOUND;

class ProductDataFetcherTest extends AbstractComponentTest {

    @Autowired
    private HttpGraphQlTester tester;

    @Test
    void shouldReturnExistingProduct() {
        ProductServiceMockExtension.withGetProductResponse();

        tester
                .mutate()
                .header(X_TENANT, REXROTH.id())
                .build()
                .documentName("query/GetProduct")
                .variable("id", "6d8ab4be-e24b-473b-a6a5-f1228380b86c")
                .execute()
                .path("product.id")
                .entity(String.class)
                .isEqualTo("6d8ab4be-e24b-473b-a6a5-f1228380b86c");
    }

    @Test
    void shouldReturnNullForMissingProduct() {
        tester
                .mutate()
                .header(X_TENANT, REXROTH.id())
                .build()
                .documentName("query/GetProduct")
                .variable("id", "6d8ab4be-e24b-473b-a6a5-f1228380b86c")
                .execute()
                .errors()
                .expect(error -> Objects.equals(error.getErrorType(), NOT_FOUND))
                .verify()
                .path("product")
                .valueIsNull();
    }

    @Test
    void shouldReturnProducts() {
        ProductServiceMockExtension.withGetAllProductsResponse();

        tester
                .mutate()
                .header(X_TENANT, REXROTH.id())
                .build()
                .documentName("query/GetProducts")
                .execute()
                .path("productSearch.count")
                .entity(Integer.class)
                .isEqualTo(7)
                .path("productSearch")
                .matchesJsonStrictly(loadJson("/graphql-test/response/GetProducts.json"));
    }

    @Test
    void shouldReturnProductsWithoutExpanding() {
        ProductServiceMockExtension.withGetAllProductsResponse();

        tester
                .mutate()
                .header(X_TENANT, REXROTH.id())
                .build()
                .documentName("query/GetProducts")
                .variable("expand", false)
                .execute()
                .path("productSearch")
                .matchesJsonStrictly(loadJson("/graphql-test/response/GetProductsWithoutExpanding.json"));

    }

    @Test
    void shouldReturnProductsWithExpanding() {
        ProductServiceMockExtension.withGetAllProductsResponse();

        tester
                .mutate()
                .header(X_TENANT, REXROTH.id())
                .build()
                .documentName("query/GetProducts")
                .variable("expand", true)
                .execute()
                .path("productSearch")
                .matchesJsonStrictly(loadJson("/graphql-test/response/GetProductsWithExpanding.json"));
    }
}
