package com.sast.store.apigraph.data;

import com.sast.store.apigraph.AbstractComponentTest;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.contractmanagement.test.ContractServiceMockExtension;
import com.sast.store.entitlementmanagement.EntitlementmanagementMockExtension;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.test.tester.HttpGraphQlTester;

import java.util.List;

import static com.sast.store.commons.tenant.TenantHeaderConstants.X_TENANT;

class ContractDataFetcherTest extends AbstractComponentTest {

    @Autowired
    private HttpGraphQlTester tester;

    @Test
    void shouldReturnContracts() {
        ContractServiceMockExtension.withGetContractsResponse();
        EntitlementmanagementMockExtension.withGetLicensesResponse();

        tester.mutate()
                .headers(headers -> {
                    headers.set(X_TENANT, Tenant.REXROTH.id());
                    headers.setBearerAuth(VALID_KEYCLOAK_TOKEN);
                })
                .build()
                .documentName("query/GetContracts")
                .execute()
                .path("contracts")
                .matchesJsonStrictly(loadJson("/graphql-test/response/GetContracts.json"));
    }

    @Test
    void shouldCancelContracts() {
        ContractServiceMockExtension.withCancelContractsSuccessfulResponse();

        tester.mutate()
                .headers(headers -> {
                    headers.set(X_TENANT, Tenant.REXROTH.id());
                    headers.setBearerAuth(VALID_KEYCLOAK_TOKEN);
                })
                .build()
                .documentName("mutation/CancelContracts")
                .variable("contractIds", List.of("valid-contract-id"))
                .execute()
                .path("cancelledIds")
                .matchesJsonStrictly(loadJson("/graphql-test/response/CancelContracts.json"));
    }
}
