directive @executionCost(value: Int!) on FIELD_DEFINITION

type Query {
  categories(locale: Locale, limit: Int): CategoryQueryResult! @public
  product(id: ID!, locale: Locale): Product @public
  productSearch(country: String, locale: Locale, limit: Int, expand: Boolean @deprecated): ProductSearchQueryResult! @public
  currentCart(locale: Locale): Cart!
  paymentConfig: PaymentConfig!
  contracts(limit: Int, offset: Int): ContractQueryResult
  defaults(contractIds: [ID!]!): [AssignDefaults!]
  payment(paymentId: ID!): Payment
  invoices: InvoiceQueryResult!
}

type Mutation {
  # FIXME the assignment mutation will return an empty list until assignLicenses in entitlementmanagement responds with the created licenses
  assignLicenses(assignments: [AssignLicense!]!): [License!]
  unassignLicense(licenseId: ID!): ID
  cancelContracts(contractIds: [ID!]!): [ID!]
  addToCurrentCart(input: CartAdd!): Cart
  updateCurrentCart(input: CartUpdate!): Cart
  checkoutCurrentCart(input: CartCheckout!): AuthorizationInformation!
}

input CartAdd {
  sku: ID!
  quantity: Long!
  addons: [CartAddAddon!]
}

input CartAddAddon {
  sku: String!
}

input CartUpdate {
  lineItemId: ID!
  quantity: Long!
}

input CartCheckout {
  paymentMethodId: ID!
  notes: [String!]
}

type AuthorizationInformation {
  paymentId: ID!
  redirectUrl: Url
}

type PaymentConfig {
  paymentMethods: [CheckoutInformation!]
}

interface CheckoutInformation {
  paymentMethodId: ID!
}

type BoschSepaCreditInformation implements CheckoutInformation {
  paymentMethodId: ID!
  accountHolder: String!
  bankName: String!
  iban: String!
  bic: String!
}

type BoschAchCreditInformation implements CheckoutInformation {
  paymentMethodId: ID!
  accountHolder: String!
  accountNumber: String!
  routingNumber: String!
  bic: String!
  bankName: String!
}

type GenericPaymentInformation implements CheckoutInformation {
  paymentMethodId: ID!
}

type Payment {
  paymentId: ID!
  paymentStatus: String
  orderId: String
}

type CategoryQueryResult {
  count: Int!
  results: [Category!]!
}

type InvoiceQueryResult {
  count: Int!
  results: [Invoice!]!
}

type ProductSearchQueryResult {
  # todo must be implemented in service first
  #offset: Int!
  count: Int!
  # todo must be implemented in service first
  #total: Long!
  results: [ProductSearchResult!]!
}

type ProductSearchResult {
  id: ID!
  product: Product
}

type Product {
  id: ID!
  name: String
  description: String
  productType: String
  images: [Url!]
  variants: [Variant!]
  externalDocuments: [LocalizedLink!]
  sellerCompany: Company
  categories: [String]
}

type Variant {
  sku: ID!
  externalProductId: ID
  price: Money
  bundleAmount: Long
  licenseType: String
  runtime: String
  noticePeriod: Period
  name: String
  description: String
  features: String
  agreements: [LocalizedLink!]
  priceList: Url
  entitlements: [String]
  addons: [AddonProduct!]
}

type Category {
  categoryId: ID!
  name: String!
  description: String
  parentCategoryId: ID
  parentCategories: [String!]
  order: BigDecimal!
}

type Invoice {
  invoiceNumber: ID!
  orderIds: [ID!]!
  invoiceDate: Date!
  status: InvoiceStatus!
  totalAmount: Money!
}

enum InvoiceStatus {
  ISSUED
  PAID
}

type Cart {
  id: ID!
  lineItems: [LineItem!]!
  totalPrice: Money!
  sellerCompany: Company
  billingAddress: Address
}

type Money {
  value: Float!
  currencyCode: String!
}

type Company {
  name: String!
  country: String
}

type Address {
  city: String
  postalCode: String
  streetName: String
  streetNumber: String
  state: String
  region: String
  country: String
  email: String
}

type LineItem {
  lineItemId: ID!
  name: String!
  productId: ID!
  sku: ID
  variant: Variant
  itemPrice: Money!
  quantity: Long!
  totalPrice: Money!
  addons: [AddonLineItem!]
}

type AddonLineItem {
  addonLineItemId: ID!
  parentLineItemId: ID!
  name: String!
  quantity: Long!
  addonVariant: AddonVariant
  itemPrice: Money!
  totalPrice: Money!
}

type LocalizedLink {
  name: String
  url: Url
  linkType: LinkType
}

enum LinkType {
  SOFTWARE_LICENSE
  PRIVACY_POLICY
}

type AddonProduct {
  id: ID
  name: String
  description: String
  addonVariants: [AddonVariant!]
}

type AddonVariant {
  sku: ID
  name: String
  description: String
  price: Money
}

type ContractQueryResult {
  offset: Int!
  count: Int!
  total: Long!
  results: [Contract!]! @executionCost(value: 5)
}

type Contract {
  companyId: ID!
  contractId: ID!
  orderNumber: ID!
  productId: ID!
  startDate: DateTime!
  projectedEndDate: DateTime
  endDate: DateTime
  contractType: ContractType!
  contractState: ContractState!
  contractPeriod: Period
  noticePeriod: Period
  addons: [AddonContract!]
  licenses: [License!] @executionCost(value: 5)
}

type AddonContract {
  contractId: ID!
  productId: ID!
  startDate: DateTime!
  endDate: DateTime
  contractType: ContractType!
  contractPeriod: Period
  noticePeriod: Period
}

interface AssignDefaults {
  licenseModel: LicenseModel!
  contractId: ID!
}

type BcCentralAssignDefaults implements AssignDefaults {
  licenseModel: LicenseModel!
  contractId: ID!
  name: String!
  emails: [String!]!
}

type DcKeycloakAssignDefaults implements AssignDefaults {
  licenseModel: LicenseModel!
  contractId: ID!
  firstname: String!
  lastname: String!
  email: String!
}

type LitmosAssignDefaults implements AssignDefaults {
  licenseModel: LicenseModel!
  contractId: ID!
  firstname: String!
  lastname: String!
  email: String!
}

interface License {
  licenseId: ID!
  licenseModel: LicenseModel!
  contractId: ID!
  contract: Contract
}

type LitmosLicense implements License {
  licenseId: ID!
  licenseModel: LicenseModel!
  contractId: ID!
  contract: Contract
  email: String
  firstname: String
  lastname: String
}

type DcKeycloakLicense implements License {
  licenseId: ID!
  licenseModel: LicenseModel!
  contractId: ID!
  contract: Contract
  email: String
  firstname: String
  lastname: String
}

type BcCentralLicense implements License {
  licenseId: ID!
  licenseModel: LicenseModel!
  contractId: ID!
  contract: Contract
  name: String
  emails: [String!]
}

input AssignLicense {
  bcCentral: BcCentralAssignLicense
  dcKeycloak: DcKeycloakAssignLicense
  litmos: LitmosAssignLicense
}

input BcCentralAssignLicense {
  contractId: ID!
  name: String!
  emails: [String!]!
}

input DcKeycloakAssignLicense {
  contractId: ID!
  firstname: String!
  lastname: String!
  email: String!
}

input LitmosAssignLicense {
  contractId: ID!
  firstname: String!
  lastname: String!
  email: String!
}

enum LicenseModel {
  BCCENTRAL
  LITMOS
  DCKEYCLOAK
}

enum ContractType {
  CONSUMPTION
  FIXED_TERM
  SUBSCRIPTION
  ONE_TIME
}

enum ContractState {
  ACTIVE
  CANCELLED
  CANCELLATION_PENDING
  EXPIRED
}

scalar BigDecimal
scalar Long
scalar Locale @specifiedBy(url: "https://tools.ietf.org/html/bcp47")
scalar Url @specifiedBy(url: "https://www.w3.org/Addressing/URL/url-spec.txt")
scalar Date @specifiedBy(url: "https://tools.ietf.org/html/rfc3339")
scalar DateTime @specifiedBy(url: "https://scalars.graphql.org/andimarek/date-time.html")
scalar Period @specifiedBy(url: "https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/time/Period.html")

directive @public on FIELD_DEFINITION
