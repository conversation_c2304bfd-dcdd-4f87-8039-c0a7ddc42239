package com.sast.store.apigraph.data;

import com.sast.store.apigraph.codegen.types.InvoiceQueryResult;
import com.sast.store.apigraph.converter.InvoiceConverter;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.ordermanagement.api.InvoiceApi;
import lombok.RequiredArgsConstructor;
import org.springframework.graphql.data.method.annotation.ContextValue;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;

@Controller
@RequiredArgsConstructor
public class InvoiceDataFetcher {
    private final InvoiceApi invoiceApi;
    private final InvoiceConverter invoiceConverter;

    @QueryMapping
    @PreAuthorize("hasRole('DEFAULT')")
    public InvoiceQueryResult invoices(@ContextValue final Tenant tenant) {
        final var invoices = invoiceApi.listAllInvoices(tenant);

        return InvoiceQueryResult.newBuilder()
            .count(invoices.size())
            .results(invoices.stream()
                .map(invoiceConverter::convert)
                .toList())
            .build();
    }
}
