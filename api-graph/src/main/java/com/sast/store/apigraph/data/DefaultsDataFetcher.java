package com.sast.store.apigraph.data;

import com.sast.store.apigraph.codegen.types.AssignDefaults;
import com.sast.store.apigraph.converter.AssignDefaultsConverter;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.entitlementmanagement.api.LicenseApi;
import lombok.RequiredArgsConstructor;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.ContextValue;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;

import java.util.List;

@Controller
@RequiredArgsConstructor
public class DefaultsDataFetcher {
    private final LicenseApi licenseApi;
    private final AssignDefaultsConverter assignDefaultsConverter;

    @QueryMapping
    @PreAuthorize("hasRole('DEFAULT')")
    public List<AssignDefaults> defaults(@ContextValue final Tenant tenant, @Argument final List<String> contractIds) {
        return licenseApi.getDefaults(tenant, contractIds).stream()
            .map(assignDefaultsConverter::convert)
            .toList();
    }
}
