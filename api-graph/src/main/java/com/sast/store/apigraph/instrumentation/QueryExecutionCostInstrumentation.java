package com.sast.store.apigraph.instrumentation;

import graphql.execution.instrumentation.Instrumentation;
import graphql.execution.instrumentation.InstrumentationContext;
import graphql.execution.instrumentation.InstrumentationState;
import graphql.execution.instrumentation.SimplePerformantInstrumentation;
import graphql.execution.instrumentation.parameters.InstrumentationFieldParameters;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Slf4j
public class QueryExecutionCostInstrumentation extends SimplePerformantInstrumentation
    implements Instrumentation {

    private final int maxExecutionCost;
    private final ExecutionCostCalculator executionCostCalculator;

    @Override
    public InstrumentationContext<Object> beginFieldExecution(final InstrumentationFieldParameters parameters,
        final InstrumentationState state) {
        executionCostCalculator.evaluate(parameters, maxExecutionCost);
        return super.beginFieldExecution(parameters, state);
    }
}
