package com.sast.store.apigraph.config;

import jakarta.validation.constraints.NotNull;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.net.URI;

@ConfigurationProperties(prefix = "bossstore")
@Validated
public record AppConfiguration(
    @NotNull URI contractmanagementUrl,
    @NotNull URI entitlementmanagementUrl,
    @NotNull URI ordermanagementUrl,
    @NotNull URI productmanagementUrl
) { }
