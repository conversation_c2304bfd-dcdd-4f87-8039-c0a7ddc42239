package com.sast.store.apigraph.data;

import com.sast.store.apigraph.codegen.types.Contract;
import com.sast.store.apigraph.codegen.types.ContractQueryResult;
import com.sast.store.apigraph.codegen.types.License;
import com.sast.store.apigraph.converter.ContractConverter;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.contractmanagement.api.CancelContractDto;
import com.sast.store.contractmanagement.api.ContractApi;
import com.sast.store.contractmanagement.api.ContractDto;
import lombok.RequiredArgsConstructor;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.BatchMapping;
import org.springframework.graphql.data.method.annotation.ContextValue;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;

@Controller
@RequiredArgsConstructor
public class ContractDataFetcher {

    private final ContractApi contractApi;
    private final ContractConverter contractConverter;

    @QueryMapping
    @PreAuthorize("hasRole('DEFAULT')")
    public ContractQueryResult contracts(@ContextValue final Tenant tenant, @Argument final Integer limit, @Argument final Integer offset) {
        final var contracts = contractApi.getContracts(tenant, null);

        // FIXME pagination must be implemented properly in contractmanagement
        final var results = contracts.stream()
            .skip(offset != null ? offset : 0)
            .limit(limit != null ? limit : Long.MAX_VALUE)
            .map(contractConverter::convert)
            .toList();

        return ContractQueryResult.newBuilder()
            .offset(offset != null ? offset : 0)
            .count(results.size())
            .total((long) contracts.size())
            .results(results)
            .build();
    }

    @MutationMapping
    @PreAuthorize("hasRole('DEFAULT')")
    public List<String> cancelContracts(@ContextValue final Tenant tenant, @Argument final List<String> contractIds) {
        contractApi.cancelContracts(tenant, null, CancelContractDto.builder().contractIds(contractIds).build());
        // FIXME return list of actually cancelled contracts, which should be provided in the response from contractmanagement
        return contractIds;
    }

    @BatchMapping
    public Mono<Map<License, Contract>> contract(final List<License> licenses, @ContextValue final Tenant tenant) {
        final var contracts = contractApi.getContracts(tenant, null).stream()
            .collect(toMap(ContractDto::contractId, identity()));

        final var map = licenses.stream()
            .collect(toMap(identity(), license -> {
                final var contract = contracts.get(license.getContractId());
                return contractConverter.convert(contract);
            }));

        return Mono.just(map);
    }
}
