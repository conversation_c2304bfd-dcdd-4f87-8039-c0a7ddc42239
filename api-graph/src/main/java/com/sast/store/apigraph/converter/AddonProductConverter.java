package com.sast.store.apigraph.converter;

import com.sast.store.apigraph.codegen.types.AddonProduct;
import com.sast.store.ordermanagement.api.ProductVariantDto;
import com.sast.store.productmanagement.api.ProductDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import static com.sast.store.apigraph.converter.Converters.convertList;

@Component
@RequiredArgsConstructor
public class AddonProductConverter {
    private final AddonVariantConverter addonVariantConverter;

    public AddonProduct convert(final ProductVariantDto.Addon addon) {
        return AddonProduct.newBuilder()
            .id(addon.id())
            .build();
    }

    public AddonProduct convert(final ProductDto.Addon addon) {
        return AddonProduct.newBuilder()
            .name(addon.name())
            .description(addon.description())
            .addonVariants(convertList(addon.addonVariants(), addonVariantConverter::convert))
            .build();
    }
}
