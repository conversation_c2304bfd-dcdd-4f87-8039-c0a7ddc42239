package com.sast.store.apigraph.converter;

import com.sast.store.apigraph.codegen.types.Category;
import com.sast.store.productmanagement.api.CategoryDto;
import org.springframework.stereotype.Component;

@Component
public class CategoryConverter {
    public Category convert(final CategoryDto category) {
        return Category.newBuilder()
            .categoryId(category.categoryId())
            .name(category.name())
            .description(category.description())
            .parentCategoryId(category.parentCategoryId())
            .parentCategories(category.parentCategories())
            .order(category.order())
            .build();
    }
}
