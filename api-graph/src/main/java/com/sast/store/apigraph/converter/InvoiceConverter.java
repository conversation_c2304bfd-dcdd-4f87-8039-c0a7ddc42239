package com.sast.store.apigraph.converter;

import com.sast.store.apigraph.codegen.types.Invoice;
import com.sast.store.apigraph.codegen.types.InvoiceStatus;
import com.sast.store.ordermanagement.api.InvoiceDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class InvoiceConverter {
    private final MoneyConverter moneyConverter;

    public Invoice convert(final InvoiceDto invoiceDto) {
        return Invoice.newBuilder()
            .invoiceNumber(invoiceDto.invoiceNumber())
            .orderIds(invoiceDto.orderIds())
            .invoiceDate(invoiceDto.invoiceDate())
            .status(InvoiceStatus.valueOf(invoiceDto.status().name()))
            .totalAmount(moneyConverter.convert(invoiceDto.totalAmount()))
            .build();
    }
}
