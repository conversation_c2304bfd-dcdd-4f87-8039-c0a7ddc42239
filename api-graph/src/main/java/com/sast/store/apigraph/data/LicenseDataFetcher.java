package com.sast.store.apigraph.data;

import com.sast.store.apigraph.codegen.types.AssignLicense;
import com.sast.store.apigraph.codegen.types.Contract;
import com.sast.store.apigraph.codegen.types.License;
import com.sast.store.apigraph.converter.AssignLicenseDtoConverter;
import com.sast.store.apigraph.converter.LicenseConverter;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.entitlementmanagement.api.LicenseApi;
import com.sast.store.entitlementmanagement.api.LicenseDto;
import lombok.RequiredArgsConstructor;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.BatchMapping;
import org.springframework.graphql.data.method.annotation.ContextValue;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

import static java.util.function.Function.identity;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

@Controller
@RequiredArgsConstructor
public class LicenseDataFetcher {

    private final LicenseApi licenseApi;
    private final LicenseConverter licenseConverter;
    private final AssignLicenseDtoConverter assignLicenseDtoConverter;

    @MutationMapping
    @PreAuthorize("hasRole('DEFAULT')")
    public List<License> assignLicenses(@ContextValue final Tenant tenant, @Argument final List<AssignLicense> assignments) {
        licenseApi.assignLicenses(tenant, assignments.stream()
            .map(assignLicenseDtoConverter::convert)
            .toList());

        // FIXME assignLicenses should respond with the created licenses for them to be returned here. Until then we return an empty list
        return List.of();
    }

    @MutationMapping
    @PreAuthorize("hasRole('DEFAULT')")
    public String unassignLicense(@ContextValue final Tenant tenant, @Argument final String licenseId) {
        licenseApi.unassignLicense(tenant, licenseId);
        return licenseId;
    }

    @BatchMapping
    public Mono<Map<Contract, List<License>>> licenses(final List<Contract> contracts, @ContextValue final Tenant tenant) {
        final var licenses = licenseApi.getLicenses(tenant).stream()
            .collect(groupingBy(LicenseDto::contractId));

        final var map = contracts.stream()
            .collect(toMap(identity(), contract -> {
                final var licenseDtos = licenses.getOrDefault(contract.getContractId(), List.of());
                return licenseDtos.stream()
                    .map(licenseConverter::convert)
                    .toList();
            }));

        return Mono.just(map);
    }
}
