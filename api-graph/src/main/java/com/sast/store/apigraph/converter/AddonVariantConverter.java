package com.sast.store.apigraph.converter;

import com.sast.store.apigraph.codegen.types.AddonVariant;
import com.sast.store.ordermanagement.api.AddonLineItemDto;
import com.sast.store.productmanagement.api.ProductDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import static com.sast.store.apigraph.converter.Converters.convertNullable;

@Component
@RequiredArgsConstructor
public class AddonVariantConverter {
    private final MoneyConverter moneyConverter;

    public AddonVariant convert(final ProductDto.AddonVariant addonVariant) {
        return AddonVariant.newBuilder()
            .sku(addonVariant.sku())
            .price(convertNullable(addonVariant.price(), moneyConverter::convert))
            .name(addonVariant.name())
            .description(addonVariant.description())
            .build();
    }

    public AddonVariant convert(final AddonLineItemDto.AddonVariant addonVariant) {
        return AddonVariant.newBuilder()
            .sku(addonVariant.sku())
            .name(addonVariant.name())
            .build();
    }
}
