package com.sast.store.apigraph.server;

import com.sast.store.commons.tenant.api.Tenant;
import org.springframework.graphql.server.WebGraphQlInterceptor;
import org.springframework.graphql.server.WebGraphQlRequest;
import org.springframework.graphql.server.WebGraphQlResponse;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.Optional;

import static com.sast.store.commons.tenant.TenantHeaderConstants.X_TENANT;

@Component
public class TenantHeaderInterceptor implements WebGraphQlInterceptor {
    @Override
    public Mono<WebGraphQlResponse> intercept(final WebGraphQlRequest request, final Chain chain) {
        Optional.ofNullable(request.getHeaders().getFirst(X_TENANT))
            .map(Tenant::fromString)
            .ifPresent(tenant -> request.configureExecutionInput(
                (executionInput, builder) -> builder.graphQLContext(Map.of("tenant", tenant)).build()
            ));

        return chain.next(request);
    }
}
