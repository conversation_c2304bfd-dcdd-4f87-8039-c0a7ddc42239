package com.sast.store.apigraph.converter;

import com.sast.store.apigraph.codegen.types.Cart;
import com.sast.store.ordermanagement.api.CartDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.sast.store.apigraph.converter.Converters.convertNullable;
import static org.apache.commons.collections4.CollectionUtils.emptyIfNull;

@Component
@RequiredArgsConstructor
public class CartConverter {
    private final AddonLineItemConverter addonLineItemConverter;
    private final AddressConverter addressConverter;
    private final CompanyConverter companyConverter;
    private final LineItemConverter lineItemConverter;
    private final MoneyConverter moneyConverter;

    public Cart convert(final CartDto cartDto) {
        return Cart.newBuilder()
            .id(cartDto.id())
            .lineItems(emptyIfNull(cartDto.lineItems()).stream()
                .map(lineItemDto -> {
                    final var lineItem = lineItemConverter.convert(lineItemDto);
                    lineItem.setAddons(emptyIfNull(cartDto.addons()).stream()
                        .filter(addonDto -> Objects.equals(addonDto.parentLineItemId(), lineItemDto.lineItemId()))
                        .map(addonLineItemConverter::convert)
                        .toList());
                    return lineItem;
                })
                .toList())
            .totalPrice(moneyConverter.convert(cartDto.totalPrice()))
            .sellerCompany(convertNullable(cartDto.sellerCompany(), companyConverter::convert))
            .billingAddress(convertNullable(cartDto.billingAddress(), addressConverter::convert))
            .build();
    }
}
