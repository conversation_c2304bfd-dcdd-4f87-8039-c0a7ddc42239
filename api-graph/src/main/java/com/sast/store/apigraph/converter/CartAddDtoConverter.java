package com.sast.store.apigraph.converter;

import com.sast.store.apigraph.codegen.types.CartAdd;
import com.sast.store.apigraph.codegen.types.CartAddAddon;
import com.sast.store.ordermanagement.api.CartAddDto;
import org.springframework.stereotype.Component;

import static org.apache.commons.collections4.ListUtils.emptyIfNull;

@Component
public class CartAddDtoConverter {

    public CartAddDto convert(final CartAdd cartAdd) {
        return CartAddDto.builder()
            .sku(cartAdd.getSku())
            .quantity(cartAdd.getQuantity())
            .addons(emptyIfNull(cartAdd.getAddons()).stream().map(this::convert).toList())
            .build();
    }

    public CartAddDto.Addons convert(final CartAddAddon cartAddAddon) {
        return CartAddDto.Addons.builder()
            .sku(cartAddAddon.getSku())
            .build();
    }
}
