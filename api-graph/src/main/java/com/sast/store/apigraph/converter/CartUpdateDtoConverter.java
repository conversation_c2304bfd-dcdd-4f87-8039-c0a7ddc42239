package com.sast.store.apigraph.converter;

import com.sast.store.apigraph.codegen.types.CartUpdate;
import com.sast.store.ordermanagement.api.CartUpdateDto;
import org.springframework.stereotype.Component;

@Component
public class CartUpdateDtoConverter {

    public CartUpdateDto convert(final CartUpdate cartUpdate) {
        return CartUpdateDto.builder()
            .lineItemId(cartUpdate.getLineItemId())
            .quantity(cartUpdate.getQuantity())
            .build();
    }
}
