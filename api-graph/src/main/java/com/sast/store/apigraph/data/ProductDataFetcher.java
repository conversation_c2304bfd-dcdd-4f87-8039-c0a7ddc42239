package com.sast.store.apigraph.data;

import com.sast.store.apigraph.codegen.types.Product;
import com.sast.store.apigraph.codegen.types.ProductSearchQueryResult;
import com.sast.store.apigraph.codegen.types.ProductSearchResult;
import com.sast.store.apigraph.converter.ProductConverter;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.productmanagement.api.ProductApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.ContextValue;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.stereotype.Controller;

import java.util.Locale;
import java.util.Optional;

import static org.apache.commons.lang3.BooleanUtils.isTrue;
import static org.apache.commons.lang3.StringUtils.isBlank;

@Controller
@RequiredArgsConstructor
@Slf4j
public class ProductDataFetcher {
    private final ProductApi productApi;
    private final ProductConverter productConverter;

    @QueryMapping
    public Product product(@ContextValue final Tenant tenant, @Argument final String id, @Argument final Locale locale) {
        // Calling getProduct with an empty string would end up in the getAllProducts endpoint. This check can be removed if we have a
        // clear separation between an endpoint for single products and product search/all products in product management.
        if (isBlank(id)) {
            return null;
        }

        return productApi.getProduct(tenant, id,
                Optional.ofNullable(locale).map(Locale::toLanguageTag).orElse(null), null)
            .map(productConverter::convert)
            .orElse(null);
    }

    @QueryMapping
    public ProductSearchQueryResult productSearch(@ContextValue final Tenant tenant, @Argument final String country,
        @Argument final Locale locale, @Argument final Integer limit, @Argument final Boolean expand) {
        final var products = productApi.getAllProducts(tenant,
            Optional.ofNullable(locale).map(Locale::toLanguageTag).orElse(null), country, limit);

        return ProductSearchQueryResult.newBuilder()
            .count(products.size())
            .results(products.stream()
                .map(product -> {
                    if (isTrue(expand)) {
                        return ProductSearchResult.newBuilder()
                            .id(product.id())
                            .product(productConverter.convert(product))
                            .build();
                    } else {
                        return ProductSearchResult.newBuilder()
                            .id(product.id())
                            .build();
                    }
                })
                .toList())
            .build();
    }
}
