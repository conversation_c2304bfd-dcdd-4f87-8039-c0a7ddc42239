package com.sast.store.apigraph.converter;

import com.sast.store.apigraph.codegen.types.Company;
import com.sast.store.ordermanagement.api.CompanyDto;
import com.sast.store.productmanagement.api.ProductDto;
import org.springframework.stereotype.Component;

@Component
public class CompanyConverter {
    public Company convert(final CompanyDto companyDto) {
        return Company.newBuilder()
            .name(companyDto.name())
            .country(companyDto.country())
            .build();
    }

    public Company convert(final ProductDto.Company company) {
        return Company.newBuilder()
            .name(company.name())
            .build();
    }
}
