package com.sast.store.apigraph.converter;

import com.sast.store.apigraph.codegen.types.CartCheckout;
import com.sast.store.ordermanagement.api.CartCheckoutDto;
import org.springframework.stereotype.Component;

@Component
public class CartCheckoutDtoConverter {
    public CartCheckoutDto convert(final CartCheckout cartCheckout) {
        return CartCheckoutDto.builder()
            .paymentMethodId(cartCheckout.getPaymentMethodId())
            .notes(cartCheckout.getNotes())
            .build();
    }
}
