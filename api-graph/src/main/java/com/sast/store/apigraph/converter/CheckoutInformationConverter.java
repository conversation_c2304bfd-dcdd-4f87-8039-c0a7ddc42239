package com.sast.store.apigraph.converter;

import com.sast.store.apigraph.codegen.types.BoschAchCreditInformation;
import com.sast.store.apigraph.codegen.types.BoschSepaCreditInformation;
import com.sast.store.apigraph.codegen.types.CheckoutInformation;
import com.sast.store.apigraph.codegen.types.GenericPaymentInformation;
import com.sast.store.ordermanagement.api.CheckoutInformationDto;
import org.springframework.stereotype.Component;

@Component
public class CheckoutInformationConverter {

    public CheckoutInformation convert(final CheckoutInformationDto checkoutInformation) {
        if (checkoutInformation.boschAchCreditInformation() != null) {
            final var boschAchCreditInformation = checkoutInformation.boschAchCreditInformation();
            return BoschAchCreditInformation.newBuilder()
                .paymentMethodId(checkoutInformation.paymentMethodId())
                .accountHolder(boschAchCreditInformation.accountHolder())
                .accountNumber(boschAchCreditInformation.accountNumber())
                .routingNumber(boschAchCreditInformation.routingNumber())
                .bic(boschAchCreditInformation.bic())
                .bankName(boschAchCreditInformation.bankName())
                .build();
        } else if (checkoutInformation.boschSepaCreditInformation() != null) {
            final var boschSepaCreditInformation = checkoutInformation.boschSepaCreditInformation();
            return BoschSepaCreditInformation.newBuilder()
                .paymentMethodId(checkoutInformation.paymentMethodId())
                .accountHolder(boschSepaCreditInformation.accountHolder())
                .bankName(boschSepaCreditInformation.bankName())
                .iban(boschSepaCreditInformation.iban())
                .bic(boschSepaCreditInformation.bic())
                .build();
        } else {
            return GenericPaymentInformation.newBuilder()
                .paymentMethodId(checkoutInformation.paymentMethodId())
                .build();
        }
    }
}
