package com.sast.store.apigraph.server;

import graphql.GraphQLError;
import graphql.execution.ResultPath;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.graphql.server.WebGraphQlInterceptor;
import org.springframework.graphql.server.WebGraphQlRequest;
import org.springframework.graphql.server.WebGraphQlResponse;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import static org.springframework.graphql.execution.ErrorType.FORBIDDEN;
import static org.springframework.graphql.execution.ErrorType.INTERNAL_ERROR;
import static org.springframework.graphql.execution.ErrorType.NOT_FOUND;
import static org.springframework.graphql.execution.ErrorType.UNAUTHORIZED;

@Component
@Order(1)
@Slf4j
public class RequestErrorInterceptor implements WebGraphQlInterceptor {

    @Override
    public Mono<WebGraphQlResponse> intercept(final WebGraphQlRequest request, final Chain chain) {
        return chain.next(request).map(response -> {
            final var errors = response.getErrors();
            if (errors.isEmpty()) {
                return response;
            }

            LOG.warn("Response for request {} contains errors: {}", request.getId(), errors);

            // Expose only certain useful errors to the client, while ensuring internal details are not exposed by accident. For example
            // though the `extension` field.
            return response.transform(builder -> builder.errors(response.getErrors().stream()
                .map(error -> switch (error.getErrorType()) {
                    case UNAUTHORIZED, FORBIDDEN, NOT_FOUND -> GraphQLError.newError()
                        .errorType(error.getErrorType())
                        .message("%s (%s)".formatted(error.getErrorType(), request.getId()))
                        .path(ResultPath.fromList(error.getParsedPath()))
                        .build();
                    default -> GraphQLError.newError()
                        .errorType(INTERNAL_ERROR)
                        .message("INTERNAL_ERROR (%s)".formatted(request.getId()))
                        .build();
                })
                .toList())
            );
        });
    }
}
