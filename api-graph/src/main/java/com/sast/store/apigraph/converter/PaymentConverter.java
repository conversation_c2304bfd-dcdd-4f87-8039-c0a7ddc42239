package com.sast.store.apigraph.converter;

import com.sast.store.apigraph.codegen.types.Payment;
import com.sast.store.ordermanagement.api.PaymentDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PaymentConverter {

    public Payment convert(final PaymentDto payment) {
        return Payment.newBuilder()
            .paymentId(payment.paymentId())
            .paymentStatus(payment.paymentStatus())
            .orderId(payment.orderId())
            .build();
    }
}
