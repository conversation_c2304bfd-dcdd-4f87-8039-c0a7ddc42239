package com.sast.store.apigraph.converter;

import com.sast.store.apigraph.codegen.types.AssignLicense;
import com.sast.store.apigraph.codegen.types.BcCentralAssignLicense;
import com.sast.store.apigraph.codegen.types.DcKeycloakAssignLicense;
import com.sast.store.apigraph.codegen.types.LitmosAssignLicense;
import com.sast.store.entitlementmanagement.api.AssignLicenseDto;
import com.sast.store.entitlementmanagement.api.LicenseModel;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.stream.Stream;

import static com.sast.store.apigraph.converter.Converters.convertNullable;
import static org.apache.commons.collections4.CollectionUtils.extractSingleton;

@Component
public class AssignLicenseDtoConverter {

    public AssignLicenseDto convert(final AssignLicense assignLicense) {
        final var assignments = Stream.of(
                convertNullable(assignLicense.getBcCentral(), this::convert),
                convertNullable(assignLicense.getDcKeycloak(), this::convert),
                convertNullable(assignLicense.getLitmos(), this::convert))
            .filter(Objects::nonNull)
            .toList();

        return extractSingleton(assignments);
    }

    public AssignLicenseDto convert(final BcCentralAssignLicense bcCentralAssignLicense) {
        return AssignLicenseDto.builder()
            .licenseModel(LicenseModel.BCCENTRAL)
            .bcCentral(AssignLicenseDto.BcCentralAssignLicenseDto.builder()
                .contractId(bcCentralAssignLicense.getContractId())
                .name(bcCentralAssignLicense.getName())
                .emails(bcCentralAssignLicense.getEmails())
                .build())
            .build();
    }

    public AssignLicenseDto convert(final DcKeycloakAssignLicense dcKeycloakAssignLicense) {
        // FIXME using the legacy fields for now. entitlement service implementation must be verified and tested before using the
        //  DcKeycloakAssignLicenseDto
        return AssignLicenseDto.builder()
            .contractId(dcKeycloakAssignLicense.getContractId())
            .firstname(dcKeycloakAssignLicense.getFirstname())
            .lastname(dcKeycloakAssignLicense.getLastname())
            .email(dcKeycloakAssignLicense.getEmail())
            .build();
    }

    public AssignLicenseDto convert(final LitmosAssignLicense litmosAssignLicense) {
        // FIXME using the legacy fields for now. entitlement service implementation must be verified and tested before using the
        //  LitmosAssignLicenseDto
        return AssignLicenseDto.builder()
            .contractId(litmosAssignLicense.getContractId())
            .firstname(litmosAssignLicense.getFirstname())
            .lastname(litmosAssignLicense.getLastname())
            .email(litmosAssignLicense.getEmail())
            .build();
    }
}
