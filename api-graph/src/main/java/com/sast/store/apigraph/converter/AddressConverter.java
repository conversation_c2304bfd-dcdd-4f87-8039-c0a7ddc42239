package com.sast.store.apigraph.converter;

import com.sast.store.apigraph.codegen.types.Address;
import com.sast.store.ordermanagement.api.AddressDto;
import org.springframework.stereotype.Component;

@Component
public class AddressConverter {
    public Address convert(final AddressDto addressDto) {
        return Address.newBuilder()
            .city(addressDto.city())
            .postalCode(addressDto.postalCode())
            .streetName(addressDto.streetName())
            .streetNumber(addressDto.streetNumber())
            .state(addressDto.state())
            .region(addressDto.region())
            .country(addressDto.country())
            .email(addressDto.email())
            .build();
    }
}
