package com.sast.store.apigraph.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sast.store.apigraph.codegen.DgsConstants.BOSCHACHCREDITINFORMATION;
import com.sast.store.apigraph.codegen.DgsConstants.BOSCHSEPACREDITINFORMATION;
import com.sast.store.apigraph.codegen.types.BoschAchCreditInformation;
import com.sast.store.apigraph.codegen.types.BoschSepaCreditInformation;
import com.sast.store.apigraph.instrumentation.ExecutionCostCalculator;
import com.sast.store.apigraph.instrumentation.QueryExecutionCostInstrumentation;
import com.sast.store.apigraph.scalars.CustomScalars;
import com.sast.store.commons.basewebapp.jackson.ObjectMapperConfiguration;
import graphql.analysis.MaxQueryComplexityInstrumentation;
import graphql.analysis.MaxQueryDepthInstrumentation;
import graphql.execution.instrumentation.Instrumentation;
import graphql.scalars.ExtendedScalars;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.graphql.GraphQlSourceBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.graphql.execution.RuntimeWiringConfigurer;
import org.springframework.graphql.server.WebGraphQlHandler;
import org.springframework.graphql.server.webmvc.GraphQlHttpHandler;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

@Configuration
@Slf4j
public class GraphQlConfiguration {
    /**
     * Ensure our custom {@code ObjectMapper} in {@link ObjectMapperConfiguration} does not interfere with Spring GraphQL. As an example
     * this ensures that {@code null} values are retained in responses.
     * <p>
     * In case of switching to Spring WebFlux this requires an adjusted implementation to return an appropriate {@code GraphQlHttpHandler}.
     *
     * @link <a href="https://github.com/spring-projects/spring-graphql/issues/860#issuecomment-2032517871">source</a>
     * @link <a href="https://docs.spring.io/spring-graphql/reference/transports.html#server.transports.http">documentation</a>
     */
    @Bean
    public GraphQlHttpHandler graphQlHttpHandler(final WebGraphQlHandler graphQlHandler) {
        final var converter = new MappingJackson2HttpMessageConverter(new ObjectMapper());
        return new GraphQlHttpHandler(graphQlHandler, converter);
    }

    /**
     * During startup, ensure that the schema is mapped properly and there are unused data fetchers by accident.
     * @link <a href="https://docs.spring.io/spring-graphql/reference/request-execution.html#execution.graphqlsource.schema-mapping-inspection">documentation</a>
     */
    @Bean
    public GraphQlSourceBuilderCustomizer graphQlSourceBuilderCustomizer() {
        return builder -> builder.inspectSchemaMappings(
            initializer -> initializer
                .classMapping(BOSCHACHCREDITINFORMATION.TYPE_NAME, BoschAchCreditInformation.class)
                .classMapping(BOSCHSEPACREDITINFORMATION.TYPE_NAME, BoschSepaCreditInformation.class),
            schemaReport -> {
                LOG.info("""
                        GraphQL schema inspection:
                            Unmapped fields: {}
                            Unmapped registrations: {}
                            Unmapped arguments: {}
                            Skipped types: {}""",
                    schemaReport.unmappedFields(),
                    schemaReport.unmappedRegistrations(),
                    schemaReport.unmappedArguments(),
                    schemaReport.skippedTypes());

                if (!schemaReport.unmappedFields().isEmpty()
                    || !schemaReport.unmappedRegistrations().isEmpty()
                    || !schemaReport.unmappedArguments().isEmpty()) {
                    throw new IllegalStateException("Schema mapping report contains unmapped findings");
                }
            });
    }

    @Bean
    public MaxQueryDepthInstrumentation maxQueryDepthInstrumentation(
        @Value("${bossstore.apigraph.instrumentation.max-query-depth}") final int maxDepth) {
        return new MaxQueryDepthInstrumentation(maxDepth);
    }

    @Bean
    public MaxQueryComplexityInstrumentation maxQueryComplexityInstrumentation(
        @Value("${bossstore.apigraph.instrumentation.max-query-complexity}") final int maxComplexity) {
        return new MaxQueryComplexityInstrumentation(maxComplexity);
    }

    @Bean
    public Instrumentation maxQueryExecutionCostInstrumentation(
        @Value("${bossstore.apigraph.instrumentation.max-query-execution-cost}") final int maxExecutionCost,
        ExecutionCostCalculator costCalculator) {
        return new QueryExecutionCostInstrumentation(maxExecutionCost, costCalculator);
    }

    @Bean
    public RuntimeWiringConfigurer runtimeWiringConfigurer() {
        return wiringBuilder -> wiringBuilder
            .scalar(CustomScalars.Period)
            .scalar(ExtendedScalars.Date)
            .scalar(ExtendedScalars.DateTime)
            .scalar(ExtendedScalars.GraphQLBigDecimal)
            .scalar(ExtendedScalars.GraphQLLong)
            .scalar(ExtendedScalars.Locale)
            .scalar(ExtendedScalars.Url);
    }
}
