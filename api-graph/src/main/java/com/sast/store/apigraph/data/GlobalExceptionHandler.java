package com.sast.store.apigraph.data;

import graphql.GraphQLError;
import graphql.schema.DataFetchingEnvironment;
import jakarta.ws.rs.ClientErrorException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.graphql.data.method.annotation.GraphQlExceptionHandler;
import org.springframework.web.bind.annotation.ControllerAdvice;

import static org.springframework.graphql.execution.ErrorType.INTERNAL_ERROR;
import static org.springframework.graphql.execution.ErrorType.NOT_FOUND;

@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @GraphQlExceptionHandler
    public GraphQLError handle(final ClientErrorException ex, final DataFetchingEnvironment env) {
        LOG.warn("Data fetching for execution {} on path {} raised exception: {}", env.getExecutionId(),
            env.getExecutionStepInfo().getPath(), ex.getMessage(), ex);

        if (ex.getResponse().getStatus() == 404) {
            return GraphQLError.newError()
                .errorType(NOT_FOUND)
                .message("Not found (%s)".formatted(env.getExecutionId()))
                .path(env.getExecutionStepInfo().getPath())
                .build();
        } else {
            return GraphQLError.newError()
                .errorType(INTERNAL_ERROR)
                .message("Internal error (%s)".formatted(env.getExecutionId()))
                .path(env.getExecutionStepInfo().getPath())
                .build();
        }
    }
}
