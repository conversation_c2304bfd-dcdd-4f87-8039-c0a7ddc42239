package com.sast.store.apigraph.converter;

import com.sast.store.apigraph.codegen.types.LinkType;
import com.sast.store.apigraph.codegen.types.LocalizedLink;
import com.sast.store.ordermanagement.api.LocalizedLinkDto;
import com.sast.store.productmanagement.api.ProductDto;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class LocalizedLinkConverter {
    @SneakyThrows
    public LocalizedLink convert(final LocalizedLinkDto link) {
        return LocalizedLink.newBuilder()
            .name(link.name())
            .url(link.url().toURL())
            .linkType(Optional.ofNullable(link.linkType())
                .map(LocalizedLinkDto.LinkType::name)
                .map(LinkType::valueOf)
                .orElse(null))
            .build();
    }

    @SneakyThrows
    public LocalizedLink convert(final ProductDto.LocalizedLink link) {
        return LocalizedLink.newBuilder()
            .name(link.name())
            .url(link.url().toURL())
            .linkType(Optional.ofNullable(link.linkType())
                .map(ProductDto.LinkType::name)
                .map(LinkType::valueOf)
                .orElse(null))
            .build();
    }
}
