package com.sast.store.apigraph.data;

import com.sast.store.apigraph.codegen.types.CategoryQueryResult;
import com.sast.store.apigraph.converter.CategoryConverter;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.productmanagement.api.CategoryApi;
import lombok.RequiredArgsConstructor;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.ContextValue;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.stereotype.Controller;

import java.util.Locale;
import java.util.Optional;

@Controller
@RequiredArgsConstructor
public class CategoryDataFetcher {
    private final CategoryApi categoryApi;
    private final CategoryConverter categoryConverter;

    @QueryMapping
    public CategoryQueryResult categories(@ContextValue final Tenant tenant, @Argument final Locale locale, @Argument final Integer limit) {
        final var categories = categoryApi.getAllCategories(tenant,
            Optional.ofNullable(locale).map(Locale::toLanguageTag), limit).stream()
            .map(categoryConverter::convert)
            .toList();

        return CategoryQueryResult.newBuilder()
            .count(categories.size())
            .results(categories)
            .build();
    }
}
