package com.sast.store.apigraph.config;

import com.sast.store.commons.basewebapp.keycloak.KeycloakTokenClientRequestFilter;
import com.sast.store.contractmanagement.api.ContractApi;
import com.sast.store.entitlementmanagement.api.LicenseApi;
import com.sast.store.ordermanagement.api.CartApi;
import com.sast.store.ordermanagement.api.InvoiceApi;
import com.sast.store.ordermanagement.api.PaymentApi;
import com.sast.store.productmanagement.api.CategoryApi;
import com.sast.store.productmanagement.api.ProductApi;
import jakarta.ws.rs.Priorities;
import jakarta.ws.rs.client.Client;
import lombok.RequiredArgsConstructor;
import org.glassfish.jersey.client.proxy.WebResourceFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.URI;

@Configuration
@RequiredArgsConstructor
public class RestServiceProducer {
    private final Client restClient;
    private final AppConfiguration appConfiguration;
    private final KeycloakTokenClientRequestFilter keycloakTokenClientRequestFilter;

    @Bean
    public CartApi getCartRestService() {
        return getProxy(appConfiguration.ordermanagementUrl(), CartApi.class);
    }

    @Bean
    public CategoryApi getCategoryRestService() {
        return getProxy(appConfiguration.productmanagementUrl(), CategoryApi.class);
    }

    @Bean
    public ContractApi getContractRestService() {
        return getProxy(appConfiguration.contractmanagementUrl(), ContractApi.class);
    }

    @Bean
    public InvoiceApi getInvoiceRestService() {
        return getProxy(appConfiguration.ordermanagementUrl(), InvoiceApi.class);
    }

    @Bean
    public LicenseApi getLicenseRestService() {
        return getProxy(appConfiguration.entitlementmanagementUrl(), LicenseApi.class);
    }

    @Bean
    public ProductApi getProductRestService() {
        return getProxy(appConfiguration.productmanagementUrl(), ProductApi.class);
    }

    @Bean
    public PaymentApi getPaymentRestService() {
        return getProxy(appConfiguration.ordermanagementUrl(), PaymentApi.class);
    }

    private <T> T getProxy(final URI url, final Class<T> proxyInterface) {
        return WebResourceFactory.newResource(proxyInterface, restClient.target(url)
            .register(keycloakTokenClientRequestFilter, Priorities.AUTHORIZATION));
    }
}
