package com.sast.store.apigraph.converter;

import com.sast.store.apigraph.codegen.types.AddonLineItem;
import com.sast.store.ordermanagement.api.AddonLineItemDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@RequiredArgsConstructor
public class AddonLineItemConverter {
    private final MoneyConverter moneyConverter;
    private final AddonVariantConverter addonVariantConverter;

    public AddonLineItem convert(final AddonLineItemDto addonLineItemDto) {
        return AddonLineItem.newBuilder()
            .addonLineItemId(addonLineItemDto.addonLineItemId())
            .parentLineItemId(addonLineItemDto.parentLineItemId())
            .name(addonLineItemDto.name())
            // FIXME currently not set in downstream service, so we assume 1
            .quantity(Optional.ofNullable(addonLineItemDto.quantity()).orElse(1L))
            .addonVariant(addonVariantConverter.convert(addonLineItemDto.addonVariant()))
            .itemPrice(moneyConverter.convert(addonLineItemDto.itemPrice()))
            .totalPrice(moneyConverter.convert(addonLineItemDto.totalPrice()))
            .build();
    }
}
