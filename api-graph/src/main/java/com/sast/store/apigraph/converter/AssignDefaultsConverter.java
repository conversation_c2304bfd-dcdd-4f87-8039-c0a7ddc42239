package com.sast.store.apigraph.converter;

import com.sast.store.apigraph.codegen.types.AssignDefaults;
import com.sast.store.apigraph.codegen.types.BcCentralAssignDefaults;
import com.sast.store.apigraph.codegen.types.DcKeycloakAssignDefaults;
import com.sast.store.apigraph.codegen.types.LicenseModel;
import com.sast.store.apigraph.codegen.types.LitmosAssignDefaults;
import com.sast.store.entitlementmanagement.api.AssignLicenseDto;
import org.springframework.stereotype.Controller;

@Controller
public class AssignDefaultsConverter {
    public AssignDefaults convert(final AssignLicenseDto assignLicenseDto) {
        return switch (assignLicenseDto.licenseModel()) {
            case BCCENTRAL -> BcCentralAssignDefaults.newBuilder()
                .licenseModel(LicenseModel.BCCENTRAL)
                .contractId(assignLicenseDto.contractId())
                .name(assignLicenseDto.bcCentral().name())
                .emails(assignLicenseDto.bcCentral().emails())
                .build();
            case DCKEYCLOAK -> DcKeycloakAssignDefaults.newBuilder()
                .licenseModel(LicenseModel.DCKEYCLOAK)
                .contractId(assignLicenseDto.contractId())
                .firstname(assignLicenseDto.dcKeycloak().firstname())
                .lastname(assignLicenseDto.dcKeycloak().lastname())
                .email(assignLicenseDto.dcKeycloak().email())
                .build();
            case LITMOS -> LitmosAssignDefaults.newBuilder()
                .licenseModel(LicenseModel.LITMOS)
                .contractId(assignLicenseDto.contractId())
                .firstname(assignLicenseDto.litmos().firstname())
                .lastname(assignLicenseDto.litmos().lastname())
                .email(assignLicenseDto.litmos().email())
                .build();
        };
    }
}
