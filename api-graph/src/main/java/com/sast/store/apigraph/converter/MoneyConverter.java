package com.sast.store.apigraph.converter;

import com.sast.store.apigraph.codegen.types.Money;
import com.sast.store.ordermanagement.api.MoneyDto;
import com.sast.store.productmanagement.api.ProductDto;
import org.springframework.stereotype.Component;

@Component
public class MoneyConverter {
    public Money convert(final MoneyDto money) {
        return Money.newBuilder()
            .value(money.value())
            .currencyCode(money.currencyCode())
            .build();
    }

    public Money convert(final ProductDto.Money money) {
        return Money.newBuilder()
            .value(money.value())
            .currencyCode(money.currencyCode())
            .build();
    }
}
