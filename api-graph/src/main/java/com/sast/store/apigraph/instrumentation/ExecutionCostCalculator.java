package com.sast.store.apigraph.instrumentation;

import graphql.execution.AbortExecutionException;
import graphql.execution.instrumentation.parameters.InstrumentationFieldParameters;
import graphql.schema.GraphQLAppliedDirective;
import graphql.schema.GraphQLAppliedDirectiveArgument;
import graphql.schema.GraphQLFieldDefinition;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.sast.store.apigraph.instrumentation.ExecutionCostDirective.ARGUMENT_NAME;
import static com.sast.store.apigraph.instrumentation.ExecutionCostDirective.DEFAULT_EXECUTION_COST;
import static com.sast.store.apigraph.instrumentation.ExecutionCostDirective.DIRECTIVE_NAME;

@RequiredArgsConstructor
@Slf4j
@Component
public class ExecutionCostCalculator {

    public void evaluate(final InstrumentationFieldParameters parameters, final int maxExecutionCost) {
        final int cost = getFieldExecutionCost(parameters.getField());
        checkCost(parameters, cost, maxExecutionCost);
    }

    private int getFieldExecutionCost(final GraphQLFieldDefinition field) {
        final GraphQLAppliedDirective directive = field.getAppliedDirective(DIRECTIVE_NAME);
        if (directive == null) {
            return DEFAULT_EXECUTION_COST;
        }

        final GraphQLAppliedDirectiveArgument value = directive.getArgument(ARGUMENT_NAME);
        if (value == null) {
            return DEFAULT_EXECUTION_COST;
        }

        return value.getValue();
    }

    private void checkCost(final InstrumentationFieldParameters parameters, final int cost, final int maxExecutionCost) {
        final Integer accumulatedCost = parameters.getExecutionContext()
            .getGraphQLContext().compute(DIRECTIVE_NAME, (k, v) -> v == null ? DEFAULT_EXECUTION_COST : v + cost);

        if (accumulatedCost > maxExecutionCost) {
            throw new AbortExecutionException(
                String.format("Query execution cost is too high! Current cost is %d, max allowed is %d.", accumulatedCost,
                    maxExecutionCost));
        }
    }
}
