package com.sast.store.apigraph.converter;

import com.sast.store.apigraph.codegen.types.Variant;
import com.sast.store.ordermanagement.api.ProductVariantDto;
import com.sast.store.productmanagement.api.ProductDto;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;

import static com.sast.store.apigraph.converter.Converters.convertList;
import static com.sast.store.apigraph.converter.Converters.convertNullable;

@Component
@RequiredArgsConstructor
public class VariantConverter {
    private final LocalizedLinkConverter localizedLinkConverter;
    private final AddonProductConverter addonProductConverter;
    private final MoneyConverter moneyConverter;

    public Variant convert(final ProductVariantDto productVariant) {
        return Variant.newBuilder()
            .name(productVariant.name())
            .licenseType(productVariant.licenseType())
            .sku(productVariant.sku())
            .runtime(productVariant.runtime().toString())
            .agreements(convertList(productVariant.agreements(), localizedLinkConverter::convert))
            .priceList(convertNullable(productVariant.priceList(), Converters::toURL))
            .addons(convertList(productVariant.addons(), addonProductConverter::convert))
            .build();
    }

    @SneakyThrows
    public Variant convert(final ProductDto.Variant productVariant) {
        return Variant.newBuilder()
            .sku(productVariant.sku())
            .externalProductId(productVariant.externalProductId())
            .price(convertNullable(productVariant.price(), moneyConverter::convert))
            .bundleAmount(productVariant.bundleAmount())
            .licenseType(productVariant.licenseType())
            .runtime(productVariant.runtime().toString())
            .noticePeriod(productVariant.noticePeriod())
            .name(productVariant.name())
            .description(productVariant.description())
            .features(productVariant.features())
            .agreements(convertList(productVariant.agreements(), localizedLinkConverter::convert))
            .priceList(convertNullable(productVariant.priceList(), Converters::toURL))
            .entitlements(productVariant.entitlements())
            .addons(convertList(productVariant.addons(), addonProductConverter::convert))
            .build();
    }
}
