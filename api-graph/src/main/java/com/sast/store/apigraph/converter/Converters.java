package com.sast.store.apigraph.converter;

import lombok.SneakyThrows;

import java.net.URI;
import java.net.URL;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

public final class Converters {
    private Converters() {}

    public static <T, U> List<T> convertList(final List<U> collection, final Function<U, T> convert) {
        return collection.stream()
            .map(convert)
            .toList();
    }

    public static <T, U> T convertNullable(final U object, final Function<U, T> convert) {
        return Optional.ofNullable(object)
            .map(convert)
            .orElse(null);
    }

    @SneakyThrows
    public static URL toURL(final URI uri) {
        return uri.toURL();
    }
}
