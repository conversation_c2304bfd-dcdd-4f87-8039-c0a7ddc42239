package com.sast.store.apigraph.converter;

import com.sast.store.apigraph.codegen.types.BcCentralLicense;
import com.sast.store.apigraph.codegen.types.DcKeycloakLicense;
import com.sast.store.apigraph.codegen.types.License;
import com.sast.store.apigraph.codegen.types.LicenseModel;
import com.sast.store.apigraph.codegen.types.LitmosLicense;
import com.sast.store.entitlementmanagement.api.LicenseDto;
import org.springframework.stereotype.Component;

@Component
public class LicenseConverter {

    public License convert(final LicenseDto licenseDto) {
        return switch (licenseDto.licenseModel()) {
            case BCCENTRAL -> BcCentralLicense.newBuilder()
                .licenseId(licenseDto.licenseId())
                .licenseModel(LicenseModel.valueOf(licenseDto.licenseModel().name()))
                .contractId(licenseDto.contractId())
                .name(licenseDto.bcCentral().name())
                .emails(licenseDto.bcCentral().emails())
                .build();
            case DCKEYCLOAK -> DcKeycloakLicense.newBuilder()
                .licenseId(licenseDto.licenseId())
                .licenseModel(LicenseModel.valueOf(licenseDto.licenseModel().name()))
                .contractId(licenseDto.contractId())
                .firstname(licenseDto.firstname())
                .lastname(licenseDto.lastname())
                .email(licenseDto.email())
                .build();
            case LITMOS -> LitmosLicense.newBuilder()
                .licenseId(licenseDto.licenseId())
                .licenseModel(LicenseModel.valueOf(licenseDto.licenseModel().name()))
                .contractId(licenseDto.contractId())
                .firstname(licenseDto.firstname())
                .lastname(licenseDto.lastname())
                .email(licenseDto.email())
                .build();
        };
    }
}
