package com.sast.store.apigraph.converter;

import com.sast.store.apigraph.codegen.types.Product;
import com.sast.store.productmanagement.api.ProductDto;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;

import static com.sast.store.apigraph.converter.Converters.convertList;
import static com.sast.store.apigraph.converter.Converters.convertNullable;

@Component
@RequiredArgsConstructor
public class ProductConverter {
    private final VariantConverter variantConverter;
    private final LocalizedLinkConverter localizedLinkConverter;
    private final CompanyConverter companyConverter;

    @SneakyThrows
    public Product convert(final ProductDto product) {
        return Product.newBuilder()
            .id(product.id())
            .name(product.name())
            .description(product.description())
            .productType(product.productType())
            .images(product.images().stream()
                .map(Converters::toURL)
                .toList())
            .variants(convertList(product.variants(), variantConverter::convert))
            .externalDocuments(convertList(product.externalDocuments(), localizedLinkConverter::convert))
            .sellerCompany(convertNullable(product.sellerCompany(), companyConverter::convert))
            .categories(product.categories())
            .build();
    }
}
