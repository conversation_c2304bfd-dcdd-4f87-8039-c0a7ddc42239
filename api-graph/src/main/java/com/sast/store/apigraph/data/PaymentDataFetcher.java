package com.sast.store.apigraph.data;

import com.sast.store.apigraph.codegen.types.Payment;
import com.sast.store.apigraph.codegen.types.PaymentConfig;
import com.sast.store.apigraph.converter.PaymentConfigConverter;
import com.sast.store.apigraph.converter.PaymentConverter;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.ordermanagement.api.PaymentApi;
import lombok.RequiredArgsConstructor;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.ContextValue;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import java.util.Optional;

@Controller
@RequiredArgsConstructor
public class PaymentDataFetcher {

    private final PaymentApi paymentApi;
    private final PaymentConfigConverter paymentConfigConverter;
    private final PaymentConverter paymentConverter;

    @QueryMapping
    @PreAuthorize("hasRole('DEFAULT')")
    public PaymentConfig paymentConfig(@ContextValue final Tenant tenant) {
        final var paymentConfig = paymentApi.getPaymentConfig(tenant);
        return paymentConfigConverter.convert(paymentConfig);
    }

    @QueryMapping
    @PreAuthorize("hasRole('DEFAULT')")
    public Payment payment(@ContextValue final Tenant tenant, @Argument final String paymentId) {
        final var payment = paymentApi.getPayment(tenant, paymentId);

        return Optional.ofNullable(payment)
            .map(paymentConverter::convert)
            .orElse(null);
    }
}
