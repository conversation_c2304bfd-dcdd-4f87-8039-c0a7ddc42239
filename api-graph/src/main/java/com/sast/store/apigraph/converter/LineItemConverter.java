package com.sast.store.apigraph.converter;

import com.sast.store.apigraph.codegen.types.LineItem;
import com.sast.store.ordermanagement.api.LineItemDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class LineItemConverter {
    private final MoneyConverter moneyConverter;
    private final VariantConverter productVariantConverter;

    public LineItem convert(final LineItemDto lineItem) {
        return LineItem.newBuilder()
            .lineItemId(lineItem.lineItemId())
            .name(lineItem.name())
            .productId(lineItem.productId())
            .sku(lineItem.sku())
            .variant(productVariantConverter.convert(lineItem.variant()))
            .itemPrice(moneyConverter.convert(lineItem.itemPrice()))
            .quantity(lineItem.quantity())
            .totalPrice(moneyConverter.convert(lineItem.totalPrice()))
            .build();
    }
}
