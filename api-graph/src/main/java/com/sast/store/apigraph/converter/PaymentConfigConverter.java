package com.sast.store.apigraph.converter;

import com.sast.store.apigraph.codegen.types.PaymentConfig;
import com.sast.store.ordermanagement.api.PaymentMethodConfigDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import static com.sast.store.apigraph.converter.Converters.convertList;

@Component
@RequiredArgsConstructor
public class PaymentConfigConverter {
    private final CheckoutInformationConverter checkoutInformationConverter;

    public PaymentConfig convert(final PaymentMethodConfigDto paymentMethodConfig) {
        return PaymentConfig.newBuilder()
            .paymentMethods(convertList(paymentMethodConfig.paymentMethods(), checkoutInformationConverter::convert))
            .build();
    }
}
