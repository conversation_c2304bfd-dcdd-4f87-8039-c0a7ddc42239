package com.sast.store.apigraph.data;

import com.sast.store.apigraph.codegen.types.AuthorizationInformation;
import com.sast.store.apigraph.codegen.types.Cart;
import com.sast.store.apigraph.codegen.types.CartAdd;
import com.sast.store.apigraph.codegen.types.CartCheckout;
import com.sast.store.apigraph.codegen.types.CartUpdate;
import com.sast.store.apigraph.converter.AuthorizationInformationConverter;
import com.sast.store.apigraph.converter.CartAddDtoConverter;
import com.sast.store.apigraph.converter.CartCheckoutDtoConverter;
import com.sast.store.apigraph.converter.CartConverter;
import com.sast.store.apigraph.converter.CartUpdateDtoConverter;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.ordermanagement.api.CartApi;
import lombok.RequiredArgsConstructor;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.ContextValue;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;

import java.util.Locale;
import java.util.Optional;

@Controller
@RequiredArgsConstructor
public class CartDataFetcher {

    private final CartApi cartApi;
    private final CartAddDtoConverter cartAddDtoConverter;
    private final CartConverter cartConverter;
    private final CartUpdateDtoConverter cartUpdateDtoConverter;
    private final CartCheckoutDtoConverter cartCheckoutDtoConverter;
    private final AuthorizationInformationConverter authorizationInformationConverter;

    @QueryMapping
    @PreAuthorize("hasRole('DEFAULT')")
    public Cart currentCart(@ContextValue final Tenant tenant, @Argument final Locale locale) {
        final var cart = cartApi.getCurrentCart(tenant,
            Optional.ofNullable(locale).map(Locale::toLanguageTag).orElse(null), null);
        return cartConverter.convert(cart);
    }

    @MutationMapping
    @PreAuthorize("hasRole('DEFAULT')")
    public Cart addToCurrentCart(@ContextValue final Tenant tenant, @Argument final CartAdd input) {
        final var cart = cartApi.addToCurrentCart(tenant, cartAddDtoConverter.convert(input));
        return cartConverter.convert(cart);
    }

    @MutationMapping
    @PreAuthorize("hasRole('DEFAULT')")
    public Cart updateCurrentCart(@ContextValue final Tenant tenant, @Argument final CartUpdate input) {
        final var cart = cartApi.updateCurrentCart(tenant, cartUpdateDtoConverter.convert(input));
        return cartConverter.convert(cart);
    }

    @MutationMapping
    @PreAuthorize("hasRole('DEFAULT')")
    public AuthorizationInformation checkoutCurrentCart(@ContextValue final Tenant tenant, @Argument final CartCheckout input) {
        final var authorizationInformation = cartApi.checkoutCart(tenant, cartCheckoutDtoConverter.convert(input));
        return authorizationInformationConverter.convert(authorizationInformation);
    }
}
