package com.sast.store.apigraph.scalars;

import com.sast.store.apigraph.scalars.period.PeriodScalar;
import graphql.schema.GraphQLScalarType;

/**
 * Custom scalars based on {@link graphql.scalars.ExtendedScalars}.
 */
@SuppressWarnings("checkstyle:ConstantNameCheck")
public final class CustomScalars {

    /**
     * A ISO-8601 duration of time based on {@link java.time.Period}.
     */
    public static final GraphQLScalarType Period = PeriodScalar.INSTANCE;

    private CustomScalars() {}
}
