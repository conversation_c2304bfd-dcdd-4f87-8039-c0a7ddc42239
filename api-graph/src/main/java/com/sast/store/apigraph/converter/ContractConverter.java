package com.sast.store.apigraph.converter;

import com.sast.store.apigraph.codegen.types.Contract;
import com.sast.store.apigraph.codegen.types.ContractState;
import com.sast.store.apigraph.codegen.types.ContractType;
import com.sast.store.contractmanagement.api.ContractDto;
import org.springframework.stereotype.Component;

@Component
public class ContractConverter {
    public Contract convert(final ContractDto contract) {
        return Contract.newBuilder()
            .companyId(contract.companyId())
            .contractId(contract.contractId())
            .orderNumber(contract.orderNumber())
            .productId(contract.productId())
            .startDate(contract.startDate())
            .projectedEndDate(contract.projectedEndDate())
            .endDate(contract.endDate())
            .contractType(ContractType.valueOf(contract.contractType().name()))
            .contractState(ContractState.valueOf(contract.contractState().name()))
            .contractPeriod(contract.contractPeriod())
            .noticePeriod(contract.noticePeriod())
            // TODO
            .addons(null)
            .build();
    }
}
