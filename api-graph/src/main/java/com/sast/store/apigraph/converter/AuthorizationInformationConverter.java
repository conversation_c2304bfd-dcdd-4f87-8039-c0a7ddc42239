package com.sast.store.apigraph.converter;

import com.sast.store.apigraph.codegen.types.AuthorizationInformation;
import com.sast.store.ordermanagement.api.AuthorizationInformationDto;
import org.springframework.stereotype.Component;

import static com.sast.store.apigraph.converter.Converters.convertNullable;

@Component
public class AuthorizationInformationConverter {
    public AuthorizationInformation convert(final AuthorizationInformationDto authorizationInformationDto) {
        return AuthorizationInformation.newBuilder()
            .paymentId(authorizationInformationDto.paymentId())
            .redirectUrl(convertNullable(authorizationInformationDto.redirectUrl(), Converters::toURL))
            .build();
    }
}
