package com.sast.store.apigraph.scalars.period;

import graphql.GraphQLContext;
import graphql.execution.CoercedVariables;
import graphql.language.StringValue;
import graphql.language.Value;
import graphql.schema.Coercing;
import graphql.schema.CoercingParseLiteralException;
import graphql.schema.CoercingParseValueException;
import graphql.schema.CoercingSerializeException;
import graphql.schema.GraphQLScalarType;
import lombok.NonNull;
import org.jetbrains.annotations.NotNull;

import java.time.Period;
import java.util.Locale;

import static graphql.scalars.util.Kit.typeName;

/**
 * Based on scalar implementations found in {@link graphql.scalars.ExtendedScalars}.
 */
@SuppressWarnings("checkstyle:AnonInnerLength")
public final class PeriodScalar {

    public static final GraphQLScalarType INSTANCE;

    static {
        final var coercing = new Coercing<Period, String>() {

            @Override
            public String serialize(@NonNull final Object dataFetcherResult, @NonNull final GraphQLContext graphQLContext,
                @NonNull final Locale locale) throws CoercingSerializeException {
                if (dataFetcherResult instanceof String string) {
                    try {
                        return Period.parse(string).toString();
                    } catch (Exception ex) {
                        throw new CoercingSerializeException(
                            "Unable to parse value to 'java.time.Period' because of: " + ex.getMessage(), ex);
                    }
                }

                if (dataFetcherResult instanceof Period period) {
                    return period.toString();
                }

                throw new CoercingSerializeException("Expected a 'java.time.Period' object but was " + typeName(dataFetcherResult));
            }

            @Override
            public Period parseValue(@NonNull final Object input, @NonNull final GraphQLContext graphQLContext,
                @NonNull final Locale locale) throws CoercingParseValueException {
                if (input instanceof Period period) {
                    return period;
                }

                if (input instanceof String string) {
                    try {
                        return Period.parse(string);
                    } catch (Exception ex) {
                        throw new CoercingParseValueException(
                            "Unable to parse value to 'java.time.Period' because of: " + ex.getMessage(), ex);
                    }
                }

                throw new CoercingParseValueException("Expected a 'java.lang.String' object but was " + typeName(input));
            }

            @Override
            public Period parseLiteral(@NonNull final Value<?> input, @NonNull final CoercedVariables variables,
                @NonNull final GraphQLContext graphQLContext, @NotNull final Locale locale) throws CoercingParseLiteralException {
                if (input instanceof StringValue stringValue) {
                    try {
                        return Period.parse(stringValue.getValue());
                    } catch (Exception ex) {
                        throw new CoercingParseLiteralException(
                            "Unable to parse value to 'java.time.Period' because of: " + ex.getMessage(), ex);
                    }
                }

                throw new CoercingParseLiteralException("Expected a 'graphql.language.StringValue' object but was " + typeName(input));
            }

            @Override
            public @NonNull Value<?> valueToLiteral(@NonNull final Object input, @NonNull final GraphQLContext graphQLContext,
                @NonNull final Locale locale) {
                final var value = serialize(input, graphQLContext, locale);
                return StringValue.newStringValue(value).build();
            }
        };

        INSTANCE = GraphQLScalarType.newScalar()
            .name("Period")
            .description("A ISO-8601 duration of time")
            .coercing(coercing)
            .build();
    }

    private PeriodScalar() {}
}
