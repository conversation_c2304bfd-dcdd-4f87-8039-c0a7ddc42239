package com.sast.store.contractmanagement.service;

import com.sast.store.brimtegration.apimodel.common.ApiVersion;
import com.sast.store.brimtegration.apimodel.common.BillingSyncStatus;
import com.sast.store.brimtegration.apimodel.common.payment.BoschTransferAchCreditPayment;
import com.sast.store.brimtegration.apimodel.common.product.terms.ConsumptionContractTypeConfiguration;
import com.sast.store.brimtegration.apimodel.common.product.terms.ContractPeriod;
import com.sast.store.brimtegration.apimodel.events.EventHeader;
import com.sast.store.brimtegration.apimodel.events.EventIdentity;
import com.sast.store.brimtegration.apimodel.events.egress.contract.EgressContractEventData;
import com.sast.store.brimtegration.apimodel.events.ingress.contract.data.BillingContract;
import com.sast.store.brimtegration.apimodel.events.ingress.order.data.BillingOrder;
import com.sast.store.brimtegration.apimodel.events.ingress.order.data.BillingOrderItem;
import com.sast.store.brimtegration.apimodel.events.ingress.order.data.BillingOrderStatus;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.contractmanagement.AbstractComponentTest;
import com.sast.store.contractmanagement.api.ContractType;
import com.sast.store.contractmanagement.dao.BrimContractDao;
import com.sast.store.contractmanagement.dao.BrimContractEntity;
import com.sast.store.contractmanagement.dao.ContractExpirationState;
import com.sast.store.contractmanagement.service.email.ContractEmailService;
import com.sast.store.contractmanagement.util.ContractmanagementTestDataGenerator;
import com.sast.store.external.brim.BrimMessagePublisher;
import jakarta.inject.Inject;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.time.Period;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import static java.time.OffsetDateTime.now;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

class ContractServiceTest extends AbstractComponentTest {

    @Inject
    private ContractmanagementTestDataGenerator testDataGenerator;

    @Inject
    private BrimContractDao brimContractDao;

    @Mock
    private BrimMessagePublisher brimMessagePublisher;

    @Mock
    private ContractEventPublisher contractEventPublisher;

    @Mock
    private ContractEmailService contractEmailService;

    @Captor
    private ArgumentCaptor<BrimContractEntity> brimContractEntityCaptor;

    @Captor
    private ArgumentCaptor<EgressContractEventData> egressContractEventDataCaptor;

    private BrimContractDao brimContractDaoSpy;

    private ContractService contractService;

    private final String contractId = "BC_123456";
    private final String productId = "DC_12345678Z100";

    @BeforeEach
    void setup() {
        this.brimContractDaoSpy = spy(this.brimContractDao);
        this.contractService = new ContractService(brimContractDaoSpy, brimMessagePublisher, contractEmailService,
            contractEventPublisher);
    }

    @Test
    void expireContracts_DoesNotProcessContractsThatAlreadyHaveExpirationState() {
        final var allContracts = Stream.of(
                Triple.of(now().minusDays(1), (ContractExpirationState) null, true),
                Triple.of(now().minusDays(1), (ContractExpirationState) null, true),
                Triple.of(now().minusDays(1), ContractExpirationState.PENDING, false),
                Triple.of(now().minusDays(1), ContractExpirationState.EXPIRED, false)
            )
            .map(pair -> Pair.of(
                testDataGenerator
                    .withContract()
                    .havingValue(entity -> entity
                        .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                        .withEndDate(pair.getLeft())
                        .withExpirationState(pair.getMiddle()))
                    .getContract(),
                pair.getRight()
            ))
            .toList();

        doAnswer(invocation -> allContracts.stream().map(Pair::getLeft))
            .when(brimContractDaoSpy)
            .findAllEndedAndNotExpired();

        contractService.expireContracts();

        verify(contractEventPublisher, times(2))
            .publishExpiration(brimContractEntityCaptor.capture());

        final var expectedContractIds = allContracts.stream()
            .filter(Pair::getRight)
            .map(Pair::getLeft)
            .map(BrimContractEntity::getContractId)
            .toList();

        assertThat(brimContractEntityCaptor.getAllValues())
            .extracting(BrimContractEntity::getContractId)
            .containsExactlyInAnyOrderElementsOf(expectedContractIds);
    }

    @Test
    void cancelContract_DoesNotProcessContractsThatAreNotSubscriptionOrConsumption() {
        assertContractCancellation(ContractType.SUBSCRIPTION, true);
        assertContractCancellation(ContractType.CONSUMPTION, true);
        assertContractCancellation(ContractType.ONE_TIME, false);
        assertContractCancellation(ContractType.FIXED_TERM, false);
    }

    private void assertContractCancellation(final ContractType contractType, final boolean assertExistence) {
        final var subscriptionContract = testDataGenerator
            .withContract()
            .havingValue(entity -> entity
                .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                .withContractType(contractType))
            .getContract();
        doAnswer(invocation -> Optional.of(subscriptionContract))
            .when(brimContractDaoSpy)
            .findByCompanyIdAndContractId(Tenant.REXROTH, COMPANY_ID, subscriptionContract.getContractId());

        final var expectedContractId = subscriptionContract.getContractId();

        if (assertExistence) {
            contractService.cancelContracts(Tenant.REXROTH, COMPANY_ID, List.of(subscriptionContract.getContractId()), "dummyuser");
            verify(brimMessagePublisher, atLeastOnce())
                .publishPlatformContractEvent(anyString(), egressContractEventDataCaptor.capture());

            assertThat(egressContractEventDataCaptor.getValue())
                .extracting(EgressContractEventData::contractId)
                .isEqualTo(expectedContractId);
        } else {
            final var contractIds = List.of(expectedContractId);
            assertThatThrownBy(() -> contractService.cancelContracts(Tenant.REXROTH, COMPANY_ID, contractIds, "dummyuser"))
                .isInstanceOf(IllegalArgumentException.class);
        }
    }

    @Test
    void processContracts_WithContractTypeConfigurationClause() {
        final Period expectedNoticePeriod = Period.ofMonths(1);
        final ConsumptionContractTypeConfiguration consumptionConfig = getNoticePeriod(expectedNoticePeriod);
        final BillingContract billingContract = getBillingContract(contractId, productId, consumptionConfig);
        final BillingOrder billingOrder = getBillingOrder(billingContract);
        final EventHeader eventHeader = getEventHeader();

        contractService.processContracts(eventHeader, billingOrder);

        final List<BrimContractEntity> savedContracts = brimContractDao.findAllAsList();
        assertThat(savedContracts).hasSize(1);
        assertThat(savedContracts.get(0).getNoticePeriod()).isEqualTo(expectedNoticePeriod);
    }

    @Test
    void processContractUpdate_UpdatesContractWithConsumptionContractTypeConfiguration() {
        final Period expectedNoticePeriod = Period.ofMonths(1);
        final ConsumptionContractTypeConfiguration consumptionConfig = getNoticePeriod(expectedNoticePeriod);
        final BillingContract billingContract = getBillingContract(contractId, productId, consumptionConfig);
        final EventHeader eventHeader = getEventHeader();
        final BrimContractEntity existingContract = getExistingContract(contractId);

        doAnswer(invocation -> Stream.of(existingContract))
            .when(brimContractDaoSpy)
            .findByContractId(eq(contractId));

        contractService.processContractUpdate(eventHeader, billingContract);

        verify(brimContractDaoSpy).save(existingContract);
        assertThat(existingContract.getNoticePeriod()).isEqualTo(expectedNoticePeriod);
        assertThat(existingContract.getProductId()).isEqualTo(productId);
    }

    private BrimContractEntity getExistingContract(final String contractId) {
        return new BrimContractEntity()
            .withContractId(contractId)
            .withCompanyId(Tenant.REXROTH, COMPANY_ID)
            .withUpdateTimestamp(OffsetDateTime.now().minusDays(1).toInstant());
    }

    private EventHeader getEventHeader() {
        return EventHeader.builder()
            .tenant(com.sast.store.brimtegration.apimodel.common.Tenant.REXROTH)
            .timestamp(ZonedDateTime.now())
            .version(ApiVersion.V1)
            .id(EventIdentity.builder().eventId("ei").senderName("se").build()).build();
    }

    private ConsumptionContractTypeConfiguration getNoticePeriod(final Period expectedNoticePeriod) {
        return ConsumptionContractTypeConfiguration.builder()
            .contractPeriod(ContractPeriod.MONTHLY)
            .noticePeriod(expectedNoticePeriod)
            .build();
    }

    private BillingContract getBillingContract(final String contractId, final String productId,
        final ConsumptionContractTypeConfiguration consumptionConfig) {
        return BillingContract.builder()
            .contractId(contractId)
            .productId(productId)
            .contractTypeConfiguration(consumptionConfig)
            .billingSyncStatus(BillingSyncStatus.IN_SYNC)
            .startDate(OffsetDateTime.now())
            .payment(BoschTransferAchCreditPayment.builder()
                .routingNumber("RN").accountNumber("AN").bankName("BN").bic("BC").build())
            .build();
    }

    private BillingOrder getBillingOrder(final BillingContract billingContract) {
        return BillingOrder.builder()
            .buyerCompanyId(COMPANY_ID)
            .orderNumber("ORDER_123")
            .payment(BoschTransferAchCreditPayment.builder()
                .routingNumber("RN").accountNumber("AN").bankName("BN").bic("BC").build())
            .billingSyncStatus(BillingSyncStatus.IN_SYNC)
            .billingOrderStatus(BillingOrderStatus.OPEN)
            .placedAt(ZonedDateTime.now())
            .items(List.of(BillingOrderItem.builder()
                .contracts(List.of(billingContract)).productId(productId)
                .quantity(1).amountAtOrderDate(BigDecimal.ONE)
                .priceStorefrontId("PS_123")
                .build()))
            .build();
    }
}
