package com.sast.store.contractmanagement.rest;

import com.sast.store.brimtegration.apimodel.events.egress.contract.EgressContractEvent;
import com.sast.store.brimtegration.apimodel.events.egress.contract.data.PlatformContractCancelled;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.contractmanagement.AbstractComponentTest;
import com.sast.store.contractmanagement.api.ContractType;
import com.sast.store.contractmanagement.dao.BrimContractDao;
import com.sast.store.contractmanagement.dao.BrimContractEntity;
import com.sast.store.contractmanagement.dao.ContractCancellationState;
import com.sast.store.contractmanagement.util.ContractmanagementTestDataGenerator;
import com.sast.store.testing.awsmockup.junit.SnsMockExtension;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import org.assertj.core.api.Assertions;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Test;

import java.time.temporal.ChronoUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.InstanceOfAssertFactories.type;

public class ContractRestServiceTest extends AbstractComponentTest {

    @Inject
    private ContractmanagementTestDataGenerator testDataGenerator;

    @Inject
    private BrimContractDao contractDao;

    @Test
    void testEmptyDatabase() {
        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .header("X-Tenant", "rexroth")
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .when().get(host + "/rest/contracts")
            .then().statusCode(200)
            .body(".", Matchers.hasSize(0));
    }

    @Test
    void testOneResult() {
        testDataGenerator
            .withContract().havingValue(e -> e.withCompanyId(Tenant.REXROTH, COMPANY_ID));
        final BrimContractEntity contract = testDataGenerator.getContract();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .header("X-Tenant", "rexroth")
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .when().get(host + "/rest/contracts")
            .then().statusCode(200)
            .body(".", Matchers.hasSize(1))
            .body("[0].companyId", Matchers.equalTo(COMPANY_ID))
            .body("[0].contractId", Matchers.equalTo(contract.getContractId()))
            .body("[0].orderNumber", Matchers.equalTo(contract.getOrderNumber()))
            .body("[0].productId", Matchers.equalTo(contract.getProductId()))
            .body("[0].startDate", Matchers.equalTo(contract.getStartDate().toString()))
            .body("[0].endDate", Matchers.equalTo(contract.getEndDate().toString()))
            .body("[0].contractType", Matchers.equalTo(contract.getContractType().toString()))
            .body("[0].contractPeriod", Matchers.equalTo(contract.getContractPeriod().toString()))
            .body("[0].noticePeriod", Matchers.equalTo(contract.getNoticePeriod().toString()));
    }

    @Test
    void testMultipleResults() {
        testDataGenerator
            .withContract().havingValue(e -> e.withCompanyId(Tenant.REXROTH, COMPANY_ID).withContractId("contractId1"))
            .withContract().havingValue(e -> e.withCompanyId(Tenant.REXROTH, COMPANY_ID).withContractId("contractId2"))
            .withContract().havingValue(e -> e.withCompanyId(Tenant.REXROTH, "someothercompanyid").withContractId("contractId0"))
            .withContract().havingValue(e -> e.withCompanyId(Tenant.REXROTH, COMPANY_ID).withContractId("contractId3"));

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .header("X-Tenant", "rexroth")
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .when().get(host + "/rest/contracts")
            .then().statusCode(200)
            .body(".", Matchers.hasSize(3))
            .body("[0].companyId", Matchers.equalTo(COMPANY_ID))
            .body("[0].contractId", Matchers.equalTo("contractId1"))
            .body("[1].contractId", Matchers.equalTo("contractId2"))
            .body("[2].contractId", Matchers.equalTo("contractId3"));

    }

    @Test
    void testCancelContract() {
        testDataGenerator
            .withContract().havingValue(e -> e.withCompanyId(Tenant.REXROTH, COMPANY_ID)
                .withContractType(ContractType.SUBSCRIPTION)
                .withCancellationState(ContractCancellationState.ACTIVE));
        final BrimContractEntity contract = testDataGenerator.getContract();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .body("{\"contractIds\":[\"" + contract.getContractId() + "\"]}")
            .header("X-Tenant", "rexroth")
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .when().post(host + "/rest/contracts/cancellations")
            .then().statusCode(204);

        final var contracts = contractDao.findAllAsList();
        assertThat(contracts).hasSize(1).first().satisfies(c -> {
            assertThat(c.getCancellationState()).isEqualTo(ContractCancellationState.CANCELLATION_PENDING);
        });

        final var messages = SnsMockExtension.getAllPublishedMessages(EgressContractEvent.class);
        assertThat(messages).hasSize(1).first().satisfies(m -> {
            assertThat(m.header().tenant()).isEqualTo(com.sast.store.brimtegration.apimodel.common.Tenant.REXROTH);
            assertThat(m.header().id().eventId()).isNotNull();
            assertThat(m.header().id().senderName()).isEqualTo("contractmanagementApplication");
            assertThat(m.data())
                .asInstanceOf(type(PlatformContractCancelled.class))
                .satisfies(data -> {
                    assertThat(data.contractId()).isEqualTo(contract.getContractId());
                    assertThat(data.cancellationReceivedAt()).isCloseToUtcNow(Assertions.within(20, ChronoUnit.SECONDS));
                });
        });
    }

    @Test
    void testCancelContractNotFound() {
        testDataGenerator
            .withContract().havingValue(e -> e.withCompanyId(Tenant.REXROTH, "someothercompany"));
        final BrimContractEntity contract = testDataGenerator.getContract();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .body("{\"contractIds\":[\"" + contract.getContractId() + "\"]}")
            .header("X-Tenant", "rexroth")
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .when().post(host + "/rest/contracts/cancellations")
            .then().statusCode(404);
    }
}
