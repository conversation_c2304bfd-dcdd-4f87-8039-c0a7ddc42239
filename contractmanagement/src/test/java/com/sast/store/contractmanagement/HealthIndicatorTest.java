package com.sast.store.contractmanagement;

import io.restassured.RestAssured;
import org.junit.jupiter.api.Test;

import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.equalTo;

class HealthIndicatorTest extends AbstractComponentTest {

    @Test
    void testInfoCheck() {
        RestAssured
            .given()
            .when().get(host + "/actuator/info")
            .then().statusCode(200);
    }

    @Test
    void testHealthCheck() {
        RestAssured
            .given()
            .when().get(host + "/actuator/health")
            .then().statusCode(200)
            .body("status", equalTo("UP"));
    }

    @Test
    void testReadynessCheck() {
        RestAssured
            .given()
            .when().get(host + "/actuator/health/readiness")
            .then().statusCode(200);
    }

    @Test
    void testLivenessCheck() {
        RestAssured
            .given()
            .when().get(host + "/actuator/health/liveness")
            .then().statusCode(200);
    }

    @Test
    void testPrometheusCheck() {
        RestAssured
            .given()
            .when().get(host + "/actuator/prometheus")
            .then().statusCode(200)
            .body(containsString("health 1.0"));
    }

}
