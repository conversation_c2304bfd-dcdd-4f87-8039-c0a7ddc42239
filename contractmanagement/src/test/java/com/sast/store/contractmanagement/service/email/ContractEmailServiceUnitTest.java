package com.sast.store.contractmanagement.service.email;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.contractmanagement.api.ContractType;
import com.sast.store.contractmanagement.config.AppConfiguration;
import com.sast.store.contractmanagement.dao.BrimContractEntity;
import com.sast.store.contractmanagement.dao.ContractCancellationState;
import com.sast.store.external.email.EmailServiceClient;
import com.sast.store.external.email.data.EmailRecipient;
import com.sast.store.external.email.data.EmailTenant;
import com.sast.store.external.email.data.TemplatedEmail;
import com.sast.store.external.ump.UmpClient;
import com.sast.store.gen.client.ump.api.UmpUserDto;
import com.sast.store.productmanagement.api.ProductApi;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.net.URI;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.Map;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
class ContractEmailServiceUnitTest {
    private static final String COMPANY_ID = "0bbd93b2-bcd3-4725-a569-cfffdfa4bf9b";
    private static final String USER_ID = "b3be2982-1693-4167-88f6-c535c0fbe94e";
    private static final String ORDER_ID = "O12345678";
    private static final String REXROTH_MARKETPLACE_URL = "https://rexroth.worksonmymachine.lol/marketplace";
    private static final String REXROTH_CONTRACTS_URL = "https://rexroth.worksonmymachine.lol/contracts";
    private static final OffsetDateTime CANCEL_DATE = OffsetDateTime.parse("2024-07-31T23:59:59+02:00");
    private static final LocalDateTime LOCAL_CANCEL_DATE = LocalDateTime.parse("2024-07-31T23:59:59");
    private static final String CONTRACT_ID = "BC_90000000001";
    private static final String FIRST_NAME = "Florp";
    private static final String LAST_NAME = "Morp";

    @Mock
    private EmailServiceClient emailServiceClient;

    @Mock
    private UmpClient umpClient;

    @Mock
    private AppConfiguration appConfiguration;

    @Mock
    private ProductApi productApi;

    @InjectMocks
    private ContractEmailService contractEmailService;

    @Captor
    private ArgumentCaptor<TemplatedEmail> templatedEmailCaptor;

    private AppConfiguration.TenantConfig rexrothConfig;

    @BeforeEach
    public void setup() throws Exception {
        final var rexrothUrlConfig = new AppConfiguration.PublicUrlConfig(new URI(REXROTH_MARKETPLACE_URL), new URI(REXROTH_CONTRACTS_URL));
        rexrothConfig = new AppConfiguration.TenantConfig(rexrothUrlConfig);
    }

    @Test
    public void testCancellationMailForUserCancellation() throws Exception {
        when(appConfiguration.tenants()).thenReturn(Map.of(Tenant.REXROTH, rexrothConfig));
        when(umpClient.getUser(eq("rexroth"), eq(USER_ID))).thenReturn(new UmpUserDto()
                .firstName(FIRST_NAME).lastName(LAST_NAME));
        final BrimContractEntity contract = new BrimContractEntity()
                .withOrderNumber(ORDER_ID)
                .withCancelledByUserId(USER_ID)
                .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                .withCancellationState(ContractCancellationState.CANCELLED)
                .withContractType(ContractType.SUBSCRIPTION)
                .withContractId(CONTRACT_ID)
                .withEndDate(CANCEL_DATE);

        contractEmailService.sendCancellationNotifications(contract);

        verify(emailServiceClient, times(2)).sendIgnoringFailures(templatedEmailCaptor.capture());
        assertThat(templatedEmailCaptor.getAllValues())
                .containsExactlyInAnyOrder(
                        TemplatedEmail.builder()
                                .to(EmailRecipient.forUmpUserId(USER_ID))
                                .tenant(EmailTenant.rexroth)
                                .templateData(RexrothCancellationEmailDto.builder()
                                        .cancelledByFirstName(FIRST_NAME)
                                        .cancelledByLastName(LAST_NAME)
                                        .orderNumber(ORDER_ID)
                                        .endDate(LOCAL_CANCEL_DATE)
                                        .marketplaceUrl(new URI(REXROTH_MARKETPLACE_URL))
                                        .build())
                                .build(),
                        TemplatedEmail.builder()
                                .to(EmailRecipient.forUmpCompanyId(COMPANY_ID))
                                .tenant(EmailTenant.rexroth)
                                .templateData(RexrothCancellationEmailDto.builder()
                                        .cancelledByFirstName(FIRST_NAME)
                                        .cancelledByLastName(LAST_NAME)
                                        .orderNumber(ORDER_ID)
                                        .endDate(LOCAL_CANCEL_DATE)
                                        .marketplaceUrl(new URI(REXROTH_MARKETPLACE_URL))
                                        .build())
                                .build()
                );
    }

    @Test
    public void testCancellationMailForBackendCancellation() throws Exception {
        when(appConfiguration.tenants()).thenReturn(Map.of(Tenant.REXROTH, rexrothConfig));
        final BrimContractEntity contract = new BrimContractEntity()
                .withOrderNumber(ORDER_ID)
                .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                .withCancellationState(ContractCancellationState.CANCELLED)
                .withContractType(ContractType.SUBSCRIPTION)
                .withContractId(CONTRACT_ID)
                .withEndDate(CANCEL_DATE);

        contractEmailService.sendCancellationNotifications(contract);

        verify(emailServiceClient, times(1)).sendIgnoringFailures(templatedEmailCaptor.capture());
        assertThat(templatedEmailCaptor.getAllValues())
                .containsExactly(TemplatedEmail.builder()
                        .to(EmailRecipient.forUmpCompanyId(COMPANY_ID))
                        .tenant(EmailTenant.rexroth)
                        .templateData(RexrothCancellationEmailDto.builder()
                                .orderNumber(ORDER_ID)
                                .endDate(LOCAL_CANCEL_DATE)
                                .marketplaceUrl(new URI(REXROTH_MARKETPLACE_URL))
                                .build())
                        .build()
                );
    }

    @Test
    public void testCancellationMailForUserCancellationOnlySendsCompanyMailIfUserCannotBeFetched() throws Exception {
        when(appConfiguration.tenants()).thenReturn(Map.of(Tenant.REXROTH, rexrothConfig));
        when(umpClient.getUser(eq("rexroth"), eq(USER_ID))).thenThrow(new RuntimeException());
        final BrimContractEntity contract = new BrimContractEntity()
                .withOrderNumber(ORDER_ID)
                .withCancelledByUserId(USER_ID)
                .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                .withCancellationState(ContractCancellationState.CANCELLED)
                .withContractType(ContractType.SUBSCRIPTION)
                .withContractId(CONTRACT_ID)
                .withEndDate(CANCEL_DATE);

        contractEmailService.sendCancellationNotifications(contract);

        verify(emailServiceClient, times(1)).sendIgnoringFailures(templatedEmailCaptor.capture());
        assertThat(templatedEmailCaptor.getAllValues())
                .containsExactly(TemplatedEmail.builder()
                        .to(EmailRecipient.forUmpCompanyId(COMPANY_ID))
                        .tenant(EmailTenant.rexroth)
                        .templateData(RexrothCancellationEmailDto.builder()
                                .orderNumber(ORDER_ID)
                                .endDate(LOCAL_CANCEL_DATE)
                                .marketplaceUrl(new URI(REXROTH_MARKETPLACE_URL))
                                .build())
                        .build()
                );
    }

    public static Stream<ContractType> testCancellationMailIsNotSentForOtherContractTypes() {
        return Stream.of(ContractType.FIXED_TERM, ContractType.ONE_TIME);
    }

    @ParameterizedTest
    @MethodSource
    public void testCancellationMailIsNotSentForOtherContractTypes(final ContractType contractType) throws Exception {
        final BrimContractEntity contract = new BrimContractEntity()
                .withOrderNumber(ORDER_ID)
                .withCancelledByUserId(USER_ID)
                .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                .withCancellationState(ContractCancellationState.CANCELLED)
                .withContractType(contractType)
                .withContractId(CONTRACT_ID)
                .withEndDate(CANCEL_DATE);

        assertThatThrownBy(() -> contractEmailService.sendCancellationNotifications(contract));
        verify(emailServiceClient, never()).sendIgnoringFailures(any());
    }

    @Test
    public void testCancellationMailIsNotSentForSubscriptionWithoutEndDate() throws Exception {
        final BrimContractEntity contract = new BrimContractEntity()
                .withOrderNumber(ORDER_ID)
                .withCancelledByUserId(USER_ID)
                .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                .withCancellationState(ContractCancellationState.CANCELLED)
                .withContractType(ContractType.SUBSCRIPTION)
                .withContractId(CONTRACT_ID);

        assertThatThrownBy(() -> contractEmailService.sendCancellationNotifications(contract));
        verify(emailServiceClient, never()).sendIgnoringFailures(any());
    }

    @Test
    public void testCancellationMailIsNotSentForNonRexrothContracts() throws Exception {
        final BrimContractEntity contract = new BrimContractEntity()
                .withOrderNumber(ORDER_ID)
                .withCancelledByUserId(USER_ID)
                .withCompanyId(Tenant.BAAM, COMPANY_ID)
                .withCancellationState(ContractCancellationState.CANCELLED)
                .withContractType(ContractType.SUBSCRIPTION)
                .withContractId(CONTRACT_ID);

        assertThatThrownBy(() -> contractEmailService.sendCancellationNotifications(contract));
        verify(emailServiceClient, never()).sendIgnoringFailures(any());
    }

    @Test
    public void testExceptionIsThrownIfTenantConfigurationIsMissing() throws Exception {
        when(appConfiguration.tenants()).thenReturn(Map.of());
        when(umpClient.getUser(eq("rexroth"), eq(USER_ID))).thenReturn(new UmpUserDto()
                .firstName(FIRST_NAME).lastName(LAST_NAME));
        final BrimContractEntity contract = new BrimContractEntity()
                .withOrderNumber(ORDER_ID)
                .withCancelledByUserId(USER_ID)
                .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                .withCancellationState(ContractCancellationState.CANCELLED)
                .withContractType(ContractType.SUBSCRIPTION)
                .withContractId(CONTRACT_ID)
                .withEndDate(CANCEL_DATE);

        assertThatThrownBy(() -> contractEmailService.sendCancellationNotifications(contract));
        verify(emailServiceClient, never()).sendIgnoringFailures(any());
    }

    @Test
    public void testExpirationMailIsNotSentIfTenantConfigurationIsMissing() {
        when(appConfiguration.tenants()).thenReturn(Map.of());
        final BrimContractEntity contract = new BrimContractEntity()
                .withOrderNumber(ORDER_ID)
                .withCancelledByUserId(USER_ID)
                .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                .withCancellationState(ContractCancellationState.CANCELLED)
                .withContractType(ContractType.SUBSCRIPTION)
                .withContractId(CONTRACT_ID)
                .withEndDate(CANCEL_DATE);

        assertThatThrownBy(() -> contractEmailService.sendExpiredNotifications(contract))
                .isInstanceOf(IllegalStateException.class);
        verify(emailServiceClient, never()).sendIgnoringFailures(any());
    }

    @Test
    public void testExpirationMailIsNotSentForNonRexrothContracts() throws Exception {
        final BrimContractEntity contract = new BrimContractEntity()
                .withOrderNumber(ORDER_ID)
                .withCancelledByUserId(USER_ID)
                .withCompanyId(Tenant.BAAM, COMPANY_ID)
                .withCancellationState(ContractCancellationState.CANCELLED)
                .withContractType(ContractType.SUBSCRIPTION)
                .withContractId(CONTRACT_ID);

        assertThatThrownBy(() -> contractEmailService.sendExpiredNotifications(contract))
                .isInstanceOf(IllegalArgumentException.class);
        verify(emailServiceClient, never()).sendIgnoringFailures(any());
    }
}
