package com.sast.store.contractmanagement.service.email;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.contractmanagement.AbstractComponentTest;
import com.sast.store.contractmanagement.api.ContractType;
import com.sast.store.contractmanagement.dao.BrimContractEntity;
import com.sast.store.contractmanagement.dao.ContractCancellationState;
import com.sast.store.contractmanagement.util.CommercetoolsMockExtension;
import com.sast.store.external.ump.test.UmpMockExtension;
import com.sast.store.productmanagement.test.ProductServiceMockExtension;
import com.sast.store.testing.awsmockup.junit.SqsMockExtension;
import jakarta.inject.Inject;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import software.amazon.awssdk.services.sqs.model.Message;

import java.time.Duration;
import java.time.OffsetDateTime;
import java.util.Optional;

class ContractEmailServiceTest extends AbstractComponentTest {
    @Inject
    private ContractEmailService contractEmailService;

    @AfterEach
    void tearDown() {
        CommercetoolsMockExtension.get().resetAll();
    }

    @Test
    void userCancelledSubscriptionCausesUserAndCompanyEmails() throws Exception {
        UmpMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withDefaultResponse();

        final BrimContractEntity contract = new BrimContractEntity()
                .withOrderNumber("O12345678")
                .withCancelledByUserId("564b26de-edd4-4494-869a-448e83838269")
                .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                .withCancellationState(ContractCancellationState.CANCELLED)
                .withContractType(ContractType.SUBSCRIPTION)
                .withContractId("BC_90000000001")
                .withProductId("DDCIH_hydraulic_hub_quarterly")
                .withEndDate(OffsetDateTime.parse("2024-07-31T23:59:59Z"));

        contractEmailService.sendCancellationNotifications(contract);

        final Message actualUserEmail = Awaitility.await().atMost(Duration.ofSeconds(5))
                .until(() -> SqsMockExtension.receiveMessage("EmailserviceMail"), Optional::isPresent)
                .orElseThrow();
        final Message actualcompanyEmail = Awaitility.await().atMost(Duration.ofSeconds(5))
                .until(() -> SqsMockExtension.receiveMessage("EmailserviceMail"), Optional::isPresent)
                .orElseThrow();

        JSONAssert.assertEquals("""
                {
                  "to": [
                    "<EMAIL>"
                  ],
                  "tenant": "rexroth",
                  "properties": {
                    "productName": "Hydraulic Hub",
                    "cancelledByFirstName": "myFirstname",
                    "firstName": "myFirstname",
                    "lastName": "myLastname",
                    "orderNumber": "O12345678",
                    "endDate": "2024-07-31T23:59:59",
                    "marketplaceUrl": "https://localhost:8000/foo/bar/baz",
                    "cancelledByLastName": "myLastname"
                  },
                  "locale": "de",
                  "templateName": "rexroth/contracts/cancelConfirmation"
                }
                """,
                actualUserEmail.body(), JSONCompareMode.LENIENT);
        JSONAssert.assertEquals("""
                {
                  "to": [
                    "<EMAIL>"
                  ],
                  "tenant": "rexroth",
                  "properties": {
                    "productName": "Hydraulic Hub",
                    "cancelledByFirstName": "myFirstname",
                    "orderNumber": "O12345678",
                    "endDate": "2024-07-31T23:59:59",
                    "marketplaceUrl": "https://localhost:8000/foo/bar/baz",
                    "cancelledByLastName": "myLastname"
                  },
                  "locale": "de",
                  "templateName": "rexroth/contracts/cancelConfirmation"
                }
                """,
                actualcompanyEmail.body(), JSONCompareMode.LENIENT);
    }

    @Test
    void systemCancelledSubscriptionCausesCompanyEmail() throws Exception {
        UmpMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withDefaultResponse();

        final BrimContractEntity contract = new BrimContractEntity()
                .withOrderNumber("O12345678")
                .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                .withCancellationState(ContractCancellationState.CANCELLED)
                .withContractType(ContractType.SUBSCRIPTION)
                .withContractId("BC_90000000001")
                .withProductId("DDCIH_hydraulic_hub_quarterly")
                .withEndDate(OffsetDateTime.parse("2024-07-31T23:59:59Z"));

        contractEmailService.sendCancellationNotifications(contract);

        final Message actualcompanyEmail = Awaitility.await().atMost(Duration.ofSeconds(5))
                .until(() -> SqsMockExtension.receiveMessage("EmailserviceMail"), Optional::isPresent)
                .orElseThrow();

        JSONAssert.assertEquals("""
                {
                  "to": [
                    "<EMAIL>"
                  ],
                  "tenant": "rexroth",
                  "properties": {
                    "productName": "Hydraulic Hub",
                    "orderNumber": "O12345678",
                    "endDate": "2024-07-31T23:59:59",
                    "marketplaceUrl": "https://localhost:8000/foo/bar/baz"
                  },
                  "locale": "de",
                  "templateName": "rexroth/contracts/cancelConfirmation"
                }
                """,
                actualcompanyEmail.body(), JSONCompareMode.LENIENT);
    }


    @Test
    void expiredNotificationsAreSentToCancellationUserAndCompany() throws Exception {
        CommercetoolsMockExtension.withDefaultResponse();
        UmpMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withDefaultResponse();

        final BrimContractEntity contract = new BrimContractEntity()
                .withOrderNumber("O12345678")
                .withCancelledByUserId("564b26de-edd4-4494-869a-448e83838269")
                .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                .withCancellationState(ContractCancellationState.CANCELLED)
                .withContractType(ContractType.SUBSCRIPTION)
                .withContractId("BC_90000000001")
                .withProductId("DDCIH_hydraulic_hub_quarterly")
                .withEndDate(OffsetDateTime.parse("2024-07-31T23:59:59Z"));

        contractEmailService.sendExpiredNotifications(contract);

        final Message actualUserEmail = Awaitility.await().atMost(Duration.ofSeconds(5))
                .until(() -> SqsMockExtension.receiveMessage("EmailserviceMail"), Optional::isPresent)
                .orElseThrow();
        final Message actualcompanyEmail = Awaitility.await().atMost(Duration.ofSeconds(5))
                .until(() -> SqsMockExtension.receiveMessage("EmailserviceMail"), Optional::isPresent)
                .orElseThrow();

        JSONAssert.assertEquals("""
                {
                  "to": [
                    "<EMAIL>"
                  ],
                  "tenant": "rexroth",
                  "properties": {
                    "firstName": "myFirstname",
                    "lastName": "myLastname",
                    "orderNumber": "O12345678",
                    "contractManagementUrl": "https://localhost:8000/florp",
                    "marketplaceUrl": "https://localhost:8000/foo/bar/baz",
                    "productName": "Hydraulic Hub"
                  },
                  "locale": "de",
                  "templateName": "rexroth/contracts/contractExpired"
                }
                """,
                actualUserEmail.body(), JSONCompareMode.LENIENT);
        JSONAssert.assertEquals("""
                {
                  "to": [
                    "<EMAIL>"
                  ],
                  "tenant": "rexroth",
                  "properties": {
                    "orderNumber": "O12345678",
                    "contractManagementUrl": "https://localhost:8000/florp",
                    "marketplaceUrl": "https://localhost:8000/foo/bar/baz",
                    "productName": "Hydraulic Hub"
                  },
                  "locale": "de",
                  "templateName": "rexroth/contracts/contractExpired"
                }
                """,
                actualcompanyEmail.body(), JSONCompareMode.LENIENT);
    }
}
