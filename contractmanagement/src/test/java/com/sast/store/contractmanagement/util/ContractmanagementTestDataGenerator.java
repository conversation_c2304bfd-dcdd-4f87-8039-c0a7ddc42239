package com.sast.store.contractmanagement.util;

import com.sast.store.contractmanagement.dao.BrimContractDao;
import com.sast.store.contractmanagement.dao.BrimContractEntity;
import com.sast.store.testing.commons.AbstractTestDataGenerator;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import org.jeasy.random.EasyRandom;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.function.Function;

@Component
public class ContractmanagementTestDataGenerator extends AbstractTestDataGenerator<ContractmanagementTestDataGenerator> {

    protected ContractmanagementTestDataGenerator generator;

    @Inject
    private BrimContractDao brimContractDao;

    private BrimContractGenerator brimContractGenerator;

    private final EasyRandom rnd = new EasyRandom();

    @PostConstruct
    public void init() {
        brimContractGenerator = this.new BrimContractGenerator(this);
        this.generator = this;
    }

    public BrimContractGenerator withContract() {
        final BrimContractEntity contract = generator.rnd.nextObject(BrimContractEntity.class);
        generator.brimContractDao.save(contract);
        generator.setData(contract);
        return generator.brimContractGenerator;
    }

    public class BrimContractGenerator extends ContractmanagementTestDataGenerator {

        public BrimContractGenerator(final ContractmanagementTestDataGenerator generator) {
            this.generator = generator;
        }

        public BrimContractGenerator havingValue(final Function<BrimContractEntity, BrimContractEntity> consumer) {
            final BrimContractEntity key = new BrimContractEntity();
            key.setCompanyId(generator.getContract().getCompanyId());
            key.setContractId(generator.getContract().getContractId());

            final BrimContractEntity newLicense = consumer.apply(generator.getContract());
            generator.brimContractDao.delete(key);
            generator.getData(BrimContractEntity.class).removeLast();

            generator.brimContractDao.save(newLicense);
            generator.setData(newLicense);
            return this;
        }

    }

    public BrimContractEntity getContract() {
        return generator.getData(BrimContractEntity.class).getLast();
    }

    public LinkedList<BrimContractEntity> getContracts() {
        return generator.getData(BrimContractEntity.class);
    }

}
