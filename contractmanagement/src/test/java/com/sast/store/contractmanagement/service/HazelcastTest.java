package com.sast.store.contractmanagement.service;

import com.hazelcast.config.Config;
import com.hazelcast.core.Hazelcast;
import com.hazelcast.core.HazelcastInstance;
import com.sast.store.commons.hazelcast.Queue;
import com.sast.store.contractmanagement.AbstractComponentTest;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Duration;

import static org.assertj.core.api.Assertions.assertThat;

public class HazelcastTest extends AbstractComponentTest {

    @Autowired
    private Config hazelcastConfig;

    @Autowired
    private HazelcastInstance hazelcastInstance1;

    private String receivedMessage;
    private TestRecord receivedMessage2;
    private TestObject receivedMessage3;

    @Test
    public void testLocalConfigurationWorks() throws Exception {
        final HazelcastInstance hazelcastInstance2 = Hazelcast.newHazelcastInstance(hazelcastConfig);

        Awaitility.await().atMost(Duration.ofSeconds(10)).untilAsserted(() -> {
            assertThat(hazelcastInstance2.getCluster().getMembers()).hasSize(2);
        });

        assertThat(hazelcastInstance1.getCluster().getMembers()).hasSize(2);
        assertThat(hazelcastInstance2.getCluster().getMembers()).hasSize(2);
        hazelcastInstance2.shutdown();
    }

    @Test
    public void testQueueSubscriber() throws Exception {
        final HazelcastInstance hazelcastInstance2 = Hazelcast.newHazelcastInstance(hazelcastConfig);

        Awaitility.await().atMost(Duration.ofSeconds(10)).untilAsserted(() -> {
            assertThat(hazelcastInstance2.getCluster().getMembers()).hasSize(2);
        });

        final String queuename = "testqueue";
        Queue.subscribe(hazelcastInstance2.getQueue(queuename), t -> {
            receivedMessage = (String) t;
        });
        hazelcastInstance1.getQueue(queuename).add("testmessage");

        Awaitility.await().atMost(Duration.ofSeconds(10)).untilAsserted(() -> {
            assertThat(receivedMessage).isEqualTo("testmessage");
        });
        hazelcastInstance2.shutdown();
    }

    @Test
    public void testQueueSubscriberWithRecord() throws Exception {
        final HazelcastInstance hazelcastInstance2 = Hazelcast.newHazelcastInstance(hazelcastConfig);

        Awaitility.await().atMost(Duration.ofSeconds(10)).untilAsserted(() -> {
            assertThat(hazelcastInstance2.getCluster().getMembers()).hasSize(2);
        });

        final String queuename = "testqueue";
        Queue.subscribe(hazelcastInstance2.getQueue(queuename), t -> {
            receivedMessage2 = (TestRecord) t;
        });
        hazelcastInstance1.getQueue(queuename).add(new TestRecord("testmessage"));

        Awaitility.await().atMost(Duration.ofSeconds(10)).untilAsserted(() -> {
            assertThat(receivedMessage2.content()).isEqualTo("testmessage");
        });
        hazelcastInstance2.shutdown();
    }

    @Test
    public void testQueueSubscriberWithObject() throws Exception {
        final HazelcastInstance hazelcastInstance2 = Hazelcast.newHazelcastInstance(hazelcastConfig);

        Awaitility.await().atMost(Duration.ofSeconds(10)).untilAsserted(() -> {
            assertThat(hazelcastInstance2.getCluster().getMembers()).hasSize(2);
        });

        final String queuename = "testqueue";
        Queue.subscribe(hazelcastInstance2.getQueue(queuename), t -> {
            receivedMessage3 = (TestObject) t;
        });
        hazelcastInstance1.getQueue(queuename).add(new TestObject("testmessage"));

        Awaitility.await().atMost(Duration.ofSeconds(10)).untilAsserted(() -> {
            assertThat(receivedMessage3.content).isEqualTo("testmessage");
        });
        hazelcastInstance2.shutdown();
    }

    private static record TestRecord(String content) {
    }

    private static class TestObject {
        private final String content;

        public TestObject(final String content) {
            this.content = content;
        }
    }
}
