package com.sast.store.contractmanagement.service.converter;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.time.OffsetDateTime;
import java.time.Period;
import java.time.ZoneOffset;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.params.provider.Arguments.arguments;

class ContractCalculatorTest {

    private static final ZoneOffset UTC = ZoneOffset.UTC;
    private static final Period ONE_YEAR = Period.ofYears(1);
    private static final Period TWO_MONTHS = Period.ofMonths(2);
    private static final Period ONE_MONTH = Period.ofMonths(1);
    private static final Period SEVEN_DAYS = Period.ofDays(7);

    @DisplayName("Test calculateEndDate with multiple scenarios")
    @ParameterizedTest
    @MethodSource("provideTestCases")
    void testCalculateEndDate(
        final OffsetDateTime startDate, final Period runtime, final Period noticePeriod, final OffsetDateTime today,
        final OffsetDateTime cancellationDate, final OffsetDateTime expectedEndDate, final String caseName) {

        final ContractCalculator calculator = new ContractCalculator(startDate, runtime, noticePeriod);
        final OffsetDateTime result = calculator.calculateEndDate(cancellationDate, today);
        assertEquals(expectedEndDate, result, caseName + " case failed");
    }

    static Stream<Arguments> provideTestCases() {
        return Stream.of(
            arguments(
                OffsetDateTime.parse("2020-01-01T00:00:00+00:00"),
                ONE_YEAR,
                TWO_MONTHS,
                OffsetDateTime.parse("2021-02-01T00:00:00+00:00"),
                OffsetDateTime.parse("2021-10-15T00:00:00+00:00"),
                OffsetDateTime.parse("2021-12-31T23:59:59+00:00"),
                "Basic functionality before notice period"
            ),
            arguments(
                OffsetDateTime.parse("2020-01-01T00:00:00+00:00"),
                ONE_YEAR,
                TWO_MONTHS,
                OffsetDateTime.parse("2021-02-01T00:00:00+00:00"),
                OffsetDateTime.parse("2021-12-15T00:00:00+00:00"),
                OffsetDateTime.parse("2022-12-31T23:59:59+00:00"),
                "Basic functionality after notice period"
            ),
            arguments(
                OffsetDateTime.parse("2024-03-01T00:00:00+00:00"),
                ONE_MONTH,
                SEVEN_DAYS,
                OffsetDateTime.parse("2024-03-15T00:00:00+00:00"),
                OffsetDateTime.parse("2024-03-25T00:00:00+00:00"),
                OffsetDateTime.parse("2024-04-30T23:59:59+00:00"),
                "Daily calculation after notice period"
            ),
            arguments(
                OffsetDateTime.parse("2024-03-01T00:00:00+00:00"),
                ONE_MONTH,
                SEVEN_DAYS,
                OffsetDateTime.parse("2024-03-15T00:00:00+00:00"),
                OffsetDateTime.parse("2024-03-10T00:00:00+00:00"),
                OffsetDateTime.parse("2024-03-31T23:59:59+00:00"),
                "Daily calculation before notice period"
            ),
            arguments(
                OffsetDateTime.parse("2024-03-01T00:00:00+00:00"),
                ONE_MONTH,
                ONE_MONTH,
                OffsetDateTime.parse("2024-03-15T00:00:00+00:00"),
                OffsetDateTime.parse("2024-03-25T00:00:00+00:00"),
                OffsetDateTime.parse("2024-04-30T23:59:59+00:00"),
                "Monthly calculation"
            )
        );
    }
}