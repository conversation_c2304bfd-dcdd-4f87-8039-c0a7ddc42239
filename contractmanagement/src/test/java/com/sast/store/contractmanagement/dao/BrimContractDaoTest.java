package com.sast.store.contractmanagement.dao;

import com.sast.store.contractmanagement.AbstractComponentTest;
import com.sast.store.contractmanagement.util.ContractmanagementTestDataGenerator;
import jakarta.inject.Inject;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.stream.Stream;

import static java.time.OffsetDateTime.now;
import static org.assertj.core.api.Assertions.assertThat;

class BrimContractDaoTest extends AbstractComponentTest {

    @Inject
    private ContractmanagementTestDataGenerator testDataGenerator;

    @Inject
    private BrimContractDao brimContractDao;

    @Test
    void findAllEndedAndNotExpired_ReturnsOnlyEndedAndUnexpiredContracts() {
        final var allContracts = Stream.of(
                Triple.of(now().minusDays(1), (ContractExpirationState) null, true),
                Triple.of(now().plusDays(1), (ContractExpirationState) null, false),
                Triple.of(now().minusDays(1), ContractExpirationState.PENDING, false),
                Triple.of(now().plusDays(1), ContractExpirationState.PENDING, false),
                Triple.of(now().minusDays(1), ContractExpirationState.EXPIRED, false),
                Triple.of(now().plusDays(1), ContractExpirationState.EXPIRED, false)
            )
            .map(pair -> Pair.of(
                testDataGenerator
                    .withContract()
                    .havingValue(entity -> entity
                        .withEndDate(pair.getLeft())
                        .withExpirationState(pair.getMiddle()))
                    .getContract(),
                pair.getRight()
            ))
            .toList();

        assertThat(allContracts)
            .extracting(Pair::getLeft)
            .extracting(BrimContractEntity::getContractId)
            .doesNotHaveDuplicates()
            .hasSize(6);

        final var foundContracts = brimContractDao.findAllEndedAndNotExpired();

        final var expectedContractIds = allContracts.stream()
            .filter(Pair::getRight)
            .map(Pair::getLeft)
            .map(BrimContractEntity::getContractId)
            .toList();

        assertThat(foundContracts)
            .extracting(BrimContractEntity::getContractId)
            .containsExactlyInAnyOrderElementsOf(expectedContractIds);
    }

    @ParameterizedTest
    @MethodSource
    void updateConditionally_OnlyUpdatesWhenEntityHasExpectedState(final ContractExpirationState initialState,
        final ContractExpirationState newState, final ContractExpirationState expectedState, final boolean expectedResult) {
        final var brimContractEntity = testDataGenerator
            .withContract()
            .havingValue(entity -> entity.withExpirationState(initialState))
            .getContract();

        assertThat(brimContractEntity)
            .extracting(BrimContractEntity::getExpirationState)
            .isEqualTo(initialState);

        brimContractEntity.setExpirationState(newState);
        final var updated = brimContractDao.updateConditionally(brimContractEntity, expectedState);

        assertThat(updated).isEqualTo(expectedResult);
    }

    static Stream<Arguments> updateConditionally_OnlyUpdatesWhenEntityHasExpectedState() {
        return Stream.of(
            Arguments.of(null, ContractExpirationState.PENDING, null, true),
            Arguments.of(ContractExpirationState.PENDING, ContractExpirationState.PENDING, null, false),
            Arguments.of(ContractExpirationState.PENDING, ContractExpirationState.EXPIRED, ContractExpirationState.PENDING, true),
            Arguments.of(null, ContractExpirationState.EXPIRED, ContractExpirationState.PENDING, false),
            Arguments.of(ContractExpirationState.EXPIRED, ContractExpirationState.EXPIRED, ContractExpirationState.PENDING, false)
        );
    }
}
