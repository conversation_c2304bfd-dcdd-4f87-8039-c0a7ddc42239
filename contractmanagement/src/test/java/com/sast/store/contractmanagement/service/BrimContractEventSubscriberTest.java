package com.sast.store.contractmanagement.service;

import com.hazelcast.config.Config;
import com.hazelcast.core.Hazelcast;
import com.hazelcast.core.HazelcastInstance;
import com.sast.store.brimtegration.apimodel.common.ApiVersion;
import com.sast.store.brimtegration.apimodel.common.BillingSyncStatus;
import com.sast.store.brimtegration.apimodel.common.payment.BoschTransferSepaCreditPayment;
import com.sast.store.brimtegration.apimodel.common.product.terms.ContractPeriod;
import com.sast.store.brimtegration.apimodel.common.product.terms.SubscriptionContractTypeConfiguration;
import com.sast.store.brimtegration.apimodel.events.EventHeader;
import com.sast.store.brimtegration.apimodel.events.EventIdentity;
import com.sast.store.brimtegration.apimodel.events.ingress.contract.IngressContractEvent;
import com.sast.store.brimtegration.apimodel.events.ingress.contract.IngressContractEventData;
import com.sast.store.brimtegration.apimodel.events.ingress.contract.data.BillingAddonContract;
import com.sast.store.brimtegration.apimodel.events.ingress.contract.data.BillingContract;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.contractmanagement.AbstractComponentTest;
import com.sast.store.contractmanagement.api.ContractEvent;
import com.sast.store.contractmanagement.api.ContractState;
import com.sast.store.contractmanagement.api.ContractType;
import com.sast.store.contractmanagement.dao.BrimContractDao;
import com.sast.store.contractmanagement.dao.BrimContractEntity;
import com.sast.store.contractmanagement.dao.ContractCancellationState;
import com.sast.store.contractmanagement.util.ContractmanagementTestDataGenerator;
import com.sast.store.external.ump.test.UmpMockExtension;
import com.sast.store.productmanagement.test.ProductServiceMockExtension;
import com.sast.store.testing.awsmockup.junit.EmailUtil;
import com.sast.store.testing.awsmockup.junit.SqsMockExtension;
import jakarta.inject.Inject;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.json.BasicJsonTester;
import software.amazon.awssdk.services.sqs.model.Message;

import java.time.Duration;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.Period;
import java.time.ZonedDateTime;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class BrimContractEventSubscriberTest extends AbstractComponentTest {

    @Inject
    private ContractmanagementTestDataGenerator testDataGenerator;

    @Inject
    private BrimContractDao contractDao;

    @Inject
    private BrimContractEventSubscriber contractEventSubscriber;

    @Inject
    private Config hazelcastConfig;

    @Test
    void testContractUpdateSqsMessage() throws Exception {
        UmpMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withDefaultResponse();

        testDataGenerator
                .withContract().havingValue(e -> e
                        .withOrderNumber("O12345678")
                        .withCancelledByUserId("564b26de-edd4-4494-869a-448e83838269")
                        .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                        .withCancellationState(ContractCancellationState.CANCELLATION_PENDING)
                        .withContractType(ContractType.SUBSCRIPTION)
                        .withContractId("BC_90000000001")
                        .withUpdateTimestamp(Instant.parse("2024-07-30T06:47:35.394Z")));

        final String body = """
                {
                  "header": {
                    "version": "V1",
                    "id": {
                      "eventId": "BrimService_1CDhiWnNtcEpTqUpcABcDo",
                      "senderName": "brim-service"
                    },
                    "tenant": "rexroth",
                    "timestamp": "2024-07-30T06:47:36.394Z",
                    "cause": {
                      "eventId": "BrimMessage_1CDhiWmjwU7b9ZRAegjkHi",
                      "senderName": "brim"
                    }
                  },
                  "data": {
                    "@type": "CONTRACT",
                    "contractId": "BC_90000000001",
                    "billingSyncStatus": "IN_SYNC",
                    "startDate": "2024-07-28T12:00:00+02:00",
                    "endDate": "2034-10-28T23:59:59+01:00",
                    "payment": {
                      "@type": "BOSCH_TRANSFER/SEPA_CREDIT",
                      "iban": "**********************",
                      "bic": "DEXYZABCXX",
                      "bankName": "First Ferengi Interplanetary"
                    },
                    "productId": "DC_12345678Z100",
                    "contractTypeConfiguration": {
                      "@type": "SUBSCRIPTION",
                      "contractPeriod": "QUARTERLY",
                      "noticePeriod": "P1M"
                    }
                  },
                  "errors": []
                }""";
        SqsMockExtension.sendMessage("/queue/bossstore-brim-contract-events.fifo", body);

        Awaitility.await().until(
                () -> contractDao.findAll().filter(c -> c.getCancellationState() == ContractCancellationState.CANCELLED).count() >= 1);

        assertThat(contractDao.findAllAsList())
                .hasSize(1)
                .anySatisfy(r -> {
                    assertThat(r.getCompanyId()).isEqualTo("rexroth/" + COMPANY_ID);
                    assertThat(r.getContractId()).isEqualTo("BC_90000000001");
                    assertThat(r.getProductId()).isEqualTo("DC_12345678Z100");
                    assertThat(r.getContractType()).isEqualTo(ContractType.SUBSCRIPTION);
                    assertThat(r.getContractPeriod()).isEqualTo(Period.ofMonths(3));
                    assertThat(r.getNoticePeriod()).isEqualTo(Period.ofMonths(1));
                    assertThat(r.getStartDate()).isEqualTo("2024-07-28T12:00:00+02:00");
                    assertThat(r.getEndDate()).isEqualTo("2034-10-28T23:59:59+01:00");
                    assertThat(r.getCancellationState()).isEqualTo(ContractCancellationState.CANCELLED);
                    assertThat(r.getUpdateTimestamp()).isEqualTo("2024-07-30T06:47:36.394Z");
                });

        EmailUtil.awaitEmails(2);

    }

    @Test
    public void testContractUpdateMessageIsSkipped() {
        testDataGenerator
                .withContract().havingValue(e -> e
                        .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                        .withCancellationState(ContractCancellationState.ACTIVE)
                        .withContractId("BC_90000000001")
                        .withEndDate(null)
                        .withUpdateTimestamp(Instant.parse("2024-07-30T06:47:35.394Z")));
        final BrimContractEntity contract = testDataGenerator.getContract();

        final IngressContractEvent<IngressContractEventData> ingressContractEvent = IngressContractEvent.builder()
                .header(EventHeader.builder()
                        .version(ApiVersion.V1)
                        .id(EventIdentity.builder().eventId("BrimService_1CDhiWnNtcEpTqUpcABcDo").senderName("brim-service").build())
                        .tenant(com.sast.store.brimtegration.apimodel.common.Tenant.REXROTH)
                        .timestamp(ZonedDateTime.now())
                        .build())
                .data(BillingContract.builder()
                        .billingSyncStatus(BillingSyncStatus.NEW)
                        .contractId("BC_90000000001")
                        .startDate(OffsetDateTime.parse("2024-07-28T12:00:00+02:00"))
                        .endDate(OffsetDateTime.parse("2034-10-28T23:59:59+01:00"))
                        .productId("DC_12345678Z100")
                        .payment(BoschTransferSepaCreditPayment.builder()
                                .bankName("First Ferengi Interplanetary")
                                .iban("**********************")
                                .bic("DEXYZABCXX")
                                .build())
                        .contractTypeConfiguration(SubscriptionContractTypeConfiguration.builder()
                                .contractPeriod(ContractPeriod.QUARTERLY)
                                .noticePeriod(Period.ofMonths(1))
                                .build())
                        .build())
                .errors(List.of())
                .build();

        contractEventSubscriber.onMessage(ingressContractEvent);

        assertThat(contractDao.findAllAsList())
                .hasSize(1)
                .anySatisfy(r -> {
                    assertThat(r.getCompanyId()).isEqualTo("rexroth/" + COMPANY_ID);
                    assertThat(r.getContractId()).isEqualTo("BC_90000000001");
                    assertThat(r.getProductId()).isEqualTo(contract.getProductId());
                    assertThat(r.getContractType()).isEqualTo(contract.getContractType());
                    assertThat(r.getStartDate()).isEqualTo(contract.getStartDate());
                    assertThat(r.getEndDate()).isNull();
                    assertThat(r.getCancellationState()).isEqualTo(ContractCancellationState.ACTIVE);
                });
    }

    @Test
    public void testContractCancellation() throws Exception {
        UmpMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withDefaultResponse();
        testDataGenerator
                .withContract().havingValue(e -> e
                        .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                        .withCancellationState(ContractCancellationState.CANCELLATION_PENDING)
                        .withContractId("BC_90000000001")
                        .withEndDate(null)
                        .withOrderNumber("myordernumber")
                        .withUpdateTimestamp(Instant.parse("2024-07-30T06:47:35.394Z"))
                        .withAddons(List.of(new BrimContractEntity.Addon()
                                .withContractId("BC_90000000002")
                                .withProductId("DC_12345678A100")
                                .withStartDate(OffsetDateTime.parse("2024-07-28T12:00:00+02:00"))
                                .withEndDate(null)
                                .withContractType(ContractType.SUBSCRIPTION)))
                );

        // spin up a second hazelcast instance to simulate the event being sent to another node
        final HazelcastInstance hazelcastInstance2 = Hazelcast.newHazelcastInstance(hazelcastConfig);
        Awaitility.await().atMost(Duration.ofSeconds(10)).untilAsserted(() -> {
            assertThat(hazelcastInstance2.getCluster().getMembers()).hasSize(2);
        });

        final IngressContractEvent<IngressContractEventData> ingressContractEvent = IngressContractEvent.builder()
                .header(EventHeader.builder()
                        .version(ApiVersion.V1)
                        .id(EventIdentity.builder().eventId("BrimService_1CDhiWnNtcEpTqUpcABcDo").senderName("brim-service").build())
                        .tenant(com.sast.store.brimtegration.apimodel.common.Tenant.REXROTH)
                        .timestamp(ZonedDateTime.parse("2024-07-30T06:47:36.394Z"))
                        .build())
                .data(BillingContract.builder()
                        .billingSyncStatus(BillingSyncStatus.IN_SYNC)
                        .contractId("BC_90000000001")
                        .startDate(OffsetDateTime.parse("2024-07-28T12:00:00+02:00"))
                        .endDate(OffsetDateTime.parse("2034-10-28T23:59:59+01:00"))
                        .productId("DC_12345678Z100")
                        .payment(BoschTransferSepaCreditPayment.builder()
                                .bankName("First Ferengi Interplanetary")
                                .iban("**********************")
                                .bic("DEXYZABCXX")
                                .build())
                        .contractTypeConfiguration(SubscriptionContractTypeConfiguration.builder()
                                .contractPeriod(ContractPeriod.QUARTERLY)
                                .noticePeriod(Period.ofMonths(1))
                                .build())
                        .addons(List.of(
                                BillingAddonContract.builder()
                                        .contractId("BC_90000000002")
                                        .billingSyncStatus(BillingSyncStatus.IN_SYNC)
                                        .startDate(OffsetDateTime.parse("2024-07-28T12:00:00+02:00"))
                                        .endDate(OffsetDateTime.parse("2034-10-28T23:59:59+01:00"))
                                        .contractTypeConfiguration(SubscriptionContractTypeConfiguration.builder()
                                                .contractPeriod(ContractPeriod.QUARTERLY)
                                                .noticePeriod(Period.ofMonths(1))
                                                .build())
                                        .productId("DC_12345678A100")
                                        .build()
                        )).build())
                .errors(List.of())
                .build();

        contractEventSubscriber.onMessage(ingressContractEvent);

        assertThat(contractDao.findAllAsList())
                .hasSize(1)
                .anySatisfy(r -> {
                    assertThat(r.getCompanyId()).isEqualTo("rexroth/" + COMPANY_ID);
                    assertThat(r.getContractId()).isEqualTo("BC_90000000001");
                    assertThat(r.getProductId()).isEqualTo("DC_12345678Z100");
                    assertThat(r.getContractType()).isEqualTo(ContractType.SUBSCRIPTION);
                    assertThat(r.getContractPeriod()).isEqualTo(Period.ofMonths(3));
                    assertThat(r.getNoticePeriod()).isEqualTo(Period.ofMonths(1));
                    assertThat(r.getStartDate()).isEqualTo("2024-07-28T12:00:00+02:00");
                    assertThat(r.getEndDate()).isEqualTo("2034-10-28T23:59:59+01:00");
                    assertThat(r.getCancellationState()).isEqualTo(ContractCancellationState.CANCELLED);
                    assertThat(r.getUpdateTimestamp()).isEqualTo("2024-07-30T06:47:36.394Z");
                    assertThat(r.getAddons()).hasSize(1)
                            .extracting(BrimContractEntity.Addon::getEndDate)
                            .containsExactly(OffsetDateTime.parse("2034-10-28T23:59:59+01:00"));
                });

        final Object message = hazelcastInstance2.getQueue("contractcancellationevents").take();
        assertThat(message).isNotNull()
                .extracting(ContractEvent.class::cast)
                .extracting(ContractEvent::contractDto)
                .satisfies(contractDto -> {
                    assertThat(contractDto.companyId()).isEqualTo(COMPANY_ID);
                    assertThat(contractDto.tenant()).isEqualTo(Tenant.REXROTH);
                    assertThat(contractDto.contractId()).isEqualTo("BC_90000000001");
                    assertThat(contractDto.productId()).isEqualTo("DC_12345678Z100");
                    assertThat(contractDto.contractType()).isEqualTo(ContractType.SUBSCRIPTION);
                    assertThat(contractDto.contractPeriod()).isEqualTo(Period.ofMonths(3));
                    assertThat(contractDto.noticePeriod()).isEqualTo(Period.ofMonths(1));
                    assertThat(contractDto.startDate()).isEqualTo("2024-07-28T12:00:00+02:00");
                    assertThat(contractDto.endDate()).isEqualTo("2034-10-28T23:59:59+01:00");
                    assertThat(contractDto.contractState()).isEqualTo(ContractState.CANCELLED);
                });

        final List<Message> messages = EmailUtil.awaitEmails(2);

        assertThat(messages).hasSize(2);
        assertThat(messages).first().extracting(Message::body).extracting(new BasicJsonTester(getClass())::from)
                .satisfies(m -> {
                    assertThat(m).extractingJsonPathStringValue("tenant").isEqualTo("rexroth");
                    assertThat(m).extractingJsonPathStringValue("locale").isEqualTo("de");
                    assertThat(m).extractingJsonPathStringValue("to[0]").isEqualTo("<EMAIL>");
                    assertThat(m).extractingJsonPathStringValue("templateName").isEqualTo("rexroth/contracts/cancelConfirmation");
                    assertThat(m).extractingJsonPathStringValue("properties.orderNumber").isEqualTo("myordernumber");
                    assertThat(m).extractingJsonPathStringValue("properties.cancelledByFirstName").isEqualTo("myFirstname");
                    assertThat(m).extractingJsonPathStringValue("properties.cancelledByLastName").isEqualTo("myLastname");
                    assertThat(m).extractingJsonPathStringValue("properties.firstName").isEqualTo("myFirstname");
                    assertThat(m).extractingJsonPathStringValue("properties.lastName").isEqualTo("myLastname");
                    assertThat(m).extractingJsonPathStringValue("properties.endDate").isEqualTo("2034-10-28T23:59:59");
                    assertThat(m).extractingJsonPathStringValue("properties.marketplaceUrl")
                            .isEqualTo("https://localhost:8000/foo/bar/baz");
                    assertThat(m).extractingJsonPathStringValue("properties.productName").isEqualTo("Hydraulic Hub");
                });
        assertThat(messages).last().extracting(Message::body).extracting(new BasicJsonTester(getClass())::from)
                .satisfies(m -> {
                    assertThat(m).extractingJsonPathStringValue("tenant").isEqualTo("rexroth");
                    assertThat(m).extractingJsonPathStringValue("locale").isEqualTo("de");
                    assertThat(m).extractingJsonPathStringValue("to[0]").isEqualTo("<EMAIL>");
                    assertThat(m).extractingJsonPathStringValue("templateName").isEqualTo("rexroth/contracts/cancelConfirmation");
                    assertThat(m).extractingJsonPathStringValue("properties.orderNumber").isEqualTo("myordernumber");
                    assertThat(m).extractingJsonPathStringValue("properties.cancelledByFirstName").isEqualTo("myFirstname");
                    assertThat(m).extractingJsonPathStringValue("properties.cancelledByLastName").isEqualTo("myLastname");
                    assertThat(m).extractingJsonPathStringValue("properties.endDate").isEqualTo("2034-10-28T23:59:59");
                    assertThat(m).extractingJsonPathStringValue("properties.marketplaceUrl")
                            .isEqualTo("https://localhost:8000/foo/bar/baz");
                    assertThat(m).extractingJsonPathStringValue("properties.productName").isEqualTo("Hydraulic Hub");
                });

        hazelcastInstance2.shutdown();
    }

    @Test
    public void testAddonCancellation() throws Exception {
        UmpMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withDefaultResponse();
        testDataGenerator
                .withContract().havingValue(e -> e
                        .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                        .withCancellationState(null)
                        .withContractId("BC_90000000001")
                        .withEndDate(null)
                        .withOrderNumber("myordernumber")
                        .withUpdateTimestamp(Instant.parse("2024-07-30T06:47:35.394Z"))
                        .withAddons(List.of(new BrimContractEntity.Addon()
                                .withContractId("BC_90000000002")
                                .withProductId("DC_12345678A100")
                                .withStartDate(OffsetDateTime.parse("2024-07-28T12:00:00+02:00"))
                                .withEndDate(null)
                                .withContractType(ContractType.SUBSCRIPTION)))
                );

        final IngressContractEvent<IngressContractEventData> ingressContractEvent = IngressContractEvent.builder()
                .header(EventHeader.builder()
                        .version(ApiVersion.V1)
                        .id(EventIdentity.builder().eventId("BrimService_1CDhiWnNtcEpTqUpcABcDo").senderName("brim-service").build())
                        .tenant(com.sast.store.brimtegration.apimodel.common.Tenant.REXROTH)
                        .timestamp(ZonedDateTime.parse("2024-07-30T06:47:36.394Z"))
                        .build())
                .data(BillingContract.builder()
                        .billingSyncStatus(BillingSyncStatus.IN_SYNC)
                        .contractId("BC_90000000001")
                        .startDate(OffsetDateTime.parse("2024-07-28T12:00:00+02:00"))
                        .productId("DC_12345678Z100")
                        .payment(BoschTransferSepaCreditPayment.builder()
                                .bankName("First Ferengi Interplanetary")
                                .iban("**********************")
                                .bic("DEXYZABCXX")
                                .build())
                        .contractTypeConfiguration(SubscriptionContractTypeConfiguration.builder()
                                .contractPeriod(ContractPeriod.QUARTERLY)
                                .noticePeriod(Period.ofMonths(1))
                                .build())
                        .addons(List.of(
                                BillingAddonContract.builder()
                                        .contractId("BC_90000000002")
                                        .billingSyncStatus(BillingSyncStatus.IN_SYNC)
                                        .startDate(OffsetDateTime.parse("2024-07-28T12:00:00+02:00"))
                                        .endDate(OffsetDateTime.parse("2024-10-28T23:59:59+01:00"))
                                        .contractTypeConfiguration(SubscriptionContractTypeConfiguration.builder()
                                                .contractPeriod(ContractPeriod.QUARTERLY)
                                                .noticePeriod(Period.ofMonths(1))
                                                .build())
                                        .productId("DC_12345678A100")
                                        .build()
                        )).build())
                .errors(List.of())
                .build();

        contractEventSubscriber.onMessage(ingressContractEvent);

        assertThat(contractDao.findAllAsList())
                .hasSize(1)
                .anySatisfy(r -> {
                    assertThat(r.getCompanyId()).isEqualTo("rexroth/" + COMPANY_ID);
                    assertThat(r.getContractId()).isEqualTo("BC_90000000001");
                    assertThat(r.getProductId()).isEqualTo("DC_12345678Z100");
                    assertThat(r.getContractType()).isEqualTo(ContractType.SUBSCRIPTION);
                    assertThat(r.getContractPeriod()).isEqualTo(Period.ofMonths(3));
                    assertThat(r.getNoticePeriod()).isEqualTo(Period.ofMonths(1));
                    assertThat(r.getStartDate()).isEqualTo("2024-07-28T12:00:00+02:00");
                    assertThat(r.getEndDate()).isNull();
                    assertThat(r.getCancellationState()).isEqualTo(ContractCancellationState.ACTIVE);
                    assertThat(r.getUpdateTimestamp()).isEqualTo("2024-07-30T06:47:36.394Z");
                    assertThat(r.getAddons()).hasSize(1)
                            .extracting(BrimContractEntity.Addon::getEndDate)
                            .containsExactly(OffsetDateTime.parse("2024-10-28T23:59:59+01:00"));
                });
    }

    @Test
    public void testAddonAddition() throws Exception {
        UmpMockExtension.withDefaultResponse();
        ProductServiceMockExtension.withDefaultResponse();
        testDataGenerator
                .withContract().havingValue(e -> e
                        .withCompanyId(Tenant.REXROTH, COMPANY_ID)
                        .withCancellationState(null)
                        .withContractId("BC_90000000001")
                        .withEndDate(null)
                        .withOrderNumber("myordernumber")
                        .withUpdateTimestamp(Instant.parse("2024-07-30T06:47:35.394Z"))
                );

        final IngressContractEvent<IngressContractEventData> ingressContractEvent = IngressContractEvent.builder()
                .header(EventHeader.builder()
                        .version(ApiVersion.V1)
                        .id(EventIdentity.builder().eventId("BrimService_1CDhiWnNtcEpTqUpcABcDo").senderName("brim-service").build())
                        .tenant(com.sast.store.brimtegration.apimodel.common.Tenant.REXROTH)
                        .timestamp(ZonedDateTime.parse("2024-07-30T06:47:36.394Z"))
                        .build())
                .data(BillingContract.builder()
                        .billingSyncStatus(BillingSyncStatus.IN_SYNC)
                        .contractId("BC_90000000001")
                        .startDate(OffsetDateTime.parse("2024-07-28T12:00:00+02:00"))
                        .productId("DC_12345678Z100")
                        .payment(BoschTransferSepaCreditPayment.builder()
                                .bankName("First Ferengi Interplanetary")
                                .iban("**********************")
                                .bic("DEXYZABCXX")
                                .build())
                        .contractTypeConfiguration(SubscriptionContractTypeConfiguration.builder()
                                .contractPeriod(ContractPeriod.QUARTERLY)
                                .noticePeriod(Period.ofMonths(1))
                                .build())
                        .addons(List.of(
                                BillingAddonContract.builder()
                                        .contractId("BC_90000000002")
                                        .billingSyncStatus(BillingSyncStatus.IN_SYNC)
                                        .startDate(OffsetDateTime.parse("2024-12-28T12:00:00+02:00"))
                                        .contractTypeConfiguration(SubscriptionContractTypeConfiguration.builder()
                                                .contractPeriod(ContractPeriod.QUARTERLY)
                                                .noticePeriod(Period.ofMonths(1))
                                                .build())
                                        .productId("DC_12345678A100")
                                        .build()
                        )).build())
                .errors(List.of())
                .build();

        contractEventSubscriber.onMessage(ingressContractEvent);

        assertThat(contractDao.findAllAsList())
                .hasSize(1)
                .anySatisfy(r -> {
                    assertThat(r.getCompanyId()).isEqualTo("rexroth/" + COMPANY_ID);
                    assertThat(r.getContractId()).isEqualTo("BC_90000000001");
                    assertThat(r.getProductId()).isEqualTo("DC_12345678Z100");
                    assertThat(r.getContractType()).isEqualTo(ContractType.SUBSCRIPTION);
                    assertThat(r.getContractPeriod()).isEqualTo(Period.ofMonths(3));
                    assertThat(r.getNoticePeriod()).isEqualTo(Period.ofMonths(1));
                    assertThat(r.getStartDate()).isEqualTo("2024-07-28T12:00:00+02:00");
                    assertThat(r.getEndDate()).isNull();
                    assertThat(r.getCancellationState()).isEqualTo(ContractCancellationState.ACTIVE);
                    assertThat(r.getUpdateTimestamp()).isEqualTo("2024-07-30T06:47:36.394Z");
                    assertThat(r.getAddons()).hasSize(1)
                            .anySatisfy(addon -> {
                                assertThat(addon.getProductId()).isEqualTo("DC_12345678A100");
                                assertThat(addon.getStartDate())
                                        .isEqualTo(OffsetDateTime.parse("2024-12-28T12:00:00+02:00"));
                                assertThat(addon.getEndDate()).isNull();
                                assertThat(addon.getContractId()).isEqualTo("BC_90000000002");
                                assertThat(addon.getContractType()).isEqualTo(ContractType.SUBSCRIPTION);
                                assertThat(addon.getContractPeriod()).isEqualTo(Period.ofMonths(3));
                                assertThat(addon.getNoticePeriod()).isEqualTo(Period.ofMonths(1));
                            });
                });
    }
}
