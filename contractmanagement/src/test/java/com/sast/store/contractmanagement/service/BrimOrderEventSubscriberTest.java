package com.sast.store.contractmanagement.service;

import com.sast.store.contractmanagement.AbstractComponentTest;
import com.sast.store.contractmanagement.api.ContractType;
import com.sast.store.contractmanagement.dao.BrimContractDao;
import com.sast.store.contractmanagement.dao.BrimContractEntity;
import com.sast.store.testing.awsmockup.junit.SqsMockExtension;
import jakarta.inject.Inject;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.Test;

import java.time.OffsetDateTime;
import java.time.Period;

import static org.assertj.core.api.Assertions.assertThat;

public class BrimOrderEventSubscriberTest extends AbstractComponentTest {

    @Inject
    private BrimContractDao contractDao;

    @Test
    void testContractTypes() throws Exception {
        SqsMockExtension.sendMessage("/queue/bossstore-contractmanagement-brim-order-events.fifo",
                loadResourceFile("/events/brim/order-created-all-types.json"));

        Awaitility.await().until(() -> contractDao.findAll().count() >= 7);

        assertThat(contractDao.findAllAsList())
            .hasSize(7)
            .anySatisfy(r -> {
                assertThat(r.getCompanyId()).isEqualTo("rexroth/52967218-7c7e-4471-8391-4962e7bbe537");
                assertThat(r.getContractId()).isEqualTo("BC_1000800001");
                assertThat(r.getOrderNumber()).isEqualTo("928374");
                assertThat(r.getProductId()).isEqualTo("DC_12345678Z100");
                assertThat(r.getContractType()).isEqualTo(ContractType.SUBSCRIPTION);
                assertThat(r.getContractPeriod()).isEqualTo(Period.ofMonths(3));
                assertThat(r.getNoticePeriod()).isEqualTo(Period.ofMonths(1));
                assertThat(r.getStartDate()).isEqualTo("2024-07-16T14:37:12.247+02:30");
                assertThat(r.getEndDate()).isNull();
            })
            .anySatisfy(r -> {
                assertThat(r.getCompanyId()).isEqualTo("rexroth/52967218-7c7e-4471-8391-4962e7bbe537");
                assertThat(r.getContractId()).isEqualTo("BC_10000000002");
                assertThat(r.getOrderNumber()).isEqualTo("928374");
                assertThat(r.getProductId()).isEqualTo("DC_12345678Z100");
                assertThat(r.getContractType()).isEqualTo(ContractType.SUBSCRIPTION);
                assertThat(r.getContractPeriod()).isEqualTo(Period.ofMonths(3));
                assertThat(r.getNoticePeriod()).isEqualTo(Period.ofMonths(1));
                assertThat(r.getStartDate()).isEqualTo("2024-07-16T14:37:12Z");
                assertThat(r.getEndDate()).isNull();
            })
            .anySatisfy(r -> {
                assertThat(r.getCompanyId()).isEqualTo("rexroth/52967218-7c7e-4471-8391-4962e7bbe537");
                assertThat(r.getContractId()).isEqualTo("BC_10000000005");
                assertThat(r.getOrderNumber()).isEqualTo("928374");
                assertThat(r.getProductId()).isEqualTo("DC_12345678Z101");
                assertThat(r.getContractType()).isEqualTo(ContractType.FIXED_TERM);
                assertThat(r.getContractPeriod()).isEqualTo(Period.ofYears(1));
                assertThat(r.getNoticePeriod()).isNull();
                assertThat(r.getStartDate()).isEqualTo("2024-07-16T14:37:12+01:00");
                assertThat(r.getEndDate()).isNull();
            })
            .anySatisfy(r -> {
                assertThat(r.getCompanyId()).isEqualTo("rexroth/52967218-7c7e-4471-8391-4962e7bbe537");
                assertThat(r.getContractId()).isEqualTo("BC_10000000006");
                assertThat(r.getOrderNumber()).isEqualTo("928374");
                assertThat(r.getProductId()).isEqualTo("DC_12345678Z101");
                assertThat(r.getContractType()).isEqualTo(ContractType.FIXED_TERM);
                assertThat(r.getContractPeriod()).isEqualTo(Period.ofYears(1));
                assertThat(r.getNoticePeriod()).isNull();
                assertThat(r.getStartDate()).isEqualTo("2024-07-16T14:37:12+01:00");
                assertThat(r.getEndDate()).isNull();
            })
            .anySatisfy(r -> {
                assertThat(r.getCompanyId()).isEqualTo("rexroth/52967218-7c7e-4471-8391-4962e7bbe537");
                assertThat(r.getContractId()).isEqualTo("BC_10000000007");
                assertThat(r.getOrderNumber()).isEqualTo("928374");
                assertThat(r.getProductId()).isEqualTo("DC_12345678Z199");
                assertThat(r.getContractType()).isEqualTo(ContractType.ONE_TIME);
                assertThat(r.getContractPeriod()).isNull();
                assertThat(r.getNoticePeriod()).isNull();
                assertThat(r.getStartDate()).isEqualTo("2024-07-16T14:37:12+01:00");
                assertThat(r.getEndDate()).isNull();
            })
            .anySatisfy(r -> {
                assertThat(r.getCompanyId()).isEqualTo("rexroth/52967218-7c7e-4471-8391-4962e7bbe537");
                assertThat(r.getContractId()).isEqualTo("BC_10000000008");
                assertThat(r.getOrderNumber()).isEqualTo("928374");
                assertThat(r.getProductId()).isEqualTo("DC_12345678Z198");
                assertThat(r.getContractType()).isEqualTo(ContractType.SUBSCRIPTION);
                assertThat(r.getContractPeriod()).isEqualTo(Period.ofDays(1));
                assertThat(r.getNoticePeriod()).isEqualTo(Period.ofDays(0));
                assertThat(r.getStartDate()).isEqualTo("2024-07-16T14:37:12+01:00");
                assertThat(r.getEndDate()).isNull();
            });
    }

    @Test
    void testContractsWithAddons() throws Exception {
        SqsMockExtension.sendMessage("/queue/bossstore-contractmanagement-brim-order-events.fifo",
                loadResourceFile("/events/brim/order-created-with-addons.json"));

        Awaitility.await().until(() -> contractDao.findAll().count() >= 1);

        assertThat(contractDao.findAllAsList())
                .hasSize(1)
                .anySatisfy(r -> {
                    assertThat(r.getCompanyId()).isEqualTo("rexroth/52967218-7c7e-4471-8391-4962e7bbe537");
                    assertThat(r.getContractId()).isEqualTo("LRX_BC_10000000001");
                    assertThat(r.getOrderNumber()).isEqualTo("928374");
                    assertThat(r.getProductId()).isEqualTo("DC_12345678Z100");
                    assertThat(r.getContractType()).isEqualTo(ContractType.SUBSCRIPTION);
                    assertThat(r.getContractPeriod()).isEqualTo(Period.ofMonths(3));
                    assertThat(r.getNoticePeriod()).isEqualTo(Period.ofMonths(1));
                    assertThat(r.getStartDate()).isEqualTo("2024-07-16T15:37:12+02:00");
                    assertThat(r.getEndDate()).isNull();
                    assertThat(r.getAddons()).containsExactly(
                            new BrimContractEntity.Addon()
                                    .withContractId("LRX_BC_10000000002")
                                    .withProductId("DC_12345678A100")
                                    .withStartDate(OffsetDateTime.parse("2024-07-16T15:37:12+02:00"))
                                    .withEndDate(null)
                                    .withContractType(ContractType.SUBSCRIPTION)
                                    .withContractPeriod(Period.ofYears(1))
                                    .withNoticePeriod(Period.ofMonths(1)),
                            new BrimContractEntity.Addon()
                                    .withContractId("LRX_BC_10000000003")
                                    .withProductId("DC_12345678A101")
                                    .withStartDate(OffsetDateTime.parse("2024-07-16T15:37:12+02:00"))
                                    .withEndDate(null)
                                    .withContractType(ContractType.SUBSCRIPTION)
                                    .withContractPeriod(Period.ofMonths(3))
                                    .withNoticePeriod(Period.ofDays(14))
                    );
                });
    }

    @Test
    void testNumberOnlyData() throws Exception {

        final String body = """
            {
              "header": {
                "version": "V1",
                "id": {
                  "eventId": "BrimService_1CDHM2bLJydDGuto8Qbq8p",
                  "senderName": "brim-service"
                },
                "tenant": "rexroth",
                "timestamp": "2024-07-17T09:51:35.138Z",
                "cause": {
                  "eventId": "create-platform-order",
                  "senderName": "Herbert"
                }
              },
              "data": {
                "@type": "ORDER_NUMBER",
                "orderNumber": "928374"
              },
              "errors": [
                {
                  "code": "INTEGRATION_ERROR",
                  "detail": "Product unknown product not found"
                }
              ]
            }""";
        SqsMockExtension.sendMessage("/queue/bossstore-contractmanagement-brim-order-events.fifo", body);

        // we will only get some logs for now
        Thread.sleep(500);

    }
}
