server:
  port: 8084

logging:
  level:
    com.sast: TRACE

spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://sso.mp-dc-d.com/auth/realms/rexroth
  cloud:
    aws:
      region:
        static: eu-central-1
      credentials:
        access-key: fakekey
        secret-key: fakekey
      sqs:
        endpoint: http://localhost:40123
      dynamodb:
        endpoint: http://localhost:9324
      sns:
        endpoint: http://localhost:9311

bossstore:
  hazelcast:
    enabled: local
  ump:
    url: http://localhost:40101
  productmanagementUrl: http://localhost:8082/rest

