{"header": {"version": "V1", "id": {"eventId": "BrimService_1CDHoMCM1YEpshtkDJmncu", "senderName": "brim-service"}, "tenant": "rex<PERSON>", "timestamp": "2024-07-17T15:36:44.873Z", "cause": {"eventId": "create-platform-order", "senderName": "<PERSON>"}}, "data": {"@type": "ORDER", "orderNumber": "928374", "billingSyncStatus": "CREATION_PENDING", "billingOrderStatus": "UNKNOWN", "placedAt": "2024-07-16T14:37:12+01:00", "buyerCompanyId": "********-7c7e-4471-8391-4962e7bbe537", "payment": {"@type": "PGW/SEPA_DIRECTDEBIT", "iban": "*****************", "mandateReference": "KJLHKJ8998767KJH7KJHO", "accountHolderName": "<PERSON>", "dateOfSignature": "2024-07-16"}, "items": [{"productId": "DC_12345678Z100", "quantity": 4, "amountAtOrderDate": 14.99, "contracts": [{"@type": "CONTRACT", "contractId": "BC_1000800001", "billingSyncStatus": "IN_SYNC", "startDate": "2024-07-16T14:37:12.247+02:30", "payment": {"@type": "PGW/SEPA_DIRECTDEBIT", "iban": "*****************", "mandateReference": "KJLHKJ8998767KJH7KJHO", "accountHolderName": "<PERSON>", "dateOfSignature": "2024-07-16"}, "productId": "DC_12345678Z100", "contractTypeConfiguration": {"@type": "SUBSCRIPTION", "contractPeriod": "QUARTERLY", "noticePeriod": "P1M"}}, {"@type": "CONTRACT", "contractId": "BC_10000000002", "billingSyncStatus": "IN_SYNC", "startDate": "2024-07-16T14:37:12Z", "payment": {"@type": "PGW/SEPA_DIRECTDEBIT", "iban": "*****************", "mandateReference": "KJLHKJ8998767KJH7KJHO", "accountHolderName": "<PERSON>", "dateOfSignature": "2024-07-16"}, "productId": "DC_12345678Z100", "contractTypeConfiguration": {"@type": "SUBSCRIPTION", "contractPeriod": "QUARTERLY", "noticePeriod": "P1M"}}, {"@type": "CONTRACT", "contractId": "BC_10000000003", "billingSyncStatus": "NEW", "startDate": "2024-07-16T14:37:12+01:00", "payment": {"@type": "PGW/SEPA_DIRECTDEBIT", "iban": "*****************", "mandateReference": "KJLHKJ8998767KJH7KJHO", "accountHolderName": "<PERSON>", "dateOfSignature": "2024-07-16"}, "productId": "DC_12345678Z100", "contractTypeConfiguration": {"@type": "SUBSCRIPTION", "contractPeriod": "QUARTERLY", "noticePeriod": "P1M"}}, {"@type": "CONTRACT", "contractId": "BC_10000000004", "billingSyncStatus": "IN_SYNC", "startDate": "2024-07-16T14:37:12+01:00", "payment": {"@type": "PGW/SEPA_DIRECTDEBIT", "iban": "*****************", "mandateReference": "KJLHKJ8998767KJH7KJHO", "accountHolderName": "<PERSON>", "dateOfSignature": "2024-07-16"}, "productId": "DC_12345678Z100", "contractTypeConfiguration": {"@type": "SUBSCRIPTION", "contractPeriod": "QUARTERLY", "noticePeriod": "P1M"}}]}, {"productId": "DC_12345678Z101", "quantity": 2, "amountAtOrderDate": 17.99, "contracts": [{"@type": "CONTRACT", "contractId": "BC_10000000005", "billingSyncStatus": "IN_SYNC", "startDate": "2024-07-16T14:37:12+01:00", "payment": {"@type": "PGW/SEPA_DIRECTDEBIT", "iban": "*****************", "mandateReference": "KJLHKJ8998767KJH7KJHO", "accountHolderName": "<PERSON>", "dateOfSignature": "2024-07-16"}, "productId": "DC_12345678Z101", "contractTypeConfiguration": {"@type": "FIXED_TERM", "duration": "YEARLY"}}, {"@type": "CONTRACT", "contractId": "BC_10000000006", "billingSyncStatus": "IN_SYNC", "startDate": "2024-07-16T14:37:12+01:00", "payment": {"@type": "PGW/SEPA_DIRECTDEBIT", "iban": "*****************", "mandateReference": "KJLHKJ8998767KJH7KJHO", "accountHolderName": "<PERSON>", "dateOfSignature": "2024-07-16"}, "productId": "DC_12345678Z101", "contractTypeConfiguration": {"@type": "FIXED_TERM", "duration": "YEARLY"}}]}, {"productId": "DC_12345678Z199", "quantity": 50, "amountAtOrderDate": 17.99, "contracts": [{"@type": "CONTRACT", "contractId": "BC_10000000007", "billingSyncStatus": "IN_SYNC", "startDate": "2024-07-16T14:37:12+01:00", "payment": {"@type": "PGW/SEPA_DIRECTDEBIT", "iban": "*****************", "mandateReference": "KJLHKJ8998767KJH7KJHO", "accountHolderName": "<PERSON>", "dateOfSignature": "2024-07-16"}, "productId": "DC_12345678Z199", "contractTypeConfiguration": {"@type": "ONE_TIME"}}]}, {"productId": "DC_12345678Z198", "quantity": 1, "amountAtOrderDate": 0.0, "contracts": [{"@type": "CONTRACT", "contractId": "BC_10000000008", "billingSyncStatus": "IN_SYNC", "startDate": "2024-07-16T14:37:12+01:00", "payment": {"@type": "PGW/SEPA_DIRECTDEBIT", "iban": "*****************", "mandateReference": "KJLHKJ8998767KJH7KJHO", "accountHolderName": "<PERSON>", "dateOfSignature": "2024-07-16"}, "productId": "DC_12345678Z198", "contractTypeConfiguration": {"@type": "FREE", "noticePeriod": "P0D", "contractPeriod": "P1D"}}]}]}, "errors": []}