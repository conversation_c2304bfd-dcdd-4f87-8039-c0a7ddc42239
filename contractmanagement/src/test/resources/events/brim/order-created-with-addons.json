{"header": {"version": "V1", "id": {"eventId": "BrimService_1CJ7TpxMtNxaHiwQDETKkL", "senderName": "brim-service"}, "tenant": "rex<PERSON>", "timestamp": "2024-12-12T08:47:06.733Z", "cause": {"eventId": "BrimMessage_1CJ7Tpx6Wahtk6gk3ShEES", "senderName": "brim"}}, "data": {"@type": "ORDER", "orderNumber": "928374", "billingSyncStatus": "IN_SYNC", "billingOrderStatus": "COMPLETED", "placedAt": "2024-07-16T14:37:12+01:00", "buyerCompanyId": "********-7c7e-4471-8391-4962e7bbe537", "payment": {"@type": "PGW/SEPA_DIRECTDEBIT", "iban": "*****************", "mandateReference": "KJLHKJ8998767KJH7KJHO", "accountHolderName": "<PERSON>", "dateOfSignature": "2024-07-16"}, "items": [{"productId": "DC_12345678Z100", "quantity": 1, "amountAtOrderDate": 14.99, "contracts": [{"@type": "CONTRACT", "contractId": "LRX_BC_10000000001", "billingSyncStatus": "IN_SYNC", "startDate": "2024-07-16T15:37:12+02:00", "payment": {"@type": "PGW/SEPA_DIRECTDEBIT", "iban": "*****************", "mandateReference": "KJLHKJ8998767KJH7KJHO", "accountHolderName": "<PERSON>", "dateOfSignature": "2024-07-16"}, "productId": "DC_12345678Z100", "contractTypeConfiguration": {"@type": "SUBSCRIPTION", "contractPeriod": "QUARTERLY", "noticePeriod": "P1M"}, "addons": [{"contractId": "LRX_BC_10000000002", "billingSyncStatus": "IN_SYNC", "startDate": "2024-07-16T15:37:12+02:00", "productId": "DC_12345678A100", "contractTypeConfiguration": {"@type": "SUBSCRIPTION", "contractPeriod": "YEARLY", "noticePeriod": "P1M"}}, {"contractId": "LRX_BC_10000000003", "billingSyncStatus": "IN_SYNC", "startDate": "2024-07-16T15:37:12+02:00", "productId": "DC_12345678A101", "contractTypeConfiguration": {"@type": "SUBSCRIPTION", "contractPeriod": "QUARTERLY", "noticePeriod": "P14D"}}]}]}]}, "errors": []}