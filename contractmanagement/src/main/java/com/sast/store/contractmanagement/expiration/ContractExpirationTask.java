package com.sast.store.contractmanagement.expiration;

import com.sast.store.contractmanagement.service.ContractService;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class ContractExpirationTask {
    private static final Logger LOG = LoggerFactory.getLogger(ContractExpirationTask.class);

    private final ContractService contractService;

    public ContractExpirationTask(final ContractService contractService) {
        this.contractService = contractService;
    }

    @Scheduled(fixedRateString = "PT60M", initialDelayString = "PT${random.int[5,55]}M")
    @SchedulerLock(name = "contractmanagement.contractExpirationTask", lockAtLeastFor = "PT60M", lockAtMostFor = "PT60M")
    public void expireContracts() {
        try {
            LOG.info("Contract expiration task started");
            contractService.expireContracts();
        } catch (final Exception e) {
            LOG.error("Contract expiration failed", e);
        } finally {
            LOG.info("Contract expiration task finished");
        }
    }
}
