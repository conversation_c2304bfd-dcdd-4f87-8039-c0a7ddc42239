package com.sast.store.contractmanagement.service.converter;

import com.google.common.annotations.VisibleForTesting;
import lombok.NonNull;

import java.time.OffsetDateTime;
import java.time.Period;
import java.time.ZoneOffset;

class ContractCalculator {
    private final OffsetDateTime startDate;
    private final Period runtime;
    private final Period noticePeriod;

    public ContractCalculator(final OffsetDateTime startDate, final Period runtime, final Period noticePeriod) {
        this.startDate = startDate;
        this.runtime = runtime;
        this.noticePeriod = noticePeriod;
    }

    public OffsetDateTime calculateEndDate() {
        return calculateEndDate(OffsetDateTime.now(ZoneOffset.UTC), OffsetDateTime.now(ZoneOffset.UTC));
    }

    @VisibleForTesting
    public OffsetDateTime calculateEndDate(@NonNull final OffsetDateTime cancellationDate,
        @NonNull final OffsetDateTime today) {

        if (isAnInformationMissing()) {
            return null;
        }

        OffsetDateTime start = this.startDate;

        while (start.plus(runtime).isBefore(today)) {
            start = start.plus(runtime);
        }

        final OffsetDateTime periodEnd = start.plus(runtime).minusDays(1).withHour(23).withMinute(59).withSecond(59);
        final OffsetDateTime noticeDeadline = periodEnd.minus(noticePeriod);

        if (cancellationDate.isBefore(noticeDeadline)) {
            return periodEnd;
        } else {
            return periodEnd.plus(runtime);
        }
    }

    private boolean isAnInformationMissing() {
        return startDate == null || runtime == null || noticePeriod == null;
    }

}
