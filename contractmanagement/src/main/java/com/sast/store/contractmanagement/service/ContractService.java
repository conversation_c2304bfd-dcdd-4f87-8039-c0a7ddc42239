package com.sast.store.contractmanagement.service;

import com.sast.store.brimtegration.apimodel.common.BillingSyncStatus;
import com.sast.store.brimtegration.apimodel.common.product.ContractTypeConfiguration;
import com.sast.store.brimtegration.apimodel.common.product.terms.ConsumptionContractTypeConfiguration;
import com.sast.store.brimtegration.apimodel.common.product.terms.FixedTermContractTypeConfiguration;
import com.sast.store.brimtegration.apimodel.common.product.terms.FreeContractTypeConfiguration;
import com.sast.store.brimtegration.apimodel.common.product.terms.OneTimeContractTypeConfiguration;
import com.sast.store.brimtegration.apimodel.common.product.terms.SubscriptionContractTypeConfiguration;
import com.sast.store.brimtegration.apimodel.events.EventHeader;
import com.sast.store.brimtegration.apimodel.events.egress.contract.EgressContractEventData;
import com.sast.store.brimtegration.apimodel.events.egress.contract.data.PlatformContractCancelled;
import com.sast.store.brimtegration.apimodel.events.ingress.contract.data.BillingAddonContract;
import com.sast.store.brimtegration.apimodel.events.ingress.contract.data.BillingContract;
import com.sast.store.brimtegration.apimodel.events.ingress.order.data.BillingOrder;
import com.sast.store.brimtegration.apimodel.events.ingress.order.data.BillingOrderItem;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.contractmanagement.api.ContractType;
import com.sast.store.contractmanagement.dao.BrimContractDao;
import com.sast.store.contractmanagement.dao.BrimContractEntity;
import com.sast.store.contractmanagement.dao.ContractCancellationState;
import com.sast.store.contractmanagement.dao.ContractExpirationState;
import com.sast.store.contractmanagement.service.email.ContractEmailService;
import com.sast.store.external.brim.BrimMessagePublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.Period;
import java.util.List;
import java.util.Optional;

import static java.util.function.Function.identity;
import static java.util.stream.Collectors.counting;
import static java.util.stream.Collectors.groupingBy;

@Component
@Slf4j
@RequiredArgsConstructor
public class ContractService {
    private final BrimContractDao contractDao;
    private final BrimMessagePublisher brimMessagePublisher;
    private final ContractEmailService contractEmailService;
    private final ContractEventPublisher contractEventPublisher;

    public void processContracts(final EventHeader eventHeader, final BillingOrder data) {
        final String companyId = data.buyerCompanyId();
        final String orderNumber = data.orderNumber();
        final var tenant = eventHeader.tenant();
        final Instant creationTime = eventHeader.timestamp().toInstant();
        data.items().stream()
            .map(BillingOrderItem::contracts).flatMap(List<BillingContract>::stream)
            .filter(c -> c.billingSyncStatus() == BillingSyncStatus.IN_SYNC)
            .forEach(contract -> {
                final var brimContract = getContract(contract, tenant, companyId, orderNumber, creationTime);
                contractDao.save(brimContract);
                contractEventPublisher.publishCreate(brimContract);
                LOG.info("processing contract {} for order {} of company {}", contract, orderNumber, companyId);
            });
    }

    private BrimContractEntity getContract(final BillingContract contract,
                                           final com.sast.store.brimtegration.apimodel.common.Tenant tenant,
                                           final String companyId, final String orderNumber, final Instant creationTime) {
        final List<BrimContractEntity.Addon> addonEntities = CollectionUtils.emptyIfNull(contract.addons()).stream()
                .map(this::createAddon)
                .toList();

        return new BrimContractEntity()
                .withCompanyId(Tenant.fromString(tenant.getJsonValue()), companyId)
                .withContractId(contract.contractId())
                .withOrderNumber(orderNumber)
                .withProductId(contract.productId())
                .withStartDate(contract.startDate())
                .withEndDate(contract.endDate())
                .withContractType(getContractType(contract.contractTypeConfiguration()))
                .withContractPeriod(getContractPeriod(contract.contractTypeConfiguration()))
                .withNoticePeriod(getNoticePeriod(contract.contractTypeConfiguration()))
                .withCancellationState(ContractCancellationState.ACTIVE)
                .withUpdateTimestamp(creationTime)
                .withAddons(addonEntities);
    }

    private BrimContractEntity.Addon createAddon(final BillingAddonContract billingAddonContract) {
        return new BrimContractEntity.Addon()
                .withContractId(billingAddonContract.contractId())
                .withProductId(billingAddonContract.productId())
                .withStartDate(billingAddonContract.startDate())
                .withEndDate(billingAddonContract.endDate())
                .withContractType(getContractType(billingAddonContract.contractTypeConfiguration()))
                .withContractPeriod(getContractPeriod(billingAddonContract.contractTypeConfiguration()))
                .withNoticePeriod(getNoticePeriod(billingAddonContract.contractTypeConfiguration()));
    }

    private Period getNoticePeriod(final ContractTypeConfiguration contractTypeConfiguration) {
        return switch (contractTypeConfiguration) {
            case final SubscriptionContractTypeConfiguration subscription -> subscription.noticePeriod();
            case final FreeContractTypeConfiguration free -> free.noticePeriod();
            case final ConsumptionContractTypeConfiguration consumption -> consumption.noticePeriod();
            default -> null;
        };
    }

    private ContractType getContractType(final ContractTypeConfiguration contractTypeConfiguration) {
        return switch (contractTypeConfiguration) {
            case final FixedTermContractTypeConfiguration ignored -> ContractType.FIXED_TERM;
            case final SubscriptionContractTypeConfiguration ignored -> ContractType.SUBSCRIPTION;
            case final OneTimeContractTypeConfiguration ignored -> ContractType.ONE_TIME;
            case final FreeContractTypeConfiguration ignored -> ContractType.SUBSCRIPTION;
            case final ConsumptionContractTypeConfiguration ignored -> ContractType.CONSUMPTION;
            default -> throw new IllegalStateException("Unknown contract type: " + contractTypeConfiguration);
        };
    }

    private Period getContractPeriod(final ContractTypeConfiguration contractTypeConfiguration) {
        return switch (contractTypeConfiguration) {
            case final FixedTermContractTypeConfiguration config -> config.duration().getEquivalentPeriod();
            case final SubscriptionContractTypeConfiguration config -> config.contractPeriod().getEquivalentPeriod();
            case final FreeContractTypeConfiguration config -> config.contractPeriod();
            case final ConsumptionContractTypeConfiguration config -> config.contractPeriod().getEquivalentPeriod();
            default -> null;
        };
    }

    public List<BrimContractEntity> getAllContracts(final Tenant tenantId, final String companyId) {
        return contractDao.findAllByCompanyId(tenantId, companyId).toList();
    }

    public Optional<BrimContractEntity> getContract(final Tenant tenantId, final String companyId, final String contractId) {
        return contractDao.findByCompanyIdAndContractId(tenantId, companyId, contractId);
    }

    public void cancelContracts(final Tenant tenant, final String companyId,
        final List<String> contractId, final String cancelledByUserId) {
        contractId.forEach(id -> cancelContract(tenant, companyId, id, cancelledByUserId));
    }

    private void cancelContract(final Tenant tenant, final String companyId, final String contractId, final String cancelledByUserId) {
        final BrimContractEntity contract = contractDao.findByCompanyIdAndContractId(tenant, companyId, contractId).orElseThrow();
        if (ContractType.SUBSCRIPTION != contract.getContractType() && ContractType.CONSUMPTION != contract.getContractType()) {
            LOG.info("Contract {} cannot be cancelled because it is of type {}", contractId, contract.getContractType());
            throw new IllegalArgumentException("Only subscription/consumption contracts can be cancelled");
        }
        contractDao.save(contract
            .withCancelledByUserId(cancelledByUserId)
            .withCancellationState(ContractCancellationState.CANCELLATION_PENDING));
        final EgressContractEventData egressContractEventData = PlatformContractCancelled.builder()
            .contractId(contractId)
            .cancellationReceivedAt(OffsetDateTime.now())
            .build();
        brimMessagePublisher.publishPlatformContractEvent(tenant.id(), egressContractEventData);
    }

    public void processContractUpdate(final EventHeader eventHeader, final BillingContract data) {
        if (data.billingSyncStatus() != BillingSyncStatus.IN_SYNC) {
            return;
        }
        contractDao.findByContractId(data.contractId())
            .filter(contract -> contract.getTenant().id().equals(eventHeader.tenant().getJsonValue()))
            .filter(contract -> contract.getUpdateTimestamp() == null
                || contract.getUpdateTimestamp().isBefore(eventHeader.timestamp().toInstant()))
            .forEach(contract -> {
                final ContractCancellationState previousState = contract.getCancellationState();

                saveContractUpdate(contract, data, eventHeader.timestamp().toInstant());

                // try sending the email only on *transition* to the cancelled state
                if (contract.getCancellationState() != previousState
                    && contract.getCancellationState() == ContractCancellationState.CANCELLED) {
                    try {
                        contractEventPublisher.publishCancellation(contract);
                        contractEmailService.sendCancellationNotifications(contract);
                    } catch (final Exception e) {
                        LOG.error("Failed to send cancellation notification: {}", e.getMessage(), e);
                    }
                } else {
                    try {
                        LOG.info("Not publishing update events until the rules are clear");
                        //contractEventPublisher.publishUpdate(contract);
                    } catch (final Exception e) {
                        LOG.error("Failed to send update notification: {}", e.getMessage(), e);
                    }
                }
                LOG.info("processed contract update {}", data);
            });
    }

    private void saveContractUpdate(final BrimContractEntity contract,
                                    final BillingContract data,
                                    final Instant updateTimestamp) {
        contract
                .withStartDate(data.startDate())
                .withEndDate(data.endDate())
                .withProductId(data.productId())
                .withContractType(getContractType(data.contractTypeConfiguration()))
                .withContractPeriod(getContractPeriod(data.contractTypeConfiguration()))
                .withNoticePeriod(getNoticePeriod(data.contractTypeConfiguration()))
                .withUpdateTimestamp(updateTimestamp);

        // the notion of cancellation is only meaningful for subscriptions or consumptions
        if (contract.getContractType() == ContractType.SUBSCRIPTION || contract.getContractType() == ContractType.CONSUMPTION) {
            contract.withCancellationState(contract.getEndDate() != null
                    ? ContractCancellationState.CANCELLED
                    : ContractCancellationState.ACTIVE);
        }

        CollectionUtils.emptyIfNull(data.addons()).forEach(addonData -> updateAddon(contract, addonData));

        contractDao.save(contract);
    }

    private void updateAddon(final BrimContractEntity contract,
                             final BillingAddonContract addonData) {
        contract.getAddons().stream()
                .filter(addon -> addon.getContractId().equals(addonData.contractId()))
                .findFirst()
                .ifPresentOrElse(
                    addon -> updateAddon(addon, addonData),
                    () -> {
                        final BrimContractEntity.Addon newAddon = createAddon(addonData);
                        contract.addAddon(newAddon);
                    }
                );
    }

    private void updateAddon(final BrimContractEntity.Addon addon,
                             final BillingAddonContract addonData) {
        addon.setStartDate(addonData.startDate());
        addon.setEndDate(addonData.endDate());
    }

    public Optional<BrimContractEntity> findByContractId(final Tenant tenant, final String companyId, final String contractId) {
        return contractDao.findByCompanyIdAndContractId(tenant, companyId, contractId);
    }

    public void expireContracts() {
        final var result = contractDao.findAllEndedAndNotExpired()
            .map(this::expireContract)
            .collect(groupingBy(identity(), counting()));

        final var successful = result.getOrDefault(true, 0L);
        final var unsuccessful = result.getOrDefault(false, 0L);

        LOG.info("Processed {} contracts, {} successful, {} unsuccessful", successful + unsuccessful, successful, unsuccessful);
    }

    private boolean expireContract(final BrimContractEntity brimContractEntity) {
        try {
            LOG.info("Processing contract {}", brimContractEntity.getContractId());

            brimContractEntity.setExpirationState(ContractExpirationState.PENDING);
            if (!contractDao.updateConditionally(brimContractEntity, null)) {
                LOG.info("Failed to set PENDING expiration state for contract {}", brimContractEntity.getContractId());
                return false;
            }

            contractEventPublisher.publishExpiration(brimContractEntity);
            contractEmailService.sendExpiredNotifications(brimContractEntity);
            brimContractEntity.setExpirationState(ContractExpirationState.EXPIRED);
            if (!contractDao.updateConditionally(brimContractEntity, ContractExpirationState.PENDING)) {
                LOG.info("Failed to set EXPIRED expiration state for contract {}", brimContractEntity.getContractId());
                return false;
            }

            LOG.info("Finished expiring contract {}", brimContractEntity.getContractId());

            return true;
        } catch (final Exception e) {
            LOG.error("Failed to expire contract {}", brimContractEntity.getContractId(), e);
            return false;
        }
    }
}
