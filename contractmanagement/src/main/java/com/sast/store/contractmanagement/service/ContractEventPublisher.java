package com.sast.store.contractmanagement.service;

import com.sast.store.commons.hazelcast.QueuePublisher;
import com.sast.store.contractmanagement.api.ContractEvent;
import com.sast.store.contractmanagement.api.ContractEventHeader;
import com.sast.store.contractmanagement.api.ContractState;
import com.sast.store.contractmanagement.dao.BrimContractEntity;
import com.sast.store.contractmanagement.service.converter.ContractDtoFactory;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
@Slf4j
@RequiredArgsConstructor
public class ContractEventPublisher {
    private final ContractDtoFactory contractDtoFactory;
    private final QueuePublisher<ContractEvent> contractCancellationQueue;
    private final QueuePublisher<ContractEvent> contractExpirationQueue;
    private final QueuePublisher<ContractEvent> contractUpdateQueue;
    private final QueuePublisher<ContractEvent> contractCreateQueue;

    public void publishCancellation(@NonNull final BrimContractEntity contract) {
        contractCancellationQueue.publish(createContractEvent(contract));
    }

    public void publishExpiration(@NonNull final BrimContractEntity contract) {
        contractExpirationQueue.publish(createContractEvent(contract));
    }

    public void publishUpdate(@NonNull final BrimContractEntity contract) {
        contractUpdateQueue.publish(createContractEvent(contract));
    }

    public void publishCreate(@NonNull final BrimContractEntity contract) {
        contractCreateQueue.publish(createContractEvent(contract));
    }

    private ContractEvent createContractEvent(final BrimContractEntity contract) {
        return ContractEvent.builder()
                .tenant(contract.getTenant())
                .companyId(contract.getUmpCompanyId())
                .contractType(contract.getContractType())
                .cancellationState(ContractState.valueOf(contract.getCancellationState().name()))
                .contractPeriod(contract.getContractPeriod())
                .noticePeriod(contract.getNoticePeriod())
                .startDate(contract.getStartDate())
                .endDate(contract.getEndDate())
                .orderNumber(contract.getOrderNumber())
                .productId(contract.getProductId())
                .cancelledByUserId(contract.getCancelledByUserId())
                .contractId(contract.getContractId())
                .header(ContractEventHeader.builder().eventId(UUID.randomUUID().toString()).build())
                .contractDto(contractDtoFactory.createContractDto(contract))
                .build();
    }
}
