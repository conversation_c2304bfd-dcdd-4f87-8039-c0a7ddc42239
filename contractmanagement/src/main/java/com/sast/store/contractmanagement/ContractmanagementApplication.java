package com.sast.store.contractmanagement;

import com.sast.store.commons.EnableCommonsAutoconfiguration;
import com.sast.store.external.EnableExternalClientsAutoconfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;

@SpringBootApplication
@EnableCaching
@EnableExternalClientsAutoconfiguration
@EnableCommonsAutoconfiguration
public class ContractmanagementApplication {

    public static void main(final String[] args) {
        SpringApplication.run(ContractmanagementApplication.class, args);
    }

}
