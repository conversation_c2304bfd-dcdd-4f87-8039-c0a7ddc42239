package com.sast.store.contractmanagement.service;

import com.sast.store.brimtegration.apimodel.events.ingress.order.IngressOrderEvent;
import com.sast.store.brimtegration.apimodel.events.ingress.order.IngressOrderEventData;
import com.sast.store.brimtegration.apimodel.events.ingress.order.data.BillingOrder;
import com.sast.store.brimtegration.apimodel.events.ingress.order.data.BillingOrderNumberOnly;
import io.awspring.cloud.sqs.annotation.SqsListener;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class BrimOrderEventSubscriber {
    private static final Logger LOG = LoggerFactory.getLogger(BrimOrderEventSubscriber.class);

    @Inject
    private ContractService contractService;

    @SqsListener(value = "bossstore-contractmanagement-brim-order-events.fifo")
    public void onMessage(final @Valid IngressOrderEvent<? extends IngressOrderEventData> message) {
        LOG.info("Received brim message: {}", message);
        if (!message.errors().isEmpty()) {
            LOG.warn("Received errors: {}", message.errors());
        }

        if (message.data() instanceof final BillingOrder data) {
            contractService.processContracts(message.header(), data);
        } else if (message.data() instanceof final BillingOrderNumberOnly data) {
            LOG.info("Ignoring brim message for order: {}", data);
        } else {
            LOG.warn("Received brim message with unknown data: {}", message.data());
        }
    }
}
