package com.sast.store.contractmanagement.config;

import com.sast.store.commons.basewebapp.rest.CommonJerseyServerConfig;
import com.sast.store.contractmanagement.rest.ContractRestService;
import jakarta.annotation.PostConstruct;
import jakarta.ws.rs.ApplicationPath;
import org.glassfish.jersey.server.ResourceConfig;
import org.springframework.context.annotation.Configuration;

@Configuration
@ApplicationPath("/rest")
public class JerseyServerConfig extends ResourceConfig {

    @PostConstruct
    public void init() {
        register(ContractRestService.class);

        CommonJerseyServerConfig.defaultRegistrationsAndProperties(clazz -> register(clazz), (prop, val) -> property(prop, val));
    }
}

