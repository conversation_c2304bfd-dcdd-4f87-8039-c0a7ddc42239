package com.sast.store.contractmanagement.dao;

import com.sast.store.commons.tenant.api.Tenant;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.Expression;
import software.amazon.awssdk.enhanced.dynamodb.model.PageIterable;
import software.amazon.awssdk.enhanced.dynamodb.model.ScanEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.UpdateItemEnhancedRequest;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;
import software.amazon.awssdk.services.dynamodb.model.ConditionalCheckFailedException;

import java.time.OffsetDateTime;
import java.util.Optional;
import java.util.stream.Stream;

@Component
public class BrimContractDao extends AbstractDynamodbDao<BrimContractEntity> {

    public BrimContractDao(final DynamoDbEnhancedClient dynamoDbEnhancedClient) {
        super(dynamoDbEnhancedClient, "BossstoreBrimContracts", BrimContractEntity.class);
    }

    public Stream<BrimContractEntity> findAllByCompanyId(final Tenant tenant, final String companyId) {
        return findAllByPartitionValue(tenant.id() + "/" + companyId);
    }

    public Stream<BrimContractEntity> findAllEndedAndNotExpired() {
        final PageIterable<BrimContractEntity> scan = table().scan(ScanEnhancedRequest.builder()
            .filterExpression(Expression.builder()
                .expression("endDate < :now AND (attribute_not_exists(expirationState) OR expirationState = :null)")
                .putExpressionValue(":now", AttributeValue.builder().s(OffsetDateTime.now().toString()).build())
                .putExpressionValue(":null", AttributeValue.builder().nul(true).build())
                .build())
            .build());
        return scan.items().stream();
    }

    public Optional<BrimContractEntity> findByCompanyIdAndContractId(final Tenant tenant, final String companyId,
        final String contractId) {
        return findByPartitionValueAndSortValue(tenant.id() + "/" + companyId, contractId);
    }

    public Stream<BrimContractEntity> findByContractId(final String contractId) {
        final PageIterable<BrimContractEntity> scan = table().scan(ScanEnhancedRequest.builder()
            .filterExpression(Expression.builder()
                .expression("contractId = :contractId")
                .putExpressionValue(":contractId", AttributeValue.builder().s(contractId).build())
                .build())
            .build());
        return scan.items().stream();
    }

    public boolean updateConditionally(final BrimContractEntity brimContractEntity, final ContractExpirationState expectedState) {
        try {
            table().updateItem(UpdateItemEnhancedRequest.builder(BrimContractEntity.class)
                .item(brimContractEntity)
                .conditionExpression(switch (expectedState) {
                    case null -> Expression.builder()
                        .expression("attribute_not_exists(expirationState) OR expirationState = :null")
                        .putExpressionValue(":null", AttributeValue.builder().nul(true).build())
                        .build();
                    case PENDING, EXPIRED -> Expression.builder()
                        .expression("expirationState = :expirationState")
                        .putExpressionValue(":expirationState", AttributeValue.builder().s(expectedState.name()).build())
                        .build();
                })
                .build());

            return true;
        } catch (final ConditionalCheckFailedException e) {
            return false;
        }
    }
}
