package com.sast.store.contractmanagement.rest;

import com.sast.store.commons.basewebapp.keycloak.AuthenticationService;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.contractmanagement.api.CancelContractDto;
import com.sast.store.contractmanagement.api.ContractApi;
import com.sast.store.contractmanagement.api.ContractDto;
import com.sast.store.contractmanagement.service.ContractService;
import com.sast.store.contractmanagement.service.converter.ContractDtoFactory;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.NotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class ContractRestService implements ContractApi {
    private final ContractService contractService;
    private final AuthenticationService authenticationService;
    private final ContractDtoFactory contractDtoFactory;

    @Override
    @PreAuthorize("hasRole('DEFAULT')")
    public List<ContractDto> getContracts(final Tenant tenant, final String company) {
        final String companyId = authenticationService.validateCompanyId(company);
        LOG.info("Get contracts for tenant {} and company {}", tenant, companyId);
        return contractService.getAllContracts(tenant, companyId).stream()
            .map(contractDtoFactory::createContractDto)
            .toList();
    }

    @Override
    @PreAuthorize("hasRole('DEFAULT')")
    public void cancelContracts(@NotNull final Tenant tenant, final String company,
        @NotNull @Valid final CancelContractDto cancelContractDto) {
        final String companyId = authenticationService.validateCompanyId(company);
        final String userId = authenticationService.getUserId();
        for (final String contractId : cancelContractDto.contractIds()) {
            if (contractService.getContract(tenant, companyId, contractId).isEmpty()) {
                LOG.info("Contract {} not found for tenant {} and company {}", contractId, tenant, companyId);
                throw new NotFoundException("Contract not found");
            }
        }
        LOG.info("Cancelling contracts for tenant {} and company {} {}", tenant, companyId, cancelContractDto);
        contractService.cancelContracts(tenant, companyId, cancelContractDto.contractIds(), userId);
    }

    @Override
    @PreAuthorize("hasRole('DEFAULT')")
    public ContractDto getContract(@NotNull final Tenant tenant, final String company, @NotNull final String contractId) {
        final String companyId = authenticationService.validateCompanyId(company);
        LOG.info("Get contract {} for tenant {} and company {}", contractId, tenant, companyId);
        return contractService.getContract(tenant, companyId, contractId)
            .map(contractDtoFactory::createContractDto)
            .orElseThrow(() -> new NotFoundException("Contract not found"));
    }
}
