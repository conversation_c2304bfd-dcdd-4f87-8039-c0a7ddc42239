package com.sast.store.contractmanagement.dao;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.contractmanagement.api.ContractType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.Period;
import java.util.LinkedList;
import java.util.List;

@DynamoDbBean
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = false, fluent = false)
public class BrimContractEntity {
    private String companyId;
    private String contractId;
    private String orderNumber;
    private String productId;
    private OffsetDateTime startDate;
    private OffsetDateTime endDate;
    private ContractType contractType;
    private Period contractPeriod;
    private Period noticePeriod;
    private ContractCancellationState cancellationState;
    private ContractExpirationState expirationState;
    private Instant updateTimestamp;
    private String cancelledByUserId;
    private String lastModifiedByUserId;
    private List<Addon> addons = new LinkedList<>();

    @DynamoDbPartitionKey
    public String getCompanyId() {
        return companyId;
    }

    public BrimContractEntity withCompanyId(final Tenant tenant, final String umpCompanyId) {
        this.companyId = tenant.id() + "/" + umpCompanyId;
        return this;
    }

    @DynamoDbIgnore
    public Tenant getTenant() {
        return Tenant.fromString(companyId.substring(0, companyId.indexOf('/')));
    }

    @DynamoDbIgnore
    public String getUmpCompanyId() {
        return companyId.substring(companyId.indexOf('/') + 1);
    }

    @DynamoDbSortKey
    public String getContractId() {
        return contractId;
    }

    public BrimContractEntity withContractId(final String contractId) {
        this.contractId = contractId;
        return this;
    }

    public BrimContractEntity withOrderNumber(final String orderNumber) {
        this.orderNumber = orderNumber;
        return this;
    }

    public BrimContractEntity withProductId(final String productId) {
        this.productId = productId;
        return this;
    }

    public BrimContractEntity withStartDate(final OffsetDateTime startDate) {
        this.startDate = startDate;
        return this;
    }

    public BrimContractEntity withEndDate(final OffsetDateTime endDate) {
        this.endDate = endDate;
        return this;
    }

    public BrimContractEntity withContractType(final ContractType contractType) {
        this.contractType = contractType;
        return this;
    }

    public BrimContractEntity withContractPeriod(final Period contractPeriod) {
        this.contractPeriod = contractPeriod;
        return this;
    }

    public BrimContractEntity withNoticePeriod(final Period noticePeriod) {
        this.noticePeriod = noticePeriod;
        return this;
    }

    public BrimContractEntity withCancellationState(final ContractCancellationState contractState) {
        this.cancellationState = contractState;
        return this;
    }

    public BrimContractEntity withExpirationState(final ContractExpirationState contractExpirationState) {
        this.expirationState = contractExpirationState;
        return this;
    }

    public BrimContractEntity withUpdateTimestamp(final Instant updateTimestamp) {
        this.updateTimestamp = updateTimestamp;
        return this;
    }

    public BrimContractEntity withCancelledByUserId(final String cancelledByUserId) {
        this.cancelledByUserId = cancelledByUserId;
        return this;
    }

    public BrimContractEntity withLastModifiedByUserId(final String lastModifiedByUserId) {
        this.lastModifiedByUserId = lastModifiedByUserId;
        return this;
    }

    public BrimContractEntity withAddons(final List<Addon> addons) {
        this.addons = addons;
        return this;
    }

    public void addAddon(final Addon addon) {
        this.addons.add(addon);
    }

    @DynamoDbBean
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = false, fluent = false)
    public static class Addon {
        private String contractId;
        private String productId;
        private OffsetDateTime startDate;
        private OffsetDateTime endDate;
        private ContractType contractType;
        private Period contractPeriod;
        private Period noticePeriod;

        public Addon withContractId(final String contractId) {
            this.contractId = contractId;
            return this;
        }

        public Addon withProductId(final String productId) {
            this.productId = productId;
            return this;
        }

        public Addon withStartDate(final OffsetDateTime startDate) {
            this.startDate = startDate;
            return this;
        }

        public Addon withEndDate(final OffsetDateTime endDate) {
            this.endDate = endDate;
            return this;
        }

        public Addon withContractType(final ContractType contractType) {
            this.contractType = contractType;
            return this;
        }

        public Addon withContractPeriod(final Period contractPeriod) {
            this.contractPeriod = contractPeriod;
            return this;
        }

        public Addon withNoticePeriod(final Period noticePeriod) {
            this.noticePeriod = noticePeriod;
            return this;
        }
    }
}
