package com.sast.store.contractmanagement.service.email;

import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.models.order.Order;
import com.google.common.base.Preconditions;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.contractmanagement.api.ContractType;
import com.sast.store.contractmanagement.config.AppConfiguration;
import com.sast.store.contractmanagement.dao.BrimContractEntity;
import com.sast.store.external.commercetools.util.CustomFieldProvider;
import com.sast.store.external.email.EmailServiceClient;
import com.sast.store.external.email.data.EmailRecipient;
import com.sast.store.external.email.data.EmailTenant;
import com.sast.store.external.email.data.TemplatedEmail;
import com.sast.store.external.ump.UmpClient;
import com.sast.store.gen.client.ump.api.UmpUserDto;
import com.sast.store.productmanagement.api.ProductApi;
import com.sast.store.productmanagement.api.ProductDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.Optional;

@Component
@Slf4j
@RequiredArgsConstructor
public class ContractEmailService {
    private final EmailServiceClient emailServiceClient;
    private final UmpClient umpClient;
    private final AppConfiguration appConfiguration;
    private final ProductApi productApi;
    private final ProjectApiRoot commercetoolsClient;

    public void sendCancellationNotifications(final BrimContractEntity contract) {
        final EmailTenant emailTenant = EmailTenant.valueOf(contract.getTenant().id());
        Preconditions.checkArgument(emailTenant == EmailTenant.rexroth,
                "Cancellation emails for tenant %s are not supported", emailTenant);
        Preconditions.checkArgument(contract.getContractType() == ContractType.SUBSCRIPTION,
                "Cancellation emails for contract type %s are not supported", contract.getContractType());
        Preconditions.checkArgument(contract.getEndDate() != null,
                "Contract %s has no end date", contract.getContractId());

        final Optional<UmpUserDto> cancellationUser = getCancellationUser(contract);
        cancellationUser.map(umpUserDto -> TemplatedEmail.builder()
            .to(EmailRecipient.forUmpUserId(contract.getCancelledByUserId()))
            .tenant(EmailTenant.rexroth)
            .templateData(RexrothCancellationEmailDto.builder()
                .productName(findProduct(contract.getTenant(), contract.getProductId()).map(ProductDto::name).orElse(null))
                .cancelledByFirstName(umpUserDto.getFirstName())
                .cancelledByLastName(umpUserDto.getLastName())
                .orderNumber(contract.getOrderNumber())
                .endDate(contract.getEndDate().toLocalDateTime())
                .marketplaceUrl(getMarketplaceUrl(contract))
                .build())
            .build())
            .ifPresent(emailServiceClient::sendIgnoringFailures);

        emailServiceClient.sendIgnoringFailures(TemplatedEmail.builder()
            .to(EmailRecipient.forUmpCompanyId(contract.getUmpCompanyId()))
            .tenant(EmailTenant.rexroth)
            .templateData(RexrothCancellationEmailDto.builder()
                .productName(findProduct(contract.getTenant(), contract.getProductId()).map(ProductDto::name).orElse(null))
                .cancelledByFirstName(cancellationUser.map(UmpUserDto::getFirstName).orElse(null))
                .cancelledByLastName(cancellationUser.map(UmpUserDto::getLastName).orElse(null))
                .orderNumber(contract.getOrderNumber())
                .endDate(contract.getEndDate().toLocalDateTime())
                .marketplaceUrl(getMarketplaceUrl(contract))
                .build())
            .build());
    }

    public void sendExpiredNotifications(final BrimContractEntity contract) {
        final EmailTenant emailTenant = EmailTenant.valueOf(contract.getTenant().id());
        Preconditions.checkArgument(emailTenant == EmailTenant.rexroth,
                "Contract expiry emails for tenant %s are not supported", emailTenant);

        final AppConfiguration.PublicUrlConfig publicUrlConfig = Optional.ofNullable(appConfiguration.tenants()
                        .get(contract.getTenant()))
                .map(AppConfiguration.TenantConfig::publicUrls)
                .orElseThrow(() -> new IllegalStateException("Missing application configuration for tenant %s"
                        .formatted(contract.getTenant())));

        final RexrothContractExpiredEmailDto contractExpiredEmailDto = RexrothContractExpiredEmailDto.builder()
                .productName(findProduct(contract.getTenant(), contract.getProductId()).map(ProductDto::name).orElse(null))
                .orderNumber(contract.getOrderNumber())
                .marketplaceUrl(publicUrlConfig.marketplaceUrl())
                .contractManagementUrl(publicUrlConfig.contractManagementUrl())
                .build();

        final Optional<String> orderedByUserId = getOrderedByUserId(contract);
        orderedByUserId.map(userId -> TemplatedEmail.builder()
                .to(EmailRecipient.forUmpUserId(userId))
                .tenant(emailTenant)
                .templateData(contractExpiredEmailDto)
                .build())
                .ifPresent(emailServiceClient::sendIgnoringFailures);

        emailServiceClient.sendIgnoringFailures(TemplatedEmail.builder()
                .to(EmailRecipient.forUmpCompanyId(contract.getUmpCompanyId()))
                .tenant(emailTenant)
                .templateData(contractExpiredEmailDto)
                .build());
    }

    /**
     * This is handled in this peculiar way as the cancelling user not existing any longer or the contract not being
     * cancelled by a platform user are valid use cases.
     */
    private Optional<UmpUserDto> getCancellationUser(final BrimContractEntity contract) {
        if (contract.getCancelledByUserId() == null) {
            return Optional.empty();
        }

        try {
            return Optional.ofNullable(umpClient.getUser(contract.getTenant().id(), contract.getCancelledByUserId()));
        } catch (final Exception e) {
            LOG.warn("Failed to query UMP for user (uid={}, tenant={}): {}",
                contract.getCancelledByUserId(), contract.getTenant(), e.getMessage(), e);
            return Optional.empty();
        }
    }

    private Optional<String> getOrderedByUserId(final BrimContractEntity contract) {
        try {
            final Order order = commercetoolsClient.orders()
                .withOrderNumber(contract.getOrderNumber())
                .get()
                .executeBlocking()
                .getBody();

            return CustomFieldProvider.getUserId(order);
        } catch (final Exception e) {
            LOG.warn("Failed to query buyer user id for contract {}", contract.getContractId(), e);
            return Optional.empty();
        }
    }

    private URI getMarketplaceUrl(final BrimContractEntity contract) {
        final Tenant tenant = contract.getTenant();
        if (appConfiguration.tenants().containsKey(tenant)) {
            return appConfiguration.tenants()
                .get(tenant)
                .publicUrls()
                .marketplaceUrl();
        }

        throw new IllegalArgumentException("Marketplace URL not available for tenant " + tenant);
    }

    private Optional<ProductDto> findProduct(final Tenant tenant, final String productId) {
        return productApi.getProductVariant(tenant, productId, null);
    }
}
