package com.sast.store.contractmanagement.config;

import com.hazelcast.core.HazelcastInstance;
import com.sast.store.commons.hazelcast.QueuePublisher;
import com.sast.store.contractmanagement.api.ContractEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static com.sast.store.commons.hazelcast.Queue.contractCancellationEvents;
import static com.sast.store.commons.hazelcast.Queue.contractCreateEvents;
import static com.sast.store.commons.hazelcast.Queue.contractExpirationEvents;
import static com.sast.store.commons.hazelcast.Queue.contractUpdateEvents;
import static com.sast.store.commons.hazelcast.Queue.publish;

@Configuration
public class QueueProducer {

    @Bean
    public QueuePublisher<ContractEvent> contractCancellationQueue(final HazelcastInstance hzInstance) {
        return publish(contractCancellationEvents(hzInstance));
    }

    @Bean
    public QueuePublisher<ContractEvent> contractExpirationQueue(final HazelcastInstance hzInstance) {
        return publish(contractExpirationEvents(hzInstance));
    }

    @Bean
    public QueuePublisher<ContractEvent> contractUpdateQueue(final HazelcastInstance hzInstance) {
        return publish(contractUpdateEvents(hzInstance));
    }

    @Bean
    public QueuePublisher<ContractEvent> contractCreateQueue(final HazelcastInstance hzInstance) {
        return publish(contractCreateEvents(hzInstance));
    }
}
