package com.sast.store.contractmanagement.service.converter;

import com.sast.store.contractmanagement.api.ContractDto;
import com.sast.store.contractmanagement.api.ContractState;
import com.sast.store.contractmanagement.dao.BrimContractEntity;
import lombok.NonNull;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.time.OffsetDateTime;

@Component
public class ContractDtoFactory {
    public ContractDto createContractDto(@NonNull final BrimContractEntity contract) {
        return ContractDto.builder()
                .tenant(contract.getTenant())
                .companyId(contract.getUmpCompanyId())
                .contractId(contract.getContractId())
                .contractPeriod(contract.getContractPeriod())
                .contractType(contract.getContractType())
                .endDate(contract.getEndDate())
                .noticePeriod(contract.getNoticePeriod())
                .orderNumber(contract.getOrderNumber())
                .productId(contract.getProductId())
                .startDate(contract.getStartDate())
                .projectedEndDate(getProjectedEndDate(contract))
                .contractState(getContractState(contract))
                .cancelledByUserId(contract.getCancelledByUserId())
                .lastModifiedByUserId(contract.getLastModifiedByUserId())
                .addons(CollectionUtils.emptyIfNull(contract.getAddons()).stream().map(this::createAddonDto).toList())
                .build();
    }

    private OffsetDateTime getProjectedEndDate(final BrimContractEntity contract) {
        return new ContractCalculator(contract.getStartDate(), contract.getContractPeriod(), contract.getNoticePeriod())
            .calculateEndDate();
    }

    private ContractDto.AddonDto createAddonDto(@NonNull final BrimContractEntity.Addon addon) {
        return ContractDto.AddonDto.builder()
                .contractId(addon.getContractId())
                .productId(addon.getProductId())
                .contractType(addon.getContractType())
                .contractPeriod(addon.getContractPeriod())
                .noticePeriod(addon.getNoticePeriod())
                .startDate(addon.getStartDate())
                .endDate(addon.getEndDate())
                .build();
    }

    private ContractState getContractState(final BrimContractEntity contract) {
        if (contract.getEndDate() != null && contract.getEndDate().isBefore(OffsetDateTime.now())) {
            return ContractState.EXPIRED;
        }
        return switch (contract.getCancellationState()) {
            case null -> ContractState.ACTIVE;
            case ACTIVE -> ContractState.ACTIVE;
            case CANCELLED -> ContractState.CANCELLED;
            case CANCELLATION_PENDING -> ContractState.CANCELLATION_PENDING;
        };
    }
}
