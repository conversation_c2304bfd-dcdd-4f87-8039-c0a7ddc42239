package com.sast.store.contractmanagement.service.email;

import com.sast.store.external.email.data.EmailTemplateData;
import com.sast.store.external.email.data.EmailTenant;
import lombok.Builder;

import java.net.URI;

@Builder
public record RexrothContractExpiredEmailDto(
        String productName,
        String orderNumber,
        URI marketplaceUrl,
        URI contractManagementUrl
) implements EmailTemplateData {
    @Override
    public String getTemplateName(final EmailTenant emailTenant) {
        return "rexroth/contracts/contractExpired";
    }
}
