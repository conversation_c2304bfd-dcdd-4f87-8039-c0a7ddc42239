package com.sast.store.contractmanagement.service.email;

import com.sast.store.external.email.data.EmailTemplateData;
import com.sast.store.external.email.data.EmailTenant;

import java.net.URI;
import java.time.LocalDateTime;

public record RexrothCancellationEmailDto(
        String cancelledByFirstName,
        String cancelledByLastName,
        String productName,
        String orderNumber,
        LocalDateTime endDate,
        URI marketplaceUrl
) implements EmailTemplateData {
    @Override
    public String getTemplateName(final EmailTenant emailTenant) {
        return "rexroth/contracts/cancelConfirmation";
    }

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {
        private String cancelledByFirstName;
        private String cancelledByLastName;
        private String productName;
        private String orderNumber;
        private LocalDateTime endDate;
        private URI marketplaceUrl;

        private Builder() {
        }

        public Builder cancelledByFirstName(final String cancelledByFirstName) {
            this.cancelledByFirstName = cancelledByFirstName;
            return this;
        }

        public Builder cancelledByLastName(final String cancelledByLastName) {
            this.cancelledByLastName = cancelledByLastName;
            return this;
        }

        public Builder productName(final String productName) {
            this.productName = productName;
            return this;
        }

        public Builder orderNumber(final String orderNumber) {
            this.orderNumber = orderNumber;
            return this;
        }

        public Builder endDate(final LocalDateTime endDate) {
            this.endDate = endDate;
            return this;
        }

        public Builder marketplaceUrl(final URI marketplaceUrl) {
            this.marketplaceUrl = marketplaceUrl;
            return this;
        }

        public RexrothCancellationEmailDto build() {
            return new RexrothCancellationEmailDto(cancelledByFirstName, cancelledByLastName,
                    productName, orderNumber, endDate, marketplaceUrl);
        }
    }
}
