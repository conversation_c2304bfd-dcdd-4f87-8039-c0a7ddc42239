package com.sast.store.contractmanagement.config;

import com.sast.store.commons.tenant.api.Tenant;
import jakarta.validation.constraints.NotNull;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.net.URI;
import java.util.Map;

@ConfigurationProperties(prefix = "bossstore")
@Validated
public record AppConfiguration(
    @NotNull  Map<Tenant, TenantConfig> tenants,
    URI productmanagementUrl
) {
    public record TenantConfig(
        @NotNull PublicUrlConfig publicUrls
    ) { }

    public record PublicUrlConfig(
        @NotNull URI marketplaceUrl,
        @NotNull URI contractManagementUrl
    ) { }
}
