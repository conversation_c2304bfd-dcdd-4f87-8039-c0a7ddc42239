package com.sast.store.contractmanagement.service;

import com.sast.store.brimtegration.apimodel.events.ingress.contract.IngressContractEvent;
import com.sast.store.brimtegration.apimodel.events.ingress.contract.IngressContractEventData;
import com.sast.store.brimtegration.apimodel.events.ingress.contract.data.BillingContract;
import com.sast.store.brimtegration.apimodel.events.ingress.contract.data.BillingContractIdOnly;
import io.awspring.cloud.sqs.annotation.SqsListener;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class BrimContractEventSubscriber {
    private static final Logger LOG = LoggerFactory.getLogger(BrimContractEventSubscriber.class);

    @Inject
    private ContractService contractService;

    @SqsListener(value = "bossstore-brim-contract-events.fifo")
    public void onMessage(final @Valid IngressContractEvent<? extends IngressContractEventData> message) {
        LOG.info("Received brim message: {}", message);
        if (!message.errors().isEmpty()) {
            LOG.warn("Received errors: {}", message.errors());
        }

        if (message.data() instanceof final BillingContract data) {
            contractService.processContractUpdate(message.header(), data);
        } else if (message.data() instanceof final BillingContractIdOnly data) {
            LOG.info("Ignoring brim message for contract: {}", data);
        } else {
            LOG.warn("Received brim message with unknown data: {}", message.data());
        }
    }
}
