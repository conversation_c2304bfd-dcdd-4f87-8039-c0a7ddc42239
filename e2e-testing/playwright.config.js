const {defineConfig} = require('@playwright/test');

const clientId = process.env.SINGLEKEYID_CLIENT_ID;
const clientSecret = process.env.SINGLEKEYID_CLIENT_SECRET;

if (!clientId || !clientSecret) {
    throw new Error("❌ CLIENT_ID or CLIENT_SECRET is missing from environment.");
}

module.exports = defineConfig({
    use: {
        screenshot: 'only-on-failure', // or 'on', 'off', 'only-on-failure'
        video: 'retain-on-failure',    // or 'on', 'off', 'retain-on-failure'
        trace: 'retain-on-failure',       // or 'on', 'off', 'retain-on-failure'
    },
    reporter: [
        ['html', {open: 'never'}],
        ['junit', {outputFile: 'test-results/rspec.xml'}]
    ],
    projects: [
        {
            name: 'dev',
            use: {
                baseURL: 'https://mp-dc-d.com/',
                authURL: 'https://stage.singlekey-id.com',
                authClientId: clientId,
                authClientSecret: clientSecret,
            },
        },
        {
            name: 'demo',
            use: {
                baseURL: 'https://mp-dc-q.com/',
                authURL: 'https://stage.singlekey-id.com',
                authClientId: clientId,
                authClientSecret: clientSecret,
            },
        },
    ],
});
