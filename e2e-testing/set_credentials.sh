#!/bin/bash
# Please set Sast MFA prior to the execution
client_id=$(credstash -p sast-pilcrow-dev_Developer get stack_UMP/idp_singlekeyid_captcha_bypass_client_id stack=UMP)
client_secret=$(credstash -p sast-pilcrow-dev_Developer get stack_UMP/idp_singlekeyid_captcha_bypass_client_secret stack=UMP)

# Check values
if [[ -z "$client_id" || -z "$client_secret" ]]; then
  echo "❌ Failed to retrieve credentials"
  exit 1
fi

# Export as environment variables (for current shell only)
export SINGLEKEYID_CLIENT_ID="$client_id"
export SINGLEKEYID_CLIENT_SECRET="$client_secret"

# Optional: Print them or next steps
echo "✅ SINGLEKEYID_CLIENT_ID and SINGLEKEYID_CLIENT_SECRET set"