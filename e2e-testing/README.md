## Requirements

* Node.js: v16.x or later (v18+ is even better)
* npm: Comes bundled with Node.js, but ideally v8.x or later

To check what you have installed, run the following commands in your terminal:
```bash
node -v
npm -v
```

## Installation

```bash
npm init -y
npm install -D @playwright/test
npx playwright install
```

## Run tests

### Dev
```bash
npm run test:dev:e2e
```
### Demo
```bash
export PLAYWRIGHT_PROJECT=demo
npm run test:demo:e2e
```
### Local
Change current directory to `boss-store/frontend` and run `yarn dev` to start the frontend application locally.
In the output, the local endpoint of frontend will be shown like the following example. 
```
...
Local:   http://localhost:5173/
```

Then change the [playwright.config.js](playwright.config.js) `dev` project's `baseURL` value to the local endpoint.
Now you can run the tests against the local frontend application.

### Running a specific test
In the `e2e-testing` directory, please run the following command:

```bash
npx playwright test tests/e2e/tests/login.spec.ts --workers=1 --project=dev --headed
```

## Generate tests
It opens a browser, records your clicks, typing, navigation, and then generates ready-to-use Playwright test code.

```bash
npx playwright codegen https://mp-dc-d.com/
```

Run tests with device emulation.

```bash
npx playwright codegen --device="iPhone 13" https://mp-dc-d.com/
```

Run tests with specific browser
```bash
npx playwright codegen --browser=firefox https://mp-dc-d.com/
```
### Generate user tests

This project includes a test generation workflow using Playwright. It requires temporary credentials and a browser-based bypass step to simulate real user behavior.

#### Prerequisites
Before running the test generation script, you need to obtain and export temporary credentials as environment variables. These credentials are required to interact with the backend API.

#### Step by Step Guide

##### 1. Set up credentials in your shell

The script assumes that temporary aws mfa is set. If not, please execute the script under operations repo.
```bash
bash operations/miscellaneous/sast_temporary_mfa_credentials.sh
```

Run the `set_credentials.sh` script using `source` to export the required environment variables into your current shell session:

```bash
source set_credentials.sh
```

This script retrieves the required `SINGLEKEYID_CLIENT_SECRET` and `SINGLEKEYID_CLIENT_ID` from credstash and exports them as environment variables.

##### 2. Generate test input using Playwright
Once credentials are set, run the test generation script:
```bash
./generate_tests.sh
```
This script contacts a secure API endpoint to request a bypass token, which allows you to skip CAPTCHA validation for test purposes.

##### 3. Manually set bypass cookie
After `generate_tests.sh` completes, it will display a URL like the following:

```
Go to: https://stage.singlekey-id.com?bot-protection-bypass-code=XYZ123
```

Open this URL in the Playwright browser context on a separate tab to set the appropriate bypass cookies.

##### 4. Continue with test generation
After setting the bypass cookie, return to your Playwright session to begin recording or executing user flows.
