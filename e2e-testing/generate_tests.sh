#!/bin/bash
# Please source set_credentials.sh prior to this script's execution.

# Checking the credentials.
if [[ -z "$SINGLEKEYID_CLIENT_ID" ]]; then
  echo "❌ SINGLEKEYID_CLIENT_ID is not set. Please run 'source set_credentials.sh' first."
  exit 1
fi

if [[ -z "$SINGLEKEYID_CLIENT_SECRET" ]]; then
  echo "❌ SINGLEKEYID_CLIENT_SECRET is not set. Please run 'source set_credentials.sh' first."
  exit 1
fi

# Getting the access token
access_token=$(curl -s --request POST   --url 'https://stage.singlekey-id.com/auth/connect/token'   --header 'content-type: application/x-www-form-urlencoded'   --data grant_type="client_credentials"   --data client_id="$SINGLEKEYID_CLIENT_ID"   --data client_secret="$SINGLEKEYID_CLIENT_SECRET"   --data scope="create_captcha_bypass_key" | jq -r '.access_token')
if [[ -z "$access_token" ]]; then
  echo "❌ Error: 'access_token' is empty or not set. Exiting."
  exit 1
fi

# Getting the bypass key
bypass_key=$(curl -s --request POST   --url 'https://stage.singlekey-id.com/auth/api/v1/captcha/key'   --header 'content-type: application/x-www-form-urlencoded'  --header "Authorization: Bearer $access_token" | jq -r '.key')
if [[ -z "$bypass_key" ]]; then
  echo "❌ Error: 'bypass_key' is empty or not set. Exiting."
  exit 1
fi

echo "⚠️ Go to https://stage.singlekey-id.com?bot-protection-bypass-code=$bypass_key in playwright browser"

npx playwright codegen https://mp-dc-d.com/
