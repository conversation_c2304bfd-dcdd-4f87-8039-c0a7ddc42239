import {test} from '../fixtures';
import {testUser} from '../testdata';
import {login, logout} from "./helper";
import {MainPage} from "../pages/main";

test.describe('Product test', () => {
    const projectName = process.env.PLAYWRIGHT_PROJECT || 'dev';
    const user = testUser[projectName];

    test.beforeEach(async ({page, baseURL, authURL, authClientId, authClientSecret}) => {
        await login(page, baseURL, user.username, user.password, authURL, authClientId, authClientSecret);
    });

    test.afterEach(async ({page}) => {
        await logout(page);
    });

    test(`Check a product`, async ({page, baseURL, authURL, authClientId, authClientSecret}) => {
        const mainPage = new MainPage(page);
        const productPage = await mainPage.clickProduct();

        await productPage.checkTitle();
        await productPage.checkSummary();
        await productPage.checkVariants(2);
        await productPage.checkAddons()
    });
});