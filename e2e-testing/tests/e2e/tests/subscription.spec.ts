import {test} from '../fixtures';
import {testUser} from '../testdata';
import {login, logout} from "./helper";
import {MainPage} from "../pages/main";


test.describe('Subscription page tests', () => {
    const projectName = process.env.PLAYWRIGHT_PROJECT || 'dev';
    const user = testUser[projectName];

    test.beforeEach(async ({page, baseURL, authURL, authClientId, authClientSecret}) => {
        await login(page, baseURL, user.username, user.password, authURL, authClientId, authClientSecret);
    });

    test.afterEach(async ({page}) => {
        await logout(page);
    });

    test(`Check a subscription load`, async ({page, baseURL, authURL, authClientId, authClientSecret}) => {
        const mainPage = new MainPage(page);
        await mainPage.navigate()
        const subscriptionPage = await mainPage.clickSubscriptions();

        await subscriptionPage.clickSubscription();
        await subscriptionPage.checkASubscription();
    });

    test(`Check an invoice load`, async ({page, baseURL, authURL, authClientId, authClientSecret}) => {
        const mainPage = new MainPage(page);
        await mainPage.navigate()
        const subscriptionPage = await mainPage.clickSubscriptions();

        await subscriptionPage.clickInvoice();
        await subscriptionPage.checkAnInvoice();
    });

});