import {expect, test} from '../fixtures';
import {testUser, productIds} from '../testdata';
import {login, logout} from "./helper";
import {ProductPage} from "../pages/product";
import {CartPage} from "../pages/cart";

test.describe('Cart tests', () => {
    const projectName = process.env.PLAYWRIGHT_PROJECT || 'dev';
    const user = testUser[projectName];
    const bodasProId = productIds[projectName].bodas_pro;

    test.beforeEach(async ({page, baseURL, authURL, authClientId, authClientSecret}) => {
        await login(page, baseURL, user.username, user.password, authURL, authClientId, authClientSecret);
        const cart = new CartPage(page);
        await cart.cleanup();
    });

    test.afterEach(async ({page}) => {
        const cart = new CartPage(page);
        await cart.cleanup();
        await logout(page);
    });

    test(`Add a product to cart`, async ({page, baseURL, authURL, authClientId, authClientSecret}) => {
        const productPage = new ProductPage(page);

        // add bodas connect pro to cart
        await productPage.navigate(bodasProId);
        const cartPage = await productPage.addToCart();

        const cartItemCount = await cartPage.getItemCount();
        expect(cartItemCount).toBe(1)
    });

    test(`Delete a product from cart`, async ({page, baseURL, authURL, authClientId, authClientSecret}) => {
        const productPage = new ProductPage(page);

        // add bodas connect pro to cart
        await productPage.navigate(bodasProId);
        const cartPage = await productPage.addToCart();

        // delete it because why not
        await cartPage.deleteFirstItem();

        expect(await cartPage.isCartEmpty()).toBe(true);
    });
});