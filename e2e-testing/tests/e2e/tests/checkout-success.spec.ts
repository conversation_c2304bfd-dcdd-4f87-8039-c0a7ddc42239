import {test} from '../fixtures';
import {testUser, paymentIds} from '../testdata';
import {login,logout} from "./helper";
import {CheckoutSuccessPage} from "../pages/checkout-success";

test.describe('Checkout success tests', () => {
    const projectName = process.env.PLAYWRIGHT_PROJECT || 'dev';
    const user = testUser[projectName];
    const paymentId = paymentIds[projectName].checkout_success;

    test.beforeEach(async ({page, baseURL, authURL, authClientId, authClientSecret}) => {
        await login(page, baseURL, user.username, user.password, authURL, authClientId, authClientSecret);
    });

    test.afterEach(async ({page}) => {
        await logout(page);
    });

    test(`Check a product`, async ({page, baseURL, authURL, authClientId, authClientSecret}) => {
        const checkoutSuccessPage = new CheckoutSuccessPage(page);
        await checkoutSuccessPage.navigate(paymentId);

        await checkoutSuccessPage.checkNoPaymentFailed();
        await checkoutSuccessPage.checkConfirmationText();
        await checkoutSuccessPage.checkSubscriptionLink();
        await checkoutSuccessPage.checkActionCard();
    });
});