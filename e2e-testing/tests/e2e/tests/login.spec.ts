import {test, expect} from '../fixtures';
import {buyers} from '../testdata';
import {login} from "./helper";

test.describe('Login tests for users', () => {
    const projectName = process.env.PLAYWRIGHT_PROJECT || 'dev';
    const testCases = buyers[projectName];
    for (const {username, password} of testCases) {
        test(`Login as ${username}`, async ({page, baseURL, authURL, authClientId, authClientSecret}) => {
            const mainPage = await login(page, baseURL, username, password, authURL, authClientId, authClientSecret);
            await mainPage.clickAccount();
            await expect(page.url()).toContain('/my-profile');
            await mainPage.navigate();
            await mainPage.logout();
        });
    }
});

