import {LoginPage} from "../pages/login";
import {MainPage} from "../pages/main";
import {CartPage} from "../pages/cart";

export async function logout(page) {
    const mainPage = new MainPage(page);
    await mainPage.navigate();
    await mainPage.logout();
}

export async function login(page, baseURL: string | undefined, username: string, password: string, authURL: string, authClientId: string, authClientSecret: string) {
    let loginPage = new LoginPage(page);
    let mainPage = await loginPage.setupCaptchaBypass(authURL, authClientId, authClientSecret);

    await mainPage.navigate();

    if (await mainPage.isLoggedIn()) {
        return;
    }

    await mainPage.acceptCookies();
    loginPage = await mainPage.clickLogin();

    const authPage = await loginPage.clickLoginRegister();
    await authPage.fillUsername(username)
    mainPage = await authPage.fillPassword(password);
    await mainPage.checkTitle();
    return mainPage;
}