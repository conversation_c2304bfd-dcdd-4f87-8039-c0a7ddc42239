import {MainPage} from '../pages/main';

const { test, expect } = require('@playwright/test');

test('check main page elements', async ({ page, baseURL }) => {
    const mainPage = new MainPage(page);
    await mainPage.navigate();
    await mainPage.acceptCookies();

    await expect(page.getByTestId(MainPage.CONTACT_BUTTON)).toBeVisible();
    await expect(page.getByTestId(MainPage.LOGIN_BUTTON)).toBeVisible();
    await expect(page.getByTestId(MainPage.COUNTRY_LANG_SWITCHER)).toBeVisible();
});

test('login redirect works', async ({ page, baseURL }) => {
    const mainPage = new MainPage(page);
    await mainPage.navigate();
    await mainPage.acceptCookies();
    await mainPage.clickLogin();

    const title = await page.title();
    await expect(title).toBe('Sign in to <PERSON><PERSON>')
});

