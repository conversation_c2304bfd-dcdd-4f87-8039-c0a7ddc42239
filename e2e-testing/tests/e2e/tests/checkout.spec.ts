import {expect, test} from "../fixtures";
import {testUser, productIds} from "../testdata";
import {login, logout} from "./helper";
import {CartPage} from "../pages/cart";
import {ProductPage} from "../pages/product";

test.describe('Checkout tests', () => {
    const projectName = process.env.PLAYWRIGHT_PROJECT || 'dev';
    const user = testUser[projectName];
    const bodasProId = productIds[projectName].bodas_pro;
    
    test.beforeEach(async ({page, baseURL, authURL, authClientId, authClientSecret}) => {
        await login(page, baseURL, user.username, user.password, authURL, authClientId, authClientSecret);
    });

    test.afterEach(async ({page}) => {
        const cart = new CartPage(page);
        await cart.cleanup();
        await logout(page);
    });

    test(`Go to checkout and check general functionality`, async ({page, baseURL, authURL, authClientId, authClientSecret}) => {
        const productPage = new ProductPage(page);

        // add bodas connect pro to cart
        await productPage.navigate(bodasProId);
        const cartPage = await productPage.addToCart();
        const checkoutPage = await cartPage.continueToCheckout();

        expect(await checkoutPage.paymentMethodCount()).toBeGreaterThan(0);
        expect(await checkoutPage.isPlaceOrderDisabled()).toBe(true);

        await checkoutPage.pickFirstPaymentMethod();
        await checkoutPage.acceptAllAgreements();

        expect(await checkoutPage.isPlaceOrderDisabled()).toBe(false);
    });
});