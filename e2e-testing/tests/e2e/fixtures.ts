import { test as baseTest } from '@playwright/test';

type MyFixtures = {
    authURL: string;
    authClientId: string;
    authClientSecret: string;
};

export const test = baseTest.extend<MyFixtures>({
    authURL: async ({}, use, testInfo) => {
        const projectAuthURL = testInfo.project.use.authURL;
        await use(projectAuthURL);
    },
    authClientId: async ({}, use, testInfo) => {
        const projectAuthClientId = testInfo.project.use.authClientId;
        await use(projectAuthClientId);
    },
    authClientSecret: async ({}, use, testInfo) => {
        const projectAuthClientSecret = testInfo.project.use.authClientSecret;
        await use(projectAuthClientSecret);
    },
});

export { expect } from '@playwright/test';