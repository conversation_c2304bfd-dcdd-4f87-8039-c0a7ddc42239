import {Page} from "@playwright/test";


export class BasePage {
    static readonly ACCEPT_COOKIES_ID = '#save-all-modal-dialog';

    protected page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    async acceptCookies() {
        await this.page.click(BasePage.ACCEPT_COOKIES_ID);
        await this.waitForLoadState();
    }

    async waitForLoadState() {
        await this.page.waitForTimeout(2000);
        await this.page.waitForLoadState('networkidle');
    }
}