import {Page} from "@playwright/test";
import {expect} from '../fixtures';
import {CartPage} from "./cart";
import {BasePage} from "./base-page";

export class ProductPage extends BasePage {

    constructor(page: Page) {
        super(page);
    }

    async navigate(productId: string) {
        await this.page.goto('/products/' + productId);
        await this.waitForLoadState();
    }

    async checkTitle() {
        const productName = this.page.locator('[data-id="text-product-name"]');
        await expect(productName).toBeVisible();
    }

    async checkSummary() {
        await expect(this.page.locator('.product-selection-summary')).toBeVisible();
    }

    async checkVariants(expectedCount) {
        const variants = this.page.locator('[data-testid^="variant-"]');
        const count = await variants.count();
        await expect(count).toBe(expectedCount);
    }

    async checkAddons() {
        const addons = this.page.locator('div.addons-wrapper');
        await expect(addons).toBeVisible();
    }

    async addToCart() {
        const button = this.page.locator('button[data-id="product-selection-summary-addtocart"]');
        await button.click();
        await this.waitForLoadState();
        return new CartPage(this.page);
    }


}