import {Page} from "@playwright/test";
import {expect} from '../fixtures';
import {BasePage} from "./base-page";

export class SubscriptionPage extends BasePage {
    constructor(page: Page) {
        super(page);
    }

    async clickSubscription() {
        await this.page.click('button[value="subscriptions"]', {state: 'visible'});
    }

    async checkASubscription() {
        await this.page.waitForSelector('tr[data-testid="subscription-row"]', {state: 'visible'});
        const subscriptionRows = this.page.locator('[data-testid^="subscription-row"]');
        const count = await subscriptionRows.count();
        await expect(count).toBeGreaterThan(0);
    }

    async clickInvoice() {
        await this.page.click('button[value="invoices"]', {state: 'visible'});
    }

    async checkAnInvoice() {
        await this.page.waitForSelector('tr[data-testid="invoice-row"]', {state: 'visible'});
        const invoiceRows = this.page.locator('[data-testid^="invoice-row"]');
        const count = await invoiceRows.count();
        await expect(count).toBeGreaterThan(0);
    }

}