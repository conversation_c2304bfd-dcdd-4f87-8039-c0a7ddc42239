import {Page} from '@playwright/test';
import {LoginPage} from "./login";
import {ProductPage} from "./product";
import {expect} from '../fixtures';
import {SubscriptionPage} from "./subscription";
import {BasePage} from "./base-page";

export class MainPage extends BasePage {

    static readonly CONTACT_BUTTON = 'contact-button';
    static readonly LOGIN_BUTTON = 'login-button';
    static readonly COUNTRY_LANG_SWITCHER = 'country-language-switcher';

    constructor(page: Page) {
        super(page);
    }

    async navigate() {
        await this.page.goto('/');
        await this.waitForLoadState();
    }


    async clickLogin() {
        await this.page.getByTestId(MainPage.LOGIN_BUTTON).click();
        return new LoginPage(this.page);
    }

    async clickAccount() {
        await this.page.waitForSelector('a[data-testid="navbar-item-user"]', {state: 'visible'});
        this.page.locator('a[data-testid="navbar-item-user"]').click();
        await this.waitForLoadState();
    }

    async isLoggedIn() {
        const accountsLink = this.page.locator('a[data-testid="navbar-item-user"]')
        return await accountsLink.count > 0;
    }

    async clickSubscriptions() {
        await this.page.waitForSelector('button[data-testid="navbar-item-apps"]', {state: 'visible'});
        this.page.locator('button[data-testid="navbar-item-apps"]').click();
        await this.waitForLoadState();
        return new SubscriptionPage(this.page);
    }

    async clickProduct() {
        await this.page.getByTestId('purchase-button').click();
        return new ProductPage(this.page);
    }

    async logout() {
        const button = this.page.locator('header .content-end button[aria-haspopup="menu"]');
        await button.click();
        await this.page.waitForSelector('[data-testid="logout-button"]', {state: 'visible'});
        await this.page.getByTestId('logout-button').click();
    }

    async checkTitle() {
        await this.page.waitForSelector('[data-testid="products-overview-header"]', {state: 'visible'});
        const title = this.page.getByTestId('products-overview-header');
        await expect(title).toBeVisible();
    }
}