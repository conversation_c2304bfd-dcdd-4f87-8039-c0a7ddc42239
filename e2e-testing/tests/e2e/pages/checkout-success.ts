import {Page} from "@playwright/test";
import {expect} from '../fixtures';
import {BasePage} from "./base-page";

export class CheckoutSuccessPage extends BasePage {

    constructor(page: Page) {
        super(page)
    }

    async navigate(paymentId: string) {
        await this.page.goto('/checkout-success?id=' + paymentId);
        await this.waitForLoadState();
    }

    async checkNoPaymentFailed() {
        await expect(this.page.getByTestId('checkout-success-payment-failed')).toHaveCount(0);
    }

    async checkConfirmationText() {
        await expect(this.page.getByTestId('checkout-success-confirmation-text')).toBeVisible();
    }

    async checkSubscriptionLink() {
        await expect(this.page.getByTestId('checkout-success-view-subscription-management')).toBeVisible();
    }

    async checkActionCard() {
        await expect(this.page.getByTestId('checkout-success-action-card-title')).toBeVisible();
        await expect(this.page.getByTestId('checkout-success-action-card-text')).toBeVisible();
        await expect(this.page.getByTestId('checkout-success-action-card-button')).toBeVisible();
    }
}