import {Page} from '@playwright/test';
import {BasePage} from "./base-page";

export class CheckoutPage extends BasePage {

    constructor(page: Page) {
        super(page);
    }

    async navigateTo() {
        await this.page.goto('/checkout');
        await this.waitForLoadState();
    }

    async paymentMethodCount() {
        const count = await this.page.locator('[data-testid^="checkout-payment-method-"]').count();
        return count;
    }

    async isPlaceOrderDisabled() {
        return await this.page.getByTestId('checkout-place-order-button').isDisabled();
    }


    async pickFirstPaymentMethod() {
        const paymentMethods = this.page.locator('[data-testid^="checkout-payment-method-radio"]');
        const pmCount = await paymentMethods.count();
        if (pmCount === 0) {
            throw new Error('No payment methods found');
        }

        for (let i = 0; i < pmCount; i++) {
            const wrapper = paymentMethods.nth(i);
            const radioInput = wrapper.locator('input[type="radio"]');
            await radioInput.click();
            return;
        }
    }

    async acceptAllAgreements() {
        const checkboxes = this.page.locator('[data-testid^="checkout-agreement-checkbox-"]');
        for (let i = 0; i < await checkboxes.count(); i++) {
            const wrapper = checkboxes.nth(i);
            const checkbox = wrapper.locator('input[type="checkbox"]');
            await checkbox.check();
        }

        await this.waitForLoadState();
    }

}