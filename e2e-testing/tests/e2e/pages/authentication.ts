import {Page} from "@playwright/test";
import {MainPage} from "./main";
import {BasePage} from "./base-page";

export class AuthenticationPage extends BasePage{

    constructor(page: Page) {
        super(page);
    }

    async fillUsername(username: string) {
        await this.page.getByRole('textbox', {name: 'Email'}).fill(username);
        await this.page.getByTestId('enter-username.submit').click();
    }

    async fillPassword(password: string) {
        await this.page.getByRole('textbox', {name: 'Password'}).fill(password);
        await this.page.getByTestId('enter-password.submit').click();
        return new MainPage(this.page);
    }
}