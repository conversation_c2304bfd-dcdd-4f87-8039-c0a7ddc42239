import {Page, request} from '@playwright/test';
import {MainPage} from "./main";
import {AuthenticationPage} from "./authentication";
import {BasePage} from "./base-page";

export class LoginPage extends BasePage {

    constructor(page: Page) {
        super(page);
    }

    async setupCaptchaBypass(authURL: string, authClientId: string, authClientSecret: string) {
        const apiContext = await request.newContext();
        const token = await this.getToken(apiContext, authURL, authClientId, authClientSecret);
        const captchaKey = await this.getKey(apiContext, token, authURL);
        await this.page.goto(`${authURL}?bot-protection-bypass-code=` + captchaKey);
        return new MainPage(this.page);
    }

    async clickLoginRegister() {
        await this.page.getByRole('link', {name: 'Login | Register with'}).click();
        return new AuthenticationPage(this.page);
    }

    async getToken(apiContext, authURL, clientId, secret) {
        const response = await apiContext.post(`${authURL}/auth/connect/token`, {
            form: {
                grant_type: 'client_credentials',
                client_id: clientId,
                client_secret: secret,
                scope: 'create_captcha_bypass_key'
            }
        });

        if (!response.ok()) {
            throw new Error(`Token request failed: ${response.status()} ${await response.text()}`);
        }

        const body = await response.json();
        const token = body.access_token;
        return token;
    }

    async getKey(apiContext, token, authURL) {
        const captchaResponse = await apiContext.post(`${authURL}/auth/api/v1/captcha/key`, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Authorization': `Bearer ${token}`,
            },
        });

        const captchaResult = await captchaResponse.json();
        const key = captchaResult.key;
        return key
    }
}