import {Page} from '@playwright/test';
import {CheckoutPage} from "./checkout";
import {BasePage} from "./base-page";

export class CartPage extends BasePage {
    constructor(page: Page) {
        super(page);
    }

    async navigateTo() {
        await this.page.goto('/cart');
        await this.waitForLoadState();
    }

    async getItemCount() {
        const items = this.page.locator('[data-testid^="cart-item"]');
        return await items.count();
    }

    async isCartEmpty() {
        const count = await this.page.locator('[data-testid="cart-empty-msg"]').count();
        return count > 0;
    }

    async deleteFirstItem() {
        const cartRemoveButtons = this.page.locator('[data-testid^="cart-remove"]');
        const count = await cartRemoveButtons.count();
        if (count > 0) {
            const firstItem = cartRemoveButtons.first();
            const deleteId = 'cart-remove-' + await firstItem.getAttribute('data-id');
            await this.page.getByTestId(deleteId).click();
            await this.waitForLoadState();
        }
    }

    async deleteAll() {
        while (!await this.isCartEmpty()) {
            await this.deleteFirstItem();
        }
    }

    async cleanup() {
        await this.navigateTo();
        await this.deleteAll();
    }


    async continueToCheckout() {
        await this.page.getByTestId('cart-checkout').click();
        await this.waitForLoadState();
        return new CheckoutPage(this.page);
    }

}