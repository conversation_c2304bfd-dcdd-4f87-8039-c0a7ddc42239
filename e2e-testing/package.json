{"name": "e2e-testing", "version": "1.0.0", "description": "End to end testing", "scripts": {"test:dev:e2e": "npx playwright test tests/e2e --project=dev --workers=1", "test:dev:debug:e2e": "npx playwright test --debug tests/e2e --project=dev", "test:demo:e2e": "npx playwright test tests/e2e --project=demo --workers=1", "codegen": "npx playwright codegen https://mp-dc-d.com/"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@playwright/test": "^1.52.0", "@types/node": "^22.14.0", "typescript": "^5.8.3"}, "packageManager": "yarn@4.9.1"}