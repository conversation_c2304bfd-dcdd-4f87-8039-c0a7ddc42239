package com.sast.store.backoffice.config;

import com.sast.store.backoffice.rest.ImportExportRestService;
import com.sast.store.commons.basewebapp.rest.CommonJerseyServerConfig;
import jakarta.annotation.PostConstruct;
import jakarta.ws.rs.ApplicationPath;
import org.glassfish.jersey.server.ResourceConfig;
import org.springframework.context.annotation.Configuration;

@Configuration
@ApplicationPath("/rest")
public class JerseyServerConfig extends ResourceConfig {

    @PostConstruct
    public void init() {
        register(ImportExportRestService.class);

        CommonJerseyServerConfig.gatewayRegistrationsAndProperties(clazz -> register(clazz), (prop, val) -> property(prop, val));
    }
}
