package com.sast.store.backoffice.config;

import jakarta.validation.constraints.NotNull;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.net.URI;

@ConfigurationProperties(prefix = "bossstore")
@Validated
public record AppConfiguration(
    @NotNull URI productmanagementUrl,
    @NotNull URI ordermanagementUrl
) { }
