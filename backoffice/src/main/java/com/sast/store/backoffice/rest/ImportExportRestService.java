package com.sast.store.backoffice.rest;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.productmanagement.api.PriceApi;
import com.sast.store.productmanagement.api.ProductApi;
import jakarta.inject.Inject;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;

import java.io.InputStream;

@Component
@Path("/tenants/{tenantId}/data")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class ImportExportRestService {

    @Inject
    private PriceApi priceService;

    @Inject
    private ProductApi productService;

    @GET
    @Path("prices")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    @PreAuthorize("hasRole('MANAGE_BACKOFFICE')")
    public Response exportPrices(@PathParam("tenantId") final Tenant tenantId) {
        return priceService.exportPrices(tenantId);
    }

    @POST
    @Path("prices")
    @Consumes(MediaType.APPLICATION_OCTET_STREAM)
    @PreAuthorize("hasRole('MANAGE_BACKOFFICE')")
    public void importPrices(@PathParam("tenantId") final Tenant tenantId, final InputStream inputStream) {
        priceService.importPrices(tenantId, inputStream);
    }

    @GET
    @Path("products")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    @PreAuthorize("hasRole('MANAGE_BACKOFFICE')")
    public Response exportProducts(@PathParam("tenantId") final Tenant tenantId) {
        return productService.exportProducts(tenantId);
    }

    @POST
    @Path("products")
    @Consumes(MediaType.APPLICATION_OCTET_STREAM)
    @PreAuthorize("hasRole('MANAGE_BACKOFFICE')")
    public void importProducts(@PathParam("tenantId") final Tenant tenantId, final InputStream inputStream) {
        productService.importProducts(tenantId, inputStream);
    }
}
