package com.sast.store.backoffice.config;

import com.sast.store.commons.basewebapp.keycloak.KeycloakTokenClientRequestFilter;
import com.sast.store.productmanagement.api.PriceApi;
import com.sast.store.productmanagement.api.ProductApi;
import jakarta.inject.Inject;
import jakarta.ws.rs.Priorities;
import jakarta.ws.rs.client.Client;
import org.glassfish.jersey.client.proxy.WebResourceFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.URI;

@Configuration
public class RestServiceProducer {

    @Inject
    private Client client;
    @Inject
    private AppConfiguration appConfiguration;

    @Inject
    private KeycloakTokenClientRequestFilter keycloakTokenClientRequestFilter;

    @Bean
    public ProductApi getLicenseRestService() {
        return getProxy(appConfiguration.productmanagementUrl(), ProductApi.class);
    }

    @Bean
    public PriceApi getPriceRestService() {
        return getProxy(appConfiguration.productmanagementUrl(), PriceApi.class);
    }

    private <T> T getProxy(final URI url, final Class<T> proxyInterface) {
        return WebResourceFactory.newResource(proxyInterface, client.target(url)
            .register(keycloakTokenClientRequestFilter, Priorities.AUTHORIZATION));
    }
}
