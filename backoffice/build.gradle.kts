plugins {
    id("bossstore.subproject-conventions")
	id("org.springframework.boot") version "3.4.3"
	id("com.google.cloud.tools.jib") version "3.4.4"
}

dependencies {
	implementation(enforcedPlatform(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES))
	implementation(project(":productmanagement-api"))
	implementation(project(":ordermanagement-api"))
	implementation(project(":commons"))
	implementation(project(":commons:base-webapp"))
	implementation(project(":commons:tenant"))
	implementation("org.springframework.boot:spring-boot-starter")
	implementation("org.springframework.boot:spring-boot-starter-jersey")
	implementation("org.springframework.boot:spring-boot-starter-security")
	implementation("org.glassfish.jersey.ext:jersey-proxy-client")
	runtimeOnly("org.glassfish.jersey.core:jersey-common") {
		because("Otherwise it can't find a converter for the plaintext output in case of errors")
	}
	testImplementation("org.springframework.boot:spring-boot-starter-test")
	testImplementation("org.springframework.security:spring-security-test")
	testImplementation("io.rest-assured:rest-assured")
	testImplementation("com.tngtech.keycloakmock:mock-junit5:0.17.0")
}

jib {
    from.image = "gcr.io/distroless/java21-debian12"
    to.image = "${property("ecrEndpoint")}/bossstore-${project.name}:${project.version}"
}
