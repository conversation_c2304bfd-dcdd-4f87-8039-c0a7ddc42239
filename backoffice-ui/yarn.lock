# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10

"@asamuzakjp/css-color@npm:^2.8.2":
  version: 2.8.3
  resolution: "@asamuzakjp/css-color@npm:2.8.3"
  dependencies:
    "@csstools/css-calc": "npm:^2.1.1"
    "@csstools/css-color-parser": "npm:^3.0.7"
    "@csstools/css-parser-algorithms": "npm:^3.0.4"
    "@csstools/css-tokenizer": "npm:^3.0.3"
    lru-cache: "npm:^10.4.3"
  checksum: 10/3fbd6b975cfca220a0620843776e7d266b880293a9e3364a48de11ca3eb54af8209343d01842a7c98d2737e457294a7621a5f6671aaf5f12e1634d10808f2508
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-string-parser@npm:7.25.9"
  checksum: 10/c28656c52bd48e8c1d9f3e8e68ecafd09d949c57755b0d353739eb4eae7ba4f7e67e92e4036f1cd43378cc1397a2c943ed7bcaf5949b04ab48607def0258b775
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-validator-identifier@npm:7.25.9"
  checksum: 10/3f9b649be0c2fd457fa1957b694b4e69532a668866b8a0d81eabfa34ba16dbf3107b39e0e7144c55c3c652bf773ec816af8df4a61273a2bb4eb3145ca9cf478e
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.25.3":
  version: 7.26.3
  resolution: "@babel/parser@npm:7.26.3"
  dependencies:
    "@babel/types": "npm:^7.26.3"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 10/e7e3814b2dc9ee3ed605d38223471fa7d3a84cbe9474d2b5fa7ac57dc1ddf75577b1fd3a93bf7db8f41f28869bda795cddd80223f980be23623b6434bf4c88a8
  languageName: node
  linkType: hard

"@babel/types@npm:^7.26.3":
  version: 7.26.3
  resolution: "@babel/types@npm:7.26.3"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.25.9"
    "@babel/helper-validator-identifier": "npm:^7.25.9"
  checksum: 10/c31d0549630a89abfa11410bf82a318b0c87aa846fbf5f9905e47ba5e2aa44f41cc746442f105d622c519e4dc532d35a8d8080460ff4692f9fc7485fbf3a00eb
  languageName: node
  linkType: hard

"@bd/cd-system3@npm:11.1.0":
  version: 11.1.0
  resolution: "@bd/cd-system3@npm:11.1.0"
  dependencies:
    "@mdi/font": "npm:^7.4.47"
    "@mdi/js": "npm:^7.4.47"
    "@vueuse/core": "npm:^10.11.0"
    vue-component-type-helpers: "npm:^2.0.0"
    vue-i18n: "npm:^10.0.5"
    webfontloader: "npm:^1.6.28"
  peerDependencies:
    vue: ^3.4.31
    vuetify: ^3.7.2
  checksum: 10/abfd1e380236256033c29dc2af50c25a5223ee913aca82d4dbb0bd6005db4b11a42bb55664ba2fe7a72da2b92d3874fe06f474a0d1ffcef9929ce2521fe04955
  languageName: node
  linkType: hard

"@csstools/color-helpers@npm:^5.0.1":
  version: 5.0.1
  resolution: "@csstools/color-helpers@npm:5.0.1"
  checksum: 10/4cb25b34997c9b0e9f401833e27942636494bc3c7fda5c6633026bc3fdfdda1c67be68ea048058bfba449a86ec22332e23b4ec5982452c50b67880c4cb13a660
  languageName: node
  linkType: hard

"@csstools/css-calc@npm:^2.1.1":
  version: 2.1.1
  resolution: "@csstools/css-calc@npm:2.1.1"
  peerDependencies:
    "@csstools/css-parser-algorithms": ^3.0.4
    "@csstools/css-tokenizer": ^3.0.3
  checksum: 10/60e8808c261eeebb15517c0f368672494095bb10e90177dfc492f956fc432760d84b17dc19db739a2e23cac0013f4bcf37bb93947f9741b95b7227eeaced250b
  languageName: node
  linkType: hard

"@csstools/css-color-parser@npm:^3.0.7":
  version: 3.0.7
  resolution: "@csstools/css-color-parser@npm:3.0.7"
  dependencies:
    "@csstools/color-helpers": "npm:^5.0.1"
    "@csstools/css-calc": "npm:^2.1.1"
  peerDependencies:
    "@csstools/css-parser-algorithms": ^3.0.4
    "@csstools/css-tokenizer": ^3.0.3
  checksum: 10/efceb60608f3fc2b6da44d5be7720a8b302e784f05c1c12f17a1da4b4b9893b2e20d0ea74ac2c2d6d5ca9b64ee046d05f803c7b78581fd5a3f85e78acfc5d98e
  languageName: node
  linkType: hard

"@csstools/css-parser-algorithms@npm:^3.0.4":
  version: 3.0.4
  resolution: "@csstools/css-parser-algorithms@npm:3.0.4"
  peerDependencies:
    "@csstools/css-tokenizer": ^3.0.3
  checksum: 10/dfb6926218d9f8ba25d8b43ea46c03863c819481f8c55e4de4925780eaab9e6bcd6bead1d56b4ef82d09fcd9d69a7db2750fa9db08eece9470fd499dc76d0edb
  languageName: node
  linkType: hard

"@csstools/css-tokenizer@npm:^3.0.3":
  version: 3.0.3
  resolution: "@csstools/css-tokenizer@npm:3.0.3"
  checksum: 10/6baa3160e426e1f177b8f10d54ec7a4a596090f65a05f16d7e9e4da049962a404eabc5f885f4867093702c259cd4080ac92a438326e22dea015201b3e71f5bbb
  languageName: node
  linkType: hard

"@esbuild/aix-ppc64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/aix-ppc64@npm:0.24.2"
  conditions: os=aix & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/android-arm64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/android-arm64@npm:0.24.2"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/android-arm@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/android-arm@npm:0.24.2"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/android-x64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/android-x64@npm:0.24.2"
  conditions: os=android & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/darwin-arm64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/darwin-arm64@npm:0.24.2"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/darwin-x64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/darwin-x64@npm:0.24.2"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/freebsd-arm64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/freebsd-arm64@npm:0.24.2"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/freebsd-x64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/freebsd-x64@npm:0.24.2"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/linux-arm64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/linux-arm64@npm:0.24.2"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/linux-arm@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/linux-arm@npm:0.24.2"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/linux-ia32@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/linux-ia32@npm:0.24.2"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/linux-loong64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/linux-loong64@npm:0.24.2"
  conditions: os=linux & cpu=loong64
  languageName: node
  linkType: hard

"@esbuild/linux-mips64el@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/linux-mips64el@npm:0.24.2"
  conditions: os=linux & cpu=mips64el
  languageName: node
  linkType: hard

"@esbuild/linux-ppc64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/linux-ppc64@npm:0.24.2"
  conditions: os=linux & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/linux-riscv64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/linux-riscv64@npm:0.24.2"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"@esbuild/linux-s390x@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/linux-s390x@npm:0.24.2"
  conditions: os=linux & cpu=s390x
  languageName: node
  linkType: hard

"@esbuild/linux-x64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/linux-x64@npm:0.24.2"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/netbsd-arm64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/netbsd-arm64@npm:0.24.2"
  conditions: os=netbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/netbsd-x64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/netbsd-x64@npm:0.24.2"
  conditions: os=netbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/openbsd-arm64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/openbsd-arm64@npm:0.24.2"
  conditions: os=openbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/openbsd-x64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/openbsd-x64@npm:0.24.2"
  conditions: os=openbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/sunos-x64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/sunos-x64@npm:0.24.2"
  conditions: os=sunos & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/win32-arm64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/win32-arm64@npm:0.24.2"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/win32-ia32@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/win32-ia32@npm:0.24.2"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/win32-x64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/win32-x64@npm:0.24.2"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0, @eslint-community/eslint-utils@npm:^4.4.0":
  version: 4.4.1
  resolution: "@eslint-community/eslint-utils@npm:4.4.1"
  dependencies:
    eslint-visitor-keys: "npm:^3.4.3"
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: 10/ae92a11412674329b4bd38422518601ec9ceae28e251104d1cad83715da9d38e321f68c817c39b64e66d0af7d98df6f9a10ad2dc638911254b47fb8932df00ef
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.10.0, @eslint-community/regexpp@npm:^4.12.1":
  version: 4.12.1
  resolution: "@eslint-community/regexpp@npm:4.12.1"
  checksum: 10/c08f1dd7dd18fbb60bdd0d85820656d1374dd898af9be7f82cb00451313402a22d5e30569c150315b4385907cdbca78c22389b2a72ab78883b3173be317620cc
  languageName: node
  linkType: hard

"@eslint/compat@npm:1.2.4":
  version: 1.2.4
  resolution: "@eslint/compat@npm:1.2.4"
  peerDependencies:
    eslint: ^9.10.0
  peerDependenciesMeta:
    eslint:
      optional: true
  checksum: 10/872ac21e3f430575ba70916d83f5a4e7e9cc7fa953111c99ecef225d1ed05b66fbdb5034761dd9035f00c3f0d7ca7657f8cbfa4ff7ead3967f630c8c783d2beb
  languageName: node
  linkType: hard

"@eslint/config-array@npm:^0.19.0":
  version: 0.19.1
  resolution: "@eslint/config-array@npm:0.19.1"
  dependencies:
    "@eslint/object-schema": "npm:^2.1.5"
    debug: "npm:^4.3.1"
    minimatch: "npm:^3.1.2"
  checksum: 10/1243b01f463de85c970c18f0994f9d1850dafe8cc8c910edb64105d845edd3cacaa0bbf028bf35a6daaf5a179021140b6a8b1dc7a2f915b42c2d35f022a9c201
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.9.0":
  version: 0.9.1
  resolution: "@eslint/core@npm:0.9.1"
  dependencies:
    "@types/json-schema": "npm:^7.0.15"
  checksum: 10/f2263f8f94fdf84fc34573e027de98f1fce6287120513ae672ddf0652c75b9fa77c314d565628fc58e0a6f959766acc34c8191f9b94f1757b910408ffa04adde
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^3.2.0":
  version: 3.2.0
  resolution: "@eslint/eslintrc@npm:3.2.0"
  dependencies:
    ajv: "npm:^6.12.4"
    debug: "npm:^4.3.2"
    espree: "npm:^10.0.1"
    globals: "npm:^14.0.0"
    ignore: "npm:^5.2.0"
    import-fresh: "npm:^3.2.1"
    js-yaml: "npm:^4.1.0"
    minimatch: "npm:^3.1.2"
    strip-json-comments: "npm:^3.1.1"
  checksum: 10/b32dd90ce7da68e89b88cd729db46b27aac79a2e6cb1fa75d25a6b766d586b443bfbf59622489efbd3c6f696f147b51111e81ec7cd23d70f215c5d474cad0261
  languageName: node
  linkType: hard

"@eslint/js@npm:9.17.0":
  version: 9.17.0
  resolution: "@eslint/js@npm:9.17.0"
  checksum: 10/1a89e62f5c50e75d44565b7f3b91701455a999132c991e10bac59c118fbb54bdd54be22b9bda1ac730f78a2e64604403d65ce5dd7726d80b2632982cfc3d84ac
  languageName: node
  linkType: hard

"@eslint/object-schema@npm:^2.1.5":
  version: 2.1.5
  resolution: "@eslint/object-schema@npm:2.1.5"
  checksum: 10/bb07ec53357047f20de923bcd61f0306d9eee83ef41daa32e633e154a44796b5bd94670169eccb8fd8cb4ff42228a43b86953a6321f789f98194baba8207b640
  languageName: node
  linkType: hard

"@eslint/plugin-kit@npm:^0.2.3":
  version: 0.2.4
  resolution: "@eslint/plugin-kit@npm:0.2.4"
  dependencies:
    levn: "npm:^0.4.1"
  checksum: 10/e34d02ea1dccd716e51369620263a4b2167aff3c0510ed776e21336cc3ad7158087449a76931baf07cdc33810cb6919db375f2e9f409435d2c6e0dd5f4786b25
  languageName: node
  linkType: hard

"@humanfs/core@npm:^0.19.1":
  version: 0.19.1
  resolution: "@humanfs/core@npm:0.19.1"
  checksum: 10/270d936be483ab5921702623bc74ce394bf12abbf57d9145a69e8a0d1c87eb1c768bd2d93af16c5705041e257e6d9cc7529311f63a1349f3678abc776fc28523
  languageName: node
  linkType: hard

"@humanfs/node@npm:^0.16.6":
  version: 0.16.6
  resolution: "@humanfs/node@npm:0.16.6"
  dependencies:
    "@humanfs/core": "npm:^0.19.1"
    "@humanwhocodes/retry": "npm:^0.3.0"
  checksum: 10/6d43c6727463772d05610aa05c83dab2bfbe78291022ee7a92cb50999910b8c720c76cc312822e2dea2b497aa1b3fef5fe9f68803fc45c9d4ed105874a65e339
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 10/e993950e346331e5a32eefb27948ecdee2a2c4ab3f072b8f566cd213ef485dd50a3ca497050608db91006f5479e43f91a439aef68d2a313bd3ded06909c7c5b3
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.3.0":
  version: 0.3.1
  resolution: "@humanwhocodes/retry@npm:0.3.1"
  checksum: 10/eb457f699529de7f07649679ec9e0353055eebe443c2efe71c6dd950258892475a038e13c6a8c5e13ed1fb538cdd0a8794faa96b24b6ffc4c87fb1fc9f70ad7f
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.4.1":
  version: 0.4.1
  resolution: "@humanwhocodes/retry@npm:0.4.1"
  checksum: 10/39fafc7319e88f61befebd5e1b4f0136534ea6a9bd10d74366698187bd63544210ec5d79a87ed4d91297f1cc64c4c53d45fb0077a2abfdce212cf0d3862d5f04
  languageName: node
  linkType: hard

"@intlify/core-base@npm:10.0.5":
  version: 10.0.5
  resolution: "@intlify/core-base@npm:10.0.5"
  dependencies:
    "@intlify/message-compiler": "npm:10.0.5"
    "@intlify/shared": "npm:10.0.5"
  checksum: 10/d0680074ca5abc26c348976047001fda76045917fabcbe041fa662c2ca66dc6d22570ebf5e198f718e8b98c3d662e29d824f891183f51ff6b888c7c68db5e4fd
  languageName: node
  linkType: hard

"@intlify/core-base@npm:11.1.2":
  version: 11.1.2
  resolution: "@intlify/core-base@npm:11.1.2"
  dependencies:
    "@intlify/message-compiler": "npm:11.1.2"
    "@intlify/shared": "npm:11.1.2"
  checksum: 10/d61d2a30b9fcefec8ce72352fea000d746edf4ccca280f0925e36f7bb0da1ee7cdbed26ba50144de325f13535f4c5fa64bb26f7a10232095e4f749cd4e149c26
  languageName: node
  linkType: hard

"@intlify/message-compiler@npm:10.0.5":
  version: 10.0.5
  resolution: "@intlify/message-compiler@npm:10.0.5"
  dependencies:
    "@intlify/shared": "npm:10.0.5"
    source-map-js: "npm:^1.0.2"
  checksum: 10/3e386b042ac6dd98533b6ea2b4af610999236a1eb7ff4d77762cd06cae1858a53735b0b7e60828c569c715a9f0ae588aaaf8496f9a4b287a44e7f0fd470857ff
  languageName: node
  linkType: hard

"@intlify/message-compiler@npm:11.1.2":
  version: 11.1.2
  resolution: "@intlify/message-compiler@npm:11.1.2"
  dependencies:
    "@intlify/shared": "npm:11.1.2"
    source-map-js: "npm:^1.0.2"
  checksum: 10/150236c5e4a0019e6f6518c6b23600ca00bc6885617cc23f8e846cfb8762d85e4805db9551dc49d471ffc515c17432720f5891659ca3d088b4b0a1341da8f86f
  languageName: node
  linkType: hard

"@intlify/shared@npm:10.0.5":
  version: 10.0.5
  resolution: "@intlify/shared@npm:10.0.5"
  checksum: 10/64552b8770ad236c11893837e95e4e2ea7c947396cfcee797bf8e26afea6d06f707178f4aa36b0b416d89800ba9cd058e6742bf2ebdd7c7ff7f9fc1ec9bc7b48
  languageName: node
  linkType: hard

"@intlify/shared@npm:11.1.2":
  version: 11.1.2
  resolution: "@intlify/shared@npm:11.1.2"
  checksum: 10/486dc693b444f908533e3e6d555733a4758664f2d6a4bd26becede0651a893b9287125aeb04b51c563174ccf5c7c241495a5a7ecdce0e92734aafe60ef531317
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10/e9ed5fd27c3aec1095e3a16e0c0cf148d1fee55a38665c35f7b3f86a9b5d00d042ddaabc98e8a1cb7463b9378c15f22a94eb35e99469c201453eb8375191f243
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
  checksum: 10/4412e9e6713c89c1e66d80bb0bb5a2a93192f10477623a27d08f228ba0316bb880affabc5bfe7f838f58a34d26c2c190da726e576cdfc18c49a72e89adabdcf5
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.5.0":
  version: 1.5.0
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.0"
  checksum: 10/4ed6123217569a1484419ac53f6ea0d9f3b57e5b57ab30d7c267bdb27792a27eb0e4b08e84a2680aa55cc2f2b411ffd6ec3db01c44fdc6dc43aca4b55f8374fd
  languageName: node
  linkType: hard

"@mdi/font@npm:^7.4.47":
  version: 7.4.47
  resolution: "@mdi/font@npm:7.4.47"
  checksum: 10/ecef7fd514f1970bb257272283dc609800c9e2f86c8388aa10d9e900a29c57a4146f766c7a0775603714799ed715a0a4515aaec6b2c7d28610bea2e95c1fc1a2
  languageName: node
  linkType: hard

"@mdi/js@npm:^7.4.47":
  version: 7.4.47
  resolution: "@mdi/js@npm:7.4.47"
  checksum: 10/c1a8fc82f23030bccc0cf324b13b73a0034d06140e79f8bc7b0e4e59275624c470e5ca6524d6141ad8c4fe3ad0f314c7af99afb3e38df163eb50d3b13b9eab17
  languageName: node
  linkType: hard

"@miragejs/pretender-node-polyfill@npm:^0.1.0":
  version: 0.1.2
  resolution: "@miragejs/pretender-node-polyfill@npm:0.1.2"
  checksum: 10/bb55983a253dc0d4162acac090b062500595c9844bfbd21fe25bc1f926808d0070d710e622aa6285fa16c673a65cf3e05f4b32e888c469f1bde8888a7934ee44
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": "npm:2.0.5"
    run-parallel: "npm:^1.1.9"
  checksum: 10/6ab2a9b8a1d67b067922c36f259e3b3dfd6b97b219c540877a4944549a4d49ea5ceba5663905ab5289682f1f3c15ff441d02f0447f620a42e1cb5e1937174d4b
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 10/012480b5ca9d97bff9261571dbbec7bbc6033f69cc92908bc1ecfad0792361a5a1994bc48674b9ef76419d056a03efadfce5a6cf6dbc0a36559571a7a483f6f0
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": "npm:2.1.5"
    fastq: "npm:^1.6.0"
  checksum: 10/40033e33e96e97d77fba5a238e4bba4487b8284678906a9f616b5579ddaf868a18874c0054a75402c9fbaaa033a25ceae093af58c9c30278e35c23c9479e79b0
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: 10/775c9a7eb1f88c195dfb3bce70c31d0fe2a12b28b754e25c08a3edb4bc4816bfedb7ac64ef1e730579d078ca19dacf11630e99f8f3c3e0fd7b23caa5fd6d30a6
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10/405c4490e1ff11cf299775449a3c254a366a4b1ffc79d87159b0ee7d5558ac9f6a2f8c0735fd6ff3873cef014cb1a44a5f9127cb6a1b2dbc408718cca9365b5a
  languageName: node
  linkType: hard

"@one-ini/wasm@npm:0.1.1":
  version: 0.1.1
  resolution: "@one-ini/wasm@npm:0.1.1"
  checksum: 10/673c11518dba2e582e42415cbefe928513616f3af25e12f6e4e6b1b98b52b3e6c14bc251a361654af63cd64f208f22a1f7556fa49da2bf7efcf28cb14f16f807
  languageName: node
  linkType: hard

"@parcel/watcher-android-arm64@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-android-arm64@npm:2.5.0"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@parcel/watcher-darwin-arm64@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-darwin-arm64@npm:2.5.0"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@parcel/watcher-darwin-x64@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-darwin-x64@npm:2.5.0"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@parcel/watcher-freebsd-x64@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-freebsd-x64@npm:2.5.0"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@parcel/watcher-linux-arm-glibc@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-linux-arm-glibc@npm:2.5.0"
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@parcel/watcher-linux-arm-musl@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-linux-arm-musl@npm:2.5.0"
  conditions: os=linux & cpu=arm & libc=musl
  languageName: node
  linkType: hard

"@parcel/watcher-linux-arm64-glibc@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-linux-arm64-glibc@npm:2.5.0"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@parcel/watcher-linux-arm64-musl@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-linux-arm64-musl@npm:2.5.0"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@parcel/watcher-linux-x64-glibc@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-linux-x64-glibc@npm:2.5.0"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@parcel/watcher-linux-x64-musl@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-linux-x64-musl@npm:2.5.0"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@parcel/watcher-win32-arm64@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-win32-arm64@npm:2.5.0"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@parcel/watcher-win32-ia32@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-win32-ia32@npm:2.5.0"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@parcel/watcher-win32-x64@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-win32-x64@npm:2.5.0"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@parcel/watcher@npm:^2.4.1":
  version: 2.5.0
  resolution: "@parcel/watcher@npm:2.5.0"
  dependencies:
    "@parcel/watcher-android-arm64": "npm:2.5.0"
    "@parcel/watcher-darwin-arm64": "npm:2.5.0"
    "@parcel/watcher-darwin-x64": "npm:2.5.0"
    "@parcel/watcher-freebsd-x64": "npm:2.5.0"
    "@parcel/watcher-linux-arm-glibc": "npm:2.5.0"
    "@parcel/watcher-linux-arm-musl": "npm:2.5.0"
    "@parcel/watcher-linux-arm64-glibc": "npm:2.5.0"
    "@parcel/watcher-linux-arm64-musl": "npm:2.5.0"
    "@parcel/watcher-linux-x64-glibc": "npm:2.5.0"
    "@parcel/watcher-linux-x64-musl": "npm:2.5.0"
    "@parcel/watcher-win32-arm64": "npm:2.5.0"
    "@parcel/watcher-win32-ia32": "npm:2.5.0"
    "@parcel/watcher-win32-x64": "npm:2.5.0"
    detect-libc: "npm:^1.0.3"
    is-glob: "npm:^4.0.3"
    micromatch: "npm:^4.0.5"
    node-addon-api: "npm:^7.0.0"
    node-gyp: "npm:latest"
  dependenciesMeta:
    "@parcel/watcher-android-arm64":
      optional: true
    "@parcel/watcher-darwin-arm64":
      optional: true
    "@parcel/watcher-darwin-x64":
      optional: true
    "@parcel/watcher-freebsd-x64":
      optional: true
    "@parcel/watcher-linux-arm-glibc":
      optional: true
    "@parcel/watcher-linux-arm-musl":
      optional: true
    "@parcel/watcher-linux-arm64-glibc":
      optional: true
    "@parcel/watcher-linux-arm64-musl":
      optional: true
    "@parcel/watcher-linux-x64-glibc":
      optional: true
    "@parcel/watcher-linux-x64-musl":
      optional: true
    "@parcel/watcher-win32-arm64":
      optional: true
    "@parcel/watcher-win32-ia32":
      optional: true
    "@parcel/watcher-win32-x64":
      optional: true
  checksum: 10/1e28b1aa9a63456ebfa7af3e41297d088bd31d9e32548604f4f26ed96c5808f4330cd515062e879c24a9eaab7894066c8a3951ee30b59e7cbe6786ab2c790dae
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10/115e8ceeec6bc69dff2048b35c0ab4f8bbee12d8bb6c1f4af758604586d802b6e669dcb02dda61d078de42c2b4ddce41b3d9e726d7daa6b4b850f4adbf7333ff
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm-eabi@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-android-arm-eabi@npm:4.40.2"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm64@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-android-arm64@npm:4.40.2"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-arm64@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-darwin-arm64@npm:4.40.2"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-x64@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-darwin-x64@npm:4.40.2"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@rollup/rollup-freebsd-arm64@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-freebsd-arm64@npm:4.40.2"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-freebsd-x64@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-freebsd-x64@npm:4.40.2"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm-gnueabihf@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-linux-arm-gnueabihf@npm:4.40.2"
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm-musleabihf@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-linux-arm-musleabihf@npm:4.40.2"
  conditions: os=linux & cpu=arm & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-gnu@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-linux-arm64-gnu@npm:4.40.2"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-musl@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-linux-arm64-musl@npm:4.40.2"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-loongarch64-gnu@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-linux-loongarch64-gnu@npm:4.40.2"
  conditions: os=linux & cpu=loong64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-powerpc64le-gnu@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-linux-powerpc64le-gnu@npm:4.40.2"
  conditions: os=linux & cpu=ppc64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-riscv64-gnu@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-linux-riscv64-gnu@npm:4.40.2"
  conditions: os=linux & cpu=riscv64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-riscv64-musl@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-linux-riscv64-musl@npm:4.40.2"
  conditions: os=linux & cpu=riscv64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-s390x-gnu@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-linux-s390x-gnu@npm:4.40.2"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-gnu@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-linux-x64-gnu@npm:4.40.2"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-musl@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-linux-x64-musl@npm:4.40.2"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-win32-arm64-msvc@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-win32-arm64-msvc@npm:4.40.2"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-win32-ia32-msvc@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-win32-ia32-msvc@npm:4.40.2"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@rollup/rollup-win32-x64-msvc@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-win32-x64-msvc@npm:4.40.2"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@types/estree@npm:1.0.7":
  version: 1.0.7
  resolution: "@types/estree@npm:1.0.7"
  checksum: 10/419c845ece767ad4b21171e6e5b63dabb2eb46b9c0d97361edcd9cabbf6a95fcadb91d89b5fa098d1336fa0b8fceaea82fca97a2ef3971f5c86e53031e157b21
  languageName: node
  linkType: hard

"@types/estree@npm:^1.0.6":
  version: 1.0.6
  resolution: "@types/estree@npm:1.0.6"
  checksum: 10/9d35d475095199c23e05b431bcdd1f6fec7380612aed068b14b2a08aa70494de8a9026765a5a91b1073f636fb0368f6d8973f518a31391d519e20c59388ed88d
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.15":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 10/1a3c3e06236e4c4aab89499c428d585527ce50c24fe8259e8b3926d3df4cfbbbcf306cfc73ddfb66cbafc973116efd15967020b0f738f63e09e64c7d260519e7
  languageName: node
  linkType: hard

"@types/node@npm:22.10.5":
  version: 22.10.5
  resolution: "@types/node@npm:22.10.5"
  dependencies:
    undici-types: "npm:~6.20.0"
  checksum: 10/a5366961ffa9921e8f15435bc18ea9f8b7a7bb6b3d92dd5e93ebcd25e8af65708872bd8e6fee274b4655bab9ca80fbff9f0e42b5b53857790f13cf68cf4cbbfc
  languageName: node
  linkType: hard

"@types/web-bluetooth@npm:^0.0.20":
  version: 0.0.20
  resolution: "@types/web-bluetooth@npm:0.0.20"
  checksum: 10/2faa323e5c994e9468fff4675e3b6d35f3730eb4dc7c761d02267fb6246dbfd659fd8f3583db0872aae05b3ee139799c25655bbe79bf1b56c08c06e665db814b
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:8.19.0":
  version: 8.19.0
  resolution: "@typescript-eslint/eslint-plugin@npm:8.19.0"
  dependencies:
    "@eslint-community/regexpp": "npm:^4.10.0"
    "@typescript-eslint/scope-manager": "npm:8.19.0"
    "@typescript-eslint/type-utils": "npm:8.19.0"
    "@typescript-eslint/utils": "npm:8.19.0"
    "@typescript-eslint/visitor-keys": "npm:8.19.0"
    graphemer: "npm:^1.4.0"
    ignore: "npm:^5.3.1"
    natural-compare: "npm:^1.4.0"
    ts-api-utils: "npm:^1.3.0"
  peerDependencies:
    "@typescript-eslint/parser": ^8.0.0 || ^8.0.0-alpha.0
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.8.0"
  checksum: 10/9b12f1e0708d5f5a0a6819119d6c98bc21c5d9b2b589ecaad6b7fdb50bcf6444b52ea3ed227ffe90d90422a957d3aa30b8ab95f10d0d4d2829996fa1152d0762
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:8.19.0":
  version: 8.19.0
  resolution: "@typescript-eslint/parser@npm:8.19.0"
  dependencies:
    "@typescript-eslint/scope-manager": "npm:8.19.0"
    "@typescript-eslint/types": "npm:8.19.0"
    "@typescript-eslint/typescript-estree": "npm:8.19.0"
    "@typescript-eslint/visitor-keys": "npm:8.19.0"
    debug: "npm:^4.3.4"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.8.0"
  checksum: 10/7e1bd88be87dd59645a314d50fda4cc2c038c9426a80a27a6d85c31b8b2d219451395280f0a59565275342af80b0a611ca650c704302f39ebc392e22a98a2950
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:8.19.0":
  version: 8.19.0
  resolution: "@typescript-eslint/scope-manager@npm:8.19.0"
  dependencies:
    "@typescript-eslint/types": "npm:8.19.0"
    "@typescript-eslint/visitor-keys": "npm:8.19.0"
  checksum: 10/ab7f72533c62b6e7c87e4c91c187098e1026a8d67044c2a8376affede949c7c2f3d1b4bec0047d4001cd16c3a30db0b01672f596ef284d12b143cd4a984cdfdc
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:8.19.0":
  version: 8.19.0
  resolution: "@typescript-eslint/type-utils@npm:8.19.0"
  dependencies:
    "@typescript-eslint/typescript-estree": "npm:8.19.0"
    "@typescript-eslint/utils": "npm:8.19.0"
    debug: "npm:^4.3.4"
    ts-api-utils: "npm:^1.3.0"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.8.0"
  checksum: 10/c8fc5ca2a3f0b701389a2c45ee1ebc5ca211769164b822d54af873a7f1735a160845fcdd14a988a547dda12e1a3249ecef5bc381a1d067b1ab9e6bbf6398f509
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:8.19.0":
  version: 8.19.0
  resolution: "@typescript-eslint/types@npm:8.19.0"
  checksum: 10/1bf02b6fcae72ccd60bbd9858e8e13ff49332bb819e9479d48b081026b62512baa8f72f2123bb3abdf816cc667aae25b311db941bee737afad6118189f761a17
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:8.19.0":
  version: 8.19.0
  resolution: "@typescript-eslint/typescript-estree@npm:8.19.0"
  dependencies:
    "@typescript-eslint/types": "npm:8.19.0"
    "@typescript-eslint/visitor-keys": "npm:8.19.0"
    debug: "npm:^4.3.4"
    fast-glob: "npm:^3.3.2"
    is-glob: "npm:^4.0.3"
    minimatch: "npm:^9.0.4"
    semver: "npm:^7.6.0"
    ts-api-utils: "npm:^1.3.0"
  peerDependencies:
    typescript: ">=4.8.4 <5.8.0"
  checksum: 10/6d396f82079f67d32a5ef644767697e8bbd79d166cc5ea034dcc2b146a62744de91d87885495ecfe3d9a0de98413e4dbb618b76bd657377df174cd2d12bab863
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:8.19.0":
  version: 8.19.0
  resolution: "@typescript-eslint/utils@npm:8.19.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.4.0"
    "@typescript-eslint/scope-manager": "npm:8.19.0"
    "@typescript-eslint/types": "npm:8.19.0"
    "@typescript-eslint/typescript-estree": "npm:8.19.0"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.8.0"
  checksum: 10/e57f5e5d6fc00be3596f58be77904b66079f67a61effa633372ebcc92a0b6090f71183b3be145c55b5a5ab8f0536f98f0034c4d9a8031c1422e4e2753b97fd21
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:8.19.0":
  version: 8.19.0
  resolution: "@typescript-eslint/visitor-keys@npm:8.19.0"
  dependencies:
    "@typescript-eslint/types": "npm:8.19.0"
    eslint-visitor-keys: "npm:^4.2.0"
  checksum: 10/065b95253be57cf5e8f15ebb09dbcb70e97cd37d238a6cf3d4be3c7bc42e94d77a062e8f97a2333a2e03eb58c736c00ec0ab3b7a1bddfef30387eb167776116e
  languageName: node
  linkType: hard

"@vitejs/plugin-vue@npm:5.2.1":
  version: 5.2.1
  resolution: "@vitejs/plugin-vue@npm:5.2.1"
  peerDependencies:
    vite: ^5.0.0 || ^6.0.0
    vue: ^3.2.25
  checksum: 10/60edb926bf919aebe5ef527402bbb84902a23bcba57ea718285e4d700abf9718bc9411806440dc7e8ea0cef06d93c0078e2d456137b4eb3fd70d17288e9db081
  languageName: node
  linkType: hard

"@volar/language-core@npm:2.4.11, @volar/language-core@npm:~2.4.11":
  version: 2.4.11
  resolution: "@volar/language-core@npm:2.4.11"
  dependencies:
    "@volar/source-map": "npm:2.4.11"
  checksum: 10/cda5959642eb469ad6c57c3d7ad731503fc3b1f82bc57c3511cc6c925138fe1f6f3ef253ddf1cc6bd3bdd63d8a5943396b337e97ecd8afec8f486ac79db5f258
  languageName: node
  linkType: hard

"@volar/source-map@npm:2.4.11":
  version: 2.4.11
  resolution: "@volar/source-map@npm:2.4.11"
  checksum: 10/689803b2e788bdfda45a7994605798e8ed5ae7bb1116912db5e8f8c8a18000c0c66ebfae5ce33d3ac4cd3a7e171819253511232ed1eb20b1781b4aaed806d100
  languageName: node
  linkType: hard

"@volar/typescript@npm:~2.4.11":
  version: 2.4.11
  resolution: "@volar/typescript@npm:2.4.11"
  dependencies:
    "@volar/language-core": "npm:2.4.11"
    path-browserify: "npm:^1.0.1"
    vscode-uri: "npm:^3.0.8"
  checksum: 10/fe4be5497a8c12ddf25ee681cb47a3acc78cff4190ed6da7763c6a42f4fb5497cd99fb276ac0e849a70e49c05f9fea73127b10ca3b1a62ed3a33a67c4297215c
  languageName: node
  linkType: hard

"@vue/compiler-core@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/compiler-core@npm:3.5.13"
  dependencies:
    "@babel/parser": "npm:^7.25.3"
    "@vue/shared": "npm:3.5.13"
    entities: "npm:^4.5.0"
    estree-walker: "npm:^2.0.2"
    source-map-js: "npm:^1.2.0"
  checksum: 10/22f042bb47c8a1edb9d602e24da8092ab542d5640f0459a9b99ecf35f90e96678f870209dd30f774f5340c6d817d3c5a46ca49cefb9659ee5b228bd42d1f076a
  languageName: node
  linkType: hard

"@vue/compiler-dom@npm:3.5.13, @vue/compiler-dom@npm:^3.5.0":
  version: 3.5.13
  resolution: "@vue/compiler-dom@npm:3.5.13"
  dependencies:
    "@vue/compiler-core": "npm:3.5.13"
    "@vue/shared": "npm:3.5.13"
  checksum: 10/5dc628c52091264a443c2d7326b759d7d3999c7e9c00078c2eb370b778e60b9f2ef258a8decf2fd97c8fa0923f895d449eabc1e5bc3d8a45d3ef99c9eb0599d7
  languageName: node
  linkType: hard

"@vue/compiler-sfc@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/compiler-sfc@npm:3.5.13"
  dependencies:
    "@babel/parser": "npm:^7.25.3"
    "@vue/compiler-core": "npm:3.5.13"
    "@vue/compiler-dom": "npm:3.5.13"
    "@vue/compiler-ssr": "npm:3.5.13"
    "@vue/shared": "npm:3.5.13"
    estree-walker: "npm:^2.0.2"
    magic-string: "npm:^0.30.11"
    postcss: "npm:^8.4.48"
    source-map-js: "npm:^1.2.0"
  checksum: 10/08d55bbdbe86ad0a1fc0501dbf5f535161d35ecb378adb478dd4a75b97e8d21852516966c0ad8aed1d6da11b0d8280b7848ff142b4181cb8f24eaaecd7827f73
  languageName: node
  linkType: hard

"@vue/compiler-ssr@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/compiler-ssr@npm:3.5.13"
  dependencies:
    "@vue/compiler-dom": "npm:3.5.13"
    "@vue/shared": "npm:3.5.13"
  checksum: 10/09f2706455a7d8a5acc67c98120d28d0105d006184402b045636be7791939f5a77fd1c37657047b0129fa431f03437dcab9befc6baa172367ecdef7618407149
  languageName: node
  linkType: hard

"@vue/compiler-vue2@npm:^2.7.16":
  version: 2.7.16
  resolution: "@vue/compiler-vue2@npm:2.7.16"
  dependencies:
    de-indent: "npm:^1.0.2"
    he: "npm:^1.2.0"
  checksum: 10/739ad06be19206b2715707c226a070509bcf28c31b539a6fc932d220eb7b0c09109d71fded573ed0c4073429793a3513ca4a4e69ad4f7afc0c5bc3c28639e871
  languageName: node
  linkType: hard

"@vue/devtools-api@npm:^6.5.0, @vue/devtools-api@npm:^6.6.4":
  version: 6.6.4
  resolution: "@vue/devtools-api@npm:6.6.4"
  checksum: 10/0fca4912b6ae0185b9375f5d113d417984077db0681c74cf39eb8522eb82c27f662a72e1ae3e0d79e105fdd0a99a7cbd65ed111465d238f60cce10922e02a812
  languageName: node
  linkType: hard

"@vue/eslint-config-typescript@npm:14.2.0":
  version: 14.2.0
  resolution: "@vue/eslint-config-typescript@npm:14.2.0"
  dependencies:
    fast-glob: "npm:^3.3.2"
    typescript-eslint: "npm:^8.18.1"
    vue-eslint-parser: "npm:^9.4.3"
  peerDependencies:
    eslint: ^9.10.0
    eslint-plugin-vue: ^9.28.0
    typescript: ">=4.8.4"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10/10cc365d97218b0c633b28cd2557a515c4c93bf7024b66a672113316836ef0163d737112e524924b69cbf929545267ae2c175af7af858ed37ddb5fce57b2089c
  languageName: node
  linkType: hard

"@vue/language-core@npm:2.2.0":
  version: 2.2.0
  resolution: "@vue/language-core@npm:2.2.0"
  dependencies:
    "@volar/language-core": "npm:~2.4.11"
    "@vue/compiler-dom": "npm:^3.5.0"
    "@vue/compiler-vue2": "npm:^2.7.16"
    "@vue/shared": "npm:^3.5.0"
    alien-signals: "npm:^0.4.9"
    minimatch: "npm:^9.0.3"
    muggle-string: "npm:^0.4.1"
    path-browserify: "npm:^1.0.1"
  peerDependencies:
    typescript: "*"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10/dc22d038509a58e5a5569fe19f67e7373067cde3531b1e405270dcbe026a8cdbb1de8a7a93d6aaa76a3c5b71393f5c6ec5d9125f0b050898cb96a5c62b4a8d88
  languageName: node
  linkType: hard

"@vue/reactivity@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/reactivity@npm:3.5.13"
  dependencies:
    "@vue/shared": "npm:3.5.13"
  checksum: 10/e4db379fad27f9fbf2cb19133b8b1320b72e34dd60ec3654756175c67b1c76b356ec4f4dd58443e3c1b73e2814bec18eb67822b6d27966a7e8e450986e0c56f2
  languageName: node
  linkType: hard

"@vue/runtime-core@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/runtime-core@npm:3.5.13"
  dependencies:
    "@vue/reactivity": "npm:3.5.13"
    "@vue/shared": "npm:3.5.13"
  checksum: 10/55ef3ec9efe59b84c2468abb486ff8ecd717607332182699ff5bbfe646687ee5c16c1bd57f968a4a4a4103289bba70667e3e7ea8b4d5eb0ebc8778411279942a
  languageName: node
  linkType: hard

"@vue/runtime-dom@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/runtime-dom@npm:3.5.13"
  dependencies:
    "@vue/reactivity": "npm:3.5.13"
    "@vue/runtime-core": "npm:3.5.13"
    "@vue/shared": "npm:3.5.13"
    csstype: "npm:^3.1.3"
  checksum: 10/f32e52b08cc1e9daf645a665ff40040ea4a8f7ee2f98c39e5d4f1e959a004cc330770ce2fb1406b3d567745148cf78e9f96935118add91af3e1a2ce2c7c11040
  languageName: node
  linkType: hard

"@vue/server-renderer@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/server-renderer@npm:3.5.13"
  dependencies:
    "@vue/compiler-ssr": "npm:3.5.13"
    "@vue/shared": "npm:3.5.13"
  peerDependencies:
    vue: 3.5.13
  checksum: 10/1da86b265dfc74336fd4212e4b3033c9cf069d024cf9823846e77edab976ec58f3a0b6bd0dbf2449620939811f4959c74a8c3a6ae65392429351d68925d73307
  languageName: node
  linkType: hard

"@vue/shared@npm:3.5.13, @vue/shared@npm:^3.5.0":
  version: 3.5.13
  resolution: "@vue/shared@npm:3.5.13"
  checksum: 10/5c0c24f443533392dde08c3e4272ff2e19af9762f90baeaa808850e05106537bbd9e2d2ad2081d979b8c4bc89902395b46036b67f74c60b76025924de37833b1
  languageName: node
  linkType: hard

"@vue/test-utils@npm:2.4.6":
  version: 2.4.6
  resolution: "@vue/test-utils@npm:2.4.6"
  dependencies:
    js-beautify: "npm:^1.14.9"
    vue-component-type-helpers: "npm:^2.0.0"
  checksum: 10/a3b445f1dae9b663e8cdb048054bb16d0b24b7901bcf45c81e84a8340030651bb7ad9fd309d36f5963addd25883968e0f81da44a06bab99bb9aa89b4235f2880
  languageName: node
  linkType: hard

"@vue/tsconfig@npm:0.7.0":
  version: 0.7.0
  resolution: "@vue/tsconfig@npm:0.7.0"
  peerDependencies:
    typescript: 5.x
    vue: ^3.4.0
  peerDependenciesMeta:
    typescript:
      optional: true
    vue:
      optional: true
  checksum: 10/29b5dd11cc179de707dfd549d0e80e220f4a0f6f7d90b9a52769bc655e599b9166b28af1218819245fbdd30a74d80fdd484538cdda6876d9d369ae623f234834
  languageName: node
  linkType: hard

"@vuetify/loader-shared@npm:^2.0.3":
  version: 2.0.3
  resolution: "@vuetify/loader-shared@npm:2.0.3"
  dependencies:
    upath: "npm:^2.0.1"
  peerDependencies:
    vue: ^3.0.0
    vuetify: ^3.0.0
  checksum: 10/19b74b040820da2e9ca4c077c0693d060ecc6a9266ecaf11d73a4db279cc428a339a34716a00794b04616a6ad1a1447e2822c9b550af79de672b540bb0a7d3c4
  languageName: node
  linkType: hard

"@vueuse/core@npm:^10.11.0":
  version: 10.11.1
  resolution: "@vueuse/core@npm:10.11.1"
  dependencies:
    "@types/web-bluetooth": "npm:^0.0.20"
    "@vueuse/metadata": "npm:10.11.1"
    "@vueuse/shared": "npm:10.11.1"
    vue-demi: "npm:>=0.14.8"
  checksum: 10/bbebdcd1ef0a77437cf2432062bb57c13e0afdadc474861ddad7ce343a8638778c5e54ad302c80e1d4351d4bdaf43501dc5937185e08d257fd000bdfea4ecb36
  languageName: node
  linkType: hard

"@vueuse/metadata@npm:10.11.1":
  version: 10.11.1
  resolution: "@vueuse/metadata@npm:10.11.1"
  checksum: 10/20336a05eb4945c1486d5e0a81c91a8ba08137f567b86d2bc1171af36d916c56d30166163016ed64642cce904cad1af09332655f7f0178eb5f2c65e54f648b4f
  languageName: node
  linkType: hard

"@vueuse/shared@npm:10.11.1":
  version: 10.11.1
  resolution: "@vueuse/shared@npm:10.11.1"
  dependencies:
    vue-demi: "npm:>=0.14.8"
  checksum: 10/5d8c28ed441a66b200c76ab44b6dbc6af152a6cecc527a1f4f645d95547e1de65e4b99e16d25f77609cb146e9e4b4961b260fe723043b08ab198e88ed1d47f4e
  languageName: node
  linkType: hard

"abbrev@npm:^2.0.0":
  version: 2.0.0
  resolution: "abbrev@npm:2.0.0"
  checksum: 10/ca0a54e35bea4ece0ecb68a47b312e1a9a6f772408d5bcb9051230aaa94b0460671c5b5c9cb3240eb5b7bc94c52476550eb221f65a0bbd0145bdc9f3113a6707
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: 10/d4371eaef7995530b5b5ca4183ff6f062ca17901a6d3f673c9ac011b01ede37e7a1f7f61f8f5cfe709e88054757bb8f3277dc4061087cdf4f2a1f90ccbcdb977
  languageName: node
  linkType: hard

"acorn@npm:^8.14.0, acorn@npm:^8.9.0":
  version: 8.14.0
  resolution: "acorn@npm:8.14.0"
  bin:
    acorn: bin/acorn
  checksum: 10/6df29c35556782ca9e632db461a7f97947772c6c1d5438a81f0c873a3da3a792487e83e404d1c6c25f70513e91aa18745f6eafb1fcc3a43ecd1920b21dd173d2
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 10/3db6d8d4651f2aa1a9e4af35b96ab11a7607af57a24f3bc721a387eaa3b5f674e901f0a648b0caefd48f3fd117c7761b79a3b55854e2aebaa96c3f32cf76af84
  languageName: node
  linkType: hard

"ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: "npm:^3.1.1"
    fast-json-stable-stringify: "npm:^2.0.0"
    json-schema-traverse: "npm:^0.4.1"
    uri-js: "npm:^4.2.2"
  checksum: 10/48d6ad21138d12eb4d16d878d630079a2bda25a04e745c07846a4ad768319533031e28872a9b3c5790fa1ec41aabdf2abed30a56e5a03ebc2cf92184b8ee306c
  languageName: node
  linkType: hard

"alien-signals@npm:^0.4.9":
  version: 0.4.12
  resolution: "alien-signals@npm:0.4.12"
  checksum: 10/53bc8a8ed0ddaa58c173c30c5f6ca9154822c236042972954271146603125981fbfad986eee09de95496a49665ff1765071dbeb256c33be4ad644effe0bf5bbb
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10/2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 10/495834a53b0856c02acd40446f7130cb0f8284f4a39afdab20d5dc42b2e198b1196119fe887beed8f9055c4ff2055e3b2f6d4641d0be018cdfb64fedf6fc1aac
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10/b4494dfbfc7e4591b4711a396bd27e540f8153914123dccb4cdbbcb514015ada63a3809f362b9d8d4f6b17a706f1d7bea3c6f974b15fa5ae76b5b502070889ff
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10/70fdf883b704d17a5dfc9cde206e698c16bcd74e7f196ab821511651aee4f9f76c9514bdfa6ca3a27b5e49138b89cb222a28caf3afe4567570139577f991df32
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 10/18640244e641a417ec75a9bd38b0b2b6b95af5199aa241b131d4b2fb206f334d7ecc600bd194861610a5579084978bfcbb02baa399dbe442d56d0ae5e60dbaef
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 10/3ce727cbc78f69d6a4722517a58ee926c8c21083633b1d3fdf66fd688f6c127a53a592141bd4866f9b63240a86e9d8e974b13919450bd17fa33c2d22c4558ad8
  languageName: node
  linkType: hard

"backoffice-ui@workspace:.":
  version: 0.0.0-use.local
  resolution: "backoffice-ui@workspace:."
  dependencies:
    "@bd/cd-system3": "npm:11.1.0"
    "@eslint/compat": "npm:1.2.4"
    "@types/node": "npm:22.10.5"
    "@vitejs/plugin-vue": "npm:5.2.1"
    "@vue/eslint-config-typescript": "npm:14.2.0"
    "@vue/test-utils": "npm:2.4.6"
    "@vue/tsconfig": "npm:0.7.0"
    eslint: "npm:9.17.0"
    eslint-formatter-gitlab: "npm:5.1.0"
    eslint-plugin-vue: "npm:9.32.0"
    jsdom: "npm:26.0.0"
    keycloak-js: "npm:26.0.7"
    miragejs: "npm:0.1.48"
    sass: "npm:1.83.0"
    sass-loader: "npm:16.0.4"
    typescript: "npm:5.7.2"
    vite: "npm:6.1.6"
    vite-plugin-vuetify: "npm:2.0.4"
    vue: "npm:3.5.13"
    vue-i18n: "npm:11.1.2"
    vue-router: "npm:4.5.0"
    vue-tsc: "npm:2.2.0"
    vuetify: "npm:3.7.6"
  languageName: unknown
  linkType: soft

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10/9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: 10/3e25c80ef626c3a3487c73dbfc70ac322ec830666c9ad915d11b701142fab25ec1e63eff2c450c74347acfd2de854ccde865cd79ef4db1683f7c7b046ea43bb0
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: "npm:^1.0.0"
    concat-map: "npm:0.0.1"
  checksum: 10/faf34a7bb0c3fcf4b59c7808bc5d2a96a40988addf2e7e09dfbb67a2251800e0d14cd2bfc1aa79174f2f5095c54ff27f46fb1289fe2d77dac755b5eb3434cc07
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10/a61e7cd2e8a8505e9f0036b3b6108ba5e926b4b55089eeb5550cd04a471fe216c96d4fe7e4c7f995c728c554ae20ddfc4244cad10aef255e72b62930afd233d1
  languageName: node
  linkType: hard

"braces@npm:^3.0.3":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: "npm:^7.1.1"
  checksum: 10/fad11a0d4697a27162840b02b1fad249c1683cbc510cd5bf1a471f2f8085c046d41094308c577a50a03a579dd99d5a6b3724c4b5e8b14df2c4443844cfcda2c6
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": "npm:^4.0.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^7.0.2"
    ssri: "npm:^12.0.0"
    tar: "npm:^7.4.3"
    unique-filename: "npm:^4.0.0"
  checksum: 10/ea026b27b13656330c2bbaa462a88181dcaa0435c1c2e705db89b31d9bdf7126049d6d0445ba746dca21454a0cfdf1d6f47fd39d34c8c8435296b30bc5738a13
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 10/072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10/cb3f3e594913d63b1814d7ca7c9bafbf895f75fbf93b92991980610dfd7b48500af4e3a5d4e3a8f337990a96b168d7eb84ee55efdce965e2ee8efc20f8c8f139
  languageName: node
  linkType: hard

"chokidar@npm:^4.0.0":
  version: 4.0.2
  resolution: "chokidar@npm:4.0.2"
  dependencies:
    readdirp: "npm:^4.0.1"
  checksum: 10/fc25d20d72ee0e74b5be1fd9df366dc8aa17709a59c364c321b6f35b6d2fd8c65d01bda74eb42ffd61ad7807e5de5e673c6bd503c2ed0ab2a79be5cb51d4c259
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: 10/b63cb1f73d171d140a2ed8154ee6566c8ab775d3196b0e03a2a94b5f6a0ce7777ee5685ca56849403c8d17bd457a6540672f9a60696a6137c7a409097495b82c
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10/fa00c91b4332b294de06b443923246bccebe9fab1b253f7fe1772d37b06a2269b4039a85e309abe1fe11b267b11c08d1d0473fda3badd6167f57313af2887a64
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10/b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: "npm:~1.0.0"
  checksum: 10/2e969e637d05d09fa50b02d74c83a1186f6914aae89e6653b62595cc75a221464f884f55f231b8f4df7a49537fba60bdc0427acd2bf324c09a1dbb84837e36e4
  languageName: node
  linkType: hard

"commander@npm:^10.0.0":
  version: 10.0.1
  resolution: "commander@npm:10.0.1"
  checksum: 10/8799faa84a30da985802e661cc9856adfaee324d4b138413013ef7f087e8d7924b144c30a1f1405475f0909f467665cd9e1ce13270a2f41b141dab0b7a58f3fb
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 10/9680699c8e2b3af0ae22592cb764acaf973f292a7b71b8a06720233011853a58e256c89216a10cbe889727532fd77f8bcd49a760cedfde271b8e006c20e079f2
  languageName: node
  linkType: hard

"config-chain@npm:^1.1.13":
  version: 1.1.13
  resolution: "config-chain@npm:1.1.13"
  dependencies:
    ini: "npm:^1.3.4"
    proto-list: "npm:~1.2.1"
  checksum: 10/83d22cabf709e7669f6870021c4d552e4fc02e9682702b726be94295f42ce76cfed00f70b2910ce3d6c9465d9758e191e28ad2e72ff4e3331768a90da6c1ef03
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.0, cross-spawn@npm:^7.0.6":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10/0d52657d7ae36eb130999dffff1168ec348687b48dd38e2ff59992ed916c88d328cf1d07ff4a4a10bc78de5e1c23f04b306d569e42f7a2293915c081e4dfee86
  languageName: node
  linkType: hard

"cssesc@npm:^3.0.0":
  version: 3.0.0
  resolution: "cssesc@npm:3.0.0"
  bin:
    cssesc: bin/cssesc
  checksum: 10/0e161912c1306861d8f46e1883be1cbc8b1b2879f0f509287c0db71796e4ddfb97ac96bdfca38f77f452e2c10554e1bb5678c99b07a5cf947a12778f73e47e12
  languageName: node
  linkType: hard

"cssstyle@npm:^4.2.1":
  version: 4.2.1
  resolution: "cssstyle@npm:4.2.1"
  dependencies:
    "@asamuzakjp/css-color": "npm:^2.8.2"
    rrweb-cssom: "npm:^0.8.0"
  checksum: 10/e287234f2fd4feb1d79217480f48356f398cc11b9d17d39e6624f7dc1bf4b51d1e2c49f12b1a324834b445c17cbbf83ae5d3ba22c89a6b229f86bcebeda746a8
  languageName: node
  linkType: hard

"csstype@npm:^3.1.3":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 10/f593cce41ff5ade23f44e77521e3a1bcc2c64107041e1bf6c3c32adc5187d0d60983292fda326154d20b01079e24931aa5b08e4467cc488b60bb1e7f6d478ade
  languageName: node
  linkType: hard

"data-urls@npm:^5.0.0":
  version: 5.0.0
  resolution: "data-urls@npm:5.0.0"
  dependencies:
    whatwg-mimetype: "npm:^4.0.0"
    whatwg-url: "npm:^14.0.0"
  checksum: 10/5c40568c31b02641a70204ff233bc4e42d33717485d074244a98661e5f2a1e80e38fe05a5755dfaf2ee549f2ab509d6a3af2a85f4b2ad2c984e5d176695eaf46
  languageName: node
  linkType: hard

"de-indent@npm:^1.0.2":
  version: 1.0.2
  resolution: "de-indent@npm:1.0.2"
  checksum: 10/30bf43744dca005f9252dbb34ed95dcb3c30dfe52bfed84973b89c29eccff04e27769f222a34c61a93354acf47457785e9032e6184be390ed1d324fb9ab3f427
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.3, debug@npm:^4.3.4":
  version: 4.4.0
  resolution: "debug@npm:4.4.0"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10/1847944c2e3c2c732514b93d11886575625686056cd765336212dc15de2d2b29612b6cd80e1afba767bb8e1803b778caf9973e98169ef1a24a7a7009e1820367
  languageName: node
  linkType: hard

"decimal.js@npm:^10.4.3":
  version: 10.4.3
  resolution: "decimal.js@npm:10.4.3"
  checksum: 10/de663a7bc4d368e3877db95fcd5c87b965569b58d16cdc4258c063d231ca7118748738df17cd638f7e9dd0be8e34cec08d7234b20f1f2a756a52fc5a38b188d0
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: 10/ec12d074aef5ae5e81fa470b9317c313142c9e8e2afe3f8efa124db309720db96d1d222b82b84c834e5f87e7a614b44a4684b6683583118b87c833b3be40d4d8
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 10/46fe6e83e2cb1d85ba50bd52803c68be9bd953282fa7096f51fc29edd5d67ff84ff753c51966061e5ba7cb5e47ef6d36a91924eddb7f3f3483b1c560f77a0020
  languageName: node
  linkType: hard

"detect-libc@npm:^1.0.3":
  version: 1.0.3
  resolution: "detect-libc@npm:1.0.3"
  bin:
    detect-libc: ./bin/detect-libc.js
  checksum: 10/3849fe7720feb153e4ac9407086956e073f1ce1704488290ef0ca8aab9430a8d48c8a9f8351889e7cdc64e5b1128589501e4fef48f3a4a49ba92cd6d112d0757
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10/9b1d3e1baefeaf7d70799db8774149cef33b97183a6addceeba0cf6b85ba23ee2686f302f14482006df32df75d32b17c509c143a3689627929e4a8efaf483952
  languageName: node
  linkType: hard

"editorconfig@npm:^1.0.4":
  version: 1.0.4
  resolution: "editorconfig@npm:1.0.4"
  dependencies:
    "@one-ini/wasm": "npm:0.1.1"
    commander: "npm:^10.0.0"
    minimatch: "npm:9.0.1"
    semver: "npm:^7.5.3"
  bin:
    editorconfig: bin/editorconfig
  checksum: 10/bd0a7236f31a7f54801cb6f3222508d4f872a24e440bef30ee29f4ba667c0741724e52e0ad521abe3409b12cdafd8384bb751de9b2a2ee5f845c740edd2e742f
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10/c72d67a6821be15ec11997877c437491c313d924306b8da5d87d2a2bcc2cec9903cb5b04ee1a088460501d8e5b44f10df82fdc93c444101a7610b80c8b6938e1
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10/915acf859cea7131dac1b2b5c9c8e35c4849e325a1d114c30adb8cd615970f6dca0e27f64f3a4949d7d6ed86ecd79a1c5c63f02e697513cddd7b5835c90948b8
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10/bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"entities@npm:^4.5.0":
  version: 4.5.0
  resolution: "entities@npm:4.5.0"
  checksum: 10/ede2a35c9bce1aeccd055a1b445d41c75a14a2bb1cd22e242f20cf04d236cdcd7f9c859eb83f76885327bfae0c25bf03303665ee1ce3d47c5927b98b0e3e3d48
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10/65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10/1d20d825cdcce8d811bfbe86340f4755c02655a7feb2f13f8c880566d9d72a3f6c92c192a6867632e490d6da67b678271f46e01044996a6443e870331100dfdd
  languageName: node
  linkType: hard

"esbuild@npm:^0.24.2":
  version: 0.24.2
  resolution: "esbuild@npm:0.24.2"
  dependencies:
    "@esbuild/aix-ppc64": "npm:0.24.2"
    "@esbuild/android-arm": "npm:0.24.2"
    "@esbuild/android-arm64": "npm:0.24.2"
    "@esbuild/android-x64": "npm:0.24.2"
    "@esbuild/darwin-arm64": "npm:0.24.2"
    "@esbuild/darwin-x64": "npm:0.24.2"
    "@esbuild/freebsd-arm64": "npm:0.24.2"
    "@esbuild/freebsd-x64": "npm:0.24.2"
    "@esbuild/linux-arm": "npm:0.24.2"
    "@esbuild/linux-arm64": "npm:0.24.2"
    "@esbuild/linux-ia32": "npm:0.24.2"
    "@esbuild/linux-loong64": "npm:0.24.2"
    "@esbuild/linux-mips64el": "npm:0.24.2"
    "@esbuild/linux-ppc64": "npm:0.24.2"
    "@esbuild/linux-riscv64": "npm:0.24.2"
    "@esbuild/linux-s390x": "npm:0.24.2"
    "@esbuild/linux-x64": "npm:0.24.2"
    "@esbuild/netbsd-arm64": "npm:0.24.2"
    "@esbuild/netbsd-x64": "npm:0.24.2"
    "@esbuild/openbsd-arm64": "npm:0.24.2"
    "@esbuild/openbsd-x64": "npm:0.24.2"
    "@esbuild/sunos-x64": "npm:0.24.2"
    "@esbuild/win32-arm64": "npm:0.24.2"
    "@esbuild/win32-ia32": "npm:0.24.2"
    "@esbuild/win32-x64": "npm:0.24.2"
  dependenciesMeta:
    "@esbuild/aix-ppc64":
      optional: true
    "@esbuild/android-arm":
      optional: true
    "@esbuild/android-arm64":
      optional: true
    "@esbuild/android-x64":
      optional: true
    "@esbuild/darwin-arm64":
      optional: true
    "@esbuild/darwin-x64":
      optional: true
    "@esbuild/freebsd-arm64":
      optional: true
    "@esbuild/freebsd-x64":
      optional: true
    "@esbuild/linux-arm":
      optional: true
    "@esbuild/linux-arm64":
      optional: true
    "@esbuild/linux-ia32":
      optional: true
    "@esbuild/linux-loong64":
      optional: true
    "@esbuild/linux-mips64el":
      optional: true
    "@esbuild/linux-ppc64":
      optional: true
    "@esbuild/linux-riscv64":
      optional: true
    "@esbuild/linux-s390x":
      optional: true
    "@esbuild/linux-x64":
      optional: true
    "@esbuild/netbsd-arm64":
      optional: true
    "@esbuild/netbsd-x64":
      optional: true
    "@esbuild/openbsd-arm64":
      optional: true
    "@esbuild/openbsd-x64":
      optional: true
    "@esbuild/sunos-x64":
      optional: true
    "@esbuild/win32-arm64":
      optional: true
    "@esbuild/win32-ia32":
      optional: true
    "@esbuild/win32-x64":
      optional: true
  bin:
    esbuild: bin/esbuild
  checksum: 10/95425071c9f24ff88bf61e0710b636ec0eb24ddf8bd1f7e1edef3044e1221104bbfa7bbb31c18018c8c36fa7902c5c0b843f829b981ebc89160cf5eebdaa58f4
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 10/98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"eslint-formatter-gitlab@npm:5.1.0":
  version: 5.1.0
  resolution: "eslint-formatter-gitlab@npm:5.1.0"
  dependencies:
    chalk: "npm:^4.0.0"
    yaml: "npm:^2.0.0"
  peerDependencies:
    eslint: ">=5"
  checksum: 10/cf5f7bcb884addd263be2126c21484e2eb9139ea5d89d484d65d4f332d35937d8dce5fb3b263037dad0088dfa1c16de82b18ba9eb79891f71897f34b807d1817
  languageName: node
  linkType: hard

"eslint-plugin-vue@npm:9.32.0":
  version: 9.32.0
  resolution: "eslint-plugin-vue@npm:9.32.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.4.0"
    globals: "npm:^13.24.0"
    natural-compare: "npm:^1.4.0"
    nth-check: "npm:^2.1.1"
    postcss-selector-parser: "npm:^6.0.15"
    semver: "npm:^7.6.3"
    vue-eslint-parser: "npm:^9.4.3"
    xml-name-validator: "npm:^4.0.0"
  peerDependencies:
    eslint: ^6.2.0 || ^7.0.0 || ^8.0.0 || ^9.0.0
  checksum: 10/2e1f22e82dfa5aa82b71a3b1ff1ba3de25b65018f62b9ae081bea6291832ce1bdbdbdcaeaab23c67863224a1dbd66bfe71ef6d40640468e93480318e60020816
  languageName: node
  linkType: hard

"eslint-scope@npm:^7.1.1":
  version: 7.2.2
  resolution: "eslint-scope@npm:7.2.2"
  dependencies:
    esrecurse: "npm:^4.3.0"
    estraverse: "npm:^5.2.0"
  checksum: 10/5c660fb905d5883ad018a6fea2b49f3cb5b1cbf2cd4bd08e98646e9864f9bc2c74c0839bed2d292e90a4a328833accc197c8f0baed89cbe8d605d6f918465491
  languageName: node
  linkType: hard

"eslint-scope@npm:^8.2.0":
  version: 8.2.0
  resolution: "eslint-scope@npm:8.2.0"
  dependencies:
    esrecurse: "npm:^4.3.0"
    estraverse: "npm:^5.2.0"
  checksum: 10/cd9ab60d5a68f3a0fcac04d1cff5a7383d0f331964d5f1c446259123caec5b3ccc542284d07846e4f4d1389da77750821cc9a6e1ce18558c674977351666f9a6
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.3.0, eslint-visitor-keys@npm:^3.4.1, eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 10/3f357c554a9ea794b094a09bd4187e5eacd1bc0d0653c3adeb87962c548e6a1ab8f982b86963ae1337f5d976004146536dcee5d0e2806665b193fbfbf1a9231b
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^4.2.0":
  version: 4.2.0
  resolution: "eslint-visitor-keys@npm:4.2.0"
  checksum: 10/9651b3356b01760e586b4c631c5268c0e1a85236e3292bf754f0472f465bf9a856c0ddc261fceace155334118c0151778effafbab981413dbf9288349343fa25
  languageName: node
  linkType: hard

"eslint@npm:9.17.0":
  version: 9.17.0
  resolution: "eslint@npm:9.17.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.2.0"
    "@eslint-community/regexpp": "npm:^4.12.1"
    "@eslint/config-array": "npm:^0.19.0"
    "@eslint/core": "npm:^0.9.0"
    "@eslint/eslintrc": "npm:^3.2.0"
    "@eslint/js": "npm:9.17.0"
    "@eslint/plugin-kit": "npm:^0.2.3"
    "@humanfs/node": "npm:^0.16.6"
    "@humanwhocodes/module-importer": "npm:^1.0.1"
    "@humanwhocodes/retry": "npm:^0.4.1"
    "@types/estree": "npm:^1.0.6"
    "@types/json-schema": "npm:^7.0.15"
    ajv: "npm:^6.12.4"
    chalk: "npm:^4.0.0"
    cross-spawn: "npm:^7.0.6"
    debug: "npm:^4.3.2"
    escape-string-regexp: "npm:^4.0.0"
    eslint-scope: "npm:^8.2.0"
    eslint-visitor-keys: "npm:^4.2.0"
    espree: "npm:^10.3.0"
    esquery: "npm:^1.5.0"
    esutils: "npm:^2.0.2"
    fast-deep-equal: "npm:^3.1.3"
    file-entry-cache: "npm:^8.0.0"
    find-up: "npm:^5.0.0"
    glob-parent: "npm:^6.0.2"
    ignore: "npm:^5.2.0"
    imurmurhash: "npm:^0.1.4"
    is-glob: "npm:^4.0.0"
    json-stable-stringify-without-jsonify: "npm:^1.0.1"
    lodash.merge: "npm:^4.6.2"
    minimatch: "npm:^3.1.2"
    natural-compare: "npm:^1.4.0"
    optionator: "npm:^0.9.3"
  peerDependencies:
    jiti: "*"
  peerDependenciesMeta:
    jiti:
      optional: true
  bin:
    eslint: bin/eslint.js
  checksum: 10/a48ee67dd4e737974bbb49ca5d12d0ce35bcd874507807599e3655bb398320ab27c9deed1aad508a963967815e626c21208f52158c2fc0796d0cc8186528efeb
  languageName: node
  linkType: hard

"espree@npm:^10.0.1, espree@npm:^10.3.0":
  version: 10.3.0
  resolution: "espree@npm:10.3.0"
  dependencies:
    acorn: "npm:^8.14.0"
    acorn-jsx: "npm:^5.3.2"
    eslint-visitor-keys: "npm:^4.2.0"
  checksum: 10/3412d44d4204c9e29d6b5dd0277400cfa0cd68495dc09eae1b9ce79d0c8985c1c5cc09cb9ba32a1cd963f48a49b0c46bdb7736afe395a300aa6bb1c0d86837e8
  languageName: node
  linkType: hard

"espree@npm:^9.3.1":
  version: 9.6.1
  resolution: "espree@npm:9.6.1"
  dependencies:
    acorn: "npm:^8.9.0"
    acorn-jsx: "npm:^5.3.2"
    eslint-visitor-keys: "npm:^3.4.1"
  checksum: 10/255ab260f0d711a54096bdeda93adff0eadf02a6f9b92f02b323e83a2b7fc258797919437ad331efec3930475feb0142c5ecaaf3cdab4befebd336d47d3f3134
  languageName: node
  linkType: hard

"esquery@npm:^1.4.0, esquery@npm:^1.5.0":
  version: 1.6.0
  resolution: "esquery@npm:1.6.0"
  dependencies:
    estraverse: "npm:^5.1.0"
  checksum: 10/c587fb8ec9ed83f2b1bc97cf2f6854cc30bf784a79d62ba08c6e358bf22280d69aee12827521cf38e69ae9761d23fb7fde593ce315610f85655c139d99b05e5a
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: "npm:^5.2.0"
  checksum: 10/44ffcd89e714ea6b30143e7f119b104fc4d75e77ee913f34d59076b40ef2d21967f84e019f84e1fd0465b42cdbf725db449f232b5e47f29df29ed76194db8e16
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 10/37cbe6e9a68014d34dbdc039f90d0baf72436809d02edffcc06ba3c2a12eb298048f877511353b130153e532aac8d68ba78430c0dd2f44806ebc7c014b01585e
  languageName: node
  linkType: hard

"estree-walker@npm:^2.0.2":
  version: 2.0.2
  resolution: "estree-walker@npm:2.0.2"
  checksum: 10/b02109c5d46bc2ed47de4990eef770f7457b1159a229f0999a09224d2b85ffeed2d7679cffcff90aeb4448e94b0168feb5265b209cdec29aad50a3d6e93d21e2
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 10/b23acd24791db11d8f65be5ea58fd9a6ce2df5120ae2da65c16cfc5331ff59d5ac4ef50af66cd4bde238881503ec839928a0135b99a036a9cdfa22d17fd56cdb
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.1
  resolution: "exponential-backoff@npm:3.1.1"
  checksum: 10/2d9bbb6473de7051f96790d5f9a678f32e60ed0aa70741dc7fdc96fec8d631124ec3374ac144387604f05afff9500f31a1d45bd9eee4cdc2e4f9ad2d9b9d5dbd
  languageName: node
  linkType: hard

"fake-xml-http-request@npm:^2.1.2":
  version: 2.1.2
  resolution: "fake-xml-http-request@npm:2.1.2"
  checksum: 10/3fa247ff0a0af7e050002b3cbe4e2aeccab9f98dcc3afb379a02ffc32efb2571879202b4474a6ff73013999eb1f85aef0f2bf6d96c008dcb307230df873c3db7
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: 10/e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-glob@npm:^3.3.2":
  version: 3.3.2
  resolution: "fast-glob@npm:3.3.2"
  dependencies:
    "@nodelib/fs.stat": "npm:^2.0.2"
    "@nodelib/fs.walk": "npm:^1.2.3"
    glob-parent: "npm:^5.1.2"
    merge2: "npm:^1.3.0"
    micromatch: "npm:^4.0.4"
  checksum: 10/222512e9315a0efca1276af9adb2127f02105d7288fa746145bf45e2716383fb79eb983c89601a72a399a56b7c18d38ce70457c5466218c5f13fad957cee16df
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: 10/2c20055c1fa43c922428f16ca8bb29f2807de63e5c851f665f7ac9790176c01c3b40335257736b299764a8d383388dabc73c8083b8e1bc3d99f0a941444ec60e
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 10/eb7e220ecf2bab5159d157350b81d01f75726a4382f5a9266f42b9150c4523b9795f7f5d9fbbbeaeac09a441b2369f05ee02db48ea938584205530fe5693cfe1
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.17.1
  resolution: "fastq@npm:1.17.1"
  dependencies:
    reusify: "npm:^1.0.4"
  checksum: 10/a443180068b527dd7b3a63dc7f2a47ceca2f3e97b9c00a1efe5538757e6cc4056a3526df94308075d7727561baf09ebaa5b67da8dcbddb913a021c5ae69d1f69
  languageName: node
  linkType: hard

"file-entry-cache@npm:^8.0.0":
  version: 8.0.0
  resolution: "file-entry-cache@npm:8.0.0"
  dependencies:
    flat-cache: "npm:^4.0.0"
  checksum: 10/********************************************************************************************************************************
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: "npm:^5.0.1"
  checksum: 10/a7095cb39e5bc32fada2aa7c7249d3f6b01bd1ce461a61b0adabacccabd9198500c6fb1f68a7c851a657e273fce2233ba869638897f3d7ed2e87a2d89b4436ea
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: "npm:^6.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10/07955e357348f34660bde7920783204ff5a26ac2cafcaa28bace494027158a97b9f56faaf2d89a6106211a8174db650dd9f503f9c0d526b1202d5554a00b9095
  languageName: node
  linkType: hard

"flat-cache@npm:^4.0.0":
  version: 4.0.1
  resolution: "flat-cache@npm:4.0.1"
  dependencies:
    flatted: "npm:^3.2.9"
    keyv: "npm:^4.5.4"
  checksum: 10/58ce851d9045fffc7871ce2bd718bc485ad7e777bf748c054904b87c351ff1080c2c11da00788d78738bfb51b71e4d5ea12d13b98eb36e3358851ffe495b62dc
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9":
  version: 3.3.2
  resolution: "flatted@npm:3.3.2"
  checksum: 10/********************************************************************************************************************************
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.0
  resolution: "foreground-child@npm:3.3.0"
  dependencies:
    cross-spawn: "npm:^7.0.0"
    signal-exit: "npm:^4.0.1"
  checksum: 10/e3a60480f3a09b12273ce2c5fcb9514d98dd0e528f58656a1b04680225f918d60a2f81f6a368f2f3b937fcee9cfc0cbf16f1ad9a0bc6a3a6e103a84c9a90087e
  languageName: node
  linkType: hard

"form-data@npm:^4.0.1":
  version: 4.0.1
  resolution: "form-data@npm:4.0.1"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.8"
    mime-types: "npm:^2.1.12"
  checksum: 10/6adb1cff557328bc6eb8a68da205f9ae44ab0e88d4d9237aaf91eed591ffc64f77411efb9016af7d87f23d0a038c45a788aa1c6634e51175c4efa36c2bc53774
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/af143246cf6884fe26fa281621d45cfe111d34b30535a475bfa38dafe343dadb466c047a924ffc7d6b7b18265df4110224ce3803806dbb07173bf2087b648d7f
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2, fsevents@npm:~2.3.3":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10/4c1ade961ded57cdbfbb5cac5106ec17bc8bccd62e16343c569a0ceeca83b9dfef87550b4dc5cbb89642da412b20c5071f304c8c464b80415446e8e155a038c0
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A~2.3.2#optional!builtin<compat/fsevents>, fsevents@patch:fsevents@npm%3A~2.3.3#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: "npm:^4.0.1"
  checksum: 10/32cd106ce8c0d83731966d31517adb766d02c3812de49c30cfe0675c7c0ae6630c11214c54a5ae67aca882cf738d27fd7768f21aa19118b9245950554be07247
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: "npm:^4.0.3"
  checksum: 10/c13ee97978bef4f55106b71e66428eb1512e71a7466ba49025fc2aec59a5bfb0954d5abd58fc5ee6c9b076eef4e1f6d3375c2e964b88466ca390da4419a786a8
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10, glob@npm:^10.3.3, glob@npm:^10.3.7":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^3.1.2"
    minimatch: "npm:^9.0.4"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^1.11.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10/698dfe11828b7efd0514cd11e573eaed26b2dff611f0400907281ce3eab0c1e56143ef9b35adc7c77ecc71fba74717b510c7c223d34ca8a98ec81777b293d4ac
  languageName: node
  linkType: hard

"globals@npm:^13.24.0":
  version: 13.24.0
  resolution: "globals@npm:13.24.0"
  dependencies:
    type-fest: "npm:^0.20.2"
  checksum: 10/62c5b1997d06674fc7191d3e01e324d3eda4d65ac9cc4e78329fa3b5c4fd42a0e1c8722822497a6964eee075255ce21ccf1eec2d83f92ef3f06653af4d0ee28e
  languageName: node
  linkType: hard

"globals@npm:^14.0.0":
  version: 14.0.0
  resolution: "globals@npm:14.0.0"
  checksum: 10/03939c8af95c6df5014b137cac83aa909090c3a3985caef06ee9a5a669790877af8698ab38007e4c0186873adc14c0b13764acc754b16a754c216cc56aa5f021
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10/bf152d0ed1dc159239db1ba1f74fdbc40cb02f626770dcd5815c427ce0688c2635a06ed69af364396da4636d0408fcf7d4afdf7881724c3307e46aff30ca49e2
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: 10/6dd60dba97007b21e3a829fab3f771803cc1292977fe610e240ea72afd67e5690ac9eeaafc4a99710e78962e5936ab5a460787c2a1180f1cb0ccfac37d29f897
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 10/261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"he@npm:^1.2.0":
  version: 1.2.0
  resolution: "he@npm:1.2.0"
  bin:
    he: bin/he
  checksum: 10/d09b2243da4e23f53336e8de3093e5c43d2c39f8d0d18817abfa32ce3e9355391b2edb4bb5edc376aea5d4b0b59d6a0482aab4c52bc02ef95751e4b818e847f1
  languageName: node
  linkType: hard

"html-encoding-sniffer@npm:^4.0.0":
  version: 4.0.0
  resolution: "html-encoding-sniffer@npm:4.0.0"
  dependencies:
    whatwg-encoding: "npm:^3.1.1"
  checksum: 10/e86efd493293a5671b8239bd099d42128433bb3c7b0fdc7819282ef8e118a21f5dead0ad6f358e024a4e5c84f17ebb7a9b36075220fac0a6222b207248bede6f
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 10/362d5ed66b12ceb9c0a328fb31200b590ab1b02f4a254a697dc796850cc4385603e75f53ec59f768b2dad3bfa1464bd229f7de278d2899a0e3beffc634b6683f
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0, http-proxy-agent@npm:^7.0.2":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 10/d062acfa0cb82beeb558f1043c6ba770ea892b5fb7b28654dbc70ea2aeea55226dd34c02a294f6c1ca179a5aa483c4ea641846821b182edbd9cc5d89b54c6848
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1, https-proxy-agent@npm:^7.0.6":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:4"
  checksum: 10/784b628cbd55b25542a9d85033bdfd03d4eda630fb8b3c9477959367f3be95dc476ed2ecbb9836c359c7c698027fc7b45723a302324433590f45d6c1706e8c13
  languageName: node
  linkType: hard

"iconv-lite@npm:0.6.3, iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10/24e3292dd3dadaa81d065c6f8c41b274a47098150d444b96e5f53b4638a9a71482921ea6a91a1f59bb71d9796de25e04afd05919fa64c360347ba65d3766f10f
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0, ignore@npm:^5.3.1":
  version: 5.3.2
  resolution: "ignore@npm:5.3.2"
  checksum: 10/cceb6a457000f8f6a50e1196429750d782afce5680dd878aa4221bd79972d68b3a55b4b1458fc682be978f4d3c6a249046aa0880637367216444ab7b014cfc98
  languageName: node
  linkType: hard

"immutable@npm:^5.0.2":
  version: 5.0.3
  resolution: "immutable@npm:5.0.3"
  checksum: 10/9aca1c783951bb204d7036fbcefac6dd42e7c8ad77ff54b38c5fc0924e6e16ce2d123c95db47c1170ba63dd3f6fc7aa74a29be7adef984031936c4cd1e9e8554
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1":
  version: 3.3.0
  resolution: "import-fresh@npm:3.3.0"
  dependencies:
    parent-module: "npm:^1.0.0"
    resolve-from: "npm:^4.0.0"
  checksum: 10/2cacfad06e652b1edc50be650f7ec3be08c5e5a6f6d12d035c440a42a8cc028e60a5b99ca08a77ab4d6b1346da7d971915828f33cdab730d3d42f08242d09baa
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10/2d30b157a91fe1c1d7c6f653cbf263f039be6c5bfa959245a16d4ee191fc0f2af86c08545b6e6beeb041c56b574d2d5b9f95343d378ab49c0f37394d541e7fc8
  languageName: node
  linkType: hard

"inflected@npm:^2.0.4":
  version: 2.1.0
  resolution: "inflected@npm:2.1.0"
  checksum: 10/1a4d88cf12803663e25214df1f66f027b572c961f014e958a5607c1e95476f42c27e641db0b8779ef7b05c6d4d1ea4744c61d04dfb7cddf6c8fc26acdca1199d
  languageName: node
  linkType: hard

"ini@npm:^1.3.4":
  version: 1.3.8
  resolution: "ini@npm:1.3.8"
  checksum: 10/314ae176e8d4deb3def56106da8002b462221c174ddb7ce0c49ee72c8cd1f9044f7b10cc555a7d8850982c3b9ca96fc212122749f5234bc2b6fb05fb942ed566
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: "npm:1.1.0"
    sprintf-js: "npm:^1.1.3"
  checksum: 10/1ed81e06721af012306329b31f532b5e24e00cb537be18ddc905a84f19fe8f83a09a1699862bf3a1ec4b9dea93c55a3fa5faf8b5ea380431469df540f38b092c
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: 10/df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10/44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 10/3ed74f2b0cdf4f401f38edb0442ddfde3092d79d7d35c9919c86641efdbcbb32e45aa3c0f70ce5eecc946896cd5a0f26e4188b9f2b881876f7cb6c505b82da11
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 10/6a6c3383f68afa1e05b286af866017c78f1226d43ac8cb064e115ff9ed85eb33f5c4f7216c96a71e4dfea289ef52c5da3aef5bbfade8ffe47a0465d70c0c8e86
  languageName: node
  linkType: hard

"is-potential-custom-element-name@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-potential-custom-element-name@npm:1.0.1"
  checksum: 10/ced7bbbb6433a5b684af581872afe0e1767e2d1146b2207ca0068a648fb5cab9d898495d1ac0583524faaf24ca98176a7d9876363097c2d14fee6dd324f3a1ab
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10/7c9f715c03aff08f35e98b1fadae1b9267b38f0615d501824f9743f3aab99ef10e303ce7db3f186763a0b70a19de5791ebfc854ff884d5a8c4d92211f642ec92
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 10/7fe1931ee4e88eb5aa524cd3ceb8c882537bc3a81b02e438b240e47012eef49c86904d0f0e593ea7c3a9996d18d0f1f3be8d3eaa92333977b0c3a9d353d5563e
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10/96f8786eaab98e4bf5b2a5d6d9588ea46c4d06bbc4f2eb861fdd7b6b182b16f71d8a70e79820f335d52653b16d4843b29dd9cdcf38ae80406756db9199497cf3
  languageName: node
  linkType: hard

"js-beautify@npm:^1.14.9":
  version: 1.15.1
  resolution: "js-beautify@npm:1.15.1"
  dependencies:
    config-chain: "npm:^1.1.13"
    editorconfig: "npm:^1.0.4"
    glob: "npm:^10.3.3"
    js-cookie: "npm:^3.0.5"
    nopt: "npm:^7.2.0"
  bin:
    css-beautify: js/bin/css-beautify.js
    html-beautify: js/bin/html-beautify.js
    js-beautify: js/bin/js-beautify.js
  checksum: 10/9fc7111fc30035e8674e778ed2c271b2762084039482252d0a53fd94341e2009daceda99f4d9d0272672f2e136367ec8ce1008a04eb00140bd42c9668c3ed9af
  languageName: node
  linkType: hard

"js-cookie@npm:^3.0.5":
  version: 3.0.5
  resolution: "js-cookie@npm:3.0.5"
  checksum: 10/366494b1630b9fb8abaef3659748db5dfd52c58c6fc3459b9f0a03b492593bc1b01c6dfcc066b46f6413c28edb3a00cc68fb61ea8cdf6991bedf1f100f8a389d
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: "npm:^2.0.1"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10/c138a34a3fd0d08ebaf71273ad4465569a483b8a639e0b118ff65698d257c2791d3199e3f303631f2cb98213fa7b5f5d6a4621fd0fff819421b990d30d967140
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 10/bebe7ae829bbd586ce8cbe83501dd8cb8c282c8902a8aeeed0a073a89dc37e8103b1244f3c6acd60278bcbfe12d93a3f83c9ac396868a3b3bbc3c5e5e3b648ef
  languageName: node
  linkType: hard

"jsdom@npm:26.0.0":
  version: 26.0.0
  resolution: "jsdom@npm:26.0.0"
  dependencies:
    cssstyle: "npm:^4.2.1"
    data-urls: "npm:^5.0.0"
    decimal.js: "npm:^10.4.3"
    form-data: "npm:^4.0.1"
    html-encoding-sniffer: "npm:^4.0.0"
    http-proxy-agent: "npm:^7.0.2"
    https-proxy-agent: "npm:^7.0.6"
    is-potential-custom-element-name: "npm:^1.0.1"
    nwsapi: "npm:^2.2.16"
    parse5: "npm:^7.2.1"
    rrweb-cssom: "npm:^0.8.0"
    saxes: "npm:^6.0.0"
    symbol-tree: "npm:^3.2.4"
    tough-cookie: "npm:^5.0.0"
    w3c-xmlserializer: "npm:^5.0.0"
    webidl-conversions: "npm:^7.0.0"
    whatwg-encoding: "npm:^3.1.1"
    whatwg-mimetype: "npm:^4.0.0"
    whatwg-url: "npm:^14.1.0"
    ws: "npm:^8.18.0"
    xml-name-validator: "npm:^5.0.0"
  peerDependencies:
    canvas: ^3.0.0
  peerDependenciesMeta:
    canvas:
      optional: true
  checksum: 10/8c230ee4657240bbbca6b4ebb484be53fc6a777a22a3357c80c5537222813666e3e1f54740bc13e769c461d9598ba7dac402c245949c6cef7ef7014ce6f36f01
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 10/82876154521b7b68ba71c4f969b91572d1beabadd87bd3a6b236f85fbc7dc4695089191ed60bb59f9340993c51b33d479f45b6ba9f3548beb519705281c32c3c
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 10/7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: 10/12786c2e2f22c27439e6db0532ba321f1d0617c27ad8cb1c352a0e9249a50182fd1ba8b52a18899291604b0c32eafa8afd09e51203f19109a0537f68db2b652d
  languageName: node
  linkType: hard

"keycloak-js@npm:26.0.7":
  version: 26.0.7
  resolution: "keycloak-js@npm:26.0.7"
  checksum: 10/794528358cdb03a28bff99e8cd7019eac5a2c10eb0ab900d1e31baf64f655a538e878350b44cdeadddec210341348d856aa310aee2fd89eed48b3b8a22e87692
  languageName: node
  linkType: hard

"keyv@npm:^4.5.4":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: "npm:3.0.1"
  checksum: 10/167eb6ef64cc84b6fa0780ee50c9de456b422a1e18802209234f7c2cf7eae648c7741f32e50d7e24ccb22b24c13154070b01563d642755b156c357431a191e75
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:~0.4.0"
  checksum: 10/2e4720ff79f21ae08d42374b0a5c2f664c5be8b6c8f565bb4e1315c96ed3a8acaa9de788ffed82d7f2378cf36958573de07ef92336cb5255ed74d08b8318c9ee
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: "npm:^5.0.0"
  checksum: 10/72eb661788a0368c099a184c59d2fee760b3831c9c1c33955e8a19ae4a21b4116e53fa736dc086cdeb9fce9f7cc508f2f92d2d3aae516f133e16a2bb59a39f5a
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: 10/d0ea2dd0097e6201be083865d50c3fb54fbfbdb247d9cc5950e086c991f448b7ab0cdab0d57eacccb43473d3f2acd21e134db39f22dac2d6c9ba6bf26978e3d6
  languageName: node
  linkType: hard

"lodash@npm:^4.0.0, lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: 10/c08619c038846ea6ac754abd6dd29d2568aa705feb69339e836dfa8d8b09abbb2f859371e86863eda41848221f9af43714491467b5b0299122431e202bb0c532
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0, lru-cache@npm:^10.4.3":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 10/e6e90267360476720fa8e83cc168aa2bf0311f3f2eea20a6ba78b90a885ae72071d9db132f40fda4129c803e7dcec3a6b6a6fbb44ca90b081630b810b5d6a41a
  languageName: node
  linkType: hard

"magic-string@npm:^0.30.11":
  version: 0.30.17
  resolution: "magic-string@npm:0.30.17"
  dependencies:
    "@jridgewell/sourcemap-codec": "npm:^1.5.0"
  checksum: 10/2f71af2b0afd78c2e9012a29b066d2c8ba45a9cd0c8070f7fd72de982fb1c403b4e3afdb1dae00691d56885ede66b772ef6bedf765e02e3a7066208fe2fec4aa
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": "npm:^3.0.0"
    cacache: "npm:^19.0.1"
    http-cache-semantics: "npm:^4.1.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^4.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^1.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^12.0.0"
  checksum: 10/fce0385840b6d86b735053dfe941edc2dd6468fda80fe74da1eeff10cbd82a75760f406194f2bc2fa85b99545b2bc1f84c08ddf994b21830775ba2d1a87e8bdf
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 10/7268db63ed5169466540b6fb947aec313200bcf6d40c5ab722c22e242f651994619bcd85601602972d3c85bd2cc45a358a4c61937e9f11a061919a1da569b0c2
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.4, micromatch@npm:^4.0.5":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: "npm:^3.0.3"
    picomatch: "npm:^2.3.1"
  checksum: 10/6bf2a01672e7965eb9941d1f02044fad2bd12486b5553dc1116ff24c09a8723157601dc992e74c911d896175918448762df3b3fd0a6b61037dd1a9766ddfbf58
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 10/54bb60bf39e6f8689f6622784e668a3d7f8bed6b0d886f5c3c446cb3284be28b30bf707ed05d0fe44a036f8469976b2629bbea182684977b084de9da274694d7
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 10/89aa9651b67644035de2784a6e665fc685d79aba61857e02b9c8758da874a754aed4a9aced9265f5ed1171fd934331e5516b84a7f0218031b6fa0270eca1e51a
  languageName: node
  linkType: hard

"minimatch@npm:9.0.1":
  version: 9.0.1
  resolution: "minimatch@npm:9.0.1"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10/b4e98f4dc740dcf33999a99af23ae6e5e1c47632f296dc95cb649a282150f92378d41434bf64af4ea2e5975255a757d031c3bf014bad9214544ac57d97f3ba63
  languageName: node
  linkType: hard

"minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: 10/e0b25b04cd4ec6732830344e5739b13f8690f8a012d73445a4a19fbc623f5dd481ef7a5827fde25954cd6026fede7574cc54dc4643c99d6c6b653d6203f94634
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.3, minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10/dd6a8927b063aca6d910b119e1f2df6d2ce7d36eab91de83167dd136bb85e1ebff97b0d3de1cb08bd1f7e018ca170b4962479fefab5b2a69e2ae12cb2edc8348
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/b251bceea62090f67a6cced7a446a36f4cd61ee2d5cea9aee7fff79ba8030e416327a1c5aa2908dc22629d06214b46d88fdab8c51ac76bacbf5703851b5ad342
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.0
  resolution: "minipass-fetch@npm:4.0.0"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^3.0.1"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10/4b0772dbee77727b469dc5bfc371541d9aba1e243fbb46ddc1b9ff7efa4de4a4cf5ff3a359d6a3b3a460ca26df9ae67a9c93be26ab6417c225e49d63b52b2801
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/40982d8d836a52b0f37049a0a7e5d0f089637298e6d9b45df9c115d4f0520682a78258905e5c8b180fb41b593b0a82cc1361d2c74b45f7ada66334f84d1ecfdd
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10/a5c6ef069f70d9a524d3428af39f2b117ff8cd84172e19b754e7264a33df460873e6eb3d6e55758531580970de50ae950c496256bb4ad3691a2974cddff189f0
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 10/c25f0ee8196d8e6036661104bacd743785b2599a21de5c516b32b3fa2b83113ac89a2358465bc04956baab37ffb956ae43be679b2262bf7be15fce467ccd7950
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.1
  resolution: "minizlib@npm:3.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
    rimraf: "npm:^5.0.5"
  checksum: 10/622cb85f51e5c206a080a62d20db0d7b4066f308cb6ce82a9644da112367c3416ae7062017e631eb7ac8588191cfa4a9a279b8651c399265202b298e98c4acef
  languageName: node
  linkType: hard

"miragejs@npm:0.1.48":
  version: 0.1.48
  resolution: "miragejs@npm:0.1.48"
  dependencies:
    "@miragejs/pretender-node-polyfill": "npm:^0.1.0"
    inflected: "npm:^2.0.4"
    lodash: "npm:^4.0.0"
    pretender: "npm:^3.4.7"
  checksum: 10/650aa4d9f46835f7bd66eeb54f927978742aa785abf8585a8e3c0c82f68dcfa548bbe603ba9203831a6524caaa1cf607c26f838e7a6d66ccb9491baa8d604f5d
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 10/16fd79c28645759505914561e249b9a1f5fe3362279ad95487a4501e4467abeb714fd35b95307326b8fd03f3c7719065ef11a6f97b7285d7888306d1bd2232ba
  languageName: node
  linkType: hard

"ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10/aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"muggle-string@npm:^0.4.1":
  version: 0.4.1
  resolution: "muggle-string@npm:0.4.1"
  checksum: 10/8fa2ea08f497c04069718bd3fd1909b382114dacbad832d10967ca72690de43f5f8492d8ccfbf827d6be63868ed5fc10395e7b7c082aa95997eea498586c6620
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.7":
  version: 3.3.8
  resolution: "nanoid@npm:3.3.8"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 10/2d1766606cf0d6f47b6f0fdab91761bb81609b2e3d367027aff45e6ee7006f660fb7e7781f4a34799fe6734f1268eeed2e37a5fdee809ade0c2d4eb11b0f9c40
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.8":
  version: 3.3.11
  resolution: "nanoid@npm:3.3.11"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 10/73b5afe5975a307aaa3c95dfe3334c52cdf9ae71518176895229b8d65ab0d1c0417dd081426134eb7571c055720428ea5d57c645138161e7d10df80815527c48
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 10/23ad088b08f898fc9b53011d7bb78ec48e79de7627e01ab5518e806033861bef68d5b0cd0e2205c2f36690ac9571ff6bcb05eb777ced2eeda8d4ac5b44592c3d
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 10/b5734e87295324fabf868e36fb97c84b7d7f3156ec5f4ee5bf6e488079c11054f818290fc33804cef7b1ee21f55eeb14caea83e7dafae6492a409b3e573153e5
  languageName: node
  linkType: hard

"neo-async@npm:^2.6.2":
  version: 2.6.2
  resolution: "neo-async@npm:2.6.2"
  checksum: 10/1a7948fea86f2b33ec766bc899c88796a51ba76a4afc9026764aedc6e7cde692a09067031e4a1bf6db4f978ccd99e7f5b6c03fe47ad9865c3d4f99050d67e002
  languageName: node
  linkType: hard

"node-addon-api@npm:^7.0.0":
  version: 7.1.1
  resolution: "node-addon-api@npm:7.1.1"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10/ee1e1ed6284a2f8cd1d59ac6175ecbabf8978dcf570345e9a8095a9d0a2b9ced591074ae77f9009287b00c402352b38aa9322a34f2199cdc9f567b842a636b94
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.0.0
  resolution: "node-gyp@npm:11.0.0"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    glob: "npm:^10.3.10"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^14.0.3"
    nopt: "npm:^8.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^7.4.3"
    which: "npm:^5.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10/5d07430e887a906f85c7c6ed87e8facb7ecd4ce42d948a2438c471df2e24ae6af70f4def114ec1a03127988d164648dda8d75fe666f3c4b431e53856379fdf13
  languageName: node
  linkType: hard

"nopt@npm:^7.2.0":
  version: 7.2.1
  resolution: "nopt@npm:7.2.1"
  dependencies:
    abbrev: "npm:^2.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10/95a1f6dec8a81cd18cdc2fed93e6f0b4e02cf6bdb4501c848752c6e34f9883d9942f036a5e3b21a699047d8a448562d891e67492df68ec9c373e6198133337ae
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.0.0
  resolution: "nopt@npm:8.0.0"
  dependencies:
    abbrev: "npm:^2.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10/2d137f64b6f9331ec97047dd1cbbe4dcd9a61ceef4fd0f2252c0bbac1d69ba15671e6fd83a441328824b3ca78afe6ebe1694f12ebcd162b73a221582a06179ff
  languageName: node
  linkType: hard

"nth-check@npm:^2.1.1":
  version: 2.1.1
  resolution: "nth-check@npm:2.1.1"
  dependencies:
    boolbase: "npm:^1.0.0"
  checksum: 10/5afc3dafcd1573b08877ca8e6148c52abd565f1d06b1eb08caf982e3fa289a82f2cae697ffb55b5021e146d60443f1590a5d6b944844e944714a5b549675bcd3
  languageName: node
  linkType: hard

"nwsapi@npm:^2.2.16":
  version: 2.2.16
  resolution: "nwsapi@npm:2.2.16"
  checksum: 10/1e5e086cdd4ca4a45f414d37f49bf0ca81d84ed31c6871ac68f531917d2910845db61f77c6d844430dc90fda202d43fce9603024e74038675de95229eb834dba
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.4
  resolution: "optionator@npm:0.9.4"
  dependencies:
    deep-is: "npm:^0.1.3"
    fast-levenshtein: "npm:^2.0.6"
    levn: "npm:^0.4.1"
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:^0.4.0"
    word-wrap: "npm:^1.2.5"
  checksum: 10/a8398559c60aef88d7f353a4f98dcdff6090a4e70f874c827302bf1213d9106a1c4d5fcb68dacb1feb3c30a04c4102f41047aa55d4c576b863d6fc876e001af6
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: "npm:^0.1.0"
  checksum: 10/7c3690c4dbf62ef625671e20b7bdf1cbc9534e83352a2780f165b0d3ceba21907e77ad63401708145ca4e25bfc51636588d89a8c0aeb715e6c37d1c066430360
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: "npm:^3.0.2"
  checksum: 10/1623088f36cf1cbca58e9b61c4e62bf0c60a07af5ae1ca99a720837356b5b6c5ba3eb1b2127e47a06865fee59dd0453cad7cc844cda9d5a62ac1a5a51b7c86d3
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 10/2ef48ccfc6dd387253d71bf502604f7893ed62090b2c9d73387f10006c342606b05233da0e4f29388227b61eb5aeface6197e166520c465c234552eeab2fe633
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 10/58ee9538f2f762988433da00e26acc788036914d57c71c246bf0be1b60cdbd77dd60b6a3e1a30465f0b248aeb80079e0b34cb6050b1dfa18c06953bb1cbc7602
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: "npm:^3.0.0"
  checksum: 10/6ba8b255145cae9470cf5551eb74be2d22281587af787a2626683a6c20fbb464978784661478dd2a3f1dad74d1e802d403e1b03c1a31fab310259eec8ac560ff
  languageName: node
  linkType: hard

"parse5@npm:^7.2.1":
  version: 7.2.1
  resolution: "parse5@npm:7.2.1"
  dependencies:
    entities: "npm:^4.5.0"
  checksum: 10/fd1a8ad1540d871e1ad6ca9bf5b67e30280886f1ce4a28052c0cb885723aa984d8cb1ec3da998349a6146960c8a84aa87b1a42600eb3b94495c7303476f2f88e
  languageName: node
  linkType: hard

"path-browserify@npm:^1.0.1":
  version: 1.0.1
  resolution: "path-browserify@npm:1.0.1"
  checksum: 10/7e7368a5207e7c6b9051ef045711d0dc3c2b6203e96057e408e6e74d09f383061010d2be95cb8593fe6258a767c3e9fc6b2bfc7ce8d48ae8c3d9f6994cca9ad8
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 10/505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10/55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10/5e8845c159261adda6f09814d7725683257fcc85a18f329880ab4d7cc1d12830967eae5d5894e453f341710d5484b8fdbbd4d75181b4d6e1eb2f4dc7aeadc434
  languageName: node
  linkType: hard

"picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: 10/e1cf46bf84886c79055fdfa9dcb3e4711ad259949e3565154b004b260cd356c5d54b31a1437ce9782624bf766272fe6b0154f5f0c744fb7af5d454d2b60db045
  languageName: node
  linkType: hard

"picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 10/60c2595003b05e4535394d1da94850f5372c9427ca4413b71210f437f7b2ca091dbd611c45e8b37d10036fa8eade25c1b8951654f9d3973bfa66a2ff4d3b08bc
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^6.0.15":
  version: 6.1.2
  resolution: "postcss-selector-parser@npm:6.1.2"
  dependencies:
    cssesc: "npm:^3.0.0"
    util-deprecate: "npm:^1.0.2"
  checksum: 10/190034c94d809c115cd2f32ee6aade84e933450a43ec3899c3e78e7d7b33efd3a2a975bb45d7700b6c5b196c06a7d9acf3f1ba6f1d87032d9675a29d8bca1dd3
  languageName: node
  linkType: hard

"postcss@npm:^8.4.48":
  version: 8.4.49
  resolution: "postcss@npm:8.4.49"
  dependencies:
    nanoid: "npm:^3.3.7"
    picocolors: "npm:^1.1.1"
    source-map-js: "npm:^1.2.1"
  checksum: 10/28fe1005b1339870e0a5006375ba5ac1213fd69800f79e7db09c398e074421ba6e162898e94f64942fed554037fd292db3811d87835d25ab5ef7f3c9daacb6ca
  languageName: node
  linkType: hard

"postcss@npm:^8.5.2":
  version: 8.5.3
  resolution: "postcss@npm:8.5.3"
  dependencies:
    nanoid: "npm:^3.3.8"
    picocolors: "npm:^1.1.1"
    source-map-js: "npm:^1.2.1"
  checksum: 10/6d7e21a772e8b05bf102636918654dac097bac013f0dc8346b72ac3604fc16829646f94ea862acccd8f82e910b00e2c11c1f0ea276543565d278c7ca35516a7c
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: 10/0b9d2c76801ca652a7f64892dd37b7e3fab149a37d2424920099bf894acccc62abb4424af2155ab36dea8744843060a2d8ddc983518d0b1e22265a22324b72ed
  languageName: node
  linkType: hard

"pretender@npm:^3.4.7":
  version: 3.4.7
  resolution: "pretender@npm:3.4.7"
  dependencies:
    fake-xml-http-request: "npm:^2.1.2"
    route-recognizer: "npm:^0.3.3"
  checksum: 10/afadd10e22ede523a59cbbf503be033bb75c5d8402ce978dcaa15f4b82c9967ecb64e43a4b003256eb2780fe0e39e0b8b29574a870b50be6e2c026b8902742ed
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: 10/35610bdb0177d3ab5d35f8827a429fb1dc2518d9e639f2151ac9007f01a061c30e0c635a970c9b00c39102216160f6ec54b62377c92fac3b7bfc2ad4b98d195c
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10/96e1a82453c6c96eef53a37a1d6134c9f2482f94068f98a59145d0986ca4e497bf110a410adf73857e588165eab3899f0ebcf7b3890c1b3ce802abc0d65967d4
  languageName: node
  linkType: hard

"proto-list@npm:~1.2.1":
  version: 1.2.4
  resolution: "proto-list@npm:1.2.4"
  checksum: 10/9cc3b46d613fa0d637033b225db1bc98e914c3c05864f7adc9bee728192e353125ef2e49f71129a413f6333951756000b0e54f299d921f02d3e9e370cc994100
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0, punycode@npm:^2.3.1":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: 10/febdc4362bead22f9e2608ff0171713230b57aff9dddc1c273aa2a651fbd366f94b7d6a71d78342a7c0819906750351ca7f2edd26ea41b626d87d6a13d1bd059
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: 10/72900df0616e473e824202113c3df6abae59150dfb73ed13273503127235320e9c8ca4aaaaccfd58cf417c6ca92a6e68ee9a5c3182886ae949a768639b388a7b
  languageName: node
  linkType: hard

"readdirp@npm:^4.0.1":
  version: 4.0.2
  resolution: "readdirp@npm:4.0.2"
  checksum: 10/4ef93103307c7d5e42e78ecf201db58c984c4d66882a27c956250478b49c2444b1ff6aea8ce0f5e4157b2c07ce2fe870ad16c92ebd7c6ff30391ded6e42b9873
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: 10/91eb76ce83621eea7bbdd9b55121a5c1c4a39e54a9ce04a9ad4517f102f8b5131c2cf07622c738a6683991bf54f2ce178f5a42803ecbd527ddc5105f362cc9e3
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10/1f914879f97e7ee931ad05fe3afa629bd55270fc6cf1c1e589b6a99fab96d15daad0fa1a52a00c729ec0078045fe3e399bd4fd0c93bcc906957bdc17f89cb8e6
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.0.4
  resolution: "reusify@npm:1.0.4"
  checksum: 10/14222c9e1d3f9ae01480c50d96057228a8524706db79cdeb5a2ce5bb7070dd9f409a6f84a02cbef8cdc80d39aef86f2dd03d155188a1300c599b05437dcd2ffb
  languageName: node
  linkType: hard

"rimraf@npm:^5.0.5":
  version: 5.0.10
  resolution: "rimraf@npm:5.0.10"
  dependencies:
    glob: "npm:^10.3.7"
  bin:
    rimraf: dist/esm/bin.mjs
  checksum: 10/f3b8ce81eecbde4628b07bdf9e2fa8b684e0caea4999acb1e3b0402c695cd41f28cd075609a808e61ce2672f528ca079f675ab1d8e8d5f86d56643a03e0b8d2e
  languageName: node
  linkType: hard

"rollup@npm:^4.30.1":
  version: 4.40.2
  resolution: "rollup@npm:4.40.2"
  dependencies:
    "@rollup/rollup-android-arm-eabi": "npm:4.40.2"
    "@rollup/rollup-android-arm64": "npm:4.40.2"
    "@rollup/rollup-darwin-arm64": "npm:4.40.2"
    "@rollup/rollup-darwin-x64": "npm:4.40.2"
    "@rollup/rollup-freebsd-arm64": "npm:4.40.2"
    "@rollup/rollup-freebsd-x64": "npm:4.40.2"
    "@rollup/rollup-linux-arm-gnueabihf": "npm:4.40.2"
    "@rollup/rollup-linux-arm-musleabihf": "npm:4.40.2"
    "@rollup/rollup-linux-arm64-gnu": "npm:4.40.2"
    "@rollup/rollup-linux-arm64-musl": "npm:4.40.2"
    "@rollup/rollup-linux-loongarch64-gnu": "npm:4.40.2"
    "@rollup/rollup-linux-powerpc64le-gnu": "npm:4.40.2"
    "@rollup/rollup-linux-riscv64-gnu": "npm:4.40.2"
    "@rollup/rollup-linux-riscv64-musl": "npm:4.40.2"
    "@rollup/rollup-linux-s390x-gnu": "npm:4.40.2"
    "@rollup/rollup-linux-x64-gnu": "npm:4.40.2"
    "@rollup/rollup-linux-x64-musl": "npm:4.40.2"
    "@rollup/rollup-win32-arm64-msvc": "npm:4.40.2"
    "@rollup/rollup-win32-ia32-msvc": "npm:4.40.2"
    "@rollup/rollup-win32-x64-msvc": "npm:4.40.2"
    "@types/estree": "npm:1.0.7"
    fsevents: "npm:~2.3.2"
  dependenciesMeta:
    "@rollup/rollup-android-arm-eabi":
      optional: true
    "@rollup/rollup-android-arm64":
      optional: true
    "@rollup/rollup-darwin-arm64":
      optional: true
    "@rollup/rollup-darwin-x64":
      optional: true
    "@rollup/rollup-freebsd-arm64":
      optional: true
    "@rollup/rollup-freebsd-x64":
      optional: true
    "@rollup/rollup-linux-arm-gnueabihf":
      optional: true
    "@rollup/rollup-linux-arm-musleabihf":
      optional: true
    "@rollup/rollup-linux-arm64-gnu":
      optional: true
    "@rollup/rollup-linux-arm64-musl":
      optional: true
    "@rollup/rollup-linux-loongarch64-gnu":
      optional: true
    "@rollup/rollup-linux-powerpc64le-gnu":
      optional: true
    "@rollup/rollup-linux-riscv64-gnu":
      optional: true
    "@rollup/rollup-linux-riscv64-musl":
      optional: true
    "@rollup/rollup-linux-s390x-gnu":
      optional: true
    "@rollup/rollup-linux-x64-gnu":
      optional: true
    "@rollup/rollup-linux-x64-musl":
      optional: true
    "@rollup/rollup-win32-arm64-msvc":
      optional: true
    "@rollup/rollup-win32-ia32-msvc":
      optional: true
    "@rollup/rollup-win32-x64-msvc":
      optional: true
    fsevents:
      optional: true
  bin:
    rollup: dist/bin/rollup
  checksum: 10/ab767c56e37410257864e051fccbdaf448ac7774129bf39295de716af816c49e0247e72749959969efbd892fc64e096880fa269764adf765579100e81abf5e7c
  languageName: node
  linkType: hard

"route-recognizer@npm:^0.3.3":
  version: 0.3.4
  resolution: "route-recognizer@npm:0.3.4"
  checksum: 10/cf1b83378bfe17d945afc087fc10082545cc19d1a5d13f9c1321117780caec40934a7cd5ace69558fadeb17475ac3963d1b59a2f75d144d51082e1964318959a
  languageName: node
  linkType: hard

"rrweb-cssom@npm:^0.8.0":
  version: 0.8.0
  resolution: "rrweb-cssom@npm:0.8.0"
  checksum: 10/07521ee36fb6569c17906afad1ac7ff8f099d49ade9249e190693ac36cdf27f88d9acf0cc66978935d5d0a23fca105643d7e9125b9a9d91ed9db9e02d31d7d80
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: "npm:^1.2.2"
  checksum: 10/cb4f97ad25a75ebc11a8ef4e33bb962f8af8516bb2001082ceabd8902e15b98f4b84b4f8a9b222e5d57fc3bd1379c483886ed4619367a7680dad65316993021d
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10/7eaf7a0cf37cc27b42fb3ef6a9b1df6e93a1c6d98c6c6702b02fe262d5fcbd89db63320793b99b21cb5348097d0a53de81bd5f4e8b86e20cc9412e3f1cfb4e83
  languageName: node
  linkType: hard

"sass-loader@npm:16.0.4":
  version: 16.0.4
  resolution: "sass-loader@npm:16.0.4"
  dependencies:
    neo-async: "npm:^2.6.2"
  peerDependencies:
    "@rspack/core": 0.x || 1.x
    node-sass: ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0 || ^9.0.0
    sass: ^1.3.0
    sass-embedded: "*"
    webpack: ^5.0.0
  peerDependenciesMeta:
    "@rspack/core":
      optional: true
    node-sass:
      optional: true
    sass:
      optional: true
    sass-embedded:
      optional: true
    webpack:
      optional: true
  checksum: 10/16e3552e04301864d59d99f1c90952e0c97cfa793a38a551b6dc19e49a9c0779ec8b3dd8ab39bc79cc4401972f13e8d5baaf577c96ea39911db5866a0b4bdcdd
  languageName: node
  linkType: hard

"sass@npm:1.83.0":
  version: 1.83.0
  resolution: "sass@npm:1.83.0"
  dependencies:
    "@parcel/watcher": "npm:^2.4.1"
    chokidar: "npm:^4.0.0"
    immutable: "npm:^5.0.2"
    source-map-js: "npm:>=0.6.2 <2.0.0"
  dependenciesMeta:
    "@parcel/watcher":
      optional: true
  bin:
    sass: sass.js
  checksum: 10/cae7c489ffeb1324ac7e766dda60206a6d7a318d0689b490290a32a6414ef1fd0f376f92d45fb1610e507baa6f6594f1a61d4d706df2f105122ff9a83d2a28e1
  languageName: node
  linkType: hard

"saxes@npm:^6.0.0":
  version: 6.0.0
  resolution: "saxes@npm:6.0.0"
  dependencies:
    xmlchars: "npm:^2.2.0"
  checksum: 10/97b50daf6ca3a153e89842efa18a862e446248296622b7473c169c84c823ee8a16e4a43bac2f73f11fc8cb9168c73fbb0d73340f26552bac17970e9052367aa9
  languageName: node
  linkType: hard

"semver@npm:^7.3.5, semver@npm:^7.3.6, semver@npm:^7.5.3, semver@npm:^7.6.0, semver@npm:^7.6.3":
  version: 7.6.3
  resolution: "semver@npm:7.6.3"
  bin:
    semver: bin/semver.js
  checksum: 10/36b1fbe1a2b6f873559cd57b238f1094a053dbfd997ceeb8757d79d1d2089c56d1321b9f1069ce263dc64cfa922fa1d2ad566b39426fe1ac6c723c1487589e10
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10/6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10/1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10/c9fa63bbbd7431066174a48ba2dd9986dfd930c3a8b59de9c29d7b6854ec1c12a80d15310869ea5166d413b99f041bfa3dd80a7947bcd44ea8e6eb3ffeabfa1f
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10/927484aa0b1640fd9473cee3e0a0bcad6fce93fd7bbc18bac9ad0c33686f5d2e2c422fba24b5899c184524af01e11dd2bd051c2bf2b07e47aff8ca72cbfc60d2
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.8.3"
  checksum: 10/ee99e1dacab0985b52cbe5a75640be6e604135e9489ebdc3048635d186012fbaecc20fbbe04b177dee434c319ba20f09b3e7dfefb7d932466c0d707744eac05c
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.3
  resolution: "socks@npm:2.8.3"
  dependencies:
    ip-address: "npm:^9.0.5"
    smart-buffer: "npm:^4.2.0"
  checksum: 10/ffcb622c22481dfcd7589aae71fbfd71ca34334064d181df64bf8b7feaeee19706aba4cffd1de35cc7bbaeeaa0af96be2d7f40fcbc7bc0ab69533a7ae9ffc4fb
  languageName: node
  linkType: hard

"source-map-js@npm:>=0.6.2 <2.0.0, source-map-js@npm:^1.0.2, source-map-js@npm:^1.2.0, source-map-js@npm:^1.2.1":
  version: 1.2.1
  resolution: "source-map-js@npm:1.2.1"
  checksum: 10/ff9d8c8bf096d534a5b7707e0382ef827b4dd360a577d3f34d2b9f48e12c9d230b5747974ee7c607f0df65113732711bb701fe9ece3c7edbd43cb2294d707df3
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: 10/e7587128c423f7e43cc625fe2f87e6affdf5ca51c1cc468e910d8aaca46bb44a7fbcfa552f787b1d3987f7043aeb4527d1b99559e6621e01b42b3f45e5a24cbb
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/7024c1a6e39b3f18aa8f1c8290e884fe91b0f9ca5a6c6d410544daad54de0ba664db879afe16412e187c6c292fd60b937f047ee44292e5c2af2dcc6d8e1a9b48
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10/e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10/7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10/ae3b5436d34fadeb6096367626ce987057713c566e1e7768818797e00ac5d62023d0f198c4e681eae9e20701721980b26a64a8f5b91238869592a9c6800719a2
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10/475f53e9c44375d6e72807284024ac5d668ee1d06010740dec0b9744f2ddf47de8d7151f80e5f6190fc8f384e802fdf9504b76a7e9020c9faee7103623338be2
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 10/492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10/c8bb7afd564e3b26b50ca6ee47572c217526a1389fe018d00345856d4a9b08ffbd61fadaf283a87368d94c3dcdb8f5ffe2650a5a65863e21ad2730ca0f05210a
  languageName: node
  linkType: hard

"symbol-tree@npm:^3.2.4":
  version: 3.2.4
  resolution: "symbol-tree@npm:3.2.4"
  checksum: 10/c09a00aadf279d47d0c5c46ca3b6b2fbaeb45f0a184976d599637d412d3a70bbdc043ff33effe1206dea0e36e0ad226cb957112e7ce9a4bf2daedf7fa4f85c53
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": "npm:^4.0.0"
    chownr: "npm:^3.0.0"
    minipass: "npm:^7.1.2"
    minizlib: "npm:^3.0.1"
    mkdirp: "npm:^3.0.1"
    yallist: "npm:^5.0.0"
  checksum: 10/12a2a4fc6dee23e07cc47f1aeb3a14a1afd3f16397e1350036a8f4cdfee8dcac7ef5978337a4e7b2ac2c27a9a6d46388fc2088ea7c80cb6878c814b1425f8ecf
  languageName: node
  linkType: hard

"tldts-core@npm:^6.1.68":
  version: 6.1.68
  resolution: "tldts-core@npm:6.1.68"
  checksum: 10/6cd30acd54a6cd402afb75d4d034ca008ab06b8d254efaa976e38814b7d0095f2fdfda2e33c162085d2f45b2b7b8b5724384192c2268930e63bee886241e399f
  languageName: node
  linkType: hard

"tldts@npm:^6.1.32":
  version: 6.1.68
  resolution: "tldts@npm:6.1.68"
  dependencies:
    tldts-core: "npm:^6.1.68"
  bin:
    tldts: bin/cli.js
  checksum: 10/5e28d274ba7364c80f4d81d922427cfae6081b2f33b27a81eab05f5e62f650daef7f8037aae489407f6b1cf997cb3aa353d41cd6bbce75758af44b1ae8b3cfc5
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: "npm:^7.0.0"
  checksum: 10/10dda13571e1f5ad37546827e9b6d4252d2e0bc176c24a101252153ef435d83696e2557fe128c4678e4e78f5f01e83711c703eef9814eb12dab028580d45980a
  languageName: node
  linkType: hard

"tough-cookie@npm:^5.0.0":
  version: 5.0.0
  resolution: "tough-cookie@npm:5.0.0"
  dependencies:
    tldts: "npm:^6.1.32"
  checksum: 10/a98d3846ed386e399e8b470c1eb08a6a296944246eabc55c9fe79d629bd2cdaa62f5a6572f271fe0060987906bd20468d72a219a3b4cbe51086bea48d2d677b6
  languageName: node
  linkType: hard

"tr46@npm:^5.0.0":
  version: 5.0.0
  resolution: "tr46@npm:5.0.0"
  dependencies:
    punycode: "npm:^2.3.1"
  checksum: 10/29155adb167d048d3c95d181f7cb5ac71948b4e8f3070ec455986e1f34634acae50ae02a3c8d448121c3afe35b76951cd46ed4c128fd80264280ca9502237a3e
  languageName: node
  linkType: hard

"ts-api-utils@npm:^1.3.0":
  version: 1.4.3
  resolution: "ts-api-utils@npm:1.4.3"
  peerDependencies:
    typescript: ">=4.2.0"
  checksum: 10/713c51e7392323305bd4867422ba130fbf70873ef6edbf80ea6d7e9c8f41eeeb13e40e8e7fe7cd321d74e4864777329797077268c9f570464303a1723f1eed39
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: "npm:^1.2.1"
  checksum: 10/14687776479d048e3c1dbfe58a2409e00367810d6960c0f619b33793271ff2a27f81b52461f14a162f1f89a9b1d8da1b237fc7c99b0e1fdcec28ec63a86b1fec
  languageName: node
  linkType: hard

"type-fest@npm:^0.20.2":
  version: 0.20.2
  resolution: "type-fest@npm:0.20.2"
  checksum: 10/8907e16284b2d6cfa4f4817e93520121941baba36b39219ea36acfe64c86b9dbc10c9941af450bd60832c8f43464974d51c0957f9858bc66b952b66b6914cbb9
  languageName: node
  linkType: hard

"typescript-eslint@npm:^8.18.1":
  version: 8.19.0
  resolution: "typescript-eslint@npm:8.19.0"
  dependencies:
    "@typescript-eslint/eslint-plugin": "npm:8.19.0"
    "@typescript-eslint/parser": "npm:8.19.0"
    "@typescript-eslint/utils": "npm:8.19.0"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.8.0"
  checksum: 10/c8a57563910c4eff7d09a009c0c87419e12c195b57d1ff9fe775afb54c9b24b4133f71c59c2f2dee3ca63494721ba8d7bcf5951ac693199cae0d6b83e67a5c68
  languageName: node
  linkType: hard

"typescript@npm:5.7.2":
  version: 5.7.2
  resolution: "typescript@npm:5.7.2"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10/4caa3904df69db9d4a8bedc31bafc1e19ffb7b24fbde2997a1633ae1398d0de5bdbf8daf602ccf3b23faddf1aeeb9b795223a2ed9c9a4fdcaf07bfde114a401a
  languageName: node
  linkType: hard

"typescript@patch:typescript@npm%3A5.7.2#optional!builtin<compat/typescript>":
  version: 5.7.2
  resolution: "typescript@patch:typescript@npm%3A5.7.2#optional!builtin<compat/typescript>::version=5.7.2&hash=5786d5"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10/d75ca10141afc64fd3474b41a8b082b640555bed388d237558aed64e5827ddadb48f90932c7f4205883f18f5bcab8b6a739a2cfac95855604b0dfeb34bc2f3eb
  languageName: node
  linkType: hard

"undici-types@npm:~6.20.0":
  version: 6.20.0
  resolution: "undici-types@npm:6.20.0"
  checksum: 10/583ac7bbf4ff69931d3985f4762cde2690bb607844c16a5e2fbb92ed312fe4fa1b365e953032d469fa28ba8b224e88a595f0b10a449332f83fa77c695e567dbe
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: "npm:^5.0.0"
  checksum: 10/6a62094fcac286b9ec39edbd1f8f64ff92383baa430af303dfed1ffda5e47a08a6b316408554abfddd9730c78b6106bef4ca4d02c1231a735ddd56ced77573df
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10/beafdf3d6f44990e0a5ce560f8f881b4ee811be70b6ba0db25298c31c8cf525ed963572b48cd03be1c1349084f9e339be4241666d7cf1ebdad20598d3c652b27
  languageName: node
  linkType: hard

"upath@npm:^2.0.1":
  version: 2.0.1
  resolution: "upath@npm:2.0.1"
  checksum: 10/7b98a83559a295d59f87f7a8d615c7549d19e4aec4dd9d52be2bf1ba93e1d6ee7d8f2188cdecbf303a22cea3768abff4268b960350152a0264125f577d9ed79e
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: "npm:^2.1.0"
  checksum: 10/b271ca7e3d46b7160222e3afa3e531505161c9a4e097febae9664e4b59912f4cbe94861361a4175edac3a03fee99d91e44b6a58c17a634bc5a664b19fc76fbcb
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.2":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 10/474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"vite-plugin-vuetify@npm:2.0.4":
  version: 2.0.4
  resolution: "vite-plugin-vuetify@npm:2.0.4"
  dependencies:
    "@vuetify/loader-shared": "npm:^2.0.3"
    debug: "npm:^4.3.3"
    upath: "npm:^2.0.1"
  peerDependencies:
    vite: ">=5"
    vue: ^3.0.0
    vuetify: ^3.0.0
  checksum: 10/a7ae42d5df64f07906a36c4b94ab0647fa0879cdc75a7ced1ae818bef10550781eca96b12af9fd732d74772201e11c3010322cacd32bd8354a8577de24141997
  languageName: node
  linkType: hard

"vite@npm:6.1.6":
  version: 6.1.6
  resolution: "vite@npm:6.1.6"
  dependencies:
    esbuild: "npm:^0.24.2"
    fsevents: "npm:~2.3.3"
    postcss: "npm:^8.5.2"
    rollup: "npm:^4.30.1"
  peerDependencies:
    "@types/node": ^18.0.0 || ^20.0.0 || >=22.0.0
    jiti: ">=1.21.0"
    less: "*"
    lightningcss: ^1.21.0
    sass: "*"
    sass-embedded: "*"
    stylus: "*"
    sugarss: "*"
    terser: ^5.16.0
    tsx: ^4.8.1
    yaml: ^2.4.2
  dependenciesMeta:
    fsevents:
      optional: true
  peerDependenciesMeta:
    "@types/node":
      optional: true
    jiti:
      optional: true
    less:
      optional: true
    lightningcss:
      optional: true
    sass:
      optional: true
    sass-embedded:
      optional: true
    stylus:
      optional: true
    sugarss:
      optional: true
    terser:
      optional: true
    tsx:
      optional: true
    yaml:
      optional: true
  bin:
    vite: bin/vite.js
  checksum: 10/bd209fd55e8291b2d9ec7f3deb7cfd978c713a508b855c4e4191f70ac142a1f36b192c38f5be6cef071db495558ab2a3509d698f9e19f9595d703ac1945c79fc
  languageName: node
  linkType: hard

"vscode-uri@npm:^3.0.8":
  version: 3.0.8
  resolution: "vscode-uri@npm:3.0.8"
  checksum: 10/e882d6b679e0d053cbc042893c0951a135d899a192b62cd07f0a8924f11ae722067a8d6b1b5b147034becf57faf9fff9fb543b17b749fd0f17db1f54f783f07c
  languageName: node
  linkType: hard

"vue-component-type-helpers@npm:^2.0.0":
  version: 2.1.10
  resolution: "vue-component-type-helpers@npm:2.1.10"
  checksum: 10/5261082e5c1077273fdafa1dc852b7dd26fcfa0ee2fa550116a3ff5126611e3de7894990d5f737bea22afbbe0cfbd3a9f3cfbe0f06689279c1f73a5d7dc3d733
  languageName: node
  linkType: hard

"vue-demi@npm:>=0.14.8":
  version: 0.14.10
  resolution: "vue-demi@npm:0.14.10"
  peerDependencies:
    "@vue/composition-api": ^1.0.0-rc.1
    vue: ^3.0.0-0 || ^2.6.0
  peerDependenciesMeta:
    "@vue/composition-api":
      optional: true
  bin:
    vue-demi-fix: bin/vue-demi-fix.js
    vue-demi-switch: bin/vue-demi-switch.js
  checksum: 10/3028239c0c25a84361a13ab936fcc9a199f54e0320c2ec1d2f4fdf7da8bae663382b7c1e5b1f5a1a43f6aebc73b955892cd4b6c7b3eaf2b766e18cc2a6f7ebea
  languageName: node
  linkType: hard

"vue-eslint-parser@npm:^9.4.3":
  version: 9.4.3
  resolution: "vue-eslint-parser@npm:9.4.3"
  dependencies:
    debug: "npm:^4.3.4"
    eslint-scope: "npm:^7.1.1"
    eslint-visitor-keys: "npm:^3.3.0"
    espree: "npm:^9.3.1"
    esquery: "npm:^1.4.0"
    lodash: "npm:^4.17.21"
    semver: "npm:^7.3.6"
  peerDependencies:
    eslint: ">=6.0.0"
  checksum: 10/228e43f0067e5f1fa87a4192f355ebbb4a224f0c7e170b1fbd4205fdf42fe7b3c6820a7e467496a8174e51ba351bc9caed00389d05519206cfa1615cac44516c
  languageName: node
  linkType: hard

"vue-i18n@npm:11.1.2":
  version: 11.1.2
  resolution: "vue-i18n@npm:11.1.2"
  dependencies:
    "@intlify/core-base": "npm:11.1.2"
    "@intlify/shared": "npm:11.1.2"
    "@vue/devtools-api": "npm:^6.5.0"
  peerDependencies:
    vue: ^3.0.0
  checksum: 10/a9fbb84001169defadff7d90e2d9eff29a8231b5aa142ab6952992e931d595c74e33a76d3dbdef8d0e078f7a98df09794606b659b308a49f1a8eec3186c8cb02
  languageName: node
  linkType: hard

"vue-i18n@npm:^10.0.5":
  version: 10.0.5
  resolution: "vue-i18n@npm:10.0.5"
  dependencies:
    "@intlify/core-base": "npm:10.0.5"
    "@intlify/shared": "npm:10.0.5"
    "@vue/devtools-api": "npm:^6.5.0"
  peerDependencies:
    vue: ^3.0.0
  checksum: 10/557c00fbce207a1eaa836894d4a5140be7c9df546168dac1e14dec7b04f2ecc351161ac11e9cc31eb34da408f863f7371477ab3947ae89f9480ad531c24db1f2
  languageName: node
  linkType: hard

"vue-router@npm:4.5.0":
  version: 4.5.0
  resolution: "vue-router@npm:4.5.0"
  dependencies:
    "@vue/devtools-api": "npm:^6.6.4"
  peerDependencies:
    vue: ^3.2.0
  checksum: 10/0dc76635924c84960614592c3dbf9919961cea765802c86e401d7d0747674ebbe4d7562ddbe1f349112e97e9ccb985b56037b12c7036dd7562c8da78b9cce9b5
  languageName: node
  linkType: hard

"vue-tsc@npm:2.2.0":
  version: 2.2.0
  resolution: "vue-tsc@npm:2.2.0"
  dependencies:
    "@volar/typescript": "npm:~2.4.11"
    "@vue/language-core": "npm:2.2.0"
  peerDependencies:
    typescript: ">=5.0.0"
  bin:
    vue-tsc: ./bin/vue-tsc.js
  checksum: 10/4370a69a63821cdb82f33b6ce234f468d68301f58e3a636445c3e36c347b1bbd8a710f9f3611c6bf54f8d650e155dcca1b120cb864119cb0a6b887b871d0f37a
  languageName: node
  linkType: hard

"vue@npm:3.5.13":
  version: 3.5.13
  resolution: "vue@npm:3.5.13"
  dependencies:
    "@vue/compiler-dom": "npm:3.5.13"
    "@vue/compiler-sfc": "npm:3.5.13"
    "@vue/runtime-dom": "npm:3.5.13"
    "@vue/server-renderer": "npm:3.5.13"
    "@vue/shared": "npm:3.5.13"
  peerDependencies:
    typescript: "*"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10/bcab4ca74c4a37a2bd3f892d6783f8b01748281cd5aedc2f5dd16521b97a3ba0303cf56df5e217eaa712d38ed2a14b75a92d875cf2596973ac985ca806f44e79
  languageName: node
  linkType: hard

"vuetify@npm:3.7.6":
  version: 3.7.6
  resolution: "vuetify@npm:3.7.6"
  peerDependencies:
    typescript: ">=4.7"
    vite-plugin-vuetify: ">=1.0.0"
    vue: ^3.3.0
    webpack-plugin-vuetify: ">=2.0.0"
  peerDependenciesMeta:
    typescript:
      optional: true
    vite-plugin-vuetify:
      optional: true
    webpack-plugin-vuetify:
      optional: true
  checksum: 10/235d6ee4fb5a8b3e56c677cef0378387fe13c30721ac7765fc25b9132f9549f79207678199a1479c7f190ced4f87e1e074c5cbd9c95601b741aa5a4e8e2df167
  languageName: node
  linkType: hard

"w3c-xmlserializer@npm:^5.0.0":
  version: 5.0.0
  resolution: "w3c-xmlserializer@npm:5.0.0"
  dependencies:
    xml-name-validator: "npm:^5.0.0"
  checksum: 10/d78f59e6b4f924aa53b6dfc56949959229cae7fe05ea9374eb38d11edcec01398b7f5d7a12576bd5acc57ff446abb5c9115cd83b9d882555015437cf858d42f0
  languageName: node
  linkType: hard

"webfontloader@npm:^1.6.28":
  version: 1.6.28
  resolution: "webfontloader@npm:1.6.28"
  checksum: 10/fba6a6a41222d893401aa43199ba3937679a6d71a7e1aeed1fc57d2f034c96fefa267b57cf9aff6af5877fed78c11a5327d7f3c8d3d6f2bfb7543fce8640e5f0
  languageName: node
  linkType: hard

"webidl-conversions@npm:^7.0.0":
  version: 7.0.0
  resolution: "webidl-conversions@npm:7.0.0"
  checksum: 10/4c4f65472c010eddbe648c11b977d048dd96956a625f7f8b9d64e1b30c3c1f23ea1acfd654648426ce5c743c2108a5a757c0592f02902cf7367adb7d14e67721
  languageName: node
  linkType: hard

"whatwg-encoding@npm:^3.1.1":
  version: 3.1.1
  resolution: "whatwg-encoding@npm:3.1.1"
  dependencies:
    iconv-lite: "npm:0.6.3"
  checksum: 10/bbef815eb67f91487c7f2ef96329743f5fd8357d7d62b1119237d25d41c7e452dff8197235b2d3c031365a17f61d3bb73ca49d0ed1582475aa4a670815e79534
  languageName: node
  linkType: hard

"whatwg-mimetype@npm:^4.0.0":
  version: 4.0.0
  resolution: "whatwg-mimetype@npm:4.0.0"
  checksum: 10/894a618e2d90bf444b6f309f3ceb6e58cf21b2beaa00c8b333696958c4076f0c7b30b9d33413c9ffff7c5832a0a0c8569e5bb347ef44beded72aeefd0acd62e8
  languageName: node
  linkType: hard

"whatwg-url@npm:^14.0.0, whatwg-url@npm:^14.1.0":
  version: 14.1.0
  resolution: "whatwg-url@npm:14.1.0"
  dependencies:
    tr46: "npm:^5.0.0"
    webidl-conversions: "npm:^7.0.0"
  checksum: 10/3afd325de6cf3a367820ce7c3566a1f78eb1409c4f27b1867c74c76dab096d26acedf49a8b9b71db53df7d806ec2e9ae9ed96990b2f7d1abe6ecf1fe753af6eb
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10/4782f8a1d6b8fc12c65e968fea49f59752bf6302dc43036c3bf87da718a80710f61a062516e9764c70008b487929a73546125570acea95c5b5dcc8ac3052c70f
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: 10/6ec99e89ba32c7e748b8a3144e64bfc74aa63e2b2eacbb61a0060ad0b961eb1a632b08fb1de067ed59b002cec3e21de18299216ebf2325ef0f78e0f121e14e90
  languageName: node
  linkType: hard

"word-wrap@npm:^1.2.5":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: 10/1ec6f6089f205f83037be10d0c4b34c9183b0b63fca0834a5b3cee55dd321429d73d40bb44c8fc8471b5203d6e8f8275717f49a8ff4b2b0ab41d7e1b563e0854
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10/cebdaeca3a6880da410f75209e68cd05428580de5ad24535f22696d7d9cab134d1f8498599f344c3cf0fb37c1715807a183778d8c648d6cc0cb5ff2bb4236540
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10/7b1e4b35e9bb2312d2ee9ee7dc95b8cb5f8b4b5a89f7dde5543fe66c1e3715663094defa50d75454ac900bd210f702d575f15f3f17fa9ec0291806d2578d1ddf
  languageName: node
  linkType: hard

"ws@npm:^8.18.0":
  version: 8.18.0
  resolution: "ws@npm:8.18.0"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10/70dfe53f23ff4368d46e4c0b1d4ca734db2c4149c6f68bc62cb16fc21f753c47b35fcc6e582f3bdfba0eaeb1c488cddab3c2255755a5c3eecb251431e42b3ff6
  languageName: node
  linkType: hard

"xml-name-validator@npm:^4.0.0":
  version: 4.0.0
  resolution: "xml-name-validator@npm:4.0.0"
  checksum: 10/f9582a3f281f790344a471c207516e29e293c6041b2c20d84dd6e58832cd7c19796c47e108fd4fd4b164a5e72ad94f2268f8ace8231cde4a2c6428d6aa220f92
  languageName: node
  linkType: hard

"xml-name-validator@npm:^5.0.0":
  version: 5.0.0
  resolution: "xml-name-validator@npm:5.0.0"
  checksum: 10/43f30f3f6786e406dd665acf08cd742d5f8a46486bd72517edb04b27d1bcd1599664c2a4a99fc3f1e56a3194bff588b12f178b7972bc45c8047bdc4c3ac8d4a1
  languageName: node
  linkType: hard

"xmlchars@npm:^2.2.0":
  version: 2.2.0
  resolution: "xmlchars@npm:2.2.0"
  checksum: 10/4ad5924974efd004a47cce6acf5c0269aee0e62f9a805a426db3337af7bcbd331099df174b024ace4fb18971b8a56de386d2e73a1c4b020e3abd63a4a9b917f1
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10/4cb02b42b8a93b5cf50caf5d8e9beb409400a8a4d85e83bb0685c1457e9ac0b7a00819e9f5991ac25ffabb56a78e2f017c1acc010b3a1babfe6de690ba531abd
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: 10/1884d272d485845ad04759a255c71775db0fac56308764b4c77ea56a20d56679fad340213054c8c9c9c26fcfd4c4b2a90df993b7e0aaf3cdb73c618d1d1a802a
  languageName: node
  linkType: hard

"yaml@npm:^2.0.0":
  version: 2.6.1
  resolution: "yaml@npm:2.6.1"
  bin:
    yaml: bin.mjs
  checksum: 10/cf412f03a33886db0a3aac70bb4165588f4c5b3c6f8fc91520b71491e5537800b6c2c73ed52015617f6e191eb4644c73c92973960a1999779c62a200ee4c231d
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: 10/f77b3d8d00310def622123df93d4ee654fc6a0096182af8bd60679ddcdfb3474c56c6c7190817c84a2785648cdee9d721c0154eb45698c62176c322fb46fc700
  languageName: node
  linkType: hard
