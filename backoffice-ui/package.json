{"name": "backoffice-ui", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build-watch": "vite build --watch", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --fix", "lint-ci": "eslint . --no-fix --format gitlab --max-warnings 0"}, "dependencies": {"@bd/cd-system3": "11.1.0", "keycloak-js": "26.0.7", "vue": "3.5.13", "vue-i18n": "11.1.2", "vue-router": "4.5.0", "vuetify": "3.7.6"}, "devDependencies": {"@eslint/compat": "1.2.4", "@types/node": "22.10.5", "@vitejs/plugin-vue": "5.2.1", "@vue/eslint-config-typescript": "14.2.0", "@vue/test-utils": "2.4.6", "@vue/tsconfig": "0.7.0", "eslint": "9.17.0", "eslint-formatter-gitlab": "5.1.0", "eslint-plugin-vue": "9.32.0", "jsdom": "26.0.0", "miragejs": "0.1.48", "sass": "1.83.0", "sass-loader": "16.0.4", "typescript": "5.7.2", "vite": "6.1.6", "vite-plugin-vuetify": "2.0.4", "vue-tsc": "2.2.0"}, "packageManager": "yarn@4.9.1"}