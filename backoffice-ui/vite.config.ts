import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import vuetify from 'vite-plugin-vuetify'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vuetify({ autoImport: true })
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  build: {
    target: 'ES2022'
  },
  css: {
    preprocessorOptions: {
      // Until Vite uses modern API by default we have to enforce it. The default legacy API makes Sass emit deprecation warnings. The
      // Option must be applied for sass and scss to make both build and dev mode happy.
      // Details:
      // - https://sass-lang.com/documentation/breaking-changes/legacy-js-api/#bundlers
      // - https://vitejs.dev/config/shared-options.html#css-preprocessoroptions
      sass: { api: 'modern-compiler' },
      scss: { api: 'modern-compiler' }
    }
  },
  server: {
    proxy: {
      // '/rest': 'http://localhost:8087/'
      '/rest': 'https://gns-backoffice.store.twbd-dev.net/'
    }
  }
})
