/* eslint no-use-before-define: 0 */

type Product = {
    id: string;
    name: string;
    description: string;
    productType: string;
    images: string[];
    variants: Variant[];
};

type Variant = {
    sku: string;
    price: Money;
    bundleAmount: number;
    licenseType: string;
    runtime: string;
};

type Cart = {
    id: string;
    lineItems: CartItem[];
    totalPrice: Money;
};

type CartItem = {
    name: string;
    productId: string;
    variantId: number;
    quantity: number;
    totalPrice: Money;
    sku: string;
};

type Money = {
    value: number;
    currencyCode: string;
};

type Order = {
    orderId: string;
    lineItems: CartItem[];
    totalPrice: Money;
    createdAt: string;
};

type Payment = {
    paymentId: string;
    paymentStatus: 'INITIAL' | 'PENDING' | 'SUCCESS' | 'FAILURE';
    orderId: string;
};

type UIComponent = {
    name: string
    props: Record<string, object | string>
}

type KeycloakUser = {
    userId: string;
    firstname: string;
    lastname: string;
    email: string;
    companyName: string;
    companyId: string;
    communication_language?: string;
}

type PaymentConfig = {
    paymentProviders: string[];
    paymentMethods: string[];
}
