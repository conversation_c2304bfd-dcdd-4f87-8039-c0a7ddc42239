import { createApp } from 'vue'
import appConfig from '@/helpers/app-config'
import i18n from '@/plugins/i18n'
import router from '@/routes/router'
import vuetify from '@/plugins/vuetify'
import keycloak from '@/plugins/keycloak'
import '@bd/cd-system3/dist/cd-system.css'
// // bosch fonts added via assets folder. Font files should be imported automatically to your project
import '@bd/cd-system3/brands/bosch/fonts/fonts.css'

import {
  CDNavbar,
  CDSidebar,
  CDFooter,
  CDNeedHelpButton,
  CDChip,
  CDFlyoutMenu,
  CDFlyoutMenuItem,
  CDNotification,
  CDToast,
  CDCookieBanner,
  CDInput,
  CDQuantity,
  CDTable,
  CDDataTable,
  CDTabs,
  CDLanguageSwitcher,
  CDUserFlyoutMenu,
  CDEnvironmentSwitcher,
  CDNavbarItem,
  CDBreadcrumbs
} from '@bd/cd-system3'

import App from './App.vue'

createApp(App)
  .use(vuetify)
  .use(i18n)
  .use(router)
  .use(keycloak, {
    url: appConfig.keycloakUrl,
    realm: 'rexroth',
    clientId: 'bossstore-backoffice-frontend',
    onLoad: 'login-required'
  })
  .component('CDInput', CDInput)
  .component('CDNavbar', CDNavbar)
  .component('CDSidebar', CDSidebar)
  .component('CDFooter', CDFooter)
  .component('CDNeedHelpButton', CDNeedHelpButton)
  .component('CDChip', CDChip)
  .component('CDFlyoutMenu', CDFlyoutMenu)
  .component('CDFlyoutMenuItem', CDFlyoutMenuItem)
  .component('CDNotification', CDNotification)
  .component('CDToast', CDToast)
  .component('CDCookieBanner', CDCookieBanner)
  .component('CDQuantity', CDQuantity)
  .component('CDTable', CDTable)
  .component('CDTabs', CDTabs)
  .component('CDDataTable', CDDataTable)
  .component('CDLanguageSwitcher', CDLanguageSwitcher)
  .component('CDUserFlyoutMenu', CDUserFlyoutMenu)
  .component('CDEnvironmentSwitcher', CDEnvironmentSwitcher)
  .component('CDNavbarItem', CDNavbarItem)
  .component('CDBreadcrumbs', CDBreadcrumbs)

  .mount('#app')
