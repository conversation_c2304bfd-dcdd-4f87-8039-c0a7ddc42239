<template>
  <div class="d-flex flex-row">
    <v-card
      variant="outlined"
      class="d-flex flex-column flex-grow-1 ma-3"
    >
      <v-card-title>{{ $t('exportPrices') }}</v-card-title>
      <v-spacer />
      <v-card-actions>
        <CDButton
          block
          :loading="loading1"
          @click="exportPrices"
        >
          <CDIcon
            icon="download"
            class="mr-2"
          />
          {{ $t('download') }}
        </CDButton>
      </v-card-actions>
    </v-card>
    <v-card
      variant="outlined"
      class="flex-grow-1 ma-3"
    >
      <v-card-title>{{ $t('importPrices') }}</v-card-title>
      <v-card-text>
        <v-file-input
          v-model="file3"
          label="Choose file"
        />
      </v-card-text>
      <v-card-actions>
        <CDButton
          block
          :loading="loading3"
          @click="importPrices()"
        >
          <CDIcon
            icon="upload"
            class="mr-2"
          />
          {{ $t('upload') }}
        </CDButton>
      </v-card-actions>
    </v-card>
  </div>
  <div class="d-flex flex-row">
    <v-card
      variant="outlined"
      class="d-flex flex-column flex-grow-1 ma-3"
    >
      <v-card-title>{{ $t('exportProducts') }}</v-card-title>
      <v-spacer />
      <v-card-actions>
        <CDButton
          block
          :loading="loading2"
          @click="exportProducts"
        >
          <CDIcon
            icon="download"
            class="mr-2"
          />
          {{ $t('download') }}
        </CDButton>
      </v-card-actions>
    </v-card>
    <v-card
      variant="outlined"
      class="flex-grow-1 ma-3"
    >
      <v-card-title>{{ $t('importProducts') }}</v-card-title>
      <v-card-text>
        <v-file-input
          v-model="file4"
          label="Choose file"
        />
      </v-card-text>
      <v-card-actions>
        <CDButton
          block
          :loading="loading4"
          @click="importProducts()"
        >
          <CDIcon
            icon="upload"
            class="mr-2"
          />
          {{ $t('upload') }}
        </CDButton>
      </v-card-actions>
    </v-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import backofficeApi from '@/api/backoffice-api'
import notificationStore from '@/stores/notification-store'
const loading1 = ref(false)
const loading2 = ref(false)
const loading3 = ref(false)
const loading4 = ref(false)
const file3 = ref<File>()
const file4 = ref<File>()

async function exportPrices () {
  try {
    loading1.value = true
    await download('prices.json', backofficeApi.exportPrices)
  } finally {
    loading1.value = false
  }
}

async function exportProducts () {
  try {
    loading2.value = true
    await download('products.json', backofficeApi.exportProducts)
  } finally {
    loading2.value = false
  }
}

async function download (filename: string, downloadFunction: () => Promise<Blob>): Promise<void> {
  try {
    const blob = await downloadFunction()
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = filename
    link.click()
    URL.revokeObjectURL(link.href)
  } catch {
    notificationStore.showWarning(`Failed to download: ${filename}. Please try again.`)
  }
}

async function importPrices () {
  try {
    loading3.value = true
    if (!file3.value) return
    console.log(file3.value)
    await backofficeApi.importPrices(file3.value)
    notificationStore.showSuccess(`Successfully imported: ${file3.value.name}.`)
  } catch (err) {
    notificationStore.showWarning(`Failed to import file. ${err}`)
  } finally {
    loading3.value = false
  }
}

async function importProducts () {
  try {
    loading4.value = true
    if (!file4.value) return
    console.log(file4.value)
    await backofficeApi.importProducts(file4.value)
    notificationStore.showSuccess(`Successfully imported: ${file4.value.name}.`)
  } catch (err) {
    notificationStore.showWarning(`Failed to import file. ${err}`)
  } finally {
    loading4.value = false
  }
}

</script>

<style scoped lang="scss">
</style>
