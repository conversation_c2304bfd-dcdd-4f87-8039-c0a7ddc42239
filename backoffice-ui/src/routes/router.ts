import { Component } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import authStore from '@/stores/auth-store'

const routes = [
  {
    path: '/',
    redirect: '/import-export'
  },
  {
    path: '/import-export',
    name: 'import-export',
    component: (): Component => import('@/pages/ImportExportPage.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

router.beforeEach(async (to, _from, next) => {
  await authStore.waitUntilInitialized
  if (authStore.loggedIn.value) {
    return next()
  }
  // at this point user is not logged in, but is required to be
  next(false)
  await authStore.login()
})

export default router
