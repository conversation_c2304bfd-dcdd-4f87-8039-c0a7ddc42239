import { createServer } from 'miragejs'

const mockData: Cart = {
  id: '1',
  lineItems: [
    {
      name: 'Item 1',
      productId: 'product1',
      variantId: 1,
      quantity: 2,
      totalPrice: {
        value: 200,
        currencyCode: 'USD'
      },
      sku: 'sku1'
    },
    {
      name: 'Item 2',
      productId: 'product2',
      variantId: 2,
      quantity: 1,
      totalPrice: {
        value: 100,
        currencyCode: 'USD'
      },
      sku: 'sku2'
    }
  ],
  totalPrice: {
    value: 300,
    currencyCode: 'USD'
  }
}

createServer({
  routes () {
    this.namespace = 'rest'
    this.get('/cart', () => mockData)
  }
})
