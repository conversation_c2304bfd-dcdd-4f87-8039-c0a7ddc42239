import { ref } from 'vue'

class NotificationStore {
  public notifications = ref<{ color: string; text: string }[]>([])

  public showWarning (text: string): void {
    this.notifications.value.push({ color: 'error', text })
  }

  public showSuccess (text: string): void {
    this.notifications.value.push({ color: 'success', text })
  }

  public closeNotification (): void {
    this.notifications.value.shift()
  }
}

export default new NotificationStore()
