import { computed, ref } from 'vue'
import Keycloak from 'keycloak-js'
import router from '@/routes/router'
import { refreshLocaleAsync } from '@/plugins/i18n'

class AuthStore {
  private keycloak = ref<Keycloak | null>(null)
  public loggedIn = ref(false)
  public loading = ref(true)
  private resolveWaitUntilInitialized = ref<(() => void) | null>(null)
  public waitUntilInitialized = new Promise((resolve) => {
    this.resolveWaitUntilInitialized.value = resolve as () => void
  })

  public userInfo = computed(() => {
    const token = this.keycloak.value?.tokenParsed
    if (!token) return undefined
    return {
      userId: token.preferred_username,
      firstname: token.given_name,
      lastname: token.family_name,
      name: token.name,
      email: token.email,
      companyName: token.company_name,
      companyId: token.company_id,
      communication_language: token.communication_language
    }
  })

  public async init (kc: Keycloak): Promise<void> {
    this.keycloak.value = kc
    this.initCallbacks()
    this.loggedIn.value = await kc.init({ onLoad: 'check-sso' })
    if (this.resolveWaitUntilInitialized.value) this.resolveWaitUntilInitialized.value()
    this.loading.value = false
  }

  public async login (redirectUri?: string): Promise<void> {
    await this.keycloak.value?.login({ redirectUri })
  }

  public async logout (): Promise<void> {
    await this.keycloak.value?.logout({ redirectUri: window.location.origin + '/' })
  }

  public async updateToken (): Promise<string> {
    await this.waitUntilInitialized
    if (!this.keycloak.value) throw new Error('Keycloak not initialized')
    try {
      const refreshed = await this.keycloak.value.updateToken(30)
      if (!refreshed && !this.keycloak.value.token) {
        console.warn('Tried to refresh token, but no token available')
      }
    } catch (err: unknown) {
      console.warn('Failed to refresh token, or session has expired', err)
    }

    if (!this.keycloak.value.token) {
      throw new Error('no token')
    }
    return this.keycloak.value.token
  }

  public async initCallbacks (): Promise<void> {
    if (!this.keycloak.value) throw new Error('Keycloak not initialized')
    this.keycloak.value.onTokenExpired = this.updateToken

    this.keycloak.value.onAuthSuccess = () => {
      const locale = this.userInfo.value?.communication_language?.split('-')[0]
      if (locale) {
        refreshLocaleAsync(locale)
      }
      router.isReady().then(() => {
        // remove auth related query parameters
        const route = { ...router.currentRoute.value }
        router.replace({ ...route, hash: '' })
      })
    }
  }
}

export default new AuthStore()
