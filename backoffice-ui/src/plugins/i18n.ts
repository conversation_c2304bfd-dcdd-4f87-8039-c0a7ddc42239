import { createI18n } from 'vue-i18n'
import en from '@/locales/en.json'
import { cdTranslationsEN } from '@bd/cd-system3'

const loadedLanguages = ['en']

const i18n = createI18n({
  locale: 'en',
  fallbackLocale: 'en',
  silentTranslationWarn: true,
  allowComposition: true,
  messages: {
    en: { ...en, $vuetify: cdTranslationsEN.en }
  },
  datetimeFormats: {
    de: {
      long: {
        year: 'numeric',
        month: 'short',
        day: '2-digit'
      }
    },
    en: {
      long: {
        year: 'numeric',
        month: 'short',
        day: '2-digit'
      }
    }
  }
})

function setI18nLanguage (lang: string) {
  if (i18n.mode === 'legacy') {
    i18n.global.locale = lang as typeof i18n.global.locale
  } else {
    throw new Error('legacy mode needs to be configured')
  }
  return lang
}

export async function refreshLocaleAsync (lang: string): Promise<void> {
  // If the same language
  if (i18n.global.locale === lang) return

  // If the language was already loaded
  if (loadedLanguages.includes(lang)) {
    setI18nLanguage(lang)
  }
}

export default i18n
