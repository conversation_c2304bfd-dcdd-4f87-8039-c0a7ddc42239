import BaseApi from './base-api'

class ImportExportApi extends BaseApi {
  constructor () {
    super('/rest/tenants/DCIH/data', 'ImportExportApi error')
  }

  public exportPrices = (): Promise<Blob> => this.get('/prices', undefined, { Accept: 'application/octet-stream' })

  public importPrices = (input: Blob): Promise<BlobPart> => this.post('/prices', input, undefined, { 'Content-Type': 'application/octet-stream' })

  public exportProducts = (): Promise<Blob> => this.get('/products', undefined, { Accept: 'application/octet-stream' })

  public importProducts = (input: Blob): Promise<BlobPart> => this.post('/products', input, undefined, { 'Content-Type': 'application/octet-stream' })
}

export default new ImportExportApi()
