/// <reference lib="dom" />

import fetchAuthenticated from '@/helpers/fetch-authenticated'

export default class BaseApi {
  baseUrl = ''
  errorMessage = ''

  public constructor (baseUrl: string, errorMessage: string) {
    this.baseUrl = baseUrl
    this.errorMessage = errorMessage
  }

  async get<T> (url: string, params?: Record<string, string | undefined>, headers?: Record<string, string>): Promise<T> {
    try {
      const uri = this.baseUrl + url + (params === undefined ? '' : '?' + new URLSearchParams(JSON.parse(JSON.stringify(params))))
      const response = await fetchAuthenticated(uri, {
        headers: {
          Accept: 'application/json',
          ...headers
        }
      })
      if (!response.ok) {
        this.handleError(response.statusText, response)
      }
      const responseContentType = response.headers.get('Content-Type')
      if (responseContentType === 'application/octet-stream') {
        // T needs to be of type Blob in this case
        return response.blob() as unknown as T
      }
      if (responseContentType === 'application/json') {
        const string = await response.text()
        const json = string === '' ? {} : JSON.parse(string)
        return (await json) as T
      }
      throw new Error('Unknown content type')
    } catch (e) {
      this.handleError(e)
      return Promise.reject(e)
    }
  }

  async post<T> (url: string, body: unknown, params?: Record<string, string | undefined>, headers?: Record<string, string>): Promise<T> {
    try {
      const uri = this.baseUrl + url + (params === undefined ? '' : '?' + new URLSearchParams(JSON.parse(JSON.stringify(params))))
      const response = await fetchAuthenticated(uri, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          ...headers
        },
        body: headers?.['Content-Type'] === 'application/octet-stream' ? body as Blob : JSON.stringify(body)
      })
      if (!response.ok) {
        this.handleError(response.statusText, response)
      }
      const string = await response.text()
      const json = string === '' ? {} : JSON.parse(string)
      return (await json) as T
    } catch (e) {
      this.handleError(e)
      return Promise.reject(e)
    }
  }

  async put<T> (url: string, body: unknown, params?: Record<string, string | undefined>, headers?: Record<string, string>): Promise<T> {
    try {
      const uri = this.baseUrl + url + (params === undefined ? '' : '?' + new URLSearchParams(JSON.parse(JSON.stringify(params))))
      const response = await fetchAuthenticated(uri, {
        method: 'PUT',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          ...headers
        },
        body: JSON.stringify(body)
      })
      if (!response.ok) {
        this.handleError(response.statusText, response)
      }
      const string = await response.text()
      const json = string === '' ? {} : JSON.parse(string)
      return (await json) as T
    } catch (e) {
      this.handleError(e)
      return Promise.reject(e)
    }
  }

  async delete<T> (url: string, params?: Record<string, string | undefined>): Promise<T> {
    try {
      const uri = this.baseUrl + url + (params === undefined ? '' : '?' + new URLSearchParams(JSON.parse(JSON.stringify(params))))
      const response = await fetchAuthenticated(uri, {
        method: 'DELETE'
      })
      if (!response.ok) {
        this.handleError(response.statusText, response)
      }
      const string = await response.text()
      const json = string === '' ? {} : JSON.parse(string)
      return (await json) as T
    } catch (e) {
      this.handleError(e)
      return Promise.reject(e)
    }
  }

  private handleError (...args: unknown[]) {
    // todo: propper logging and error notification
    console.error(`${this.errorMessage} ${JSON.stringify(args)}`)
    throw new Error(args.toString())
  }
}
