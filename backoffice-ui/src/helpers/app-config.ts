type EnvironmentVariables = {
  KEYCLOAK_URL?: string;
  STAGE?: string;
  VERSION?: string;
  USER_MANAGEMENT_URL?: string;
  TRANSLATION_URL?: string;
  CROWDIN_DISTRIBUTION_ID?: string;
}

export type Environment = {
  keycloakUrl: string;
  stage: string;
  version: string;
  userManagementUrl: string;
  translationUrl: string;
  crowdinDistributionId?: string;
}

declare global {
  interface Window {
    configuration?: EnvironmentVariables;
  }
}

const env: Environment = {
  keycloakUrl: window.configuration?.KEYCLOAK_URL ?? 'http://localhost:8000/auth',
  stage: window.configuration?.STAGE ?? 'LOCAL',
  version: window.configuration?.VERSION ?? '0.0.0',
  userManagementUrl: window.configuration?.USER_MANAGEMENT_URL ?? 'https://accounts.store.twbd-dev.net',
  translationUrl: window.configuration?.TRANSLATION_URL ?? 'https://translations.store.twbd-dev.net',
  crowdinDistributionId: window.configuration?.CROWDIN_DISTRIBUTION_ID ?? 'e-15f5cc7f41ca56013cd73b33hc',
}

export default env
