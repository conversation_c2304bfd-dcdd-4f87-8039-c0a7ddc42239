import authStore from '@/stores/auth-store'

export default async (resource: RequestInfo | URL, config?: RequestInit): Promise<Response> => {
  if (!authStore.loggedIn.value) {
    return fetch(resource, config)
  } else {
    const token = await authStore.updateToken()
    const headers = new Headers(config?.headers)
    headers.set('Authorization', `Bearer ${token}`)
    const newConfig = { ...config, headers }
    return fetch(resource, newConfig)
  }
}
