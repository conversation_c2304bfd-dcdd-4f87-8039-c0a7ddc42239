<template>
  <v-app>
    <v-layout>
      <CDNavbar subbar-title="GNS Backoffice">
        <template #logo>
          <img
            style="height: 40px;"
            :src="logo"
          >
        </template>
        <template #content-end>
          <CDNavbarItem
            title="Home"
            to="/"
            icon="user"
          />
          <v-progress-circular
            v-if="authStore.loading.value"
            color="primary"
            indeterminate
          />
          <CDButton
            v-else-if="!authStore.loggedIn.value"
            color="primary"
            @click="() => authStore.login()"
          >
            <CDIcon
              icon="arrowright"
              class="mr-2"
            />
            {{ $t('login') }}
          </CDButton>
          <CDUserFlyoutMenu
            v-else
            :avatar-initials="avatarInitials"
            :header="{
              text: authStore.userInfo.value?.name,
              sub: authStore.userInfo.value?.companyName
            }"
          >
            <CDFlyoutMenuItem>
              <CDButton
                block
                color="grey-lighten3"
                @click="authStore.logout()"
              >
                <CDIcon
                  icon="logout"
                  class="mr-2"
                />
                {{ $t('signOut') }}
              </CDButton>
            </CDFlyoutMenuItem>
          </CDUserFlyoutMenu>
        </template>
        <template #content-subbar>
          <CDBreadcrumbs
            :items="breadcrumbs"
          />
        </template>
      </CDNavbar>
      <v-main v-if="authStore.loggedIn.value && !authStore.loading.value">
        <router-view />
        <CDFooter />
      </v-main>
      <v-main v-else-if="authStore.loading.value">
        <v-progress-linear indeterminate />
      </v-main>
      <v-main v-else />
      <v-snackbar
        v-if="notificationStore.notifications.value.length > 0"
        v-model="show"
        :color="notificationStore.notifications.value[0].color"
        timeout="5000"
        top
      >
        {{ notificationStore.notifications.value[0].text }}
        <template #actions>
          <v-btn
            variant="text"
            @click="notificationStore.closeNotification()"
          >
            <v-icon>close</v-icon>
          </v-btn>
        </template>
      </v-snackbar>
    </v-layout>
  </v-app>
</template>

<script setup lang="ts">
import { computed, reactive } from 'vue'
import authStore from '@/stores/auth-store'
import notificationStore from '@/stores/notification-store'
import logo from '@/assets/bosch.svg'
import { CDFooter } from '@bd/cd-system3'

const breadcrumbs = reactive([])
const avatarInitials = computed(() => authStore.userInfo.value?.name?.substr(0, 1))

const show = computed({
  get () {
    return notificationStore.notifications.value.length > 0
  },
  set (value: boolean) {
    if (!value) {
      notificationStore.closeNotification()
    }
  }
})

</script>

<style scoped lang="scss">
</style>
