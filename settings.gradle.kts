rootProject.name = "boss-store"

include("api-gateway")
include("api-graph")
include("productmanagement-api")
include("productmanagement")
include("ordermanagement-api")
include("ordermanagement")
include("external-clients")
include("external-clients:bc-central")
include("external-clients:brim-service")
include("external-clients:commercetools")
include("external-clients:countries-service")
include("external-clients:dc-keycloak")
include("external-clients:dc-litmos")
include("external-clients:email-service")
include("external-clients:mattermost")
include("external-clients:pgw")
include("external-clients:ump")
include("testing-awsmockup")
include("testing-commons")
include("testing-localmocks")
include("backoffice")
include("contractmanagement")
include("contractmanagement-api")
include("commons")
include("commons:jersey-client")
include("commons:base-webapp")
include("commons:aws")
include("commons:hazelcast")
include("commons:shedlock")
include("commons:tenant")
include("entitlementmanagement-api")
include("entitlementmanagement")

pluginManagement {
    repositories {
        mavenLocal()
        maven {
            name = "nexus"
            url = uri("${extra["nexusUrl"]}/repository/maven-all")
            credentials(PasswordCredentials::class)
        }
    }
}

dependencyResolutionManagement {
    repositories {
        mavenLocal()
        maven {
            name = "nexus"
            url = uri("${extra["nexusUrl"]}/repository/maven-all")
            credentials(PasswordCredentials::class)
        }
    }
}

