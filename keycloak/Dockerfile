FROM quay.io/keycloak/keycloak:26.1.2

ENV KC_HTTP_RELATIVE_PATH=/auth
ENV KC_FEATURES=token-exchange,admin-fine-grained-authz
ENV KC_DB=dev-file
ENV KC_HEALTH_ENABLED=true
ENV KC_LOG_LEVEL=info
ENV KEYCLOAK_ADMIN=keycloak_admin
ENV KEYCLOAK_ADMIN_PASSWORD=password
ENV KEYCLOAK_IMPORT=/opt/keycloak/data/import/dcih-realm.json,/opt/keycloak/data/import/dcih-users-0.json

COPY --chown=keycloak realms-and-users/ /opt/keycloak/data/import/
COPY --chown=keycloak export-data.sh /opt/keycloak/bin/

CMD ["start-dev", "--import-realm"]
