{"realm": "dcih", "users": [{"id": "cb5fa62b-cbc1-4d9f-9c9e-9f5e967f6ba9", "createdTimestamp": *************, "username": "cb5fa62b-cbc1-4d9f-9c9e-9f5e967f6ba9", "enabled": true, "totp": false, "emailVerified": false, "firstName": "<PERSON><PERSON>", "lastName": "Germany", "email": "<EMAIL>", "attributes": {"communicationLanguage": ["de"]}, "credentials": [{"id": "bc16a96a-34e5-4102-ac81-ccc82e733ca1", "type": "password", "userLabel": "", "createdDate": *************, "secretData": "{\"value\":\"ktJO5YnZ43iM7rcOQTnKzzZuwg8EKOUSJhAC4/9tDVOGD7gOplepRoLhh7D76gpmk6G5dEwcBvmDQJMyMsa+fA==\",\"salt\":\"FxtXHR2VRlw6jgF+f827VQ==\",\"additionalParameters\":{}}", "credentialData": "{\"hashIterations\":27500,\"algorithm\":\"pbkdf2-sha256\",\"additionalParameters\":{}}"}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["uma_authorization", "offline_access", "DEFAULT"], "clientRoles": {"account": ["view-profile", "manage-account"]}, "notBefore": 0, "groups": ["/Companies/91ae83b9-302b-4da2-8b15-************"]}, {"id": "b77dc836-1ead-4690-99fd-de95f99cc32f", "createdTimestamp": *************, "username": "b77dc836-1ead-4690-99fd-de95f99cc32f", "enabled": true, "totp": false, "emailVerified": false, "firstName": "Buyer1", "lastName": "Germany", "email": "<EMAIL>", "attributes": {"communicationLanguage": ["de"]}, "credentials": [{"id": "16c260be-a7d3-4450-8311-928ff3d28ab7", "type": "password", "userLabel": "", "createdDate": *************, "secretData": "{\"value\":\"t8TkvdH4wiuvVfYcC9SFTTG+W5g993jrBgonxGZbC+OmsuoH9BUzzU0RukKGVlcOZHGXJXg/GqKDu80EY0xGZA==\",\"salt\":\"M6stkwa9FHnP6sG8oOVqnA==\",\"additionalParameters\":{}}", "credentialData": "{\"hashIterations\":27500,\"algorithm\":\"pbkdf2-sha256\",\"additionalParameters\":{}}"}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["uma_authorization", "offline_access", "DEFAULT"], "clientRoles": {"account": ["view-profile", "manage-account"]}, "notBefore": 0, "groups": ["/Companies/7913618f-b0b3-49c8-8fbc-bc2992cb11f5"]}]}