{"id": "8d4105de-9583-45f1-bd14-26ec658baa16", "realm": "dcih", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "4b8e9bd8-1f7d-4e88-aa74-cf090080e875", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "8d4105de-9583-45f1-bd14-26ec658baa16", "attributes": {}}, {"id": "7ade2853-4957-4ca0-95d9-972a2ea81d6e", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "8d4105de-9583-45f1-bd14-26ec658baa16", "attributes": {}}, {"id": "4dbd1dd2-be76-4cbb-944c-78abcbef3ae1", "name": "default-roles-dcih", "description": "${role_default-roles}", "composite": true, "composites": {"realm": ["offline_access", "uma_authorization"], "client": {"account": ["manage-account", "view-profile"]}}, "clientRole": false, "containerId": "8d4105de-9583-45f1-bd14-26ec658baa16", "attributes": {}}], "client": {"realm-management": [{"id": "dd1bfd45-d6e5-4d4c-95e5-0196a8a913ba", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "1eb7de92-3764-45fd-8435-339af64df43d", "attributes": {}}, {"id": "6cf8c5e4-5067-4f07-b86a-1831d44ab632", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "1eb7de92-3764-45fd-8435-339af64df43d", "attributes": {}}, {"id": "7f78a46c-7835-4ca8-a429-7f62320b8f23", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "1eb7de92-3764-45fd-8435-339af64df43d", "attributes": {}}, {"id": "49db31fe-3c5e-4202-b505-d570af48594c", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "1eb7de92-3764-45fd-8435-339af64df43d", "attributes": {}}, {"id": "1c9ada2f-0a4a-455d-ae51-08f9565fea3e", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "1eb7de92-3764-45fd-8435-339af64df43d", "attributes": {}}, {"id": "86aea31b-7ff6-432f-8fbc-14bc18cece8e", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "1eb7de92-3764-45fd-8435-339af64df43d", "attributes": {}}, {"id": "c88e8e22-21e3-465a-9c04-37c887e5cc4d", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "1eb7de92-3764-45fd-8435-339af64df43d", "attributes": {}}, {"id": "eea13353-fc01-4e69-b0f1-4b2a058d89ee", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "1eb7de92-3764-45fd-8435-339af64df43d", "attributes": {}}, {"id": "f5a4b8df-1966-4b31-b46d-7810ec5605d0", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "1eb7de92-3764-45fd-8435-339af64df43d", "attributes": {}}, {"id": "4903bdba-7096-4b18-a262-3cf2af306265", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "1eb7de92-3764-45fd-8435-339af64df43d", "attributes": {}}, {"id": "cb1eb490-5bc6-4063-a643-0b09670d312f", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "1eb7de92-3764-45fd-8435-339af64df43d", "attributes": {}}, {"id": "acf037e4-3662-4dca-9e71-a351ea953313", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["manage-identity-providers", "view-clients", "view-identity-providers", "manage-realm", "manage-authorization", "impersonation", "manage-users", "manage-events", "query-clients", "create-client", "query-users", "query-groups", "view-events", "view-realm", "view-users", "query-realms", "manage-clients", "view-authorization"]}}, "clientRole": true, "containerId": "1eb7de92-3764-45fd-8435-339af64df43d", "attributes": {}}, {"id": "5552100a-770c-4e55-a9c3-742790808504", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "1eb7de92-3764-45fd-8435-339af64df43d", "attributes": {}}, {"id": "bf860e0a-cd30-405c-9626-7e90082a11b4", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "1eb7de92-3764-45fd-8435-339af64df43d", "attributes": {}}, {"id": "b34d83d0-ec48-4896-85f4-d6538e2eb24e", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "1eb7de92-3764-45fd-8435-339af64df43d", "attributes": {}}, {"id": "06ff271a-fc9f-4ab7-9bc6-acc25f12a233", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-users", "query-groups"]}}, "clientRole": true, "containerId": "1eb7de92-3764-45fd-8435-339af64df43d", "attributes": {}}, {"id": "c1698db8-3b49-41b4-b28e-ec19d127808a", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "1eb7de92-3764-45fd-8435-339af64df43d", "attributes": {}}, {"id": "********-403c-431c-84d3-63fe34503c60", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "1eb7de92-3764-45fd-8435-339af64df43d", "attributes": {}}, {"id": "8be3c2fc-6510-4a9c-b0a3-cd5554ab375a", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "1eb7de92-3764-45fd-8435-339af64df43d", "attributes": {}}], "security-admin-console": [], "admin-cli": [], "account-console": [], "broker": [{"id": "baad4c1d-f29e-4daf-b8c5-c3f7ec516583", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "84d412d0-4cc1-414b-9dab-243d2d9588f3", "attributes": {}}], "account": [{"id": "72a96150-c662-48b6-a66b-ac765e3d45ac", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "fbf3d367-4e66-4be2-a0a3-a7b7139e5e83", "attributes": {}}, {"id": "4837d0cf-93b1-42c6-8171-141ae3dda8e1", "name": "view-groups", "description": "${role_view-groups}", "composite": false, "clientRole": true, "containerId": "fbf3d367-4e66-4be2-a0a3-a7b7139e5e83", "attributes": {}}, {"id": "3cb678c4-442f-4a6c-b061-5560880ea511", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "fbf3d367-4e66-4be2-a0a3-a7b7139e5e83", "attributes": {}}, {"id": "db76f1e8-2311-4202-9634-285e73d58913", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "fbf3d367-4e66-4be2-a0a3-a7b7139e5e83", "attributes": {}}, {"id": "3f3c95a1-a9d3-4368-a27d-423634e478cc", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "fbf3d367-4e66-4be2-a0a3-a7b7139e5e83", "attributes": {}}, {"id": "63ce29df-c9d6-4a03-8a48-a7f9fd722cbb", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "fbf3d367-4e66-4be2-a0a3-a7b7139e5e83", "attributes": {}}, {"id": "c608c528-fe42-46ae-96e8-fe356d3fe328", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "fbf3d367-4e66-4be2-a0a3-a7b7139e5e83", "attributes": {}}, {"id": "e64ae25a-8c56-4e4f-a728-7e1b2cdac976", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "fbf3d367-4e66-4be2-a0a3-a7b7139e5e83", "attributes": {}}], "boss-store-frontend": []}}, "groups": [{"id": "3acd1a51-805f-44f6-b257-e0cf6799f33d", "name": "Companies", "path": "/Companies", "attributes": {}, "realmRoles": [], "clientRoles": {}, "subGroups": [{"id": "1e1e7eb3-aa73-48b2-83cb-645a5be50cd4", "name": "91ae83b9-302b-4da2-8b15-************", "path": "/Companies/91ae83b9-302b-4da2-8b15-************", "attributes": {"isBanktransferEnabled": ["true"], "communicationLanguage": ["de"], "companyName": ["Germany seller 1"], "companyStatus": ["APPROVED_COMMERCIAL"], "operationalStage": ["OPERATIONAL"], "companyWebSite": ["www.germanyseller1.de"], "licensingEmail": ["<EMAIL>"], "isEu": ["true"], "companyId": ["91ae83b9-302b-4da2-8b15-************"], "companyEmail": ["<EMAIL>"], "taxId": ["DE900005001"], "companyCountry": ["DE"], "creditLimit": ["10000.00"], "manualAppApprovalEnabled": ["true"]}, "realmRoles": [], "clientRoles": {}, "subGroups": []}, {"id": "bc4791f9-5fee-4460-8de4-610b98e9f2e2", "name": "7913618f-b0b3-49c8-8fbc-bc2992cb11f5", "path": "/Companies/7913618f-b0b3-49c8-8fbc-bc2992cb11f5", "attributes": {"isBanktransferEnabled": ["true"], "communicationLanguage": ["de"], "customerGroup": ["{  \"customizationId\": \"IDW000\",   \"id\": \"INDEPENDENT_WORKSHOP\" }"], "companyName": ["Germany Buyer 1"], "companyStatus": ["APPROVED_COMMERCIAL"], "operationalStage": ["OPERATIONAL"], "companyWebSite": ["www.germanybuyer1.de"], "licensingEmail": ["<EMAIL>"], "isEu": ["true"], "companyId": ["7913618f-b0b3-49c8-8fbc-bc2992cb11f5"], "companyEmail": ["<EMAIL>"], "taxId": ["DE900005021"], "companyCountry": ["DE"], "creditLimit": ["10000.00"], "manualAppApprovalEnabled": ["true"]}, "realmRoles": [], "clientRoles": {}, "subGroups": []}]}], "defaultRole": {"id": "4dbd1dd2-be76-4cbb-944c-78abcbef3ae1", "name": "default-roles-dcih", "description": "${role_default-roles}", "composite": true, "clientRole": false, "containerId": "8d4105de-9583-45f1-bd14-26ec658baa16"}, "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpPolicyCodeReusable": false, "otpSupportedApplications": ["totpAppFreeOTPName", "totpAppGoogleName", "totpAppMicrosoftAuthenticatorName"], "localizationTexts": {}, "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyExtraOrigins": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "webAuthnPolicyPasswordlessExtraOrigins": [], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account", "view-groups"]}]}, "clients": [{"id": "fbf3d367-4e66-4be2-a0a3-a7b7139e5e83", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/dcih/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/dcih/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "5240f8e1-89c7-47db-a88e-214f6995a656", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/dcih/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/dcih/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "88fa2a4d-6909-4880-8c76-1df6b15b96de", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "a78a3305-b340-4f07-a6d7-ff6b4178629d", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "84d412d0-4cc1-414b-9dab-243d2d9588f3", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "1eb7de92-3764-45fd-8435-339af64df43d", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "authorizationServicesEnabled": true, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"], "authorizationSettings": {"allowRemoteResourceManagement": false, "policyEnforcementMode": "ENFORCING", "resources": [], "policies": [], "scopes": [], "decisionStrategy": "UNANIMOUS"}}, {"id": "df84d383-2bdd-4c76-a88e-9424b7d301c1", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/dcih/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/admin/dcih/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "f2829a42-e07d-439a-b82f-067f57e58c1c", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "6ea6529d-74c5-44b8-aa29-bc7747a865df", "clientId": "boss-store-frontend", "rootUrl": "https://localhost:8084", "adminUrl": "https://localhost:8084/sso", "baseUrl": "https://localhost:8084/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "8631832a-88d6-4c39-b980-b3331e9bd974", "redirectUris": ["*"], "webOrigins": ["*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"saml.assertion.signature": "false", "saml.force.post.binding": "false", "saml.multivalued.roles": "false", "saml.encrypt": "false", "post.logout.redirect.uris": "+", "saml.server.signature": "false", "saml.server.signature.keyinfo.ext": "false", "exclude.session.state.from.auth.response": "false", "saml_force_name_id_format": "false", "saml.client.signature": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "d8a65db2-c417-48fd-9582-8f5207d15a22", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientId", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientId", "jsonType.label": "String"}}, {"id": "677bf0d3-3677-4074-b8ed-4fa2acce3dfe", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String"}}, {"id": "c18e5ae8-212d-4b32-aee2-f103a1938180", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "roles", "profile", "company", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "0bf10858-8b5c-4fc8-99bf-31f82beb587c", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "677062ce-a612-4633-a556-24557bef1159", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}, {"id": "9e3f7cf9-190d-49e3-9994-7f1c1d3bfca6", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}]}, {"id": "19e7b650-507d-4138-b54f-47f9464f247d", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "95e68118-10bc-49c0-8bd7-47d01a790d53", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "introspection.token.claim": "true", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "c1e623fd-2bab-4fcb-90fd-97cc68b7c359", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "149df61e-40ef-42c2-ad2a-4f2caf13915d", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String"}}, {"id": "c90ea789-bc39-42e5-93a6-7be83e722ab2", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String"}}, {"id": "51d9ab76-5805-4d8a-99d7-714d968c5628", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {"access.token.claim": "true", "introspection.token.claim": "true"}}]}, {"id": "ecf48663-da0c-4cc3-a4aa-ef3087aeba3c", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "28ec6582-c9a9-49ac-8fc8-e1edf01e6ba1", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}, {"id": "00fabc72-8f00-4204-8f67-3b668f7efc0b", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}]}, {"id": "151a792e-cf16-453c-aacf-465a5b73ba4d", "name": "acr", "description": "OpenID Connect scope for add acr (authentication context class reference) to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "56c05e47-a888-4cc9-aa50-e1db0bf844d8", "name": "acr loa level", "protocol": "openid-connect", "protocolMapper": "oidc-acr-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "introspection.token.claim": "true"}}]}, {"id": "eca87c3b-e2b0-4c47-9e8d-c59239732338", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "675fdb5e-d8e0-43cd-ae36-be3a2165f4e6", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {"access.token.claim": "true", "introspection.token.claim": "true"}}]}, {"id": "865c7544-aaec-4ef6-9c6e-1f135a9388a9", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "a771f0d3-7865-4be7-804e-50c12dee4a53", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "6893feaf-d0a5-4113-833f-4a1e48d24c26", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}]}, {"id": "b9220d2a-a6f2-4dd7-bb29-b7afcca77cee", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "491b303b-44df-439a-9892-eeb3911bc2dd", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "1983ae69-d8e5-4cbb-b416-a9ba16c65d40", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "6ecd5c38-e481-463a-8be5-b76aa3b7ace8", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "4dc5fecc-1518-4a3c-8d49-6e91f327e589", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "9c58584a-dac1-438e-9fab-809979525633", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "e9527076-365d-4f70-b1b2-9667fbac6b4b", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "40c574f2-460b-4f72-8416-8c87dffdaeb0", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "8e5e89b4-b65a-47e7-90a9-909f0d5c46c7", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "fe20e618-59cb-4736-871f-c318d09dfb52", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "21a39285-b177-4ffd-bab3-06e2e2c641ea", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "introspection.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "00f9e61e-2760-4e3b-a009-60253084603b", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "long"}}, {"id": "8bfd4370-ae82-4024-a0b1-e57662278d3e", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "fd9a52c7-cc11-45ea-bbb8-7ac7ba2a0d5f", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "762fc170-9c72-474b-b530-d29d3d654a39", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "834da596-af19-4c98-bf18-0e645b3914cb", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "4ef721da-6ab0-4eb7-bbaa-e7d2713698bb", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "e4b5c356-cbee-4443-89af-3f286b392d49", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}]}], "defaultDefaultClientScopes": ["acr", "role_list", "profile", "email", "roles", "web-origins"], "defaultOptionalClientScopes": ["microprofile-jwt", "address", "offline_access", "phone"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "referrerPolicy": "no-referrer", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [], "identityProviderMappers": [], "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "a1b2986e-a230-4a32-baa5-657139784ddb", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "c10d527c-a50b-4fc2-ad83-5089806c5210", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "f2cff48a-78d3-4d16-9a27-b86541577f30", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "40d9e20f-1df4-4d7e-9fb5-42137de30014", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "87993dc9-8dba-474f-9fbe-3b17cc9ce404", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "c53e1219-346d-4c0f-b398-8a7a2347380c", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-sha256-pairwise-sub-mapper", "oidc-address-mapper", "saml-role-list-mapper", "saml-user-property-mapper", "oidc-usermodel-attribute-mapper", "saml-user-attribute-mapper", "oidc-full-name-mapper", "oidc-usermodel-property-mapper"]}}, {"id": "2eb1cdb7-0ca1-459c-afed-2be74653df7a", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "c43131cc-d686-4121-acaa-f9fef770ddda", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-full-name-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-property-mapper", "oidc-usermodel-attribute-mapper", "saml-role-list-mapper", "saml-user-property-mapper", "oidc-address-mapper", "saml-user-attribute-mapper"]}}], "org.keycloak.keys.KeyProvider": [{"id": "b0ce1984-9e13-432b-978b-aa3f2e916b9c", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEowIBAAKCAQEAsuMydkr802PTNYjpVVXpbV0qExvOGREmZUhf3JW9GqeFxHBH0TdVF6H9DjnJIq/JLSPXSl/HWwiSZhhWAJ/omHuWyAELJejw0pEuA2X3pb2gwQssy++G1HtBZcWgUKUKk4xfiXnllhpODbAGd6NX8o1DDfPXqCnjA7I5QMooz+2gx3zVnmNiGTeFrzUDO0NFp9oIorWM0uHFfrJ3iJ6vl1M0LVI45bsPhX2bOdAv/SPvC509BuTxvjBicPPbS+XOHlyiYSqGyGGnDhouAS8Pi6haNLwmOC8b3wqNwW9syHb06IRLdP+b+W2s1RT21EY4LYhtO47iuU6LItEOrdRX4QIDAQABAoIBAAQffW3Bi7M5lUgiGDKp0L//CjW3idgFD6v874yvfNTM8KXW6qjVTCZHywPVbB/bCll6fn9N1tPvziQBQpYwGVXOX2rLr1yX0lkgFH3qjzsoNDjOz6EBq8tg7eBxHiFNtIEzFo6eGx8LCyqAmJ2QS7og7F85dAqBsKRIqs4l+qvUJeDzXRmc89Rc2PNWuPfgngRVFSgW2nCIR0ZJk7B0ec0nuNZ8e65e72h2hMofp3E7dXZWcmbqVPED4gLd6l7hckDoe8wxcQaY9nK95QUvzUD3W4/Ax53T1z/GmWU/OGWiqx2bjrYQvlMSduvLbRNztkkUvB7moZXmNoCXAEPBdsMCgYEA5i9+rBLXA7nmGkZjHrPeBshnZ5UokuY0+vHM65UKqbM3YhnjD2qEKegA2KIuahGTg7KpEe6fHIUsbIpsc6B9EVIN49JOKgTKEcDDmX0upjgyKBSYBZBxziB8us8MRky1Essqw4wnoaiPwkmZqfMrgLOzis8Opo32uY+qSxuAnmMCgYEAxvL2cT09UNhhuKkxbXO4pdc2CrHcNBNdQMBxoEihHUHTyuwlJ8+uP6BhhAeq/6XBSAuZ9tVfiI8Odr955BpHwDZcsQ3uNJcws29YfBjAMEAfP2k2yuFJBvdwuqcIn5n/ncRtj247ezymU+rsyg+pohJcQ9ocqutl/Kf/NwdmMesCgYAVsD3wOqo1uKtcqGsodmwiGXncwaocBULSrjvPqrBhgvhh7ux9BF1FkY2a29Uhu1w52XiirughCM9Bvt0vG90M571VTPh+bwsXFyGW8buWf88CBca5J34BnQADngSbDxk0lx4FZMxLKBW7ibHH2JitvMDbAF1yBghMPaQfBY1d3wKBgHySbZWCTv5sPuQtrPFn4FqXYxNXQxaoeV+uIHfIgnmbWk5ZhvpipCza87SESKJC27+M5B8Wg/cx/FCGnIAjpIHKiEek/V6YvLneIn2x0cVkoSxHaTOn1N4z2bZGGLQ27UOQVznUQBJua4doNlRfckw1SuiBjinHs9h8KOYAEBF5AoGBAMZPCNdNrzQqOJbGwP+vX2J4onRjCsXwYMi6iSsmFB2jCFwha9By4SpTWBZNNxbrBadh0mLSJMM5rr5ooF8d3O1JVrI1wwFv09Xlpq3IOTVgYR3QrYlwlVtWLRlSluNSOE0Fmx2oV9YRR8JgZENs8Kh4RuLDugSbZrs5lQXxKyEC"], "keyUse": ["SIG"], "certificate": ["MIIClzCCAX8CBgGOXD6X3zANBgkqhkiG9w0BAQsFADAPMQ0wCwYDVQQDDARkY2loMB4XDTI0MDMyMDE0MjEyOVoXDTM0MDMyMDE0MjMwOVowDzENMAsGA1UEAwwEZGNpaDCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALLjMnZK/NNj0zWI6VVV6W1dKhMbzhkRJmVIX9yVvRqnhcRwR9E3VReh/Q45ySKvyS0j10pfx1sIkmYYVgCf6Jh7lsgBCyXo8NKRLgNl96W9oMELLMvvhtR7QWXFoFClCpOMX4l55ZYaTg2wBnejV/KNQw3z16gp4wOyOUDKKM/toMd81Z5jYhk3ha81AztDRafaCKK1jNLhxX6yd4ier5dTNC1SOOW7D4V9mznQL/0j7wudPQbk8b4wYnDz20vlzh5comEqhshhpw4aLgEvD4uoWjS8JjgvG98KjcFvbMh29OiES3T/m/ltrNUU9tRGOC2IbTuO4rlOiyLRDq3UV+ECAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAP3F8B/P0wKOYqBPJpbE7WeIvZ3XuNP5Fc6jm45ccnYJMoNWK3GaWcCe3GiSsjr40LcGgCbnMny+PZCyAp6N6Y1/RGEPZ2JO2itSyFsY5sz9BTxu7gRgnW7IysU0ZLMFoEnCHMFy+fwRNWO7VcqXx1W8I4zWpdqcs9zTpHf4sg+itYwucZEU2zwBseUyE9iWtSaXaGqnlt829aBuQUrlhM7t2mGRCAFJk3guDKbFp1fyMj2rlwlV8g+AkNv5zNboXOo1V7LUp8Z6qqKhScOd/briWXJqoF0tFgdBPEqJpkdsJnL3+POF9Dd/QhHUJByISzcpG9e/YyAlLEuPOYt2dcg=="], "priority": ["100"]}}, {"id": "32cf985a-1d61-4520-b32d-a5cc5e73c967", "name": "rsa-enc-generated", "providerId": "rsa-enc-generated", "subComponents": {}, "config": {"privateKey": ["MIIEogIBAAKCAQEA11JqeNyOIgIMRNDAetIADBi0cY8fk07sbIbaIsasAT1v6r8PNwEKjADlWsQwKqukCJvZD73k8fgdYzbUswKMFG6DjthuoPALYdJ01G2KjtWkFBfcmgcZTCt0jLCyViyPL+lLBFltjsxFfZ6VCgJE71k39MVsKccI0UBAYIfHjvxAPIGkp/6EfUJG47BE7AM1tQ6uZaDAU2QMuL6Z1fRh02wEUg6LTaRN/35+PuxC8D8C98pcOb03r87l8UJhsL4jrlMjvfRM2rzgJgoOWZ45VE6ji7R1EyMcV3VBQn7A2JvcCt/fN1s7xWdAE+WvrJLhwCXwo1kpOLCIut5JeieovQIDAQABAoIBAEbAa4DsQ/3Tu3FPQLf60g4nff9EfqUaTWB5zSqZKoK7y88QWbECtBL95l81UPhsbtlvUFs2VAjYNNvZc/mgMCQ0jiD9aBiU18CcdUwp9FnrxF7f2lp4U60uKD1kgC0FtkURfNdghTnjUEAvQBzKUxlssRDKYDi4EGMloCG36/BEwv9X+pu1qDitGgq/NEy3owa5fdgfIQ6hrqBsRSiFi85psvJ6G0WuxsaH7HlXo/1DBoHPpSwB2Y5EpDYT4YkVWfHNPPn5h2dcol/6xTZKEdw7XC2w0DM9k0oQzBi87siQpRMOdcv1ZSCLXKeMcuezvWXwvTMytJ2QVMMZXRAUWGcCgYEA/92HKveHuEm8kT6T5nWW+paDznqMNTHHOWHVz4HITJjDJSDJk1CsYDYqzV39U5VaEdVLhYhO1mCifit4vKJ9fjIs+bfhybxkcAPPeG9jg00zKf7oU/I+me+q38qQVuZfwHrlzIU+QYjnZqcm/9dkJmtKNyR+Z5g/gq6/dxUV228CgYEA129s9Nb7z+yVVuyJTZ/SubuooRhnNzw7ktEQj3NT9P1ZBcE1HPxeGA5gG5fZ4i2wvSy3nY7a3kwZHI4lL6m/Y5IXi2/Ef9CjxlfpsrNnqdI9PM1d2kOaHVOuXvQqW7v5K9KaKEFjzHNnYC5fr2u6rMjUPtS6VxuhaNGc02Nb2JMCgYAMqz5fHUSGFniUxIF0PsxL+hnqply84YsYx0Rb8vQk/sdi1j3E4pC2btgttQKvGc0M5v/HS9mDeeNx1dszk+Qc1IUvlDBE2eQrO7URJIrL3I20/Z8pUOxSmPBgBlqhQuTo1ez89HgBrLKwfYRL1LFryZIyKsdrf7ldUiO+1LdxVwKBgDb7cx1RuNaqFLsohypOfeN1J2Zl6gHI15Ny0/0oe+bsucmkM8AAEKunD5Q7PxpgetjtDrehe+58EXSeOpNdiUEKSgQGNGpCf2nhif1XmD9ZLtPTvf40fqK5dqk5O45J/R/YEgvwFF5bnaiIbVdB8VSjtLeMZzpQDPPJe3LfytMNAoGAcQkLIBPpT5CXzXJSuaYeBxAYmVwwplwXMw6ZLp9bkz67faa1ZintryeKSIMducDGXUEFeLMz1gbLv8uIZNYuWLjAlH0owJva2NNEFJaSclWbnCDGIPPzE8YzYfBA/uJGHuN6tShjBWxyChiFoAJvDZ8GE3Ap5XQ9YFywBLZ9lEU="], "keyUse": ["ENC"], "certificate": ["MIIClzCCAX8CBgGOXD6YijANBgkqhkiG9w0BAQsFADAPMQ0wCwYDVQQDDARkY2loMB4XDTI0MDMyMDE0MjEyOVoXDTM0MDMyMDE0MjMwOVowDzENMAsGA1UEAwwEZGNpaDCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBANdSanjcjiICDETQwHrSAAwYtHGPH5NO7GyG2iLGrAE9b+q/DzcBCowA5VrEMCqrpAib2Q+95PH4HWM21LMCjBRug47YbqDwC2HSdNRtio7VpBQX3JoHGUwrdIywslYsjy/pSwRZbY7MRX2elQoCRO9ZN/TFbCnHCNFAQGCHx478QDyBpKf+hH1CRuOwROwDNbUOrmWgwFNkDLi+mdX0YdNsBFIOi02kTf9+fj7sQvA/AvfKXDm9N6/O5fFCYbC+I65TI730TNq84CYKDlmeOVROo4u0dRMjHFd1QUJ+wNib3Arf3zdbO8VnQBPlr6yS4cAl8KNZKTiwiLreSXonqL0CAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAKFfN/2jTT/HdSm3aNocxwsU6MfKOaBVkKZ9kOBM8tAYw90M0NmKAR94PIu8vcxexk+pHgfliLvxGEU2UcKcVx+CKJs3x/LChC0wcbb+2hTVytbZo5OniaD/O01fhPFTxfsXRtkIx3MHI/4XhNxnSd/J5uLkVjYjEcA+d+RG0s3yaVWPjBnmZV5gkaTijIQAMokurW4aYE5J2Uqj74FT0QDtLg2hDCY6NMrD7EQmBRx2YEtZbW5Lb4FSuzdYefeQEkL6McIJoEXvqVJQHxUy2fD7ZxNDLMXVvcCqoMcdpvhZrViigqMHBCAIb1NQS4rl0JH50mWQv7+pKDsUq4Qbfqw=="], "priority": ["100"], "algorithm": ["RSA-OAEP"]}}, {"id": "f1a7321f-e31f-41f7-abaa-a9b40dc747fb", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["b9bc635e-2f79-4f43-b226-731252a5d1f6"], "secret": ["Bj3YBG96PalSKimpxn6xwlhRejnFx1o_VT8_5qMZUXEBI9n1rFWH1_p3fnMAoG2iMeWcDmV5CRtYKHOCFtmG8w"], "priority": ["100"], "algorithm": ["HS256"]}}, {"id": "afbc05aa-f1b7-41e6-819c-d5d56bf6db74", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["9657382f-672d-4e2c-b060-490b9d70d9de"], "secret": ["VdUrYdlK8_vSux8TbjDOOw"], "priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "0a3d34f0-e64b-4a1b-8f9a-eba275f8d179", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false}]}, {"id": "909dd3f9-76d3-4777-977b-c77848ed9c3e", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "fd3bc01a-e257-4cc5-a7d8-b0cbafa7af81", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "c0e17fc0-f65a-47a7-aae5-5f5045ec28ef", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "616f7fd2-0e30-4310-9611-ae013523a256", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Account verification options", "userSetupAllowed": false}]}, {"id": "ed8e6e61-ae6b-4ae2-bb5c-9fc1c827edab", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "81d8004e-61b5-40ea-9a5c-1d754ba55303", "alias": "User Creation and Linking", "description": "Flow for existing/non-existing users", "providerId": "basic-flow", "topLevel": false, "builtIn": false, "authenticationExecutions": [{"authenticator": "idp-create-user-if-unique", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 0, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "idp-auto-link", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 1, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "df09852d-8edf-490a-a520-3b43943cfdb3", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false}]}, {"id": "bdaec61c-e135-426b-8af9-99afded4eb84", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false}]}, {"id": "b1eb1be5-379d-4b75-9729-fcd013a069fd", "alias": "boss first login", "description": "", "providerId": "basic-flow", "topLevel": true, "builtIn": false, "authenticationExecutions": [{"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 0, "autheticatorFlow": true, "flowAlias": "User Creation and Linking", "userSetupAllowed": false}]}, {"id": "0c9bd791-7a5d-4f25-9d4a-22ab9ce15157", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 25, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": true, "flowAlias": "forms", "userSetupAllowed": false}]}, {"id": "d3bae8d0-eb2a-446f-9f32-2343ebadd7fb", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "6b5f0516-8f3b-451a-a6b0-fd366f86aee8", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 30, "autheticatorFlow": true, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false}]}, {"id": "576dac71-42ee-4851-ba18-ee991614aa5e", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "3b873743-0d3f-41a6-a33b-4d985dd621ac", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "User creation or linking", "userSetupAllowed": false}]}, {"id": "570b6723-6939-491c-a025-5bed72f82446", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false}]}, {"id": "d2fcf3f4-7adf-41f6-95a8-2ee0df41b181", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": true, "flowAlias": "registration form", "userSetupAllowed": false}]}, {"id": "dcc35d26-07ec-48ed-af2f-01811a02e317", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-password-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 50, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 60, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "bf2a3767-9197-4568-af3c-8cd292889457", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-credential-email", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 40, "autheticatorFlow": true, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false}]}, {"id": "1c7b1516-5d1f-47d7-a525-27a053a09516", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}], "authenticatorConfig": [{"id": "269ab95f-d3ec-4197-ac0d-802ef384b85d", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "84de8a16-6c22-4f66-88e4-31c45fa8c4e7", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "TERMS_AND_CONDITIONS", "name": "Terms and Conditions", "providerId": "TERMS_AND_CONDITIONS", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "delete_account", "name": "Delete Account", "providerId": "delete_account", "enabled": false, "defaultAction": false, "priority": 60, "config": {}}, {"alias": "webauthn-register", "name": "Webauthn Register", "providerId": "webauthn-register", "enabled": true, "defaultAction": false, "priority": 70, "config": {}}, {"alias": "webauthn-register-passwordless", "name": "Webauthn Register Passwordless", "providerId": "webauthn-register-passwordless", "enabled": true, "defaultAction": false, "priority": 80, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"cibaBackchannelTokenDeliveryMode": "poll", "cibaExpiresIn": "120", "cibaAuthRequestedUserHint": "login_hint", "oauth2DeviceCodeLifespan": "600", "oauth2DevicePollingInterval": "5", "parRequestUriLifespan": "60", "cibaInterval": "5", "realmReusableOtpCode": "false"}, "keycloakVersion": "23.0.4", "userManagedAccessAllowed": false, "clientProfiles": {"profiles": []}, "clientPolicies": {"policies": []}}