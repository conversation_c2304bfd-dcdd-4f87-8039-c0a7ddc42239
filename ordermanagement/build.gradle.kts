plugins {
    id("bossstore.subproject-conventions")
    id("org.springframework.boot") version "3.4.3"
    id("com.google.cloud.tools.jib") version "3.4.4"
}

dependencies {
    implementation(project(":ordermanagement-api"))
    implementation(project(":commons"))
    implementation(project(":commons:base-webapp"))
    implementation(project(":commons:aws"))
    implementation(project(":commons:tenant"))
    implementation(project(":external-clients"))
    implementation(project(":external-clients:commercetools"))
    implementation(project(":external-clients:countries-service"))
    implementation(project(":external-clients:email-service"))
    implementation(project(":external-clients:pgw"))
    implementation(project(":external-clients:mattermost"))
    implementation(project(":external-clients:ump"))
    implementation(project(":external-clients:brim-service"))
    implementation(project(":commons:hazelcast"))

    implementation(enforcedPlatform(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES))
    implementation("org.springframework.boot:spring-boot-starter")
    implementation("org.springframework.boot:spring-boot-starter-jersey")
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation(platform("io.awspring.cloud:spring-cloud-aws-dependencies:3.3.0"))
    implementation("io.awspring.cloud:spring-cloud-aws-starter-sqs")
    implementation("io.awspring.cloud:spring-cloud-aws-starter-dynamodb")
    implementation("io.awspring.cloud:spring-cloud-aws-starter-s3")
    implementation("com.github.ben-manes.caffeine:caffeine")
    implementation("org.apache.commons:commons-text:1.13.0")
    implementation("commons-codec:commons-codec:1.18.0")
    implementation("com.google.guava:guava:33.4.0-jre")
    runtimeOnly("org.glassfish.jersey.core:jersey-common") {
        because("Otherwise it can't find a converter for the plaintext output in case of errors")
    }
    implementation("org.apache.commons:commons-collections4:4.4")

    testImplementation(testFixtures(project(":testing-awsmockup")))
    testImplementation(testFixtures(project(":testing-commons")))
    testImplementation(testFixtures(project(":external-clients:countries-service")))
    testImplementation(testFixtures(project(":external-clients:pgw")))
    testImplementation(testFixtures(project(":external-clients:ump")))
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.springframework.security:spring-security-test")
    testImplementation("io.rest-assured:rest-assured")
    testImplementation("org.wiremock.integrations:wiremock-spring-boot:3.9.0")
    testImplementation("com.tngtech.keycloakmock:mock-junit5:0.17.0")
    testImplementation("org.jeasy:easy-random-core:5.0.0")
    testImplementation("com.amazonaws:DynamoDBLocal:2.5.4") {
        exclude(group = "software.amazon.awssdk", module = "url-connection-client")
    }
}

jib {
    from.image = "gcr.io/distroless/java21-debian12"
    to.image = "${property("ecrEndpoint")}/bossstore-${project.name}:${project.version}"
}

tasks.bootTestRun {
    args = listOf("-Daws.maxAttempts=20", "--spring.profiles.active=local")
}
