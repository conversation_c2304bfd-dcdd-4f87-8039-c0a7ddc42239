package com.sast.store.ordermanagement.invoice.rest;

import com.amazonaws.util.Base64;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.ordermanagement.AbstractComponentTest;
import com.sast.store.ordermanagement.invoice.model.InvoiceEntity;
import com.sast.store.ordermanagement.util.OrdermanagementTestDataGenerator;
import com.sast.store.testing.awsmockup.junit.S3MockExtension;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import org.apache.commons.codec.digest.DigestUtils;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.file.Paths;
import java.time.LocalDate;

import static org.assertj.core.api.Assertions.assertThat;

public class InvoiceRestServiceTest extends AbstractComponentTest {

    @Autowired
    private OrdermanagementTestDataGenerator testDataGenerator;

    @Test
    void testEmptyResponse() {
        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/invoices")
            .then().statusCode(200);
    }

    @Test
    void testValidResponse() {
        testDataGenerator
            .withInvoice()
            .havingValue(i -> i.setTenant(Tenant.REXROTH))
            .havingValue(i -> i.setCompany(COMPANY_ID))
            .havingValue(i -> i.setDocumentDate(LocalDate.of(2024, 11, 1)))
            .withInvoice()
            .havingValue(i -> i.setTenant(Tenant.REXROTH))
            .havingValue(i -> i.setCompany("someOtherCompany"))
            .havingValue(i -> i.setDocumentDate(LocalDate.of(2024, 11, 2)))
            .withInvoice()
            .havingValue(i -> i.setTenant(Tenant.REXROTH))
            .havingValue(i -> i.setCompany(COMPANY_ID))
            .havingValue(i -> i.setDocumentDate(LocalDate.of(2024, 11, 3)));

        final InvoiceEntity first = testDataGenerator.getInvoices().getFirst();
        final InvoiceEntity last = testDataGenerator.getInvoices().getLast();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/invoices")
            .then().statusCode(200)
            .body(".", Matchers.hasSize(2))
            .body("[0].invoiceNumber", Matchers.equalTo(first.getDocumentNumber()))
            .body("[0].invoiceDate", Matchers.equalTo(first.getCreationDate().toString()))
            .body("[0].status", Matchers.equalTo("ISSUED"))
            .body("[0].totalAmount.value", Matchers.equalTo(first.getTotalAmounts().getGrossAmount().floatValue()))
            .body("[0].totalAmount.currencyCode", Matchers.equalTo(first.getTotalAmounts().getCurrency()))
            .body("[1].invoiceNumber", Matchers.equalTo(last.getDocumentNumber()))
            .body("[1].invoiceDate", Matchers.equalTo(last.getCreationDate().toString()))
            .body("[1].status", Matchers.equalTo("ISSUED"))
            .body("[1].totalAmount.value", Matchers.equalTo(last.getTotalAmounts().getGrossAmount().floatValue()))
            .body("[1].totalAmount.currencyCode", Matchers.equalTo(last.getTotalAmounts().getCurrency()));
    }

    @Test
    void testDownloadNotFound() {
        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.fromContentType("application/octet-stream"))
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/invoices/{documentNumber}", "1234")
            .then().statusCode(404);
    }

    @Test
    void testDownloadDifferentCompany() {
        testDataGenerator
            .withInvoice()
            .havingValue(i -> i.setTenant(Tenant.REXROTH))
            .havingValue(i -> i.setCompany("someOtherCompany"));

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.fromContentType("application/octet-stream"))
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/invoices/{documentNumber}", testDataGenerator.getInvoice().getDocumentNumber())
            .then().statusCode(404);
    }

    @Test
    void testDownload() throws Exception {
        testDataGenerator
            .withInvoice()
            .havingValue(i -> i.setTenant(Tenant.REXROTH))
            .havingValue(i -> i.setCompany(COMPANY_ID))
            .havingValue(i -> i.getS3File().setBucket("brim-service-documents-dev"));
        S3MockExtension.getMock().putContent("brim-service-documents-dev", testDataGenerator.getInvoice().getS3File().getKey(),
            "testcontent");

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.fromContentType("application/octet-stream"))
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/invoices/{documentNumber}", testDataGenerator.getInvoice().getDocumentNumber())
            .then()
            .statusCode(200)
            .header("Content-Type", "application/octet-stream")
            .body(Matchers.equalTo("testcontent"));
    }

    @Test
    void testDownloadBinary() throws Exception {
        testDataGenerator
            .withInvoice()
            .havingValue(i -> i.setTenant(Tenant.REXROTH))
            .havingValue(i -> i.setCompany(COMPANY_ID))
            .havingValue(i -> i.getS3File().setBucket("brim-service-documents-dev"));
        S3MockExtension.getMock().putContent("brim-service-documents-dev", testDataGenerator.getInvoice().getS3File().getKey(),
            Paths.get(getClass().getResource("/brim/invoice/invoice.pdf").toURI()));

        final byte[] byteArray = RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.fromContentType("application/octet-stream"))
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/invoices/{documentNumber}", testDataGenerator.getInvoice().getDocumentNumber())
            .asByteArray();

        assertThat(byteArray).hasSize(56210);
        assertThat(DigestUtils.sha256Hex(byteArray)).isEqualTo("c61679178e4793b2ae8ed9bad33511a93b3e60bf1e4ab88aef99838a786f92cd");
    }

    @Test
    void testDownload64NotFound() {
        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.fromContentType("text/plain"))
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/invoices/{documentNumber}/64", "1234")
            .then().statusCode(404);
    }

    @Test
    void testDownload64DifferentCompany() {
        testDataGenerator
            .withInvoice()
            .havingValue(i -> i.setTenant(Tenant.REXROTH))
            .havingValue(i -> i.setCompany("someOtherCompany"));

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.fromContentType("text/plain"))
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/invoices/{documentNumber}/64", testDataGenerator.getInvoice().getDocumentNumber())
            .then().statusCode(404);
    }

    @Test
    void testDownload64() throws Exception {
        testDataGenerator
            .withInvoice()
            .havingValue(i -> i.setTenant(Tenant.REXROTH))
            .havingValue(i -> i.setCompany(COMPANY_ID))
            .havingValue(i -> i.getS3File().setBucket("brim-service-documents-dev"));
        S3MockExtension.getMock().putContent("brim-service-documents-dev", testDataGenerator.getInvoice().getS3File().getKey(),
            "testcontent");

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.fromContentType("text/plain"))
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/invoices/{documentNumber}/64", testDataGenerator.getInvoice().getDocumentNumber())
            .then()
            .statusCode(200)
            .header("Content-Type", "text/plain")
            .body(Matchers.equalToCompressingWhiteSpace(Base64.encodeAsString("testcontent".getBytes())));
    }
}
