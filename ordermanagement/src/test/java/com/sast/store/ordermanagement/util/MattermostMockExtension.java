package com.sast.store.ordermanagement.util;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.github.tomakehurst.wiremock.junit5.WireMockExtension;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching;

public class MattermostMockExtension extends WireMockExtension {

    private static MattermostMockExtension instance;

    public MattermostMockExtension() {
        super(WireMockExtension
            .extensionOptions()
            .options(WireMockConfiguration.options()
                .port(34861))
            .configureStaticDsl(true));
        instance = this;
    }

    public static MattermostMockExtension get() {
        return instance;
    }

    public static MattermostMockExtension withDefaultResponse() {
        withWebhookResponse();
        withPostsResponse();
        return instance;
    }

    public static MattermostMockExtension withWebhookResponse() {
        instance.stubFor(WireMock.post(urlPathMatching("/hooks/.*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "text/plain")
                .withBody("ok")));

        return instance;
    }

    public static MattermostMockExtension withPostsResponse() {
        instance.stubFor(WireMock.post(urlPathMatching("/api/v4/posts"))
            .willReturn(aResponse()
                .withStatus(201)
                .withHeader("content-type", "application/json")
                .withBody(
                    """
                        {
                          "id": "xx54q4jdnfr97edu5hop6s7f9c",
                          "create_at": 1731576475889,
                          "update_at": 1731576475889,
                          "edit_at": 0,
                          "delete_at": 0,
                          "is_pinned": false,
                          "user_id": "rz5gb4g9tiygfe11r9gt4wphma",
                          "channel_id": "krui9pe6etf5tdt5uw6fe9ebih",
                          "root_id": "",
                          "original_id": "",
                          "message": "UPM Tester (Austria :austria:) successfully purchased 1 items [Hydraulic Hub] from seller UPM Tester in store rexroth. They bought 1 items with total price of 600 EUR and paid via SEPA_DIRECTDEBIT. The order Id is 928374",
                          "type": "",
                          "props": {
                            "from_bot": "true"
                          },
                          "hashtags": "",
                          "pending_post_id": "",
                          "reply_count": 0,
                          "last_reply_at": 0,
                          "participants": null,
                          "metadata": {}
                        }""")));

        return instance;
    }

}
