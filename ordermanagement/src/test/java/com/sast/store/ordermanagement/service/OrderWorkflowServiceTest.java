package com.sast.store.ordermanagement.service;

import com.commercetools.api.models.cart.Cart;
import com.commercetools.api.models.type.CustomFields;
import com.commercetools.api.models.type.FieldContainer;
import com.sast.store.brimtegration.apimodel.common.payment.BoschTransferAchCreditPayment;
import com.sast.store.brimtegration.apimodel.common.payment.BoschTransferSepaCreditPayment;
import com.sast.store.external.ump.test.UmpMockExtension;
import com.sast.store.ordermanagement.AbstractComponentTest;
import com.sast.store.ordermanagement.dto.AuthorizationResultDto;
import com.sast.store.ordermanagement.util.CommercetoolsMockExtension;
import com.sast.store.testing.awsmockup.junit.SnsMockExtension;
import org.json.JSONException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.RegularExpressionValueMatcher;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.skyscreamer.jsonassert.comparator.JSONComparator;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

public class OrderWorkflowServiceTest extends AbstractComponentTest {
    @Autowired
    private OrderWorkflowService orderWorkflowService;

    @AfterEach
    public void tearDown() {
        CommercetoolsMockExtension.get().resetAll();
        SnsMockExtension.resetSnsRequests();
    }

    @Test
    public void placeOrderCreatesOrderAndPublishesPlatformEvent() throws JSONException {
        CommercetoolsMockExtension.withCreateOrderResponse();
        UmpMockExtension.withDefaultResponse();

        orderWorkflowService.placeOrder(createTestCart(), createSepaAuthResult());

        final List<String> messages = SnsMockExtension.getAllPublishedMessages();
        assertThat(messages.size()).isEqualTo(1);
        JSONAssert.assertEquals(loadJson("/json/egress/order/platform-order-placed.json"),
                messages.get(0), platformMessageComparator());
    }

    @Test
    public void placeOrderCreatesOrderAndPublishesPlatformEventForBoschTransferAchPayment() throws JSONException {
        CommercetoolsMockExtension.withCreateOrderResponse();
        UmpMockExtension.withDefaultResponse();

        orderWorkflowService.placeOrder(createTestCart(), createAchAuthResult());

        final List<String> messages = SnsMockExtension.getAllPublishedMessages();
        assertThat(messages.size()).isEqualTo(1);
        JSONAssert.assertEquals(loadJson("/json/egress/order/platform-order-placed-ach.json"),
                messages.get(0), platformMessageComparator());
    }

    @Test
    public void placeOrderCreatesOrderAndPublishesPlatformEventWithAddons() throws JSONException {
        CommercetoolsMockExtension.withCreateOrderWithAddonsResponse();
        UmpMockExtension.withDefaultResponse();

        orderWorkflowService.placeOrder(createTestCart(), createSepaAuthResult());

        final List<String> messages = SnsMockExtension.getAllPublishedMessages();
        assertThat(messages.size()).isEqualTo(1);
        JSONAssert.assertEquals(loadJson("/json/egress/order/platform-order-placed-addons.json"),
                messages.get(0), platformMessageComparator());
    }

    @Test
    public void placeOrderDoesNotPublishEventWhenOrderCreationFails() {
        CommercetoolsMockExtension.withCreateOrderFailure();
        UmpMockExtension.withDefaultResponse();

        assertThatThrownBy(() -> orderWorkflowService.placeOrder(createTestCart(), createSepaAuthResult()));

        final List<String> messages = SnsMockExtension.getAllPublishedMessages();
        assertThat(messages.size()).isEqualTo(0);
    }

    @Test
    public void placeOrderThrowsForNullCart() {
        assertThatThrownBy(() -> orderWorkflowService.placeOrder(null, createSepaAuthResult()))
                .isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void placeOrderThrowsForNullAuthResult() {
        assertThatThrownBy(() -> orderWorkflowService.placeOrder(createTestCart(), null))
                .isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void placeOrderThrowsForInvalidCart() {
        CommercetoolsMockExtension.withCreateOrderResponse();
        UmpMockExtension.withDefaultResponse();
        assertThatThrownBy(() -> orderWorkflowService.placeOrder(createTestCart(101L), createSepaAuthResult()))
                .isInstanceOf(IllegalStateException.class);
    }

    private static Cart createTestCart() {
        return createTestCart(1L);

    }

    private static Cart createTestCart(final long totalQuantity) {
        return Cart.builder()
                .version(1L)
                .id("fooCart1")
                .key("fooCart1Key")
                .lineItems(List.of())
                .totalLineItemQuantity(totalQuantity)
                .custom(CustomFields.builder()
                        .fields(FieldContainer.builder()
                                .addValue("tenant", "rexroth")
                                .addValue("companyId", "********-7c7e-4471-8391-4962e7bbe537")
                                .build())
                        .buildUnchecked())
                .buildUnchecked();
    }

    private static AuthorizationResultDto createSepaAuthResult() {
        return AuthorizationResultDto.builder()
                .brimPaymentData(BoschTransferSepaCreditPayment.builder()
                        .iban("**********************")
                        .bic("DEUECU79XXX")
                        .bankName("First Ferengi Interplanetary Bank")
                        .build())
                .build();
    }

    private static AuthorizationResultDto createAchAuthResult() {
        return AuthorizationResultDto.builder()
                .brimPaymentData(BoschTransferAchCreditPayment.builder()
                        .routingNumber("982374")
                        .accountNumber("********")
                        .bic("BOFAUS3N")
                        .bankName("Bank of America, New York")
                        .build())
                .build();
    }

    public static JSONComparator platformMessageComparator() {
        return new CustomComparator(JSONCompareMode.NON_EXTENSIBLE,
                new Customization("data.orderNumber", new RegularExpressionValueMatcher<>("LRX_OR_\\d+")),
                new Customization("header.timestamp", new RegularExpressionValueMatcher<>("\\S+")),
                new Customization("header.id.eventId", new RegularExpressionValueMatcher<>("\\S+")),
                new Customization("header.cause.eventId", new RegularExpressionValueMatcher<>("\\S+")));
    }
}
