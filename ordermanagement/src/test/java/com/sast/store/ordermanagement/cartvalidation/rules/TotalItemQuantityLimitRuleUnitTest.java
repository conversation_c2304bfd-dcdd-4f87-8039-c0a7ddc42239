package com.sast.store.ordermanagement.cartvalidation.rules;

import com.commercetools.api.models.cart.Cart;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.gen.client.ump.api.UmpExternalCompanyDto;
import com.sast.store.ordermanagement.cartvalidation.CartErrorCode;
import com.sast.store.ordermanagement.cartvalidation.CartValidationContext;
import com.sast.store.ordermanagement.cartvalidation.CartValidationViolation;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TotalItemQuantityLimitRuleUnitTest {
    private TotalItemQuantityLimitRule totalItemQuantityLimitRule;

    @Mock
    private Cart mockCart;

    private CartValidationContext cartValidationContext;

    @BeforeEach
    void setUp() {
        totalItemQuantityLimitRule = new TotalItemQuantityLimitRule();
        cartValidationContext = CartValidationContext.builder()
                .tenant(Tenant.REXROTH)
                .umpCompany(new UmpExternalCompanyDto())
                .build();
    }

    @Test
    void nullTotalQuantityPassesValidation() {
        when(mockCart.getTotalLineItemQuantity()).thenReturn(null);

        final Set<CartValidationViolation> actualViolations = totalItemQuantityLimitRule
                .apply(mockCart, cartValidationContext);

        assertThat(actualViolations).isEmpty();
    }

    @Test
    void totalQuantityAtLimitPassesValidation() {
        when(mockCart.getTotalLineItemQuantity()).thenReturn(100L);

        final Set<CartValidationViolation> actualViolations = totalItemQuantityLimitRule
                .apply(mockCart, cartValidationContext);

        assertThat(actualViolations).isEmpty();
    }

    @Test
    void totalQuantityAboveLimitFailsValidation() {
        when(mockCart.getTotalLineItemQuantity()).thenReturn(101L);

        final Set<CartValidationViolation> actualViolations = totalItemQuantityLimitRule
                .apply(mockCart, cartValidationContext);

        assertThat(actualViolations).hasSize(1)
                .first().extracting(CartValidationViolation::error)
                .isEqualTo(CartErrorCode.TOTAL_QUANTITY_LIMIT_EXCEEDED);
    }
}
