package com.sast.store.ordermanagement.util;

import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.RegularExpressionValueMatcher;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.skyscreamer.jsonassert.comparator.JSONComparator;

public final class JsonComparatorFactory {
    private JsonComparatorFactory() {
        // noop for util class
    }

    public static JSONComparator emailMessageComparator() {
        return new CustomComparator(JSONCompareMode.LENIENT,
                new Customization("messageId", new RegularExpressionValueMatcher<>("\\S+")));
    }
}
