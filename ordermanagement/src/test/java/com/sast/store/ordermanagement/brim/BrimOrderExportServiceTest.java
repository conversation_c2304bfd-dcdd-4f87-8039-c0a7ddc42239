package com.sast.store.ordermanagement.brim;

import com.commercetools.api.models.cart.LineItem;
import com.commercetools.api.models.common.CentPrecisionMoneyBuilder;
import com.commercetools.api.models.common.Price;
import com.commercetools.api.models.common.TypedMoney;
import com.commercetools.api.models.order.Order;
import com.commercetools.api.models.product.Attribute;
import com.commercetools.api.models.product.AttributeAccess;
import com.commercetools.api.models.product.ProductVariant;
import com.commercetools.api.models.product_type.AttributePlainEnumValue;
import com.commercetools.api.models.type.CustomFields;
import com.commercetools.api.models.type.FieldContainer;
import com.sast.store.brimtegration.apimodel.common.payment.PaymentData;
import com.sast.store.brimtegration.apimodel.events.egress.order.data.PlatformOrderItem;
import com.sast.store.brimtegration.apimodel.events.egress.order.data.PlatformOrderPlaced;
import com.sast.store.external.brim.BrimMessagePublisher;
import com.sast.store.ordermanagement.AbstractComponentTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import static java.time.temporal.ChronoUnit.DAYS;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class BrimOrderExportServiceTest extends AbstractComponentTest {

    @Mock
    private BrimMessagePublisher brimMessagePublisher;

    @InjectMocks
    private BrimOrderExportService brimOrderExportService;

    @Mock
    private Order order;

    @Mock
    private LineItem lineItem;

    @Mock
    private ProductVariant productVariant;

    @Mock
    private PaymentData paymentData;

    @Mock
    private Price price;

    @BeforeEach
    void setup() {
        when(order.getOrderNumber()).thenReturn("orderNumber");
        when(order.getCreatedAt()).thenReturn(ZonedDateTime.now());
        when(order.getCustom()).thenReturn(CustomFields.builder()
                .fields(FieldContainer.builder()
                    .addValue("companyId", "companyId")
                    .build())
            .buildUnchecked());
        when(order.getLineItems()).thenReturn(List.of(lineItem));

        when(lineItem.getVariant()).thenReturn(productVariant);
        when(lineItem.getPrice()).thenReturn(Price.builder()
                .value(TypedMoney.centPrecisionBuilder()
                    .centAmount(100L)
                    .fractionDigits(2)
                    .buildUnchecked())
            .buildUnchecked());

        when(productVariant.getSku()).thenReturn("sku");
        when(lineItem.getPrice()).thenReturn(price);
        when(price.getKey()).thenReturn("aPriceKey");
        when(price.getValue()).thenReturn(CentPrecisionMoneyBuilder.of().centAmount(1234L)
                .fractionDigits(2).currencyCode("EUR").buildUnchecked());
    }


    @Test
    void exportOrder_withoutContractStart_doesNotExportRequestedContractStart() {
        brimOrderExportService.exportOrder(order, paymentData);

        final var orderPlacedCaptor = ArgumentCaptor.forClass(PlatformOrderPlaced.class);
        verify(brimMessagePublisher).publishPlatformOrderEvent(any(), orderPlacedCaptor.capture());

        final var orderPlaced = orderPlacedCaptor.getValue();
        assertThat(orderPlaced.items())
            .hasSize(1)
            .extracting(PlatformOrderItem::requestedContractStart)
            .containsExactly((ZonedDateTime) null);
    }

    @Test
    void exportOrder_withContractStartNextFirst_exportsRequestedContractStart() {
        when(productVariant.findAttributeByName("contractStart"))
            .thenReturn(Optional.of(AttributeAccess.of(Attribute.builder()
                .name("contractStart")
                .value(AttributePlainEnumValue.builder()
                    .key("NEXT_FIRST")
                    .buildUnchecked())
                .build())));

        brimOrderExportService.exportOrder(order, paymentData);

        final var orderPlacedCaptor = ArgumentCaptor.forClass(PlatformOrderPlaced.class);
        verify(brimMessagePublisher).publishPlatformOrderEvent(any(), orderPlacedCaptor.capture());

        final var orderPlaced = orderPlacedCaptor.getValue();
        assertThat(orderPlaced.items())
            .hasSize(1)
            .extracting(PlatformOrderItem::requestedContractStart)
            .containsExactly(ZonedDateTime.now(ZoneOffset.UTC).plusMonths(1).withDayOfMonth(1).truncatedTo(DAYS));
    }

    @Test
    void exportOrder_exportsPriceKeyToBrim() {
        brimOrderExportService.exportOrder(order, paymentData);

        final var orderPlacedCaptor = ArgumentCaptor.forClass(PlatformOrderPlaced.class);
        verify(brimMessagePublisher).publishPlatformOrderEvent(any(), orderPlacedCaptor.capture());

        final var orderPlaced = orderPlacedCaptor.getValue();
        assertThat(orderPlaced.items())
                .hasSize(1)
                .extracting(PlatformOrderItem::priceStorefrontId)
                .containsExactly("aPriceKey");
    }

}
