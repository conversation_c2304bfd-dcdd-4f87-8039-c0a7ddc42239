package com.sast.store.ordermanagement.rest;

import com.sast.store.ordermanagement.AbstractIntegrationTest;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import org.junit.jupiter.api.Test;

import static org.hamcrest.Matchers.equalTo;

class PaymentRestServiceIntegrationTest extends AbstractIntegrationTest {

    @Test
    void testGetPaymentExisting() {
        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/payments/{paymentId}", "8aaa8277-fd5b-4bd6-b0ff-dbb261d907b1")
            .then().statusCode(200)
            .body("paymentId", equalTo("8aaa8277-fd5b-4bd6-b0ff-dbb261d907b1"))
            .body("paymentStatus", equalTo("SUCCESS"))
            .body("orderId", equalTo("0334895531464"));
    }
}
