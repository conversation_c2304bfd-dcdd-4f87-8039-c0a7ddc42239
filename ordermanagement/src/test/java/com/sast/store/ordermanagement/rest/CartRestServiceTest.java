package com.sast.store.ordermanagement.rest;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.verification.LoggedRequest;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.sast.store.external.countriesservice.util.CountriesServiceMockExtension;
import com.sast.store.external.pgw.test.PgwMockExtension;
import com.sast.store.external.ump.test.UmpMockExtension;
import com.sast.store.ordermanagement.AbstractComponentTest;
import com.sast.store.ordermanagement.service.ProductTypeService;
import com.sast.store.ordermanagement.util.CommercetoolsMockExtension;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static com.github.tomakehurst.wiremock.client.WireMock.absent;
import static com.github.tomakehurst.wiremock.client.WireMock.matchingJsonPath;
import static com.github.tomakehurst.wiremock.client.WireMock.not;
import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;

class CartRestServiceTest extends AbstractComponentTest {

    @Inject
    private ProductTypeService productTypeService;

    @BeforeEach
    void setUp() {
        CommercetoolsMockExtension.withGetTypesResponse();
        productTypeService.initializeCache();
    }

    @Test
    void testGetCart() {
        UmpMockExtension.withDefaultResponse();
        CommercetoolsMockExtension.withCartsNonEmptyResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/cart")
            .then().statusCode(200)
            .body("id", equalTo("d0418fba-8f51-4eca-9842-03887a323b8c"))
            .body("lineItems", hasSize(1))
            .body("lineItems[0].lineItemId", equalTo("6364000b-ef57-4908-9dbb-02dc3bcd27ae"))
            .body("lineItems[0].name", equalTo("Hydraulic Hub"))
            .body("lineItems[0].productId", equalTo("6d8ab4be-e24b-473b-a6a5-f1228380b86c"))
            .body("lineItems[0].sku", equalTo("DDCIH_hydraulic_hub_yearly"))
            .body("lineItems[0].variant.name", equalTo("Jährlich"))
            .body("lineItems[0].variant.licenseType", equalTo("SUBSCRIPTION"))
            .body("lineItems[0].variant.sku", equalTo("DDCIH_hydraulic_hub_yearly"))
            .body("lineItems[0].variant.runtime", equalTo("P1Y"))
            .body("lineItems[0].variant.agreements", hasSize(2))
            .body("lineItems[0].variant.agreements[0].name", equalTo("Device Management T&C"))
            .body("lineItems[0].variant.agreements[0].linkType", equalTo("SOFTWARE_LICENSE"))
            .body("lineItems[0].variant.agreements[0].url",
                equalTo("https://dc-mkt-prod.cloud.bosch.tech/xrm/media/global/documents/legal_notice/tc_devicemanagement_saas.pdf"))
            .body("lineItems[0].variant.agreements[1].name", equalTo("Data Management T&C"))
            .body("lineItems[0].variant.agreements[1].linkType", equalTo("SOFTWARE_LICENSE"))
            .body("lineItems[0].variant.agreements[1].url",
                equalTo(
                    "https://dc-mkt-prod.cloud.bosch.tech/xrm/media/global/documents/legal_notice/tc_for_data_management_services.pdf"))
            .body("lineItems[0].itemPrice.value", equalTo(200.0f))
            .body("lineItems[0].itemPrice.currencyCode", equalTo("EUR"))
            .body("lineItems[0].quantity", equalTo(3))
            .body("lineItems[0].totalPrice.value", equalTo(600.0f))
            .body("lineItems[0].totalPrice.currencyCode", equalTo("EUR"))
            .body("addons", hasSize(0))
            .body("totalPrice.value", equalTo(600.0f))
            .body("totalPrice.currencyCode", equalTo("EUR"))
            .body("sellerCompany.name", equalTo("UPM Tester"))
            .body("billingAddress.city", equalTo("Linz"))
            .body("billingAddress.postalCode", equalTo("4010"))
            .body("billingAddress.streetName", equalTo("Bistdudeppertgassl"))
            .body("billingAddress.streetNumber", equalTo("12"))
            .body("billingAddress.state", equalTo("Oberösterreich"))
            .body("billingAddress.region", equalTo("Linz-Land"))
            .body("billingAddress.country", equalTo("AT"))
            .body("billingAddress.email", equalTo("<EMAIL>"));
    }

    @Test
    void testAddToCart() {
        UmpMockExtension.withDefaultResponse();
        CommercetoolsMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .body("""
                {
                    "sku": "DDCIH_hydraulic_hub_yearly",
                    "quantity": 1
                }
                """)
            .header("X-Tenant", "rexroth")
            .when().post(host + "/rest/cart")
            .then().statusCode(200)
            .body("id", notNullValue());

        CommercetoolsMockExtension.get().verify(1,
            postRequestedFor(urlMatching(".*/carts/key=78e5c8e3-7567-497d-b8e8-28c2f0c46165"))
                .withRequestBody(matchingJsonPath("actions[0].action", WireMock.equalTo("addLineItem")))
                .withRequestBody(matchingJsonPath("actions[0].sku", WireMock.equalTo("DDCIH_hydraulic_hub_yearly")))
                .withRequestBody(matchingJsonPath("actions[0].quantity", WireMock.equalTo("1"))));
    }

    @Test
    void testAddToCartWillMergeItems() {
        UmpMockExtension.withDefaultResponse();
        CommercetoolsMockExtension.withDefaultResponse()
            .withCartsNonEmptyResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .body("""
                {
                    "sku": "DDCIH_hydraulic_hub_yearly",
                    "quantity": 1
                }
                """)
            .header("X-Tenant", "rexroth")
            .when().post(host + "/rest/cart")
            .then().statusCode(200)
            .body("id", notNullValue());

        CommercetoolsMockExtension.get().verify(1,
            postRequestedFor(urlMatching(".*/carts/key=d0418fba-8f51-4eca-9842-03887a323b8c"))
                .withRequestBody(matchingJsonPath("actions[0].action", WireMock.equalTo("changeLineItemQuantity")))
                .withRequestBody(matchingJsonPath("actions[0].lineItemId", WireMock.equalTo("6364000b-ef57-4908-9dbb-02dc3bcd27ae")))
                .withRequestBody(matchingJsonPath("actions[0].quantity", WireMock.equalTo("4"))));
    }

    @Test
    void testUpdateCart() {
        UmpMockExtension.withDefaultResponse();
        CommercetoolsMockExtension
            .withDefaultResponse()
            .withCartsNonEmptyResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .body("""
                {
                    "lineItemId": "6364000b-ef57-4908-9dbb-02dc3bcd27ae",
                    "quantity": 3
                }
                """)
            .header("X-Tenant", "rexroth")
            .when().put(host + "/rest/cart")
            .then().statusCode(200)
            .body("id", notNullValue());

        CommercetoolsMockExtension.get().verify(1,
            postRequestedFor(urlMatching(".*/carts/key=d0418fba-8f51-4eca-9842-03887a323b8c"))
                .withRequestBody(matchingJsonPath("actions[0].action", WireMock.equalTo("changeLineItemQuantity")))
                .withRequestBody(matchingJsonPath("actions[0].lineItemId", WireMock.equalTo("6364000b-ef57-4908-9dbb-02dc3bcd27ae")))
                .withRequestBody(matchingJsonPath("actions[0].quantity", WireMock.equalTo("3"))));
    }

    @Test
    void testUpdateCartValidationFailed() {
        UmpMockExtension.withDefaultResponse();
        CommercetoolsMockExtension
            .withDefaultResponse()
            .withCartsNonEmptyResponse()
            .withChangeLineItemQuantityFailedResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .body("""
                {
                    "lineItemId": "6364000b-ef57-4908-9dbb-02dc3bcd27ae",
                    "quantity": 3
                }
                """)
            .header("X-Tenant", "rexroth")
            .when().put(host + "/rest/cart")
            .then().statusCode(400)
            .body("errorMessage", equalTo("Cart update rejected"))
            .body("errorCode", equalTo("CLIENT_ERROR"));

        CommercetoolsMockExtension.get().verify(1, postRequestedFor(urlMatching(".*/carts/key=d0418fba-8f51-4eca-9842-03887a323b8c")));
    }

    @Test
    void testUpdateCartValidationTimeout() {
        UmpMockExtension.withDefaultResponse();
        CommercetoolsMockExtension
            .withDefaultResponse()
            .withCartsNonEmptyResponse()
            .withChangeLineItemQuantityTimeoutResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .body("""
                {
                    "lineItemId": "6364000b-ef57-4908-9dbb-02dc3bcd27ae",
                    "quantity": 3
                }
                """)
            .header("X-Tenant", "rexroth")
            .when().put(host + "/rest/cart")
            .then().statusCode(503)
            .body("errorMessage", equalTo("Service Unavailable"))
            .body("errorCode", equalTo("SERVICE_UNAVAILABLE"));

        CommercetoolsMockExtension.get().verify(2, postRequestedFor(urlMatching(".*/carts/key=d0418fba-8f51-4eca-9842-03887a323b8c")));
    }

    @Test
    void testUpdateCartTooMuch() {
        UmpMockExtension.withDefaultResponse();
        CommercetoolsMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .body("""
                {
                    "sku": "DDCIH_hydraulic_hub_yearly",
                    "quantity": 101
                }
                """)
            .header("X-Tenant", "rexroth")
            .when().put(host + "/rest/cart")
            .then().statusCode(400).contentType(ContentType.JSON);
    }

    @Test
    void testClearCart() {
        UmpMockExtension.withDefaultResponse();
        CommercetoolsMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().delete(host + "/rest/cart?countryCode=DE")
            .then().statusCode(204);
    }

    @Test
    void testCheckoutCartBoschTransfer() {
        UmpMockExtension.withDefaultResponse();
        PgwMockExtension.withDefaultResponse();
        CountriesServiceMockExtension.withDefaultResponse();
        CommercetoolsMockExtension
            .withDefaultResponse()
            .withCartsNonEmptyResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/cart/checkout?paymentMethodId=BOSCH_TRANSFER/SEPA_CREDIT")
            .then().statusCode(200)
            .body("redirectUrl", nullValue())
            .body("paymentId", notNullValue());
    }

    @Test
    void testCheckoutWithNotes() {
        UmpMockExtension.withDefaultResponse();
        PgwMockExtension.withDefaultResponse();
        CountriesServiceMockExtension.withDefaultResponse();
        CommercetoolsMockExtension
            .withDefaultResponse()
            .withCartsNonEmptyResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .body("""
                {
                    "paymentMethodId": "BOSCH_TRANSFER/SEPA_CREDIT",
                    "notes": [
                        "notes line 1",
                        "notes line 2"
                        ]
                }
                """)
            .header("X-Tenant", "rexroth")
            .when().post(host + "/rest/cart/checkout")
            .then().statusCode(200)
            .body("redirectUrl", nullValue())
            .body("paymentId", notNullValue());
    }

    @Test
    void testCheckoutOneLine() {
        UmpMockExtension.withDefaultResponse();
        PgwMockExtension.withDefaultResponse();
        CountriesServiceMockExtension.withDefaultResponse();
        CommercetoolsMockExtension
            .withDefaultResponse()
            .withCartsNonEmptyResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .body("""
                {
                    "paymentMethodId": "BOSCH_TRANSFER/SEPA_CREDIT",
                    "notes": [
                        "notes line 1"
                        ]
                }
                """)
            .header("X-Tenant", "rexroth")
            .when().post(host + "/rest/cart/checkout")
            .then().statusCode(200);
    }

    @Test
    void testCheckoutWithNotesValidationError() {
        UmpMockExtension.withDefaultResponse();
        PgwMockExtension.withDefaultResponse();
        CountriesServiceMockExtension.withDefaultResponse();
        CommercetoolsMockExtension
            .withDefaultResponse()
            .withCartsNonEmptyResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .body("""
                {
                    "paymentMethodId": "BOSCH_TRANSFER/SEPA_CREDIT",
                    "notes": [
                        "notes line 1",
                        "somethingwithmorethan50characterssomethingwithmorethan50characterssomethingwithmorethan50characters"
                        ]
                }
                """)
            .header("X-Tenant", "rexroth")
            .when().post(host + "/rest/cart/checkout")
            .then().statusCode(400).contentType(ContentType.JSON);
    }

    @Test
    void testAddToCartWithAddon() {
        UmpMockExtension.withDefaultResponse();
        CommercetoolsMockExtension.withDefaultResponse();
        CommercetoolsMockExtension.withCartAddLineItemWithAddonsResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .body("""
                {
                    "sku": "DRX_PR_BODAS_COMPACT",
                    "quantity": 1,
                    "addons": [
                        {
                            "sku": "DRX_PR_BODAS_CONNECTION_50MB",
                            "quantity": 1
                        }
                    ]
                }
                """)
            .header("X-Tenant", "rexroth")
            .when().post(host + "/rest/cart")
            .then().statusCode(200)
            .body("id", equalTo("ca49c858-eed5-4afc-8052-3e27b46ff13e"))
            .body("lineItems[0].lineItemId", equalTo("2baee68c-eefa-41a4-9094-5ff2a8150876"))
            .body("lineItems[0].variant.sku", equalTo("DRX_PR_BODAS_COMPACT"))
            .body("addons[0].addonLineItemId", equalTo("fc66ca6d-ea09-442b-b39a-b5e91fd84ca1"))
            .body("addons[0].parentLineItemId", equalTo("2baee68c-eefa-41a4-9094-5ff2a8150876"))
            .body("addons[0].addonVariant.sku", equalTo("DRX_PR_BODAS_CONNECTION_50MB"));

        CommercetoolsMockExtension.get().verify(1, postRequestedFor(urlMatching(".*/carts")));

        CommercetoolsMockExtension.get().verify(1,
            postRequestedFor(urlMatching(".*/carts/key=78e5c8e3-7567-497d-b8e8-28c2f0c46165"))
                .withRequestBody(matchingJsonPath("actions[0].action", WireMock.equalTo("addLineItem")))
                .withRequestBody(matchingJsonPath("actions[0].sku", WireMock.equalTo("DRX_PR_BODAS_COMPACT")))
                .withRequestBody(matchingJsonPath("actions[0].key", not(absent())))
                .withRequestBody(matchingJsonPath("actions[0].quantity", WireMock.equalTo("1")))
                .withRequestBody(matchingJsonPath("actions[0].custom.fields.addonItemKeys", not(absent())))
                .withRequestBody(matchingJsonPath("actions[1].sku", WireMock.equalTo("DRX_PR_BODAS_CONNECTION_50MB")))
                .withRequestBody(matchingJsonPath("actions[1].key", not(absent())))
                .withRequestBody(matchingJsonPath("actions[1].quantity", WireMock.equalTo("1")))
                .withRequestBody(matchingJsonPath("actions[1].custom.fields.parentLineItemKey", not(absent()))));

        final List<LoggedRequest> commercetoolsRequest = CommercetoolsMockExtension.get()
            .findAll(postRequestedFor(urlMatching(".*/carts/key=78e5c8e3-7567-497d-b8e8-28c2f0c46165")));
        final DocumentContext documentContext = JsonPath.parse(commercetoolsRequest.get(0).getBodyAsString());

        assertThat((String) documentContext.read("$.actions[0].key"))
            .isEqualTo(documentContext.read("$.actions[1].custom.fields.parentLineItemKey"));
        assertThat((String) documentContext.read("$.actions[1].key"))
            .isEqualTo(documentContext.read("$.actions[0].custom.fields.addonItemKeys[0]"));
    }

    @Test
    void testAddToCartWithAddonsWillNotMergeItems() {
        UmpMockExtension.withDefaultResponse();
        CommercetoolsMockExtension.withDefaultResponse()
            .withCartsWithAddonsResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .body("""
                {
                    "sku": "DRX_PR_BODAS_COMPACT",
                    "quantity": 1
                }
                """)
            .header("X-Tenant", "rexroth")
            .when().post(host + "/rest/cart")
            .then().statusCode(200)
            .body("id", notNullValue());

        CommercetoolsMockExtension.get().verify(1,
            postRequestedFor(urlMatching(".*/carts/key=ca49c858-eed5-4afc-8052-3e27b46ff13e"))
                .withRequestBody(matchingJsonPath("actions[0].action", WireMock.equalTo("addLineItem")))
                .withRequestBody(matchingJsonPath("actions[0].sku", WireMock.equalTo("DRX_PR_BODAS_COMPACT")))
                .withRequestBody(matchingJsonPath("actions[0].quantity", WireMock.equalTo("1"))));
    }

    @Test
    void testRemoveItemFromCartWithAddonsWillRemoveAddonsAsWell() {
        UmpMockExtension.withDefaultResponse();
        CommercetoolsMockExtension.withDefaultResponse()
            .withCartsWithAddonsResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .body("""
                {
                    "lineItemId": "2baee68c-eefa-41a4-9094-5ff2a8150876",
                    "quantity": 0
                }
                """)
            .header("X-Tenant", "rexroth")
            .when().put(host + "/rest/cart")
            .then().statusCode(200)
            .body("id", notNullValue());

        CommercetoolsMockExtension.get().verify(1,
            postRequestedFor(urlMatching(".*/carts/key=ca49c858-eed5-4afc-8052-3e27b46ff13e"))
                .withRequestBody(matchingJsonPath("actions[0].action", WireMock.equalTo("removeLineItem")))
                .withRequestBody(matchingJsonPath("actions[0].lineItemId", WireMock.equalTo("fc66ca6d-ea09-442b-b39a-b5e91fd84ca1")))
                .withRequestBody(matchingJsonPath("actions[1].action", WireMock.equalTo("changeLineItemQuantity")))
                .withRequestBody(matchingJsonPath("actions[1].lineItemId", WireMock.equalTo("2baee68c-eefa-41a4-9094-5ff2a8150876")))
                .withRequestBody(matchingJsonPath("actions[1].quantity", WireMock.equalTo("0"))));
    }

    @Test
    void testGetCartWithAddons() {
        UmpMockExtension.withDefaultResponse();
        CommercetoolsMockExtension.withCartsWithAddonsResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/cart?countryCode=AT")
            .then().statusCode(200)
            .body("id", equalTo("ca49c858-eed5-4afc-8052-3e27b46ff13e"))
            .body("lineItems", hasSize(1))
            .body("lineItems[0].lineItemId", equalTo("2baee68c-eefa-41a4-9094-5ff2a8150876"))
            .body("lineItems[0].name", equalTo("RCU Serie 10 & 20"))
            .body("lineItems[0].productId", equalTo("5abd3673-2110-4aa0-bbf9-c97cd1e24c89"))
            .body("lineItems[0].variant.licenseType", equalTo("CONSUMPTION"))
            .body("lineItems[0].variant.sku", equalTo("DRX_PR_BODAS_COMPACT"))
            .body("addons", hasSize(1))
            .body("addons[0].addonLineItemId", equalTo("fc66ca6d-ea09-442b-b39a-b5e91fd84ca1"))
            .body("addons[0].parentLineItemId", equalTo("2baee68c-eefa-41a4-9094-5ff2a8150876"))
            .body("addons[0].addonVariant.sku", equalTo("DRX_PR_BODAS_CONNECTION_50MB"))
            .body("addons[0].addonVariant.name", equalTo("50 MB"))
            .body("addons[0].itemPrice.value", equalTo(0.0f))
            .body("addons[0].itemPrice.currencyCode", equalTo("EUR"))
            .body("addons[0].totalPrice.value", equalTo(0.0f))
            .body("addons[0].totalPrice.currencyCode", equalTo("EUR"));
    }
}
