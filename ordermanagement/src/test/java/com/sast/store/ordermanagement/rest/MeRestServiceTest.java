package com.sast.store.ordermanagement.rest;

import com.sast.store.external.ump.test.UmpMockExtension;
import com.sast.store.ordermanagement.AbstractComponentTest;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import org.junit.jupiter.api.Test;
import org.springframework.cache.CacheManager;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching;
import static org.hamcrest.Matchers.equalTo;

class MeRestServiceTest extends AbstractComponentTest {

    @Inject
    private CacheManager cacheManager;

    @Test
    void getCompany_forExistingCompany_returnsCompany() {
        UmpMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when()
            .get(host + "/rest/me/company")
            .then()
            .statusCode(200)
            .body("name", equalTo("UPM Tester"))
            .body("country", equalTo("AT"));
    }

    @Test
    void getCompany_forMissingCompany_returnsServerError() {
        // Need to clear the cache to ensure the mocked response below will be taken into account
        cacheManager.getCache("companyDetails").clear();

        UmpMockExtension.instance().stubFor(get(urlPathMatching("/internal/companies/[^/]*[/]*"))
            .willReturn(aResponse()
                .withStatus(404)));

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when()
            .get(host + "/rest/me/company")
            .then()
            .statusCode(503);
    }
}
