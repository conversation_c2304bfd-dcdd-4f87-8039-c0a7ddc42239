package com.sast.store.ordermanagement.invoice.service;

import com.sast.store.brimtegration.apimodel.common.ApiVersion;
import com.sast.store.brimtegration.apimodel.common.document.PlatformDocumentType;
import com.sast.store.brimtegration.apimodel.events.EventHeader;
import com.sast.store.brimtegration.apimodel.events.EventIdentity;
import com.sast.store.brimtegration.apimodel.events.ingress.document.data.BillingBuyerDocumentCreated;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.ordermanagement.invoice.model.InvoiceEntity;
import com.sast.store.ordermanagement.invoice.model.InvoiceRepository;
import com.sast.store.ordermanagement.invoice.util.TestDataBuilder;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InvoiceNotificationProcessorUnitTest {
    private static final String INVOICE_NUMBER = "981239";
    private static final String CREDIT_NOTE_NUMBER = "981240";

    @Mock
    private InvoiceRepository invoiceRepository;

    @Mock
    private InvoicePersistenceService invoicePersistenceService;

    @Mock
    private InvoiceEmailService invoiceEmailService;

    @InjectMocks
    private InvoiceNotificationProcessor invoiceNotificationProcessor;

    private final EventHeader header = EventHeader.builder().tenant(com.sast.store.brimtegration.apimodel.common.Tenant.REXROTH)
        .version(ApiVersion.V1)
        .id(EventIdentity.builder().eventId("123").senderName("456").build())
        .timestamp(ZonedDateTime.now())
        .build();

    @Test
    void newInvoiceNotificationIsProcessed() {
        final BillingBuyerDocumentCreated invoiceNotification = TestDataBuilder
            .invoiceNotification(builder -> builder.documentNumber(INVOICE_NUMBER));
        when(invoiceRepository.findByDocumentNumber(INVOICE_NUMBER)).thenReturn(Optional.empty());
        when(invoicePersistenceService.saveInvoice(Tenant.REXROTH, invoiceNotification)).thenReturn(new InvoiceEntity());

        invoiceNotificationProcessor.process(header, invoiceNotification);

        verify(invoicePersistenceService, times(1))
            .saveInvoice(Tenant.REXROTH, invoiceNotification);
        verify(invoiceEmailService, times(1))
            .sendInvoiceNotification(any());
    }

    @Test
    void emailExceptionsAreNotPropagated() {
        final BillingBuyerDocumentCreated invoiceNotification = TestDataBuilder
            .invoiceNotification(builder -> builder.documentNumber(INVOICE_NUMBER));
        when(invoiceRepository.findByDocumentNumber(INVOICE_NUMBER)).thenReturn(Optional.empty());
        doThrow(new RuntimeException()).when(invoiceEmailService).sendInvoiceNotification(any());
        when(invoicePersistenceService.saveInvoice(Tenant.REXROTH, invoiceNotification)).thenReturn(new InvoiceEntity());

        invoiceNotificationProcessor.process(header, invoiceNotification);

        verify(invoicePersistenceService, times(1))
            .saveInvoice(Tenant.REXROTH, invoiceNotification);
        verify(invoiceEmailService, times(1))
            .sendInvoiceNotification(any());
    }

    @Test
    void duplicateInvoiceNotificationIsIgnored() {
        final BillingBuyerDocumentCreated invoiceNotification = TestDataBuilder
            .invoiceNotification(t -> t.documentNumber(INVOICE_NUMBER));
        when(invoiceRepository.findByDocumentNumber(INVOICE_NUMBER)).thenReturn(Optional.of(new InvoiceEntity()));

        invoiceNotificationProcessor.process(header, invoiceNotification);

        verifyNoInteractions(invoicePersistenceService);
        verifyNoInteractions(invoiceEmailService);
    }

    @Test
    void newCreditNoteIsProcessed() {
        final BillingBuyerDocumentCreated creditNoteNotification = TestDataBuilder.creditNoteNotification(
            t -> t.documentType(PlatformDocumentType.CREDIT_NOTE).documentNumber(CREDIT_NOTE_NUMBER)
                .parentDocumentNumber(INVOICE_NUMBER));
        final InvoiceEntity existingInvoice = new InvoiceEntity();
        existingInvoice.setDocumentNumber(INVOICE_NUMBER);

        when(invoiceRepository.findByDocumentNumber(INVOICE_NUMBER)).thenReturn(Optional.of(existingInvoice));

        invoiceNotificationProcessor.process(header, creditNoteNotification);

        verify(invoicePersistenceService, times(1))
            .saveCreditNote(existingInvoice, creditNoteNotification);
    }

    @Test
    void duplicateCreditNoteIsIgnored() {
        final BillingBuyerDocumentCreated creditNoteNotification = TestDataBuilder.creditNoteNotification(
            t -> t.documentType(PlatformDocumentType.CREDIT_NOTE).documentNumber(CREDIT_NOTE_NUMBER)
                .parentDocumentNumber(INVOICE_NUMBER));
        final InvoiceEntity.CreditNote existingCreditNote = new InvoiceEntity.CreditNote();
        existingCreditNote.setDocumentNumber(CREDIT_NOTE_NUMBER);
        final InvoiceEntity existingInvoice = new InvoiceEntity();
        existingInvoice.setDocumentNumber(INVOICE_NUMBER);
        existingInvoice.setCreditNotes(List.of(existingCreditNote));

        when(invoiceRepository.findByDocumentNumber(INVOICE_NUMBER)).thenReturn(Optional.of(existingInvoice));

        invoiceNotificationProcessor.process(header, creditNoteNotification);

        verifyNoInteractions(invoicePersistenceService);
    }

    @Test
    void creditNoteForUnknownInvoiceThrows() {
        final BillingBuyerDocumentCreated creditNoteNotification = TestDataBuilder.creditNoteNotification(
            t -> t.documentType(PlatformDocumentType.CREDIT_NOTE).documentNumber(CREDIT_NOTE_NUMBER)
                .parentDocumentNumber(INVOICE_NUMBER));

        when(invoiceRepository.findByDocumentNumber(INVOICE_NUMBER)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> invoiceNotificationProcessor.process(header, creditNoteNotification));

        verifyNoInteractions(invoicePersistenceService);
    }
}
