package com.sast.store.ordermanagement.payment;

import com.commercetools.api.models.cart.Cart;
import com.commercetools.api.models.cart.LineItem;
import com.commercetools.api.models.common.CentPrecisionMoney;
import com.commercetools.api.models.product.Attribute;
import com.commercetools.api.models.product.ProductVariant;
import com.commercetools.api.models.product_type.AttributePlainEnumValue;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class SupportedCartPredicatesUnitTest {
    private final SupportedCartPredicates supportedCartPredicates = new SupportedCartPredicates();

    @Test
    void givenZeroTotalAmount_whenHasZeroTotalPrice_thenReturnsTrue() {
        final var cart = createValidCart();

        final var result = supportedCartPredicates.hasZeroTotalPrice(cart);

        assertThat(result)
            .isTrue();
    }

    @Test
    void givenNonZeroTotalAmount_whenSupportsCart_thenReturnsFalse() {
        final var cart = createValidCart();
        cart.getTotalPrice().setCentAmount(1L);

        final var result = supportedCartPredicates.hasZeroTotalPrice(cart);

        assertThat(result)
            .isFalse();
    }

    @Test
    void givenSubscriptionProduct_whenContainsLicenseTypeConsumption_thenReturnsFalse() {
        final var cart = createValidCart();

        final var result = supportedCartPredicates.containsLicenseTypes(cart, "CONSUMPTION");

        assertThat(result)
            .isFalse();
    }

    @Test
    void givenConsumptionProduct_whenContainsLicenseTypeConsumption_thenReturnsTrue() {
        final var cart = createValidCart();
        cart.getLineItems().getFirst().getVariant().getAttribute("licenseType")
            .setValue(AttributePlainEnumValue.builder()
                .key("CONSUMPTION")
                .buildUnchecked());

        final var result = supportedCartPredicates.containsLicenseTypes(cart, "CONSUMPTION");

        assertThat(result)
            .isTrue();
    }

    private Cart createValidCart() {
        return Cart.builder()
            .lineItems(LineItem.builder()
                .variant(ProductVariant.builder()
                    .attributes(Attribute.builder()
                        .name("licenseType")
                        .value(AttributePlainEnumValue.builder()
                            .key("SUBSCRIPTION")
                            .buildUnchecked())
                        .buildUnchecked())
                    .buildUnchecked())
                .buildUnchecked())
            .totalPrice(CentPrecisionMoney.builder()
                .centAmount(0L)
                .currencyCode("EUR")
                .fractionDigits(2)
                .buildUnchecked())
            .buildUnchecked();
    }
}
