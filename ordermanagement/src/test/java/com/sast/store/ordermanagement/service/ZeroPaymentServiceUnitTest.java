package com.sast.store.ordermanagement.service;

import com.commercetools.api.models.cart.Cart;
import com.commercetools.api.models.payment.Payment;
import com.commercetools.api.models.payment.TransactionState;
import com.sast.store.brimtegration.apimodel.common.payment.ZeroPayment;
import com.sast.store.ordermanagement.api.AuthorizationInformationDto;
import com.sast.store.ordermanagement.api.CheckoutInformationDto;
import com.sast.store.ordermanagement.dto.AuthorizationResultDto;
import com.sast.store.ordermanagement.payment.SupportedCartPredicates;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ZeroPaymentServiceUnitTest {
    private static final String SELLER_COMPANY_ID = "67bfa306-db09-41e7-a938-77d132504b1b";

    @Mock
    private CommercetoolsPaymentFactory commercetoolsPaymentFactory;

    @Mock
    private SupportedCartPredicates supportedCartPredicates;

    @Mock
    private Cart cart;

    @Mock
    private Payment payment;

    @InjectMocks
    private ZeroPaymentService zeroPaymentService;

    @Test
    void givenValidCart_whenSupportsCart_thenReturnsTrue() {
        when(supportedCartPredicates.hasZeroTotalPrice(cart)).thenReturn(true);
        when(supportedCartPredicates.containsLicenseTypes(cart, "CONSUMPTION")).thenReturn(false);

        final var result = zeroPaymentService.supportsCart(cart);

        assertThat(result)
            .isTrue();
    }

    @Test
    void givenNonZeroTotalAmount_whenSupportsCart_thenReturnsFalse() {
        when(supportedCartPredicates.hasZeroTotalPrice(cart)).thenReturn(false);

        final var result = zeroPaymentService.supportsCart(cart);

        assertThat(result)
            .isFalse();
    }

    @Test
    void givenConsumptionProduct_whenSupportsCart_thenReturnsFalse() {
        when(supportedCartPredicates.hasZeroTotalPrice(cart)).thenReturn(true);
        when(supportedCartPredicates.containsLicenseTypes(cart, "CONSUMPTION")).thenReturn(true);

        final var result = zeroPaymentService.supportsCart(cart);

        assertThat(result)
            .isFalse();
    }

    @Test
    void givenValidCart_whenPrepareCheckout_thenReturnsCorrectInformation() {
        final var result = zeroPaymentService.prepareCheckout(cart);

        assertThat(result)
            .isNotNull()
            .isEqualTo(CheckoutInformationDto.builder()
                .paymentMethodId("ZERO/ZERO")
                .build());
    }

    @Test
    void givenValidCart_whenAuthorize_thenSavesPayment() {
        when(commercetoolsPaymentFactory.createAuthorizationPayment(cart, TransactionState.SUCCESS, zeroPaymentService))
            .thenReturn(Payment.builder()
                .id("barbatz")
                .buildUnchecked());

        final var result = zeroPaymentService.authorize(cart);

        assertThat(result)
            .isNotNull()
            .isEqualTo(AuthorizationInformationDto.builder()
                .paymentId("barbatz")
                .build());
    }

    @Test
    void givenValidPayment_whenGetAuthorizationResult_thenReturnsCorrectAuthData() {
        final var result = zeroPaymentService.getAuthorizationResult(payment);

        assertThat(result)
            .isNotNull()
            .isEqualTo(AuthorizationResultDto.builder()
                .brimPaymentData(ZeroPayment.builder()
                    .build())
                .status(AuthorizationResultDto.Status.SUCCESS)
                .build());
    }
}
