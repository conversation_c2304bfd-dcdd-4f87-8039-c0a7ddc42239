package com.sast.store.ordermanagement.cartvalidation.rules;

import com.commercetools.api.models.cart.Cart;
import com.commercetools.api.models.common.CentPrecisionMoney;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.gen.client.ump.api.UmpExternalCompanyDto;
import com.sast.store.ordermanagement.cartvalidation.CartErrorCode;
import com.sast.store.ordermanagement.cartvalidation.CartValidationContext;
import com.sast.store.ordermanagement.cartvalidation.CartValidationViolation;
import com.sast.store.ordermanagement.config.AppConfiguration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
class TotalAmountLimitRuleUnitTest {
    @Mock
    private AppConfiguration appConfiguration;

    @Mock
    private AppConfiguration.Tenant tenantConfig;

    @InjectMocks
    private TotalAmountLimitRule totalAmountLimitRule;

    @Mock
    private Cart mockCart;


    private CartValidationContext cartValidationContext;

    private Map<String, BigDecimal> amountLimitMap;

    @BeforeEach
    void setup() {
        amountLimitMap = Map.of(
                "EUR", BigDecimal.valueOf(10_000L),
                "USD", BigDecimal.valueOf(5_000L)
        );
        cartValidationContext = CartValidationContext.builder()
                .tenant(Tenant.REXROTH)
                .umpCompany(new UmpExternalCompanyDto())
                .build();
    }

    @Test
    void testNullAmountPassesValidation() {
        when(mockCart.getTotalPrice()).thenReturn(null);

        final Set<CartValidationViolation> actualViolations = totalAmountLimitRule.apply(mockCart, cartValidationContext);

        assertThat(actualViolations).isEmpty();
    }

    @Test
    void testCartBelowLimitPassesValidation() {
        when(appConfiguration.tenant(Tenant.REXROTH)).thenReturn(tenantConfig);
        when(tenantConfig.cartLimitsByCurrency()).thenReturn(amountLimitMap);
        when(mockCart.getTotalPrice()).thenReturn(CentPrecisionMoney.builder()
                .currencyCode("EUR").centAmount(10_000_00L).fractionDigits(2).build());

        final Set<CartValidationViolation> actualViolations = totalAmountLimitRule.apply(mockCart, cartValidationContext);

        assertThat(actualViolations).isEmpty();
    }

    @Test
    void testCartAboveLimitFailsValidation() {
        when(appConfiguration.tenant(Tenant.REXROTH)).thenReturn(tenantConfig);
        when(tenantConfig.cartLimitsByCurrency()).thenReturn(amountLimitMap);
        when(mockCart.getTotalPrice()).thenReturn(CentPrecisionMoney.builder()
                .currencyCode("EUR").centAmount(10_000_01L).fractionDigits(2).build());

        final Set<CartValidationViolation> actualViolations = totalAmountLimitRule.apply(mockCart, cartValidationContext);

        assertThat(actualViolations).hasSize(1)
                .first().extracting(CartValidationViolation::error)
                .isEqualTo(CartErrorCode.TOTAL_AMOUNT_LIMIT_EXCEEDED);
    }

    @Test
    void testMissingTenantConfigurationCausesException() {
        when(appConfiguration.tenant(Tenant.REXROTH)).thenReturn(null);
        when(mockCart.getTotalPrice()).thenReturn(CentPrecisionMoney.builder()
                .currencyCode("EUR").centAmount(10_000_00L).fractionDigits(2).build());

        assertThatThrownBy(() -> totalAmountLimitRule.apply(mockCart, cartValidationContext))
                .isInstanceOf(IllegalStateException.class);
    }

    @Test
    void testMissingCurrencyLimitConfigurationCausesException() {
        when(appConfiguration.tenant(Tenant.REXROTH)).thenReturn(tenantConfig);
        when(tenantConfig.cartLimitsByCurrency()).thenReturn(Map.of());
        when(mockCart.getTotalPrice()).thenReturn(CentPrecisionMoney.builder()
                .currencyCode("EUR").centAmount(10_000_00L).fractionDigits(2).build());

        assertThatThrownBy(() -> totalAmountLimitRule.apply(mockCart, cartValidationContext))
                .isInstanceOf(IllegalStateException.class);
    }

}
