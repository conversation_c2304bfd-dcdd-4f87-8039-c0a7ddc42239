package com.sast.store.ordermanagement.invoice.service;

import com.sast.store.external.ump.test.UmpMockExtension;
import com.sast.store.ordermanagement.AbstractComponentTest;
import com.sast.store.ordermanagement.invoice.model.InvoiceEntity;
import com.sast.store.ordermanagement.invoice.model.InvoiceRepository;
import com.sast.store.ordermanagement.util.CommercetoolsMockExtension;
import com.sast.store.testing.awsmockup.junit.EmailUtil;
import com.sast.store.testing.awsmockup.junit.SqsMockExtension;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.json.BasicJsonTester;
import software.amazon.awssdk.services.sqs.model.Message;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

public class DocumentNotificationTest extends AbstractComponentTest {
    private static final String DOCUMENT_NUMBER = "022760010225";
    private static final String CREDIT_NOTE_NUMBER = "022760010226";

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Test
    public void processInvoiceEvent() {
        UmpMockExtension.withDefaultResponse();
        CommercetoolsMockExtension.withDefaultResponse();
        SqsMockExtension.sendMessage("/queue/bossstore-businessprocessing-invoice-events.fifo",
            loadJson("/brim/ingress/invoice-notification.json"));

        final Optional<InvoiceEntity> invoice = Awaitility.await()
            .until(() -> invoiceRepository.findByDocumentNumber(DOCUMENT_NUMBER), Optional::isPresent);
        assertThat(invoice.orElseThrow().getDocumentNumber()).isEqualTo(DOCUMENT_NUMBER);

        final List<Message> messages = EmailUtil.awaitEmails(2);

        assertThat(messages).hasSize(2);
        assertThat(messages).first().extracting(Message::body).extracting(new BasicJsonTester(getClass())::from)
            .satisfies(m -> {
                assertThat(m).extractingJsonPathStringValue("tenant")
                    .isEqualTo("rexroth");
                assertThat(m).extractingJsonPathStringValue("locale")
                    .isEqualTo("de");
                assertThat(m).extractingJsonPathStringValue("to[0]")
                    .isEqualTo("<EMAIL>");
                assertThat(m).extractingJsonPathStringValue("templateName")
                    .isEqualTo("rexroth/store/invoiceNotification");
                assertThat(m).extractingJsonPathStringValue("properties.orderNumber")
                    .isEqualTo("1000001");
                assertThat(m).extractingJsonPathStringValue("attachments[0]")
                    .isEqualTo("s3://brim-documents/LRX_BD_022760010225.pdf");
            });
        assertThat(messages).last().extracting(Message::body).extracting(new BasicJsonTester(getClass())::from)
            .satisfies(m -> {
                assertThat(m).extractingJsonPathStringValue("tenant")
                    .isEqualTo("rexroth");
                assertThat(m).extractingJsonPathStringValue("locale")
                    .isEqualTo("de");
                assertThat(m).extractingJsonPathStringValue("to[0]")
                    .isEqualTo("<EMAIL>");
                assertThat(m).extractingJsonPathStringValue("templateName")
                    .isEqualTo("rexroth/store/invoiceNotification");
                assertThat(m).extractingJsonPathStringValue("properties.orderNumber")
                    .isEqualTo("1000001");
                assertThat(m).extractingJsonPathStringValue("properties.firstName")
                    .isEqualTo("myFirstname");
                assertThat(m).extractingJsonPathStringValue("properties.lastName")
                    .isEqualTo("myLastname");
                assertThat(m).extractingJsonPathStringValue("attachments[0]")
                    .isEqualTo("s3://brim-documents/LRX_BD_022760010225.pdf");
            });
    }

    @Test
    public void processCreditNoteEvent() {
        SqsMockExtension.sendMessage("/queue/bossstore-businessprocessing-invoice-events.fifo",
            loadJson("/brim/ingress/invoice-notification.json"));

        final Optional<InvoiceEntity> invoice = Awaitility.await()
            .until(() -> invoiceRepository.findByDocumentNumber(DOCUMENT_NUMBER), Optional::isPresent);
        assertThat(invoice.get().getDocumentNumber()).isEqualTo(DOCUMENT_NUMBER);

        SqsMockExtension.sendMessage("/queue/bossstore-businessprocessing-invoice-events.fifo",
            loadJson("/brim/ingress/credit-note-notification.json"));

        final List<InvoiceEntity.CreditNote> creditNotes = Awaitility.await()
            .until(() -> invoiceRepository.findByDocumentNumber(DOCUMENT_NUMBER).orElseThrow().getCreditNotes(),
                c -> !c.isEmpty());

        assertThat(creditNotes).hasSize(1)
            .extracting(InvoiceEntity.CreditNote::getDocumentNumber)
            .containsExactly(CREDIT_NOTE_NUMBER);
    }
}
