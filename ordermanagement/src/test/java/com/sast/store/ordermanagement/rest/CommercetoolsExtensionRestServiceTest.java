package com.sast.store.ordermanagement.rest;

import com.commercetools.api.models.cart.CartBuilder;
import com.commercetools.api.models.cart.CartReferenceBuilder;
import com.commercetools.api.models.common.CentPrecisionMoneyBuilder;
import com.commercetools.api.models.extension.ExtensionAction;
import com.commercetools.api.models.extension.ExtensionInputBuilder;
import com.commercetools.api.models.type.CustomFieldsBuilder;
import com.commercetools.api.models.type.FieldContainerBuilder;
import com.sast.store.external.ump.test.UmpMockExtension;
import com.sast.store.ordermanagement.AbstractComponentTest;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.hamcrest.Matchers.equalTo;

class CommercetoolsExtensionRestServiceTest extends AbstractComponentTest {

    @Test
    public void testWrongPayload() {
        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .body("{\"resource\": \"whatever\"}")
            .when().post(host + "/rest/commercetools/cartvalidation")
            .then().statusCode(400);
    }

    @Test
    public void testUpdatePayload() {
        UmpMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .body(
                """
                    {
                        "action": "Update",
                        "resource": {
                            "typeId": "cart",
                            "id": "90909e78-c5ca-41bb-9f87-99f3f485e4d8",
                            "obj": {
                                "type": "Cart",
                                "id": "90909e78-c5ca-41bb-9f87-99f3f485e4d8",
                                "version": 3,
                                "versionModifiedAt": "2024-11-26T13:14:48.867Z",
                                "lastMessageSequenceNumber": 1,
                                "createdAt": "2024-11-22T09:22:53.283Z",
                                "lastModifiedAt": "2024-11-26T13:14:48.867Z",
                                "lastModifiedBy": {
                                    "clientId": "Dimh0TYzpM0NHPQZ5qSPUgA_",
                                    "isPlatformClient": false
                                },
                                "createdBy": {
                                    "clientId": "Dimh0TYzpM0NHPQZ5qSPUgA_",
                                    "isPlatformClient": false
                                },
                                "key": "e30a19f6-168a-46a1-8e86-24a0edc127d6",
                                "lineItems": [
                                    {
                                        "id": "2d182f2d-e8b1-48dc-a3bc-31b6ff2fe90e",
                                        "productId": "6393b78f-f6f7-4d2f-a6a1-5ddb59dca45e",
                                        "productKey": "hydraulic_hub_de",
                                        "name": {
                                            "en": "Hydraulic Hub",
                                            "de": "Hydraulic Hub DE"
                                        },
                                        "productType": {
                                            "typeId": "product-type",
                                            "id": "a46f1d35-7553-4813-8a20-993f1e84b9f4",
                                            "version": 54
                                        },
                                        "productSlug": {
                                            "en": "hydraulic-hub-de",
                                            "de": "hydraulic-hub"
                                        },
                                        "variant": {
                                            "id": 3,
                                            "sku": "DRX_PR_HUB_DE_3M",
                                            "key": "DRX_PR_HUB_DE_3M",
                                            "prices": [],
                                            "images": [],
                                            "attributes": [
                                                {
                                                    "name": "name",
                                                    "value": {
                                                        "en": "Premium - Quarterly",
                                                        "de": "Premium - Vierteljährlich"
                                                    }
                                                },
                                                {
                                                    "name": "description",
                                                    "value": {
                                                        "en": "Quarterly payment",
                                                        "de": "verlängert sich jährlich"
                                                    }
                                                },
                                                {
                                                    "name": "features",
                                                    "value": {
                                                        "en": "renews quarterly\\n1 seat\\nfeature 3",
                                                        "de": "funktionalität 1\\nfunktionalität 2"
                                                    }
                                                },
                                                {
                                                    "name": "bundleAmount",
                                                    "value": 1
                                                },
                                                {
                                                    "name": "licenseType",
                                                    "value": {
                                                        "key": "SUBSCRIPTION",
                                                        "label": "subscription product, paid at the beginning of each 'runtime' period (SUBSCRIPTION)"
                                                    }
                                                },
                                                {
                                                    "name": "runtime",
                                                    "value": "P3M"
                                                },
                                                {
                                                    "name": "noticePeriod",
                                                    "value": "P1M"
                                                },
                                                {
                                                    "name": "externalProductId",
                                                    "value": "R961015295"
                                                },
                                                {
                                                    "name": "sellerCompanyId",
                                                    "value": "f01d4e3c-ab09-4cd2-b5ff-312de81ab7bf"
                                                },
                                                {
                                                    "name": "billingName",
                                                    "value": {
                                                        "en": "Hydraulic Hub Premium",
                                                        "de": "Hydraulic Hub Premium"
                                                    }
                                                },
                                                {
                                                    "name": "entitlements",
                                                    "value": [
                                                        "LITMOS",
                                                        "DCKEYCLOAK:PREMIUM"
                                                    ]
                                                },
                                                {
                                                    "name": "agreements",
                                                    "value": [
                                                        [
                                                            {
                                                                "name": "link",
                                                                "value": {
                                                                    "en": "https://dc-mkt-prod.cloud.bosch.tech/xrm/media/global/documents/legal_notice/tc_for_data_management_services.pdf",
                                                                    "de": "https://dc-mkt-prod.cloud.bosch.tech/xrm/media/global/documents/legal_notice/tc_for_data_management_services.pdf"
                                                                }
                                                            },
                                                            {
                                                                "name": "name",
                                                                "value": {
                                                                    "en": "Data Management T&C",
                                                                    "de": "Data Management T&C"
                                                                }
                                                            }
                                                        ],
                                                        [
                                                            {
                                                                "name": "link",
                                                                "value": {
                                                                    "en": "https://dc-mkt-prod.cloud.bosch.tech/xrm/media/global/documents/legal_notice/tc_devicemanagement_saas.pdf",
                                                                    "de": "https://dc-mkt-prod.cloud.bosch.tech/xrm/media/global/documents/legal_notice/tc_devicemanagement_saas.pdf"
                                                                }
                                                            },
                                                            {
                                                                "name": "name",
                                                                "value": {
                                                                    "en": "Device Management T&C",
                                                                    "de": "Device Management T&C"
                                                                }
                                                            }
                                                        ]
                                                    ]
                                                },
                                                {
                                                    "name": "tenant",
                                                    "value": {
                                                        "key": "rexroth",
                                                        "label": "rexroth"
                                                    }
                                                },
                                                {
                                                    "name": "externalDocuments",
                                                    "value": [
                                                        [
                                                            {
                                                                "name": "link",
                                                                "value": {
                                                                    "en": "https://worksonmymachine.lol/document_en.pdf",
                                                                    "de": "https://worksonmymachine.lol/document_de.pdf"
                                                                }
                                                            },
                                                            {
                                                                "name": "name",
                                                                "value": {
                                                                    "en": "Fact Sheet",
                                                                    "de": "Datenblatt"
                                                                }
                                                            }
                                                        ]
                                                    ]
                                                }
                                            ],
                                            "assets": []
                                        },
                                        "price": {
                                            "id": "a07ae510-b6e9-4351-a815-a579c236829b",
                                            "value": {
                                                "type": "centPrecision",
                                                "currencyCode": "EUR",
                                                "centAmount": 27000,
                                                "fractionDigits": 2
                                            },
                                            "key": "DRX_PR_HUB_DE_3M_EUR_2",
                                            "validFrom": "2024-09-26T00:00:00.000Z"
                                        },
                                        "quantity": 1,
                                        "discountedPricePerQuantity": [],
                                        "taxRate": {
                                            "name": "MwSt",
                                            "amount": 0.19,
                                            "includedInPrice": false,
                                            "country": "DE",
                                            "id": "CAynvyIK",
                                            "subRates": []
                                        },
                                        "perMethodTaxRate": [],
                                        "addedAt": "2024-11-26T13:14:48.840Z",
                                        "lastModifiedAt": "2024-11-26T13:14:48.840Z",
                                        "state": [
                                            {
                                                "quantity": 1,
                                                "state": {
                                                    "typeId": "state",
                                                    "id": "3ee09d93-9d61-477b-b6c7-83b94c9e7d2e"
                                                }
                                            }
                                        ],
                                        "priceMode": "Platform",
                                        "lineItemMode": "Standard",
                                        "totalPrice": {
                                            "type": "centPrecision",
                                            "currencyCode": "EUR",
                                            "centAmount": 27000,
                                            "fractionDigits": 2
                                        },
                                        "taxedPrice": {
                                            "totalNet": {
                                                "type": "centPrecision",
                                                "currencyCode": "EUR",
                                                "centAmount": 27000,
                                                "fractionDigits": 2
                                            },
                                            "totalGross": {
                                                "type": "centPrecision",
                                                "currencyCode": "EUR",
                                                "centAmount": 32130,
                                                "fractionDigits": 2
                                            },
                                            "taxPortions": [
                                                {
                                                    "rate": 0.19,
                                                    "amount": {
                                                        "type": "centPrecision",
                                                        "currencyCode": "EUR",
                                                        "centAmount": 5130,
                                                        "fractionDigits": 2
                                                    },
                                                    "name": "MwSt"
                                                }
                                            ],
                                            "totalTax": {
                                                "type": "centPrecision",
                                                "currencyCode": "EUR",
                                                "centAmount": 5130,
                                                "fractionDigits": 2
                                            }
                                        },
                                        "taxedPricePortions": []
                                    }
                                ],
                                "cartState": "Active",
                                "totalPrice": {
                                    "type": "centPrecision",
                                    "currencyCode": "EUR",
                                    "centAmount": 27000,
                                    "fractionDigits": 2
                                },
                                "taxedPrice": {
                                    "totalNet": {
                                        "type": "centPrecision",
                                        "currencyCode": "EUR",
                                        "centAmount": 27000,
                                        "fractionDigits": 2
                                    },
                                    "totalGross": {
                                        "type": "centPrecision",
                                        "currencyCode": "EUR",
                                        "centAmount": 32130,
                                        "fractionDigits": 2
                                    },
                                    "taxPortions": [
                                        {
                                            "rate": 0.19,
                                            "amount": {
                                                "type": "centPrecision",
                                                "currencyCode": "EUR",
                                                "centAmount": 5130,
                                                "fractionDigits": 2
                                            },
                                            "name": "MwSt"
                                        }
                                    ],
                                    "totalTax": {
                                        "type": "centPrecision",
                                        "currencyCode": "EUR",
                                        "centAmount": 5130,
                                        "fractionDigits": 2
                                    }
                                },
                                "country": "DE",
                                "shippingMode": "Single",
                                "shippingAddress": {
                                    "streetName": "Moosacher Str.",
                                    "streetNumber": "80",
                                    "postalCode": "80809",
                                    "city": "München",
                                    "country": "DE"
                                },
                                "shipping": [],
                                "customLineItems": [],
                                "discountCodes": [],
                                "directDiscounts": [],
                                "custom": {
                                    "type": {
                                        "typeId": "type",
                                        "id": "184f9fa2-740d-43b8-b8eb-f49e6e5f672c"
                                    },
                                    "fields": {
                                        "companyId": "d9a1491f-8657-4cdc-a13f-a0a9d2745248",
                                        "userId": "c09fbeca-dd9b-4155-a8ea-16f2d44f7539",
                                        "tenant": "rexroth"
                                    }
                                },
                                "inventoryMode": "None",
                                "taxMode": "Platform",
                                "taxRoundingMode": "HalfEven",
                                "taxCalculationMode": "LineItemLevel",
                                "refusedGifts": [],
                                "origin": "Customer",
                                "billingAddress": {
                                    "streetName": "Moosacher Str.",
                                    "streetNumber": "80",
                                    "postalCode": "80809",
                                    "city": "München",
                                    "country": "DE",
                                    "email": "<EMAIL>"
                                },
                                "itemShippingAddresses": [],
                                "totalLineItemQuantity": 1
                            }
                        }
                    }""")
            .when().post(host + "/rest/commercetools/cartvalidation")
            .then().statusCode(204)
            .body(equalTo(""));
    }

    @Test
    public void testUpdateQuantityLimitExceeded() {
        UmpMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .body(new ExtensionInputBuilder().action(ExtensionAction.UPDATE)
                .resource(CartReferenceBuilder.of()
                    .obj(CartBuilder.of()
                        .lineItems(List.of())
                        .totalLineItemQuantity(101L)
                        .custom(CustomFieldsBuilder.of()
                            .fields(FieldContainerBuilder.of()
                                .addValue("tenant", "rexroth")
                                .addValue("companyId", "somecompany")
                                .buildUnchecked())
                            .buildUnchecked())
                        .buildUnchecked())
                    .buildUnchecked())
                .buildUnchecked())
            .when().post(host + "/rest/commercetools/cartvalidation")
            .then().statusCode(400)
            .body("errorMessage", equalTo("[TOTAL_QUANTITY_LIMIT_EXCEEDED]"));
    }

    @Test
    public void testUpdateCartLimitExceeded() {
        UmpMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .body(new ExtensionInputBuilder().action(ExtensionAction.UPDATE)
                .resource(CartReferenceBuilder.of()
                    .obj(CartBuilder.of()
                        .lineItems(List.of())
                        .totalPrice(CentPrecisionMoneyBuilder.of()
                            .centAmount(10_000_000L)
                            .currencyCode("EUR")
                            .fractionDigits(2)
                            .build())
                        .custom(CustomFieldsBuilder.of()
                            .fields(FieldContainerBuilder.of()
                                .addValue("tenant", "rexroth")
                                .addValue("companyId", "somecompany")
                                .buildUnchecked())
                            .buildUnchecked())
                        .buildUnchecked())
                    .buildUnchecked())
                .buildUnchecked())
            .when().post(host + "/rest/commercetools/cartvalidation")
            .then().statusCode(400)
            .body("errorMessage", equalTo("[TOTAL_AMOUNT_LIMIT_EXCEEDED]"));
    }

    @Test
    public void testCreatePayload() {
        UmpMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .body(
                """
                    {
                        "action": "Create",
                        "resource": {
                            "typeId": "cart",
                            "id": "fcd62d11-4514-4b2d-bd75-a10ddcf19378",
                            "obj": {
                                "type": "Cart",
                                "id": "fcd62d11-4514-4b2d-bd75-a10ddcf19378",
                                "version": 1,
                                "lastMessageSequenceNumber": 1,
                                "createdAt": "1970-01-01T00:00:00.000Z",
                                "lastModifiedAt": "1970-01-01T00:00:00.000Z",
                                "lastModifiedBy": {
                                    "clientId": "SSAIdVhBpKt80ChCMjZwxi4c",
                                    "isPlatformClient": false
                                },
                                "createdBy": {
                                    "clientId": "SSAIdVhBpKt80ChCMjZwxi4c",
                                    "isPlatformClient": false
                                },
                                "key": "70eac18a-83d8-47f1-91fe-c1259d794e22",
                                "customerGroup": {
                                    "typeId": "customer-group",
                                    "id": "36318b6d-7e67-4a6f-9cf1-65932a5c65f2"
                                },
                                "lineItems": [],
                                "cartState": "Active",
                                "totalPrice": {
                                    "type": "centPrecision",
                                    "currencyCode": "EUR",
                                    "centAmount": 0,
                                    "fractionDigits": 2
                                },
                                "taxedPrice": {
                                    "totalNet": {
                                        "type": "centPrecision",
                                        "currencyCode": "EUR",
                                        "centAmount": 0,
                                        "fractionDigits": 2
                                    },
                                    "totalGross": {
                                        "type": "centPrecision",
                                        "currencyCode": "EUR",
                                        "centAmount": 0,
                                        "fractionDigits": 2
                                    },
                                    "taxPortions": [],
                                    "totalTax": {
                                        "type": "centPrecision",
                                        "currencyCode": "EUR",
                                        "centAmount": 0,
                                        "fractionDigits": 2
                                    }
                                },
                                "country": "AT",
                                "shippingMode": "Single",
                                "shippingAddress": {
                                    "streetName": "Berg",
                                    "streetNumber": "18",
                                    "postalCode": "5652",
                                    "city": "Dienten am Hochkönig",
                                    "country": "AT"
                                },
                                "shipping": [],
                                "customLineItems": [],
                                "discountCodes": [],
                                "directDiscounts": [],
                                "custom": {
                                    "type": {
                                        "typeId": "type",
                                        "id": "184f9fa2-740d-43b8-b8eb-f49e6e5f672c"
                                    },
                                    "fields": {
                                        "companyId": "9fb2c757-5863-45f6-9a7d-310431187149",
                                        "userId": "7cb0fdd4-9503-41e0-a279-84ad098c8a00",
                                        "tenant": "rexroth"
                                    }
                                },
                                "inventoryMode": "None",
                                "taxMode": "Platform",
                                "taxRoundingMode": "HalfEven",
                                "taxCalculationMode": "LineItemLevel",
                                "refusedGifts": [],
                                "origin": "Customer",
                                "billingAddress": {
                                    "streetName": "Bistdudeppertgassl",
                                    "streetNumber": "12",
                                    "postalCode": "4010",
                                    "city": "Linz",
                                    "region": "Linz-Land",
                                    "state": "Oberösterreich",
                                    "country": "AT",
                                    "email": "<EMAIL>"
                                },
                                "itemShippingAddresses": []
                            }
                        }
                    }""")
            .when().post(host + "/rest/commercetools/cartvalidation")
            .then().statusCode(204)
            .body(equalTo(""));
    }

    @Test
    public void testValidateAddons() {
        UmpMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .body(
                """
                    {
                        "action": "Update",
                        "resource": {
                            "typeId": "cart",
                            "id": "11ee6db4-a4bb-453a-bf7c-62fd1bb7fe8b",
                            "obj": {
                                "type": "Cart",
                                "id": "11ee6db4-a4bb-453a-bf7c-62fd1bb7fe8b",
                                "version": 5,
                                "versionModifiedAt": "2024-12-11T18:20:43.032Z",
                                "lastMessageSequenceNumber": 1,
                                "createdAt": "2024-12-11T18:20:43.032Z",
                                "lastModifiedAt": "2024-12-11T18:20:43.032Z",
                                "lastModifiedBy": {
                                    "clientId": "SSAIdVhBpKt80ChCMjZwxi4c",
                                    "isPlatformClient": false
                                },
                                "createdBy": {
                                    "clientId": "SSAIdVhBpKt80ChCMjZwxi4c",
                                    "isPlatformClient": false
                                },
                                "key": "92b551fc-56e1-46b5-85e0-76e405a8d5c2",
                                "customerGroup": {
                                    "typeId": "customer-group",
                                    "id": "36318b6d-7e67-4a6f-9cf1-65932a5c65f2"
                                },
                                "lineItems": [
                                    {
                                        "id": "600f4c17-ce1f-43b5-8118-a992ec2a9956",
                                        "key": "816569bd-dc5f-400b-9d85-bef373364978",
                                        "productId": "5abd3673-2110-4aa0-bbf9-c97cd1e24c89",
                                        "productKey": "bodas_connect_10",
                                        "name": {
                                            "en": "RCU Series 10 & 20",
                                            "de": "RCU Serie 10 & 20"
                                        },
                                        "productType": {
                                            "typeId": "product-type",
                                            "id": "a46f1d35-7553-4813-8a20-993f1e84b9f4",
                                            "version": 54
                                        },
                                        "productSlug": {
                                            "en": "bodas-connect-for-rcu-series-10-20"
                                        },
                                        "variant": {
                                            "id": 1,
                                            "sku": "DRX_PR_BODAS_COMPACT",
                                            "key": "DRX_PR_BODAS_COMPACT",
                                            "prices": [],
                                            "images": [
                                                {
                                                    "url": "https://images.cdn.europe-west1.gcp.commercetools.com/b4800138-a539-4ad0-9511-9260c02d29bb/bodasconnect-ooNblHOt.jpeg",
                                                    "dimensions": {
                                                        "w": 1600,
                                                        "h": 1600
                                                    }
                                                },
                                                {
                                                    "url": "https://images.cdn.europe-west1.gcp.commercetools.com/b4800138-a539-4ad0-9511-9260c02d29bb/bodasconnect_icon-dbWuJ_-R.jpeg",
                                                    "dimensions": {
                                                        "w": 224,
                                                        "h": 224
                                                    }
                                                }
                                            ],
                                            "attributes": [
                                                {
                                                    "name": "tenant",
                                                    "value": {
                                                        "key": "rexroth",
                                                        "label": "rexroth"
                                                    }
                                                },
                                                {
                                                    "name": "name",
                                                    "value": {
                                                        "en": "Device Management Compact"
                                                    }
                                                },
                                                {
                                                    "name": "bundleAmount",
                                                    "value": 1
                                                },
                                                {
                                                    "name": "licenseType",
                                                    "value": {
                                                        "key": "CONSUMPTION",
                                                        "label": "product with postpaid billing and consumption pricing (CONSUMPTION)"
                                                    }
                                                },
                                                {
                                                    "name": "externalProductId",
                                                    "value": "R917014006"
                                                },
                                                {
                                                    "name": "sellerCompanyId",
                                                    "value": "f01d4e3c-ab09-4cd2-b5ff-312de81ab7bf"
                                                },
                                                {
                                                    "name": "description",
                                                    "value": {
                                                        "en": "The Telematics solution for machine OEMs to gain internal R&D and customer support efficiency & establish external data driven business models. Includes integrated Device Management, Cellular Connectivity and Data Management and offers Over-the-Air functionality for updating ECUs."
                                                    }
                                                },
                                                {
                                                    "name": "features",
                                                    "value": {
                                                        "en": "SOTA\\nCustom Snaps\\nCybersecurity"
                                                    }
                                                },
                                                {
                                                    "name": "runtime",
                                                    "value": "P1M"
                                                },
                                                {
                                                    "name": "noticePeriod",
                                                    "value": "P28D"
                                                },
                                                {
                                                    "name": "addons",
                                                    "value": [
                                                        {
                                                            "typeId": "product",
                                                            "id": "d74b86e4-233c-4bec-b137-d7ef4b630b64"
                                                        }
                                                    ]
                                                },
                                                {
                                                    "name": "externalDocuments",
                                                    "value": [
                                                        [
                                                            {
                                                                "name": "link",
                                                                "value": {
                                                                    "en": "https://www.boschrexroth.com/de/de/media-details/29d12ccd-61cb-455c-9378-8a58440cf4ae",
                                                                    "de": ""
                                                                }
                                                            },
                                                            {
                                                                "name": "name",
                                                                "value": {
                                                                    "en": "Fact Sheet Device Connectivity"
                                                                }
                                                            }
                                                        ]
                                                    ]
                                                }
                                            ],
                                            "assets": []
                                        },
                                        "price": {
                                            "id": "9c50dda8-7bca-43a8-9b22-63057ea8eab6",
                                            "value": {
                                                "type": "centPrecision",
                                                "currencyCode": "EUR",
                                                "centAmount": 24900,
                                                "fractionDigits": 2
                                            },
                                            "key": "DRX_PR_BODAS_COMPACT"
                                        },
                                        "quantity": 1,
                                        "discountedPricePerQuantity": [],
                                        "taxRate": {
                                            "name": "MwSt",
                                            "amount": 0.2,
                                            "includedInPrice": false,
                                            "country": "AT",
                                            "id": "tr8WoQjy",
                                            "subRates": []
                                        },
                                        "perMethodTaxRate": [],
                                        "addedAt": "2024-12-11T18:25:22.674Z",
                                        "lastModifiedAt": "2024-12-11T18:25:22.674Z",
                                        "state": [
                                            {
                                                "quantity": 1,
                                                "state": {
                                                    "typeId": "state",
                                                    "id": "3ee09d93-9d61-477b-b6c7-83b94c9e7d2e"
                                                }
                                            }
                                        ],
                                        "priceMode": "Platform",
                                        "lineItemMode": "Standard",
                                        "totalPrice": {
                                            "type": "centPrecision",
                                            "currencyCode": "EUR",
                                            "centAmount": 24900,
                                            "fractionDigits": 2
                                        },
                                        "taxedPrice": {
                                            "totalNet": {
                                                "type": "centPrecision",
                                                "currencyCode": "EUR",
                                                "centAmount": 24900,
                                                "fractionDigits": 2
                                            },
                                            "totalGross": {
                                                "type": "centPrecision",
                                                "currencyCode": "EUR",
                                                "centAmount": 29880,
                                                "fractionDigits": 2
                                            },
                                            "taxPortions": [
                                                {
                                                    "rate": 0.2,
                                                    "amount": {
                                                        "type": "centPrecision",
                                                        "currencyCode": "EUR",
                                                        "centAmount": 4980,
                                                        "fractionDigits": 2
                                                    },
                                                    "name": "MwSt"
                                                }
                                            ],
                                            "totalTax": {
                                                "type": "centPrecision",
                                                "currencyCode": "EUR",
                                                "centAmount": 4980,
                                                "fractionDigits": 2
                                            }
                                        },
                                        "taxedPricePortions": []
                                    },
                                    {
                                        "id": "1ffe18df-7765-448f-af10-49c242f6b50b",
                                        "key": "8bd897c6-0940-4cf6-9059-64e598b6e3b0",
                                        "productId": "d74b86e4-233c-4bec-b137-d7ef4b630b64",
                                        "productKey": "cellular-connection",
                                        "name": {
                                            "en": "Cellular Connection"
                                        },
                                        "productType": {
                                            "typeId": "product-type",
                                            "id": "75a25e43-ec58-4581-90f4-367b4cae1cfc",
                                            "version": 2
                                        },
                                        "productSlug": {
                                            "en": "cellular-connection"
                                        },
                                        "variant": {
                                            "id": 1,
                                            "sku": "DRX_PR_BODAS_CONNECTION_50MB",
                                            "key": "DRX_PR_BODAS_CONNECTION_50MB",
                                            "prices": [],
                                            "images": [],
                                            "attributes": [
                                                {
                                                    "name": "tenant",
                                                    "value": {
                                                        "key": "rexroth",
                                                        "label": "rexroth"
                                                    }
                                                },
                                                {
                                                    "name": "name",
                                                    "value": {
                                                        "en": "50 MB"
                                                    }
                                                }
                                            ],
                                            "assets": []
                                        },
                                        "price": {
                                            "id": "0cd58d42-f263-4138-96a1-e1d704143b89",
                                            "value": {
                                                "type": "centPrecision",
                                                "currencyCode": "EUR",
                                                "centAmount": 0,
                                                "fractionDigits": 2
                                            }
                                        },
                                        "quantity": 1,
                                        "discountedPricePerQuantity": [],
                                        "taxRate": {
                                            "name": "MwSt",
                                            "amount": 0.2,
                                            "includedInPrice": false,
                                            "country": "AT",
                                            "id": "tr8WoQjy",
                                            "subRates": []
                                        },
                                        "perMethodTaxRate": [],
                                        "addedAt": "2024-12-11T18:25:22.681Z",
                                        "lastModifiedAt": "2024-12-11T18:25:22.681Z",
                                        "state": [
                                            {
                                                "quantity": 1,
                                                "state": {
                                                    "typeId": "state",
                                                    "id": "3ee09d93-9d61-477b-b6c7-83b94c9e7d2e"
                                                }
                                            }
                                        ],
                                        "priceMode": "ExternalPrice",
                                        "lineItemMode": "Standard",
                                        "totalPrice": {
                                            "type": "centPrecision",
                                            "currencyCode": "EUR",
                                            "centAmount": 0,
                                            "fractionDigits": 2
                                        },
                                        "taxedPrice": {
                                            "totalNet": {
                                                "type": "centPrecision",
                                                "currencyCode": "EUR",
                                                "centAmount": 0,
                                                "fractionDigits": 2
                                            },
                                            "totalGross": {
                                                "type": "centPrecision",
                                                "currencyCode": "EUR",
                                                "centAmount": 0,
                                                "fractionDigits": 2
                                            },
                                            "taxPortions": [
                                                {
                                                    "rate": 0.2,
                                                    "amount": {
                                                        "type": "centPrecision",
                                                        "currencyCode": "EUR",
                                                        "centAmount": 0,
                                                        "fractionDigits": 2
                                                    },
                                                    "name": "MwSt"
                                                }
                                            ],
                                            "totalTax": {
                                                "type": "centPrecision",
                                                "currencyCode": "EUR",
                                                "centAmount": 0,
                                                "fractionDigits": 2
                                            }
                                        },
                                        "taxedPricePortions": [],
                                        "custom": {
                                            "type": {
                                                "typeId": "type",
                                                "id": "d1217476-774e-4cb8-a53a-25d597a91cc3"
                                            },
                                            "fields": {
                                                "parentLineItemKey": "816569bd-dc5f-400b-9d85-bef373364978"
                                            }
                                        }
                                    },
                                    {
                                        "id": "e4fa19dc-76d8-42bf-bdb8-b188e5a34592",
                                        "key": "b3bd41a0-c06f-46e1-8f69-a4bc5d4d421b",
                                        "productId": "d74b86e4-233c-4bec-b137-d7ef4b630b64",
                                        "productKey": "cellular-connection",
                                        "name": {
                                            "en": "Cellular Connection"
                                        },
                                        "productType": {
                                            "typeId": "product-type",
                                            "id": "75a25e43-ec58-4581-90f4-367b4cae1cfc",
                                            "version": 2
                                        },
                                        "productSlug": {
                                            "en": "cellular-connection"
                                        },
                                        "variant": {
                                            "id": 2,
                                            "sku": "DRX_PR_BODAS_CONNECTION_100MB",
                                            "key": "DRX_PR_BODAS_CONNECTION_100MB",
                                            "prices": [],
                                            "images": [],
                                            "attributes": [
                                                {
                                                    "name": "tenant",
                                                    "value": {
                                                        "key": "rexroth",
                                                        "label": "rexroth"
                                                    }
                                                },
                                                {
                                                    "name": "name",
                                                    "value": {
                                                        "en": "100 MB"
                                                    }
                                                },
                                                {
                                                    "name": "description",
                                                    "value": {
                                                        "en": "⭐️ Recommended",
                                                        "de": "⭐️ Empfohlen"
                                                    }
                                                }
                                            ],
                                            "assets": []
                                        },
                                        "price": {
                                            "id": "a4c3c860-0f96-4315-bea7-db1badeb0da6",
                                            "value": {
                                                "type": "centPrecision",
                                                "currencyCode": "EUR",
                                                "centAmount": 0,
                                                "fractionDigits": 2
                                            }
                                        },
                                        "quantity": 1,
                                        "discountedPricePerQuantity": [],
                                        "taxRate": {
                                            "name": "MwSt",
                                            "amount": 0.2,
                                            "includedInPrice": false,
                                            "country": "AT",
                                            "id": "tr8WoQjy",
                                            "subRates": []
                                        },
                                        "perMethodTaxRate": [],
                                        "addedAt": "2024-12-11T18:25:22.686Z",
                                        "lastModifiedAt": "2024-12-11T18:25:22.686Z",
                                        "state": [
                                            {
                                                "quantity": 1,
                                                "state": {
                                                    "typeId": "state",
                                                    "id": "3ee09d93-9d61-477b-b6c7-83b94c9e7d2e"
                                                }
                                            }
                                        ],
                                        "priceMode": "ExternalPrice",
                                        "lineItemMode": "Standard",
                                        "totalPrice": {
                                            "type": "centPrecision",
                                            "currencyCode": "EUR",
                                            "centAmount": 0,
                                            "fractionDigits": 2
                                        },
                                        "taxedPrice": {
                                            "totalNet": {
                                                "type": "centPrecision",
                                                "currencyCode": "EUR",
                                                "centAmount": 0,
                                                "fractionDigits": 2
                                            },
                                            "totalGross": {
                                                "type": "centPrecision",
                                                "currencyCode": "EUR",
                                                "centAmount": 0,
                                                "fractionDigits": 2
                                            },
                                            "taxPortions": [
                                                {
                                                    "rate": 0.2,
                                                    "amount": {
                                                        "type": "centPrecision",
                                                        "currencyCode": "EUR",
                                                        "centAmount": 0,
                                                        "fractionDigits": 2
                                                    },
                                                    "name": "MwSt"
                                                }
                                            ],
                                            "totalTax": {
                                                "type": "centPrecision",
                                                "currencyCode": "EUR",
                                                "centAmount": 0,
                                                "fractionDigits": 2
                                            }
                                        },
                                        "taxedPricePortions": [],
                                        "custom": {
                                            "type": {
                                                "typeId": "type",
                                                "id": "d1217476-774e-4cb8-a53a-25d597a91cc3"
                                            },
                                            "fields": {
                                                "parentLineItemKey": "816569bd-dc5f-400b-9d85-bef373364978"
                                            }
                                        }
                                    }
                                ],
                                "cartState": "Active",
                                "totalPrice": {
                                    "type": "centPrecision",
                                    "currencyCode": "EUR",
                                    "centAmount": 24900,
                                    "fractionDigits": 2
                                },
                                "taxedPrice": {
                                    "totalNet": {
                                        "type": "centPrecision",
                                        "currencyCode": "EUR",
                                        "centAmount": 24900,
                                        "fractionDigits": 2
                                    },
                                    "totalGross": {
                                        "type": "centPrecision",
                                        "currencyCode": "EUR",
                                        "centAmount": 29880,
                                        "fractionDigits": 2
                                    },
                                    "taxPortions": [
                                        {
                                            "rate": 0.2,
                                            "amount": {
                                                "type": "centPrecision",
                                                "currencyCode": "EUR",
                                                "centAmount": 4980,
                                                "fractionDigits": 2
                                            },
                                            "name": "MwSt"
                                        }
                                    ],
                                    "totalTax": {
                                        "type": "centPrecision",
                                        "currencyCode": "EUR",
                                        "centAmount": 4980,
                                        "fractionDigits": 2
                                    }
                                },
                                "country": "AT",
                                "shippingMode": "Single",
                                "shippingAddress": {
                                    "streetName": "Berg",
                                    "streetNumber": "18",
                                    "postalCode": "5652",
                                    "city": "Dienten am Hochkönig",
                                    "country": "AT"
                                },
                                "shipping": [],
                                "customLineItems": [],
                                "discountCodes": [],
                                "directDiscounts": [],
                                "custom": {
                                    "type": {
                                        "typeId": "type",
                                        "id": "184f9fa2-740d-43b8-b8eb-f49e6e5f672c"
                                    },
                                    "fields": {
                                        "companyId": "d9a1491f-8657-4cdc-a13f-a0a9d2745248",
                                        "userId": "7cb0fdd4-9503-41e0-a279-84ad098c8a00",
                                        "tenant": "rexroth"
                                    }
                                },
                                "inventoryMode": "None",
                                "taxMode": "Platform",
                                "taxRoundingMode": "HalfEven",
                                "taxCalculationMode": "LineItemLevel",
                                "refusedGifts": [],
                                "origin": "Customer",
                                "billingAddress": {
                                    "streetName": "Bistdudeppertgassl",
                                    "streetNumber": "12",
                                    "postalCode": "4010",
                                    "city": "Linz",
                                    "region": "Linz-Land",
                                    "state": "Oberösterreich",
                                    "country": "AT",
                                    "email": "<EMAIL>"
                                },
                                "itemShippingAddresses": [],
                                "totalLineItemQuantity": 3
                            }
                        }
                    }""")
            .when().post(host + "/rest/commercetools/cartvalidation")
            .then().statusCode(204)
            .body(equalTo(""));
    }
}
