package com.sast.store.ordermanagement.company;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.external.ump.messaging.CompanyUpdateNotification;
import com.sast.store.external.ump.messaging.UnhandledNotification;
import com.sast.store.gen.client.ump.api.UmpAddressDto;
import com.sast.store.gen.client.ump.api.UmpInternalCustomerGroupConfigurationDto;
import com.sast.store.ordermanagement.service.sync.CompanySyncService;
import com.sast.store.ordermanagement.service.sync.UserGroupSyncService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

@ExtendWith(MockitoExtension.class)
class UmpNotificationConsumerTest {

    @Mock private CompanySyncService companySyncService;
    @Mock private UserGroupSyncService userGroupSyncService;
    @InjectMocks private UmpNotificationConsumer consumer;

    @Test
    void whenCompanyUpdateNotificationArrives_thenBothSyncServicesAreCalled() {
        final CompanyUpdateNotification update = CompanyUpdateNotification.builder()
            .tenant("rexroth")
            .companyId("cid-123")
            .companyName("Acme Corp")
            .companyCountry("DE")
            .billingAddress(new UmpAddressDto())
            .customerGroup(new UmpInternalCustomerGroupConfigurationDto())
            .build();

        consumer.consume(update);

        verify(companySyncService).handleCompanyUpdate(
            update.asExternalCompanyDto(),
            Tenant.REXROTH
        );
        verify(userGroupSyncService).handleUserGroupChange(
            "cid-123",
            update.customerGroup(),
            Tenant.REXROTH
        );
    }

    @Test
    void whenUnhandledNotificationArrives_thenMessageIsIgnored() {
        final UnhandledNotification randomEvent = UnhandledNotification.builder()
            .tenant("rexroth")
            .subject("USER_UPDATE")
            .build();

        consumer.consume(randomEvent);

        verifyNoInteractions(companySyncService);
        verifyNoInteractions(userGroupSyncService);
    }

    @Test
    void whenCompanyUpdateNotificationArrivesForIgnoredTenant_thenMessageIsIgnored() {
        final CompanyUpdateNotification update = CompanyUpdateNotification.builder()
                .tenant("baam")
                .companyId("cid-123")
                .companyName("Acme Corp")
                .companyCountry("DE")
                .billingAddress(new UmpAddressDto())
                .customerGroup(new UmpInternalCustomerGroupConfigurationDto())
                .build();

        consumer.consume(update);

        verifyNoInteractions(companySyncService);
        verifyNoInteractions(userGroupSyncService);
    }

    @Test
    void whenCompanyUpdateProcessingThrows_thenExceptionPropagates() {
        final var thrownException = new RuntimeException();
        final CompanyUpdateNotification update = CompanyUpdateNotification.builder()
                .tenant("rexroth")
                .companyId("cid-123")
                .companyName("Acme Corp")
                .companyCountry("DE")
                .billingAddress(new UmpAddressDto())
                .customerGroup(new UmpInternalCustomerGroupConfigurationDto())
                .build();

        doThrow(thrownException).when(companySyncService).handleCompanyUpdate(any(), any());

        assertThatThrownBy(() -> consumer.consume(update))
                .isEqualTo(thrownException);
    }

    @Test
    void whenCustomerGroupUpdateProcessingThrows_thenExceptionPropagates() {
        final var thrownException = new RuntimeException();
        final CompanyUpdateNotification update = CompanyUpdateNotification.builder()
                .tenant("rexroth")
                .companyId("cid-123")
                .companyName("Acme Corp")
                .companyCountry("DE")
                .billingAddress(new UmpAddressDto())
                .customerGroup(new UmpInternalCustomerGroupConfigurationDto())
                .build();

        doThrow(thrownException).when(userGroupSyncService).handleUserGroupChange(any(), any(), any());

        assertThatThrownBy(() -> consumer.consume(update))
                .isEqualTo(thrownException);
    }
}