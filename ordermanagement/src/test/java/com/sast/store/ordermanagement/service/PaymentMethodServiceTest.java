package com.sast.store.ordermanagement.service;

import com.sast.store.ordermanagement.AbstractComponentTest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class PaymentMethodServiceTest extends AbstractComponentTest {

    @Inject
    private PaymentMethodRegistry paymentMethodRegistry;

    @Test
    void testCompareTo() {
        final List<PaymentMethodService> list = paymentMethodRegistry.getAll().stream().sorted(PaymentMethodService::compareTo).toList();
        assertThat(list).hasExactlyElementsOfTypes(
            BoschTransferPaymentService.class,
            BoschTransferAchPaymentService.class,
            ZeroPaymentService.class);
    }

}
