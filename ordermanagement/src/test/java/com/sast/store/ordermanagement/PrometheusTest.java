package com.sast.store.ordermanagement;

import com.sast.store.external.countriesservice.util.CountriesServiceMockExtension;
import com.sast.store.external.ump.test.UmpMockExtension;
import com.sast.store.ordermanagement.util.CommercetoolsMockExtension;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import org.junit.jupiter.api.Test;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.annotation.DirtiesContext.MethodMode;

import static org.hamcrest.Matchers.containsString;

class PrometheusTest extends AbstractComponentTest {

    @Test
    void testPrometheusCheck() {
        RestAssured
            .given()
            .when().get(host + "/actuator/prometheus")
            .then().statusCode(200)
            .body(containsString("health 1.0"));
    }

    @Test
    @DirtiesContext(methodMode = MethodMode.BEFORE_METHOD)
    void testPrometheuseJerseyEndpoint() {
        UmpMockExtension.withDefaultResponse();
        CommercetoolsMockExtension.withDefaultResponse();
        CountriesServiceMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/cart")
            .then().statusCode(200);

        RestAssured
            .given()
            .when().get(host + "/actuator/prometheus")
            .then().statusCode(200)
            .body(containsString("health 1.0"))
            .body(containsString(
                """
                    http_rest_client_requests_seconds_count{clientName="localhost",method="GET",outcome="SUCCESSFUL",status="200",uri="/internal/companies/{var}"}"""))
            .body(containsString(
                """
                    http_rest_client_requests_seconds_count{clientName="localhost",method="GET",outcome="SUCCESSFUL",status="200",\
                    uri="/glorious-new-store/carts?where={var}&var.tenant={var}&var.companyId={var}&var.userId={var}"}"""))
            .body(containsString(
                """
                    http_server_requests_seconds_count{error="none",exception="None",method="GET",outcome="SUCCESS",status="200",uri="/rest/cart"}"""));
    }
}
