package com.sast.store.ordermanagement.invoice.service;

import com.sast.store.brimtegration.apimodel.common.document.PlatformDocumentType;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.ordermanagement.AbstractComponentTest;
import com.sast.store.ordermanagement.invoice.model.InvoiceEntity;
import com.sast.store.ordermanagement.invoice.model.InvoiceRepository;
import com.sast.store.ordermanagement.invoice.util.TestDataBuilder;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Currency;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

public class InvoicePersistenceServiceTest extends AbstractComponentTest {
    private static final Currency EUR = Currency.getInstance("EUR");
    private static final String INVOICE_NUMBER = "981239";
    private static final String CREDIT_NOTE_NUMBER = "981240";
    private static final String REVERSAL_NUMBER = "981241";

    @Autowired
    private InvoicePersistenceService invoicePersistenceService;

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Test
    void testInvoiceIsSavedFromNotification() {
        invoicePersistenceService.saveInvoice(Tenant.REXROTH,
            TestDataBuilder.invoiceNotification(b -> b.documentNumber(INVOICE_NUMBER)));

        final InvoiceEntity byDocumentNumber = invoiceRepository.findByDocumentNumber(INVOICE_NUMBER).orElseThrow();
        assertThat(byDocumentNumber).isEqualTo(exepctedInvoiceEntity());
    }

    @Test
    void testCreditNoteIsSavedFromNotification() {
        final var expectedInvoice = exepctedInvoiceEntity();
        expectedInvoice.setVersion(2L);
        expectedInvoice.setCreditNotes(List.of(
            expectedCreditNote(InvoiceEntity.CreditNoteType.CREDIT_NOTE, CREDIT_NOTE_NUMBER)));
        invoicePersistenceService.saveInvoice(Tenant.REXROTH,
            TestDataBuilder.invoiceNotification(b -> b.documentNumber(INVOICE_NUMBER)));
        final InvoiceEntity givenInvoice = invoiceRepository.findByDocumentNumber(INVOICE_NUMBER).orElseThrow();

        invoicePersistenceService.saveCreditNote(givenInvoice, TestDataBuilder.creditNoteNotification(
            t -> t.documentNumber(CREDIT_NOTE_NUMBER).parentDocumentNumber(INVOICE_NUMBER)));

        final InvoiceEntity actualInvoice = invoiceRepository.findByDocumentNumber(INVOICE_NUMBER).orElseThrow();
        assertThat(actualInvoice).isEqualTo(expectedInvoice);
    }

    @Test
    void testCreditNoteWithInvoiceTypeIsInvalidAndNotSaved() {
        final var expectedInvoice = exepctedInvoiceEntity();
        expectedInvoice.setVersion(1L);
        invoicePersistenceService.saveInvoice(Tenant.REXROTH,
            TestDataBuilder.invoiceNotification(b -> b.documentNumber(INVOICE_NUMBER)));
        final InvoiceEntity givenInvoice = invoiceRepository.findByDocumentNumber(INVOICE_NUMBER).orElseThrow();

        assertThatThrownBy(() -> invoicePersistenceService
            .saveCreditNote(givenInvoice, TestDataBuilder.creditNoteNotification(
                t -> t.documentType(PlatformDocumentType.INVOICE).documentNumber(CREDIT_NOTE_NUMBER)
                    .parentDocumentNumber(INVOICE_NUMBER))));

        final InvoiceEntity actualInvoice = invoiceRepository.findByDocumentNumber(INVOICE_NUMBER).orElseThrow();
        assertThat(actualInvoice).isEqualTo(expectedInvoice);
    }

    @Test
    void testReversalIsSavedFromNotification() {
        final var expectedInvoice = exepctedInvoiceEntity();
        expectedInvoice.setVersion(2L);
        expectedInvoice.setCreditNotes(List.of(
            expectedCreditNote(InvoiceEntity.CreditNoteType.REVERSAL, REVERSAL_NUMBER)));
        invoicePersistenceService.saveInvoice(Tenant.REXROTH,
            TestDataBuilder.invoiceNotification(b -> b.documentNumber(INVOICE_NUMBER)));
        final InvoiceEntity givenInvoice = invoiceRepository.findByDocumentNumber(INVOICE_NUMBER).orElseThrow();

        invoicePersistenceService.saveCreditNote(givenInvoice, TestDataBuilder.creditNoteNotification(
            t -> t.documentType(PlatformDocumentType.REVERSAL).documentNumber(REVERSAL_NUMBER).parentDocumentNumber(INVOICE_NUMBER)));

        final InvoiceEntity actualInvoice = invoiceRepository.findByDocumentNumber(INVOICE_NUMBER).orElseThrow();
        assertThat(actualInvoice).isEqualTo(expectedInvoice);
    }

    @Test
    void testCreditNotesAndReversalsAreCumulative() {
        final var expectedInvoice = exepctedInvoiceEntity();
        expectedInvoice.setVersion(3L);
        expectedInvoice.setCreditNotes(List.of(
            expectedCreditNote(InvoiceEntity.CreditNoteType.CREDIT_NOTE, CREDIT_NOTE_NUMBER),
            expectedCreditNote(InvoiceEntity.CreditNoteType.REVERSAL, REVERSAL_NUMBER)));
        invoicePersistenceService.saveInvoice(Tenant.REXROTH,
            TestDataBuilder.invoiceNotification(builder -> builder.documentNumber(INVOICE_NUMBER)));
        final InvoiceEntity givenInvoice = invoiceRepository.findByDocumentNumber(INVOICE_NUMBER).orElseThrow();

        invoicePersistenceService.saveCreditNote(givenInvoice, TestDataBuilder.creditNoteNotification(
            t -> t.documentNumber(CREDIT_NOTE_NUMBER).parentDocumentNumber(INVOICE_NUMBER)));
        final InvoiceEntity givenInvoiceWithCreditNote = invoiceRepository.findByDocumentNumber(INVOICE_NUMBER).orElseThrow();
        invoicePersistenceService.saveCreditNote(givenInvoiceWithCreditNote, TestDataBuilder.creditNoteNotification(
            t -> t.documentType(PlatformDocumentType.REVERSAL).documentNumber(REVERSAL_NUMBER).parentDocumentNumber(INVOICE_NUMBER)));

        final InvoiceEntity actualInvoice = invoiceRepository.findByDocumentNumber(INVOICE_NUMBER).orElseThrow();
        assertThat(actualInvoice).isEqualTo(expectedInvoice);
    }

    private static InvoiceEntity exepctedInvoiceEntity() {
        final var expectedFirstLineItem = new InvoiceEntity.LineItem();
        expectedFirstLineItem.setPosition(1);
        expectedFirstLineItem.setProductId("firstProduct");
        expectedFirstLineItem.setQuantity(BigDecimal.ONE);
        expectedFirstLineItem.setNetAmount(new BigDecimal("10.00"));

        final var expectedSecondLineItem = new InvoiceEntity.LineItem();
        expectedSecondLineItem.setPosition(2);
        expectedSecondLineItem.setProductId("secondProduct");
        expectedSecondLineItem.setQuantity(BigDecimal.TWO);
        expectedSecondLineItem.setNetAmount(new BigDecimal("20.00"));

        final var expectedTotalAmounts = new InvoiceEntity.InvoiceTotalAmounts();
        expectedTotalAmounts.setCurrency(EUR.getCurrencyCode());
        expectedTotalAmounts.setNetAmount(new BigDecimal("30.00"));
        expectedTotalAmounts.setGrossAmount(new BigDecimal("35.70"));
        expectedTotalAmounts.setTaxAmount(new BigDecimal("5.70"));

        final var expectedS3File = new InvoiceEntity.S3File();
        expectedS3File.setBucket("eimer");
        expectedS3File.setKey("datei");
        expectedS3File.setSha256sum("abc123def");
        expectedS3File.setContentType("application/pdf");

        final var exepctedInvoiceEntity = new InvoiceEntity();
        exepctedInvoiceEntity.setDocumentNumber(INVOICE_NUMBER);
        exepctedInvoiceEntity.setTenant(Tenant.REXROTH);
        exepctedInvoiceEntity.setCompany("i am a company ID.");
        exepctedInvoiceEntity.setOrderNumbers(List.of("1234"));
        exepctedInvoiceEntity.setLineItems(List.of(expectedFirstLineItem, expectedSecondLineItem));
        exepctedInvoiceEntity.setTotalAmounts(expectedTotalAmounts);
        exepctedInvoiceEntity.setDocumentDate(LocalDate.parse("2024-08-15"));
        exepctedInvoiceEntity.setCreationDate(LocalDate.parse("2024-08-16"));
        exepctedInvoiceEntity.setS3File(expectedS3File);
        exepctedInvoiceEntity.setVersion(1L);

        return exepctedInvoiceEntity;
    }

    private static InvoiceEntity.CreditNote expectedCreditNote(final InvoiceEntity.CreditNoteType creditNoteType,
        final String documentNumber) {
        final var expectedFirstLineItem = new InvoiceEntity.LineItem();
        expectedFirstLineItem.setPosition(1);
        expectedFirstLineItem.setProductId("firstProduct");
        expectedFirstLineItem.setQuantity(BigDecimal.ONE);
        expectedFirstLineItem.setNetAmount(new BigDecimal("-10.00"));

        final var expectedSecondLineItem = new InvoiceEntity.LineItem();
        expectedSecondLineItem.setPosition(2);
        expectedSecondLineItem.setProductId("secondProduct");
        expectedSecondLineItem.setQuantity(BigDecimal.TWO);
        expectedSecondLineItem.setNetAmount(new BigDecimal("-20.00"));

        final var expectedTotalAmounts = new InvoiceEntity.InvoiceTotalAmounts();
        expectedTotalAmounts.setCurrency(EUR.getCurrencyCode());
        expectedTotalAmounts.setNetAmount(new BigDecimal("-30.00"));
        expectedTotalAmounts.setGrossAmount(new BigDecimal("-35.70"));
        expectedTotalAmounts.setTaxAmount(new BigDecimal("-5.70"));

        final var expectedS3File = new InvoiceEntity.S3File();
        expectedS3File.setBucket("eimer");
        expectedS3File.setKey("dateiCreditNote");
        expectedS3File.setSha256sum("abc123def");
        expectedS3File.setContentType("application/pdf");

        final var expectedCreditNote = new InvoiceEntity.CreditNote();
        expectedCreditNote.setDocumentNumber(documentNumber);
        expectedCreditNote.setCreditNoteType(creditNoteType);
        expectedCreditNote.setTotalAmounts(expectedTotalAmounts);
        expectedCreditNote.setLineItems(List.of(expectedFirstLineItem, expectedSecondLineItem));
        expectedCreditNote.setDocumentDate(LocalDate.parse("2024-09-15"));
        expectedCreditNote.setCreationDate(LocalDate.parse("2024-09-16"));
        expectedCreditNote.setS3File(expectedS3File);

        return expectedCreditNote;
    }
}
