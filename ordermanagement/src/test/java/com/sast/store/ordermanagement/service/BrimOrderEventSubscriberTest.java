package com.sast.store.ordermanagement.service;

import com.sast.store.external.ump.test.UmpMockExtension;
import com.sast.store.ordermanagement.AbstractComponentTest;
import com.sast.store.ordermanagement.util.CommercetoolsMockExtension;
import com.sast.store.ordermanagement.util.MattermostMockExtension;
import com.sast.store.testing.awsmockup.junit.SqsMockExtension;
import org.junit.jupiter.api.Test;
import software.amazon.awssdk.services.sqs.model.Message;

import java.time.Duration;
import java.util.Optional;

import static com.github.tomakehurst.wiremock.client.WireMock.equalTo;
import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson;
import static com.github.tomakehurst.wiremock.client.WireMock.matchingJsonPath;
import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.sast.store.ordermanagement.util.JsonComparatorFactory.emailMessageComparator;
import static org.awaitility.Awaitility.await;
import static org.skyscreamer.jsonassert.JSONAssert.assertEquals;

class BrimOrderEventSubscriberTest extends AbstractComponentTest {

    @Test
    void testBillingCompletedOrderMessage() throws Exception {
        CommercetoolsMockExtension.withDefaultResponse();
        CommercetoolsMockExtension.withOpenOrderResponse();
        UmpMockExtension.withDefaultResponse();
        MattermostMockExtension.withDefaultResponse();

        SqsMockExtension.sendMessage("/queue/bossstore-brim-order-events.fifo",
            loadJson("/json/ingress/order/order-completed.json"));

        await().untilAsserted(() -> CommercetoolsMockExtension.get()
            .verify(postRequestedFor(urlEqualTo("/glorious-new-store/orders/5c51ffdd-0465-4fd3-a8df-c68dd74bb498"))));

        CommercetoolsMockExtension.get().verify(1,
            postRequestedFor(urlEqualTo("/glorious-new-store/orders/5c51ffdd-0465-4fd3-a8df-c68dd74bb498"))
                .withRequestBody(equalToJson("""
                    {
                        "version": 3,
                        "actions": [
                            {
                                "orderState": "Complete",
                                "action": "changeOrderState"
                            }
                        ]
                    }""")));

        final Message userEmail = await()
            .atMost(Duration.ofSeconds(5))
            .until(() -> SqsMockExtension.receiveMessage("EmailserviceMail"), Optional::isPresent)
            .orElseThrow();

        assertEquals(loadJson("/json/email/order-confirmation-user.json"), userEmail.body(), emailMessageComparator());

        final Message companyEmail = await()
            .atMost(Duration.ofSeconds(5))
            .until(() -> SqsMockExtension.receiveMessage("EmailserviceMail"), Optional::isPresent)
            .orElseThrow();

        assertEquals(loadJson("/json/email/order-confirmation-company.json"), companyEmail.body(), emailMessageComparator());

        MattermostMockExtension.get().verify(1, postRequestedFor(urlEqualTo("/api/v4/posts?set_online=false"))
            .withRequestBody(matchingJsonPath("message", equalTo(
                ":moneybag: The company UPM Tester (Austria :flag-at:) successfully purchased 1 product [Hydraulic Hub] from seller UPM Tester in store rexroth. "
                    + "They bought 3 items with a total price of 600 EUR and paid via SEPA_DIRECTDEBIT. The order ID is 928374.")))
            .withRequestBody(matchingJsonPath("channel_id", equalTo("testchannelid"))));
    }

    @Test
    void testBillingOpenOrderMessage() throws Exception {
        CommercetoolsMockExtension.withDefaultResponse();
        CommercetoolsMockExtension.withOpenOrderResponse();
        UmpMockExtension.withDefaultResponse();
        MattermostMockExtension.withDefaultResponse();

        SqsMockExtension.sendMessage("/queue/bossstore-brim-order-events.fifo",
            loadJson("/json/ingress/order/order-open.json"));

        await().untilAsserted(() -> CommercetoolsMockExtension.get()
            .verify(postRequestedFor(urlEqualTo("/glorious-new-store/orders/5c51ffdd-0465-4fd3-a8df-c68dd74bb498"))));

        CommercetoolsMockExtension.get().verify(1,
            postRequestedFor(urlEqualTo("/glorious-new-store/orders/5c51ffdd-0465-4fd3-a8df-c68dd74bb498"))
                .withRequestBody(equalToJson("""
                    {
                        "version": 3,
                        "actions": [
                            {
                                "orderState": "Confirmed",
                                "action": "changeOrderState"
                            }
                        ]
                    }""")));

        final Message userEmail = await()
            .atMost(Duration.ofSeconds(5))
            .until(() -> SqsMockExtension.receiveMessage("EmailserviceMail"), Optional::isPresent)
            .orElseThrow();

        assertEquals(loadJson("/json/email/order-confirmation-user.json"), userEmail.body(), emailMessageComparator());

        final Message companyEmail = await()
            .atMost(Duration.ofSeconds(5))
            .until(() -> SqsMockExtension.receiveMessage("EmailserviceMail"), Optional::isPresent)
            .orElseThrow();

        assertEquals(loadJson("/json/email/order-confirmation-company.json"), companyEmail.body(), emailMessageComparator());

        MattermostMockExtension.get().verify(1, postRequestedFor(urlEqualTo("/api/v4/posts?set_online=false"))
            .withRequestBody(matchingJsonPath("message", equalTo(
                ":moneybag: The company UPM Tester (Austria :flag-at:) successfully purchased 1 product [Hydraulic Hub] from seller UPM Tester in store rexroth. "
                    + "They bought 3 items with a total price of 600 EUR and paid via SEPA_DIRECTDEBIT. The order ID is 928374.")))
            .withRequestBody(matchingJsonPath("channel_id", equalTo("testchannelid"))));
        MattermostMockExtension.get().resetRequests();

        CommercetoolsMockExtension.withConfirmedOrderResponse();

        SqsMockExtension.sendMessage("/queue/bossstore-brim-order-events.fifo",
            loadJson("/json/ingress/order/order-completed.json"));

        await().untilAsserted(() -> CommercetoolsMockExtension.get()
            .verify(postRequestedFor(urlEqualTo("/glorious-new-store/orders/5c51ffdd-0465-4fd3-a8df-c68dd74bb498"))));

        CommercetoolsMockExtension.get().verify(1,
            postRequestedFor(urlEqualTo("/glorious-new-store/orders/5c51ffdd-0465-4fd3-a8df-c68dd74bb498"))
                .withRequestBody(equalToJson("""
                    {
                        "version": 3,
                        "actions": [
                            {
                                "orderState": "Complete",
                                "action": "changeOrderState"
                            }
                        ]
                    }""")));

        await()
            .during(Duration.ofSeconds(5))
            .until(() -> SqsMockExtension.receiveMessage("EmailserviceMail"), Optional::isEmpty);

        MattermostMockExtension.get().verify(0, postRequestedFor(urlEqualTo("/api/v4/posts?set_online=false")));
    }

    @Test
    void testNumberOnlyData() throws Exception {
        CommercetoolsMockExtension.withDefaultResponse();

        SqsMockExtension.sendMessage("/queue/bossstore-brim-order-events.fifo",
            loadJson("/json/ingress/order/order-failed.json"));

        // we will only get some logs for now
        Thread.sleep(500);
    }
}
