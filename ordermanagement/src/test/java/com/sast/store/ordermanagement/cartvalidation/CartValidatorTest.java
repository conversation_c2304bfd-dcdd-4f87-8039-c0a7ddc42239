package com.sast.store.ordermanagement.cartvalidation;

import com.commercetools.api.models.cart.Cart;
import com.commercetools.api.models.cart.CartBuilder;
import com.commercetools.api.models.cart.LineItemBuilder;
import com.commercetools.api.models.common.CentPrecisionMoney;
import com.commercetools.api.models.product.AttributeBuilder;
import com.commercetools.api.models.product.ProductReferenceBuilder;
import com.commercetools.api.models.product.ProductVariantBuilder;
import com.commercetools.api.models.type.CustomFields;
import com.commercetools.api.models.type.FieldContainer;
import com.sast.store.external.ump.test.UmpMockExtension;
import com.sast.store.ordermanagement.AbstractComponentTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Set;
import java.util.function.Function;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

class CartValidatorTest extends AbstractComponentTest {

    @Autowired
    private CartValidator cartValidator;

    @Test
    void mostMinimalCartPassesValidation() {
        UmpMockExtension.withCompanyDetailsResponse();

        final Cart cart = CartBuilder.of()
            .lineItems(List.of())
            .custom(CustomFields.builder()
                .fields(FieldContainer.builder()
                    .addValue("tenant", "rexroth")
                    .addValue("companyId", "52967218-7c7e-4471-8391-4962e7bbe537")
                    .build())
                .buildUnchecked())
            .buildUnchecked();

        final Set<CartValidationViolation> actualViolations = cartValidator.validate(cart);

        assertThat(actualViolations).isEmpty();
    }

    @Test
    void cartWithoutTenantCannotBeValidated() {
        UmpMockExtension.withCompanyDetailsResponse();

        final Cart cart = buildCart(c -> c
            .custom(CustomFields.builder()
                .fields(FieldContainer.builder()
                    .addValue("companyId", "52967218-7c7e-4471-8391-4962e7bbe537")
                    .build())
                .buildUnchecked()));

        assertThatThrownBy(() -> cartValidator.validate(cart))
            .isInstanceOf(IllegalStateException.class);
    }

    @Test
    void cartWithoutCompanyIdCannotBeValidated() {
        UmpMockExtension.withCompanyDetailsResponse();

        final Cart cart = buildCart(c -> c
            .custom(CustomFields.builder()
                .fields(FieldContainer.builder()
                    .addValue("tenant", "rexroth")
                    .build())
                .buildUnchecked()));

        assertThatThrownBy(() -> cartValidator.validate(cart))
            .isInstanceOf(IllegalStateException.class);
    }

    @Test
    void cartExceedingTotalQuantityFailsValidation() {
        UmpMockExtension.withCompanyDetailsResponse();

        final Cart cart = buildCart(c -> c
            .lineItems(List.of())
            .totalLineItemQuantity(101L));

        final Set<CartValidationViolation> actualViolations = cartValidator.validate(cart);

        assertThat(actualViolations).hasSize(1)
            .first().extracting(CartValidationViolation::error)
            .isEqualTo(CartErrorCode.TOTAL_QUANTITY_LIMIT_EXCEEDED);
    }

    @Test
    void cartExceedingTotalAmountFailsValidation() {
        UmpMockExtension.withCompanyDetailsResponse();

        final Cart cart = buildCart(c -> c
            .lineItems(List.of())
            .totalPrice(CentPrecisionMoney.builder().centAmount(10_000_01L).fractionDigits(2).currencyCode("EUR").build()));

        final Set<CartValidationViolation> actualViolations = cartValidator.validate(cart);

        assertThat(actualViolations).hasSize(1)
            .first().extracting(CartValidationViolation::error)
            .isEqualTo(CartErrorCode.TOTAL_AMOUNT_LIMIT_EXCEEDED);
    }

    @Test
    void cartWithValidAddons() {
        UmpMockExtension.withCompanyDetailsResponse();

        final Cart cart = buildCart(c -> c
            .plusLineItems(LineItemBuilder.of()
                .key("mykey")
                .quantity(1L)
                .variant(ProductVariantBuilder.of()
                    .attributes(AttributeBuilder.of()
                        .name("addons")
                        .value(List.of(ProductReferenceBuilder.of()
                            .id("myproductid")
                            .buildUnchecked()))
                        .buildUnchecked())
                    .buildUnchecked())
                .buildUnchecked())
            .plusLineItems(LineItemBuilder.of()
                .quantity(1L)
                .productId("myproductid")
                .variant(ProductVariantBuilder.of()
                    .attributes(List.of())
                    .buildUnchecked())
                .custom(CustomFields.builder()
                    .fields(FieldContainer.builder()
                        .addValue("parentLineItemKey", "mykey")
                        .build())
                    .buildUnchecked())
                .buildUnchecked()));

        final Set<CartValidationViolation> actualViolations = cartValidator.validate(cart);

        assertThat(actualViolations).hasSize(0);
    }

    @Test
    void cartWithInvalidAddonsFailsValidation() {
        UmpMockExtension.withCompanyDetailsResponse();

        final Cart cart = buildCart(c -> c
            .plusLineItems(LineItemBuilder.of()
                .quantity(1L)
                .variant(ProductVariantBuilder.of()
                    .attributes(List.of())
                    .buildUnchecked())
                .custom(CustomFields.builder()
                    .fields(FieldContainer.builder()
                        .addValue("parentLineItemKey", "whatever")
                        .build())
                    .buildUnchecked())
                .buildUnchecked()));

        final Set<CartValidationViolation> actualViolations = cartValidator.validate(cart);

        assertThat(actualViolations).hasSize(1)
            .first().extracting(CartValidationViolation::error)
            .isEqualTo(CartErrorCode.ADDON_PARENT_NOT_FOUND);
    }

    @Test
    void addonsAreNotConsidderedValidParent() {
        UmpMockExtension.withCompanyDetailsResponse();

        final Cart cart = buildCart(c -> c
            .plusLineItems(LineItemBuilder.of()
                .key("mykey1")
                .quantity(1L)
                .variant(ProductVariantBuilder.of()
                    .attributes(AttributeBuilder.of()
                        .name("addons")
                        .value(List.of(ProductReferenceBuilder.of()
                            .id("myproductid")
                            .buildUnchecked()))
                        .buildUnchecked())
                    .buildUnchecked())
                .buildUnchecked())
            .plusLineItems(LineItemBuilder.of()
                .key("mykey2")
                .productId("myproductid")
                .quantity(1L)
                .variant(ProductVariantBuilder.of()
                    .attributes(List.of())
                    .buildUnchecked())
                .custom(CustomFields.builder()
                    .fields(FieldContainer.builder()
                        .addValue("parentLineItemKey", "mykey1")
                        .build())
                    .buildUnchecked())
                .buildUnchecked())
            .plusLineItems(LineItemBuilder.of()
                .quantity(1L)
                .productId("myproductid")
                .variant(ProductVariantBuilder.of()
                    .attributes(List.of())
                    .buildUnchecked())
                .custom(CustomFields.builder()
                    .fields(FieldContainer.builder()
                        .addValue("parentLineItemKey", "mykey2")
                        .build())
                    .buildUnchecked())
                .buildUnchecked()));

        final Set<CartValidationViolation> actualViolations = cartValidator.validate(cart);

        assertThat(actualViolations).hasSize(1)
            .first().extracting(CartValidationViolation::error)
            .isEqualTo(CartErrorCode.ADDON_PARENT_NOT_FOUND);
    }

    @Test
    void addonsNeedToHaveCorrectProductId() {
        UmpMockExtension.withCompanyDetailsResponse();

        final Cart cart = buildCart(c -> c
            .plusLineItems(LineItemBuilder.of()
                .key("mykey")
                .quantity(1L)
                .variant(ProductVariantBuilder.of()
                    .attributes(AttributeBuilder.of()
                        .name("addons")
                        .value(List.of(ProductReferenceBuilder.of()
                            .id("myproductid")
                            .buildUnchecked()))
                        .buildUnchecked())
                    .buildUnchecked())
                .buildUnchecked())
            .plusLineItems(LineItemBuilder.of()
                .quantity(1L)
                .variant(ProductVariantBuilder.of()
                    .attributes(List.of())
                    .buildUnchecked())
                .productId("wrongproductid")
                .custom(CustomFields.builder()
                    .fields(FieldContainer.builder()
                        .addValue("parentLineItemKey", "mykey")
                        .build())
                    .buildUnchecked())
                .buildUnchecked()));

        final Set<CartValidationViolation> actualViolations = cartValidator.validate(cart);

        assertThat(actualViolations).hasSize(1)
            .first().extracting(CartValidationViolation::error)
            .isEqualTo(CartErrorCode.ADDON_PARENT_NOT_FOUND);
    }

    private Cart buildCart(final Function<CartBuilder, CartBuilder> customizer) {
        final CartBuilder cartBuilder = CartBuilder.of()
            .custom(CustomFields.builder()
                .fields(FieldContainer.builder()
                    .addValue("tenant", "rexroth")
                    .addValue("companyId", "52967218-7c7e-4471-8391-4962e7bbe537")
                    .build())
                .buildUnchecked());
        return customizer.apply(cartBuilder)
            .buildUnchecked();
    }
}
