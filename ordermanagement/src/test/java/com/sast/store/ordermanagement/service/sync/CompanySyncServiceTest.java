package com.sast.store.ordermanagement.service.sync;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.gen.client.ump.api.UmpExternalCompanyDto;
import com.sast.store.ordermanagement.service.CartService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class CompanySyncServiceTest {
    @Mock private CartService cartService;
    @InjectMocks private CompanySyncService service;

    @Test
    void handleCompanyUpdate_shouldDelegateToCartService() {
        final UmpExternalCompanyDto dto = new UmpExternalCompanyDto().companyId("my-id");
        final Tenant tenant = Tenant.REXROTH;

        service.handleCompanyUpdate(dto, tenant);

        verify(cartService).syncCompanyBilling(tenant, dto);
    }
}