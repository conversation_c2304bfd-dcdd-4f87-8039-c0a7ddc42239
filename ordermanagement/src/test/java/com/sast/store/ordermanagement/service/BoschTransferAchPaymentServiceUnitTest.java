package com.sast.store.ordermanagement.service;

import com.commercetools.api.models.cart.Cart;
import com.commercetools.api.models.cart.CartBuilder;
import com.commercetools.api.models.common.CentPrecisionMoneyBuilder;
import com.commercetools.api.models.payment.Payment;
import com.commercetools.api.models.payment.PaymentBuilder;
import com.commercetools.api.models.payment.TransactionState;
import com.commercetools.api.models.type.CustomFieldsBuilder;
import com.commercetools.api.models.type.FieldContainerBuilder;
import com.sast.store.brimtegration.apimodel.common.payment.BoschTransferAchCreditPayment;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.ordermanagement.api.AuthorizationInformationDto;
import com.sast.store.ordermanagement.api.CheckoutInformationDto;
import com.sast.store.ordermanagement.config.AppConfiguration;
import com.sast.store.ordermanagement.dto.AuthorizationResultDto;
import com.sast.store.ordermanagement.payment.SupportedCartPredicates;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BoschTransferAchPaymentServiceUnitTest {
    private static final String SELLER_COMPANY_ID = "67bfa306-db09-41e7-a938-77d132504b1b";
    private static final String ACCOUNT_HOLDER = "Kiroshi Optics";
    private static final String ROUTING_NUMBER = "********";
    private static final String ACCOUNT_NUMBER = "9832714";
    private static final String BIC = "AJKHSDK";
    private static final String BANK_NAME = "Arasaka";


    @Mock
    private CountriesService countriesService;

    @Mock
    private SellerService sellerService;

    @Mock
    private CommercetoolsPaymentFactory commercetoolsPaymentFactory;

    @Mock
    private SupportedCartPredicates supportedCartPredicates;

    private BoschTransferAchPaymentService boschTransferAchPaymentService;

    @BeforeEach
    public void setUp() {
        final AppConfiguration appConfiguration = new AppConfiguration("L", Map.of(), Map.of(
                SELLER_COMPANY_ID, new AppConfiguration.SellerCompanyConfiguration(
                        null,
                        new AppConfiguration.BoschTransferAchConfiguration(
                                ACCOUNT_HOLDER, ROUTING_NUMBER, ACCOUNT_NUMBER, BIC, BANK_NAME
                        )
                )
        ), null);

        boschTransferAchPaymentService = new BoschTransferAchPaymentService(
                countriesService, appConfiguration, sellerService, commercetoolsPaymentFactory, supportedCartPredicates
        );
    }

    @Test
    public void testSupportedCart() {
        final Cart cart = createValidCart();

        when(countriesService.isEnabled(Tenant.REXROTH, "de", "EUR", boschTransferAchPaymentService))
                .thenReturn(true);
        when(sellerService.getSellerCompanyId(cart)).thenReturn(Optional.of(SELLER_COMPANY_ID));

        final boolean actualSupportsCart = boschTransferAchPaymentService.supportsCart(cart);

        assertThat(actualSupportsCart).isTrue();
    }

    @Test
    public void testCartWithZeroAmountIsUnsupported() {
        final Cart cart = createValidCart();
        cart.getTotalPrice().setCentAmount(0L);
        when(supportedCartPredicates.hasZeroTotalPrice(cart)).thenReturn(true);

        final boolean actualSupportsCart = boschTransferAchPaymentService.supportsCart(cart);

        assertThat(actualSupportsCart).isFalse();
    }

    @Test
    public void testCartWithZeroAmountAndConsumptionProductIsSupported() {
        final Cart cart = createValidCart();
        cart.getTotalPrice().setCentAmount(0L);
        when(countriesService.isEnabled(Tenant.REXROTH, "de", "EUR", boschTransferAchPaymentService))
            .thenReturn(true);
        when(sellerService.getSellerCompanyId(cart)).thenReturn(Optional.of(SELLER_COMPANY_ID));
        when(supportedCartPredicates.hasZeroTotalPrice(cart)).thenReturn(true);
        when(supportedCartPredicates.containsLicenseTypes(cart, "CONSUMPTION")).thenReturn(true);

        final boolean actualSupportsCart = boschTransferAchPaymentService.supportsCart(cart);

        assertThat(actualSupportsCart).isTrue();
    }

    @Test
    public void testCartIsUnsupportedIfPaymentMethodNotEnabledForCountry() {
        final Cart cart = createValidCart();

        when(countriesService.isEnabled(Tenant.REXROTH, "de", "EUR", boschTransferAchPaymentService))
                .thenReturn(false);

        final boolean actualSupportsCart = boschTransferAchPaymentService.supportsCart(cart);

        assertThat(actualSupportsCart).isFalse();
    }

    @Test
    public void testCartIsUnsupportedIfSellerIsNotConfiguredForAch() {
        final Cart cart = createValidCart();

        when(countriesService.isEnabled(Tenant.REXROTH, "de", "EUR", boschTransferAchPaymentService))
                .thenReturn(true);
        when(sellerService.getSellerCompanyId(cart)).thenReturn(Optional.of("somethingElse"));

        final boolean actualSupportsCart = boschTransferAchPaymentService.supportsCart(cart);

        assertThat(actualSupportsCart).isFalse();
    }

    @Test
    public void testCartIsUnsupportedIfSellerCannotBedetermined() {
        final Cart cart = createValidCart();

        when(countriesService.isEnabled(Tenant.REXROTH, "de", "EUR", boschTransferAchPaymentService))
                .thenReturn(true);
        when(sellerService.getSellerCompanyId(cart)).thenReturn(Optional.empty());

        final boolean actualSupportsCart = boschTransferAchPaymentService.supportsCart(cart);

        assertThat(actualSupportsCart).isFalse();
    }

    @Test
    public void testPrepareCheckoutReturnsCorrectInformation() {
        final Cart cart = createValidCart();

        when(sellerService.getSellerCompanyId(cart)).thenReturn(Optional.of(SELLER_COMPANY_ID));

        final CheckoutInformationDto actualCheckoutInfo = boschTransferAchPaymentService.prepareCheckout(cart);
        assertThat(actualCheckoutInfo).isNotNull()
                .isEqualTo(CheckoutInformationDto.builder()
                        .paymentMethodId("BOSCH_TRANSFER/ACH_CREDIT")
                        .boschAchCreditInformation(CheckoutInformationDto.BoschAchCreditInformation.builder()
                                .accountHolder(ACCOUNT_HOLDER)
                                .accountNumber(ACCOUNT_NUMBER)
                                .routingNumber(ROUTING_NUMBER)
                                .bic(BIC)
                                .bankName(BANK_NAME)
                                .build())
                        .build());
    }

    @Test
    public void testPrepareCheckoutThrowsIfSellerCannotBeDetermined() {
        final Cart cart = createValidCart();

        when(sellerService.getSellerCompanyId(cart)).thenReturn(Optional.empty());

        assertThatThrownBy(() ->  boschTransferAchPaymentService.prepareCheckout(cart))
            .isInstanceOf(IllegalStateException.class);
    }

    @Test
    public void testPrepareCheckoutThrowsIfSellerIsUnconfigured() {
        final Cart cart = createValidCart();

        when(sellerService.getSellerCompanyId(cart)).thenReturn(Optional.of("foobarbaz"));

        assertThatThrownBy(() ->  boschTransferAchPaymentService.prepareCheckout(cart))
                .isInstanceOf(IllegalStateException.class);
    }

    @Test
    public void testAuthorizeSavesPayment() {
        final Cart cart = createValidCart();

        when(commercetoolsPaymentFactory.createAuthorizationPayment(cart, TransactionState.PENDING, boschTransferAchPaymentService))
                .thenReturn(PaymentBuilder.of().id("barbatz").buildUnchecked());

        final AuthorizationInformationDto actualAuthInfo = boschTransferAchPaymentService.authorize(cart);

        assertThat(actualAuthInfo).isNotNull()
                .isEqualTo(AuthorizationInformationDto.builder().paymentId("barbatz").build());
    }

    @Test
    public void getAuthorizationResultReturnsCorrectAuthData() {
        final Payment payment = createValidPayment();

        final AuthorizationResultDto actualAuthResult = boschTransferAchPaymentService.getAuthorizationResult(payment);

        assertThat(actualAuthResult).isNotNull()
                .isEqualTo(AuthorizationResultDto.builder()
                        .brimPaymentData(BoschTransferAchCreditPayment.builder()
                                .routingNumber(ROUTING_NUMBER)
                                .accountNumber(ACCOUNT_NUMBER)
                                .bic(BIC)
                                .bankName(BANK_NAME)
                                .build())
                        .status(AuthorizationResultDto.Status.SUCCESS)
                        .build());

    }

    @Test
    public void getAuthorizationResultThrowsIfPaymentHasNoSeller() {
        final Payment payment = createValidPayment();
        payment.setCustom(CustomFieldsBuilder.of().buildUnchecked());

        assertThatThrownBy(() -> boschTransferAchPaymentService.getAuthorizationResult(payment))
            .isInstanceOf(IllegalStateException.class);
    }

    @Test
    public void getAuthorizationResultThrowsIfSellerHasNoAchConfig() {
        final Payment payment = createValidPayment();
        payment.setCustom(CustomFieldsBuilder.of()
                .fields(FieldContainerBuilder.of()
                        .addValue("sellerCompanyId", "foobarbaz")
                        .buildUnchecked())
                .buildUnchecked());

        assertThatThrownBy(() -> boschTransferAchPaymentService.getAuthorizationResult(payment))
                .isInstanceOf(IllegalStateException.class);
    }

    private Cart createValidCart() {
        return CartBuilder.of()
                .totalPrice(CentPrecisionMoneyBuilder.of()
                        .centAmount(1L)
                        .currencyCode("EUR")
                        .fractionDigits(2)
                        .buildUnchecked())
                .country("de")
                .custom(CustomFieldsBuilder.of()
                        .fields(FieldContainerBuilder.of()
                                .addValue("tenant", "rexroth")
                                .buildUnchecked())
                        .buildUnchecked())
                .buildUnchecked();
    }

    private Payment createValidPayment() {
        return PaymentBuilder.of()
                .custom(CustomFieldsBuilder.of()
                        .fields(FieldContainerBuilder.of()
                                .addValue("sellerCompanyId", SELLER_COMPANY_ID)
                                .buildUnchecked())
                        .buildUnchecked())
                .buildUnchecked();
    }

}
