package com.sast.store.ordermanagement.company;

import com.sast.store.external.ump.test.UmpMockEvent;
import com.sast.store.ordermanagement.AbstractComponentTest;
import com.sast.store.ordermanagement.util.CommercetoolsMockExtension;
import com.sast.store.testing.awsmockup.junit.SqsMockExtension;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.Test;

import java.time.Duration;

import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson;
import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;

class UmpNotificationConsumerIntegrationTest extends AbstractComponentTest {

    @Test
    void whenCompanyUpdateConsumed_thenBothSyncServicesCalled() throws Exception {
        CommercetoolsMockExtension.withDefaultResponse();
        CommercetoolsMockExtension.withCartsNonEmptyResponse();

        SqsMockExtension.sendMessage("/queue/bossstore-ordermanagement-account-events",
                UmpMockEvent.COMPANY_UPDATE.payload());

        Awaitility.await().during(Duration.ofSeconds(3)).atMost(Duration.ofSeconds(5))
                .until(() -> CommercetoolsMockExtension.get()
                        .countRequestsMatching(postRequestedFor(urlMatching("/.+/carts/key=.+")).build())
                        .getCount() == 2);

        CommercetoolsMockExtension.get()
                .verify(1, postRequestedFor(urlMatching("/.+/carts/key=.+"))
                        .withRequestBody(equalToJson(
                                """
                                        {
                                          "version": 3,
                                          "actions": [
                                            {
                                              "address": {
                                                "country": "AT",
                                                "streetName": "Bistdudeppertgassl",
                                                "streetNumber": "12",
                                                "postalCode": "4020",
                                                "city": "Linz",
                                                "region": "Linz-Land",
                                                "state": "Oberösterreich",
                                                "email": "<EMAIL>"
                                              },
                                              "action": "setBillingAddress"
                                            },
                                            {
                                              "action": "recalculate"
                                            }
                                          ]
                                        }
                                        """)));

        CommercetoolsMockExtension.get()
                .verify(1, postRequestedFor(urlMatching("/.+/carts/key=.+"))
                        .withRequestBody(equalToJson(
                                """
                                              {
                                                "version": 3,
                                                "actions": [
                                                  {
                                                    "customerGroup": {
                                                      "id": "e0723008-15c7-4dcb-8aba-028ad747d90b",
                                                      "typeId": "customer-group"
                                                    },
                                                    "action": "setCustomerGroup"
                                                  },
                                                  {
                                                    "action": "recalculate"
                                                  }
                                                ]
                                              }
                                        """)));

    }
}