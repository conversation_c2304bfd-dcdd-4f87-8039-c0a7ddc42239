package com.sast.store.ordermanagement.invoice.util;

import com.sast.store.brimtegration.apimodel.common.document.DocumentLineItem;
import com.sast.store.brimtegration.apimodel.common.document.PlatformDocumentCategory;
import com.sast.store.brimtegration.apimodel.common.document.PlatformDocumentType;
import com.sast.store.brimtegration.apimodel.common.document.S3File;
import com.sast.store.brimtegration.apimodel.events.ingress.document.data.BillingBuyerDocumentCreated;
import com.sast.store.brimtegration.apimodel.events.ingress.document.data.BillingBuyerDocumentCreated.BillingBuyerDocumentCreatedBuilder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Currency;
import java.util.List;
import java.util.Set;
import java.util.function.Function;

public final class TestDataBuilder {
    public static final Currency EUR = Currency.getInstance("EUR");

    private TestDataBuilder() {
    }

    public static BillingBuyerDocumentCreated invoiceNotification(
        final Function<BillingBuyerDocumentCreatedBuilder, BillingBuyerDocumentCreatedBuilder> consumer) {
        final BillingBuyerDocumentCreatedBuilder billingBuyerDocumentCreated = BillingBuyerDocumentCreated.builder()
            .documentType(PlatformDocumentType.INVOICE)
            .documentCategory(PlatformDocumentCategory.INVOICE)
            .documentNumber("12345679")
            .orderNumbers(Set.of("1234"))
            .documentDate(LocalDate.parse("2024-08-15"))
            .creationDate(LocalDate.parse("2024-08-16"))
            .recipientCompanyId("i am a company ID.")
            .lineItems(List.of(
                DocumentLineItem.builder()
                    .position(1)
                    .productId("firstProduct")
                    .quantity(BigDecimal.ONE)
                    .netAmount(new BigDecimal("10.00"))
                    .currency(EUR)
                    .build(),
                DocumentLineItem.builder()
                    .position(2)
                    .productId("secondProduct")
                    .quantity(BigDecimal.TWO)
                    .netAmount(new BigDecimal("20.00"))
                    .currency(EUR)
                    .build()))
            .currency(EUR)
            .netAmount(new BigDecimal("30.00"))
            .grossAmount(new BigDecimal("35.70"))
            .taxAmount(new BigDecimal("5.70"))
            .file(S3File.builder()
                .key("datei")
                .bucket("eimer")
                .sha256sum("abc123def")
                .contentType("application/pdf")
                .build());
        return consumer
            .apply(billingBuyerDocumentCreated)
            .build();
    }

    public static BillingBuyerDocumentCreated creditNoteNotification(
        final Function<BillingBuyerDocumentCreatedBuilder, BillingBuyerDocumentCreatedBuilder> consumer) {
        final BillingBuyerDocumentCreatedBuilder billingBuyerDocumentCreated = BillingBuyerDocumentCreated.builder()
            .documentType(PlatformDocumentType.CREDIT_NOTE)
            .documentCategory(PlatformDocumentCategory.INVOICE)
            .documentNumber("documentNumber")
            .orderNumbers(Set.of("1234"))
            .documentDate(LocalDate.parse("2024-09-15"))
            .creationDate(LocalDate.parse("2024-09-16"))
            .recipientCompanyId("i am a company ID.")
            .parentDocumentNumber("parentDocumentNumber")
            .lineItems(List.of(
                DocumentLineItem.builder()
                    .position(1)
                    .productId("firstProduct")
                    .quantity(BigDecimal.ONE)
                    .netAmount(new BigDecimal("-10.00"))
                    .currency(EUR)
                    .build(),
                DocumentLineItem.builder()
                    .position(2)
                    .productId("secondProduct")
                    .quantity(BigDecimal.TWO)
                    .netAmount(new BigDecimal("-20.00"))
                    .currency(EUR)
                    .build()))
            .currency(EUR)
            .netAmount(new BigDecimal("-30.00"))
            .grossAmount(new BigDecimal("-35.70"))
            .taxAmount(new BigDecimal("-5.70"))
            .file(S3File.builder()
                .key("dateiCreditNote")
                .bucket("eimer")
                .sha256sum("abc123def")
                .contentType("application/pdf")
                .build());
        return consumer
            .apply(billingBuyerDocumentCreated)
            .build();
    }
}
