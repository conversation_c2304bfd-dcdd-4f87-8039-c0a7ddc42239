package com.sast.store.ordermanagement;

import com.sast.store.external.countriesservice.util.CountriesServiceMockExtension;
import com.sast.store.external.pgw.test.PgwMockExtension;
import com.sast.store.external.ump.test.UmpMockExtension;
import com.sast.store.ordermanagement.util.CommercetoolsMockExtension;
import com.sast.store.ordermanagement.util.MattermostMockExtension;
import com.sast.store.ordermanagement.util.OrdermanagementTestDataGenerator;
import com.sast.store.testing.awsmockup.junit.DynamodbMockExtension;
import com.sast.store.testing.awsmockup.junit.S3MockExtension;
import com.sast.store.testing.awsmockup.junit.SnsMockExtension;
import com.sast.store.testing.awsmockup.junit.SqsMockExtension;
import com.tngtech.keycloakmock.api.ServerConfig;
import com.tngtech.keycloakmock.junit5.KeycloakMockExtension;
import io.restassured.RestAssured;
import io.restassured.filter.log.RequestLoggingFilter;
import io.restassured.filter.log.ResponseLoggingFilter;
import io.restassured.specification.RequestSpecification;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.util.StreamUtils;
import software.amazon.awssdk.auth.credentials.AnonymousCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;

import static com.tngtech.keycloakmock.api.TokenConfig.aTokenConfig;

@ExtendWith(CommercetoolsMockExtension.class)
@ExtendWith(PgwMockExtension.class)
@ExtendWith(UmpMockExtension.class)
@ExtendWith(CountriesServiceMockExtension.class)
@ExtendWith(SqsMockExtension.class)
@ExtendWith(SnsMockExtension.class)
@ExtendWith(DynamodbMockExtension.class)
@ExtendWith(S3MockExtension.class)
@ExtendWith(MattermostMockExtension.class)
@SpringBootTest(classes = OrdermanagementApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, properties = {
    // disable caching of health endpoints
    "endpoints.health.time-to-live=0",
    "bossstore.hazelcast.enabled=local",
    "bossstore.pgw.apiKey=dummy-api-key",
    "bossstore.pgw.url=http://localhost:40001",
    "bossstore.ump.url=http://localhost:40101",
    "bossstore.ump.username=admin",
    "bossstore.mattermost.url=http://localhost:34861",
    "bossstore.mattermost.apiKey=testkey",
    "bossstore.mattermost.channelId=testchannelid",
    "bossstore.scheduling.enabled=false",
    "logging.level.com.sast=TRACE",
    "logging.level.commercetools=DEBUG",
    "bossstore.countriesservice.url=http://localhost:40002",
    "spring.cloud.aws.region.static=eu-central-1",
    "spring.cloud.aws.credentials.access-key=fakekey",
    "spring.cloud.aws.credentials.secret-key=fakekey",
})
@Import({ CommercetoolsMockExtension.CommercetoolsMockClientConfig.class, AbstractComponentTest.S3ClientConfig.class })
public abstract class AbstractComponentTest {

    protected static final String COMPANY_ID = UUID.randomUUID().toString();

    // CHECKSTYLE OFF: StaticVariableNameCheck
    protected static RequestSpecification VALID_KEYCLOAK_TOKEN;

    @RegisterExtension
    private static final KeycloakMockExtension KEYCLOAK_MOCK = new KeycloakMockExtension(
        ServerConfig.aServerConfig()
            .withPort(8000)
            .withDefaultRealm("baam")
            .build());

    @Value("http://localhost:${local.server.port}")
    protected String host;

    @Autowired
    private OrdermanagementTestDataGenerator testDataGenerator;

    @DynamicPropertySource
    private static void overrideProperties(final DynamicPropertyRegistry registry) {
        registry.add("spring.cloud.aws.sqs.endpoint", () -> "http://localhost:" + SqsMockExtension.RANDOM_PORT);
        registry.add("spring.cloud.aws.sns.endpoint", () -> "http://localhost:" + SnsMockExtension.getDefaultPort());
        registry.add("spring.cloud.aws.dynamodb.endpoint", () -> "http://localhost:" + DynamodbMockExtension.PORT);
        registry.add("spring.cloud.aws.s3.endpoint", () -> "http://localhost:" + S3MockExtension.PORT);
    }

    @BeforeAll
    public static void setUpUrl() {
        Locale.setDefault(Locale.US);
        RestAssured.enableLoggingOfRequestAndResponseIfValidationFails();
        RestAssured.filters(new RequestLoggingFilter(), new ResponseLoggingFilter());
    }

    @BeforeAll
    public static void setInitialData() {
        CommercetoolsMockExtension.withGetTypesResponse();
    }

    @BeforeEach
    public void setUpKeycloakToken() {
        setupKeycloakToken("VIEW_ORDER_DETAILS", "EDIT_PAYMENT_DETAILS", "VIEW_PRICE", "PLACE_ORDER", "MANAGE_BACKOFFICE", "DEFAULT");
    }

    @BeforeEach
    public void cleanTestData() {
        testDataGenerator.clean();
    }

    protected static void setupKeycloakToken(final String... roles) {
        final String accessToken = KEYCLOAK_MOCK.getAccessToken(aTokenConfig()
            .withSubject("user")
            .withAuthorizedParty("bossstore-frontend")
            .withClaims(Map.of(
                "resource_access", Map.of("bossstore-backend", Map.of("roles", Arrays.asList(roles))),
                "name", "John Doe",
                "preferred_username", "7cb0fdd4-9503-41e0-a279-84ad098c8a00",
                "given_name", "John",
                "family_name", "Doe",
                "email", "<EMAIL>",
                "company_name", "THE Company",
                "communication_language", "de",
                "company_id", COMPANY_ID))
            .build());
        VALID_KEYCLOAK_TOKEN = RestAssured.given().auth().preemptive()
            .oauth2(accessToken);
    }

    protected String loadJson(final String path) {
        try (final InputStream resourceInputStream = getClass().getResourceAsStream(path)) {
            if (resourceInputStream == null) {
                throw new FileNotFoundException(path);
            }
            return StreamUtils.copyToString(resourceInputStream, StandardCharsets.UTF_8);
        } catch (final IOException e) {
            throw new RuntimeException(e);
        }
    }

    @TestConfiguration
    public static class S3ClientConfig {
        private static final Logger LOG = LoggerFactory.getLogger(S3ClientConfig.class);

        // for some reason the spring.cloud.aws.s3.endpoint property is not picked up automatically
        @Bean
        @Primary
        public S3Client s3Client(@Value("${spring.cloud.aws.region.static}") final Region region,
            @Value("${spring.cloud.aws.s3.endpoint:unset}") final String endpoint) {
            LOG.warn("using {} as s3 api endpoint", endpoint);
            return S3Client.builder().region(region)
                .endpointOverride(URI.create(endpoint))
                .credentialsProvider(AnonymousCredentialsProvider.create())
                .forcePathStyle(true)
                .build();
        }
    }

}
