package com.sast.store.ordermanagement.cartvalidation;

import com.commercetools.api.models.cart.Cart;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.gen.client.ump.api.UmpExternalCompanyDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CartValidatorUnitTest {

    @Mock
    private CartValidationRule firstRule;

    @Mock
    private CartValidationRule secondRule;

    @Mock
    private CartValidationContextProvider cartValidationContextProvider;

    private CartValidator cartValidator;

    @Mock
    private Cart cart;

    private CartValidationContext validationContext;

    @BeforeEach
    void setUp() {
        cartValidator = new CartValidator(Set.of(firstRule, secondRule), cartValidationContextProvider);
        validationContext = CartValidationContext.builder()
                .tenant(Tenant.REXROTH)
                .umpCompany(new UmpExternalCompanyDto())
                .build();
    }

    @Test
    void cartPassesIfAllRulesReturnEmpty() {
        when(cartValidationContextProvider.cartContext(eq(cart))).thenReturn(validationContext);
        when(firstRule.apply(eq(cart), eq(validationContext))).thenReturn(Set.of());
        when(secondRule.apply(eq(cart), eq(validationContext))).thenReturn(Set.of());

        final Set<CartValidationViolation> actualViolations = cartValidator.validate(cart);

        verify(firstRule, times(1)).apply(eq(cart), eq(validationContext));
        verify(secondRule, times(1)).apply(eq(cart), eq(validationContext));
        assertThat(actualViolations).isEmpty();
    }

    @Test
    void violationsAccumulate() {
        final Set<CartValidationViolation> firstRuleViolations = Set.of(
                CartValidationViolation.builder()
                        .message("rule1foo")
                        .error(CartErrorCode.TOTAL_AMOUNT_LIMIT_EXCEEDED)
                        .build(),
                CartValidationViolation.builder()
                        .message("rule1bar")
                        .error(CartErrorCode.TOTAL_AMOUNT_LIMIT_EXCEEDED)
                        .build()
        );

        final Set<CartValidationViolation> secondRuleViolations = Set.of(
                CartValidationViolation.builder()
                        .message("rule2foo")
                        .error(CartErrorCode.TOTAL_AMOUNT_LIMIT_EXCEEDED)
                        .build(),
                CartValidationViolation.builder()
                        .message("rule2bar")
                        .error(CartErrorCode.TOTAL_AMOUNT_LIMIT_EXCEEDED)
                        .build()
        );

        when(cartValidationContextProvider.cartContext(eq(cart))).thenReturn(validationContext);
        when(firstRule.apply(eq(cart), any())).thenReturn(firstRuleViolations);
        when(secondRule.apply(eq(cart), any())).thenReturn(secondRuleViolations);

        final Set<CartValidationViolation> actualViolations = cartValidator.validate(cart);

        verify(firstRule, times(1)).apply(eq(cart), eq(validationContext));
        verify(secondRule, times(1)).apply(eq(cart), eq(validationContext));

        assertThat(actualViolations)
                .hasSize(4)
                .containsAll(firstRuleViolations)
                .containsAll(secondRuleViolations);
    }

    @Test
    void ruleExceptionPropagates() {
        when(cartValidationContextProvider.cartContext(eq(cart))).thenReturn(validationContext);
        lenient().when(firstRule.apply(eq(cart), eq(validationContext))).thenThrow(new RuntimeException());
        lenient().when(secondRule.apply(eq(cart), eq(validationContext))).thenReturn(Set.of());

        assertThatThrownBy(() -> cartValidator.validate(cart));
    }

    @Test
    void validationContextProviderExceptionPropagates() {
        when(cartValidationContextProvider.cartContext(eq(cart))).thenThrow(new RuntimeException());

        assertThatThrownBy(() -> cartValidator.validate(cart));
    }
}
