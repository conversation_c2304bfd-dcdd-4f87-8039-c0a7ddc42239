package com.sast.store.ordermanagement.util;

import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.defaultconfig.ApiRootBuilder;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.github.tomakehurst.wiremock.junit5.WireMockExtension;
import com.sast.store.external.commercetools.CommercetoolsConfiguration;
import com.sast.store.external.commercetools.CommercetoolsRestMetricsJerseyClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.containing;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching;

public class CommercetoolsMockExtension extends WireMockExtension {

    private static CommercetoolsMockExtension instance;

    public CommercetoolsMockExtension() {
        super(WireMockExtension
            .extensionOptions()
            .options(WireMockConfiguration.options()
                .port(40000))
            .configureStaticDsl(true));
        instance = this;
    }

    public static CommercetoolsMockExtension get() {
        return instance;
    }

    public static CommercetoolsMockExtension withDefaultResponse() {
        withProductProjectionsSearchResponse();
        withGetCartsEmptyResponse();
        withCartCreationResponse();
        withCartRecalculateResponse(3, 60000);
        withCartAddLineItemResponse();
        withCartDeleteResponse();
        withCustomerGroupsResponse();
        withOrderResponse();
        withCreateOrderResponse();
        withUpdateOrderResponse();
        withCreatePaymentsResponse();
        withCartAddPaymentResponse();
        withSetCusomFieldResponse();
        withChangeLineItemQuantityResponse();
        withGetPaymentResponse();
        withOrdersSearchResponse();
        withGetTypesResponse();
        return instance;
    }

    public static CommercetoolsMockExtension withCreatePaymentsResponse() {
        instance.stubFor(WireMock.post(urlPathMatching("/.*/payments"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody(
                    """
                        {
                            "id": "81bb42ea-a169-4b8e-85ed-0be600664aa0",
                            "amountPlanned": {
                                "type": "centPrecision",
                                "currencyCode": "EUR",
                                "centAmount": 60000,
                                "fractionDigits": 2
                            },
                            "paymentMethodInfo": {
                                "paymentInterface": "BOSCH_TRANSFER",
                                "method": "BOSCH_TRANSFER/SEPA_CREDIT"
                            },
                            "custom": {
                                "fields": {
                                    "sellerCompanyId": "fc82b363-c3f7-4797-a97c-af37711ac1fd"
                                }
                            },
                            "paymentStatus": {},
                            "transactions": [
                                {
                                    "id": "11c02393-d9f1-46b2-bdc3-ccf8cb4136e2",
                                    "timestamp": "2024-11-28T12:35:28.575Z",
                                    "type": "Authorization",
                                    "amount": {
                                        "type": "centPrecision",
                                        "currencyCode": "EUR",
                                        "centAmount": 60000,
                                        "fractionDigits": 2
                                    },
                                    "state": "Pending"
                                }
                            ],
                            "interfaceInteractions": []
                        }
                        ]""")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withGetPaymentResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/payments/.*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody("""
                    {
                        "id": "{{request.pathSegments.2}}",
                        "version": 4,
                        "versionModifiedAt": "2024-07-04T11:33:54.105Z",
                        "lastMessageSequenceNumber": 2,
                        "createdAt": "2024-07-04T11:33:32.685Z",
                        "lastModifiedAt": "2024-07-04T11:33:54.105Z",
                        "lastModifiedBy": {
                            "clientId": "Dimh0TYzpM0NHPQZ5qSPUgA_",
                            "isPlatformClient": false
                        },
                        "createdBy": {
                            "clientId": "Dimh0TYzpM0NHPQZ5qSPUgA_",
                            "isPlatformClient": false
                        },
                        "interfaceId": "c41127f736eb4d68a5f0aaaa5a6a823f",
                        "amountPlanned": {
                            "type": "centPrecision",
                            "currencyCode": "EUR",
                            "centAmount": 30000,
                            "fractionDigits": 2
                        },
                        "paymentMethodInfo": {
                            "paymentInterface": "BOSCH_TRANSFER",
                            "method": "BOSCH_TRANSFER/SEPA_CREDIT"
                        },
                        "custom": {
                            "type": {
                                "typeId": "type",
                                "id": "066df4e6-1380-4ba7-b9de-ee6afbe6da81"
                            },
                            "fields": {
                                "sellerCompanyId": "fc82b363-c3f7-4797-a97c-af37711ac1fd"
                            }
                        },
                        "paymentStatus": {},
                        "transactions": [
                            {
                                "id": "9a8d9063-5277-4e28-95f2-27238c63efa1",
                                "timestamp": "2024-07-04T11:33:32.654Z",
                                "type": "Authorization",
                                "amount": {
                                    "type": "centPrecision",
                                    "currencyCode": "EUR",
                                    "centAmount": 0,
                                    "fractionDigits": 2
                                },
                                "interactionId": "c41127f736eb4d68a5f0aaaa5a6a823f",
                                "state": "Pending"
                            }
                        ],
                        "interfaceInteractions": []
                    }""")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withGetPaymentNotFoundResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/payments/.*"))
            .willReturn(aResponse()
                .withStatus(404)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody(
                    """
                        {
                            "statusCode": 404,
                            "message": "The Resource with ID '{{request.pathSegments.2}}' was not found.",
                            "errors": [
                                {
                                    "code": "ResourceNotFound",
                                    "message": "The Resource with ID '{{request.pathSegments.2}}' was not found."
                                }
                            ]
                        }""")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withSetCusomFieldResponse() {
        instance.stubFor(WireMock.post(urlPathMatching("/.*/carts/.*"))
            .withRequestBody(containing("setCustomField"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("commercetools/cart-get-response.json.hbs")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withCartAddPaymentResponse() {
        instance.stubFor(WireMock.post(urlPathMatching("/.*/carts/.*"))
            .withRequestBody(containing("addPayment"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("commercetools/cart-get-response.json.hbs")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withChangeLineItemQuantityResponse() {
        instance.stubFor(WireMock.post(urlPathMatching("/.*/carts/.*"))
            .withRequestBody(containing("changeLineItemQuantity"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("commercetools/cart-get-response.json.hbs")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withChangeLineItemQuantityFailedResponse() {
        instance.stubFor(WireMock.post(urlPathMatching("/.*/carts/.*"))
            .withRequestBody(containing("changeLineItemQuantity"))
            .willReturn(aResponse()
                .withStatus(400)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody("""
                    {
                        "statusCode": 400,
                        "message": "cartvalidation failed",
                        "errors": [
                            {
                                "code": "InvalidInput",
                                "message": "cartvalidation failed",
                                "errorByExtension": {
                                    "id": "e00154de-6f89-4a3b-a723-2925a1f702ba",
                                    "key": "cartvalidation"
                                }
                            }
                        ]
                    }""")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withChangeLineItemQuantityTimeoutResponse() {
        instance.stubFor(WireMock.post(urlPathMatching("/.*/carts/.*"))
            .withRequestBody(containing("changeLineItemQuantity"))
            .inScenario("")
            .willReturn(aResponse()
                .withStatus(504)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody("""
                    {
                        "statusCode": 504,
                        "message": "Extension did not respond in time.",
                        "errors": [
                            {
                                "code": "ExtensionNoResponse",
                                "message": "Extension did not respond in time.",
                                "extensionId": "e00154de-6f89-4a3b-a723-2925a1f702ba",
                                "extensionKey": "cartvalidation"
                            }
                        ]
                    }""")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withCustomerGroupsResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/customer-groups/key=.*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody(
                    """
                        {
                          "id": "e0723008-15c7-4dcb-8aba-028ad747d90b",
                          "version": 1,
                          "versionModifiedAt": "2024-04-12T13:47:40.669Z",
                          "createdAt": "2024-04-12T13:47:40.669Z",
                          "lastModifiedAt": "2024-04-12T13:47:40.669Z",
                          "lastModifiedBy": {
                            "clientId": "HS19nIjzrbmIUDIhVgGP3xmq",
                            "isPlatformClient": false
                          },
                          "createdBy": {
                            "clientId": "HS19nIjzrbmIUDIhVgGP3xmq",
                            "isPlatformClient": false
                          },
                          "name": "IDW000",
                          "key": "IDW000"
                        }
                        """)
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withCartCreationResponse() {
        instance.stubFor(WireMock.post(urlPathMatching("/.*/carts"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("commercetools/cart-create-response.json")
                .withTransformers("response-template")));

        return instance;

    }

    public static CommercetoolsMockExtension withGetCartsEmptyResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/carts"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("commercetools/carts-get-empty-response.json")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withCartRecalculateResponse(final int totalQuantity, final int totalAmount) {
        instance.stubFor(WireMock.post(urlPathMatching("/.*/carts/key.*"))
                .withRequestBody(containing("recalculate"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("commercetools/cart-get-response.json.hbs")
                        .withTransformers("response-template")
                        .withTransformerParameter("totalQuantity", totalQuantity)
                        .withTransformerParameter("totalAmount", totalAmount)
                ));

        return instance;
    }

    public static CommercetoolsMockExtension withCartAddLineItemResponse() {
        instance.stubFor(WireMock.post(urlPathMatching("/.*/carts/key.*"))
            .withRequestBody(containing("addLineItem"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("commercetools/cart-get-response.json.hbs")
                .withTransformers("response-template")
            ));

        return instance;
    }

    public static CommercetoolsMockExtension withCartAddLineItemWithAddonsResponse() {
        instance.stubFor(WireMock.post(urlPathMatching("/.*/carts/key.*"))
            .withRequestBody(containing("addLineItem"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("commercetools/cart-get-with-addons-response.json.hbs")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withCartDeleteResponse() {
        instance.stubFor(WireMock.delete(urlPathMatching("/.*/carts/key.*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("commercetools/cart-get-response.json.hbs")
                .withTransformers("response-template")));
        return instance;
    }

    public static CommercetoolsMockExtension withCartsNonEmptyResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/carts"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("commercetools/carts-get-non-empty-response.json")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withCartsWithAddonsResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/carts"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("commercetools/carts-get-with-addons-response.json.hbs")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withProductProjectionsSearchResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/product-projections/search"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("commercetools/product-projection-search-response.json")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withOrderResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/orders/order-number=.*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("commercetools/order-get-response.json")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withOpenOrderResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/orders/order-number=.*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("commercetools/order-get-response.json")
                .withTransformers("response-template")
                .withTransformerParameter("orderState", "Open")));

        return instance;
    }

    public static CommercetoolsMockExtension withConfirmedOrderResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/orders/order-number=.*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("commercetools/order-get-response.json")
                .withTransformers("response-template")
                .withTransformerParameter("orderState", "Confirmed")));

        return instance;
    }

    public static CommercetoolsMockExtension withOrdersSearchResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/orders"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody("""
                    {
                        "limit": 20,
                        "offset": 0,
                        "count": 1,
                        "total": 1,
                        "results": [
                            {
                                "type": "Order",
                                "id": "eed24c02-19a1-4713-b308-fd64c7d71109",
                                "version": 1,
                                "versionModifiedAt": "2024-07-04T11:33:54.250Z",
                                "lastMessageSequenceNumber": 1,
                                "createdAt": "2024-07-04T11:33:54.231Z",
                                "lastModifiedAt": "2024-07-04T11:33:54.231Z",
                                "lastModifiedBy": {
                                    "clientId": "Dimh0TYzpM0NHPQZ5qSPUgA_",
                                    "isPlatformClient": false
                                },
                                "createdBy": {
                                    "clientId": "Dimh0TYzpM0NHPQZ5qSPUgA_",
                                    "isPlatformClient": false
                                },
                                "orderNumber": "0334895531464",
                                "customerGroup": {
                                    "typeId": "customer-group",
                                    "id": "36318b6d-7e67-4a6f-9cf1-65932a5c65f2"
                                },
                                "totalPrice": {
                                    "type": "centPrecision",
                                    "currencyCode": "EUR",
                                    "centAmount": 30000,
                                    "fractionDigits": 2
                                },
                                "taxedPrice": {
                                    "totalNet": {
                                        "type": "centPrecision",
                                        "currencyCode": "EUR",
                                        "centAmount": 30000,
                                        "fractionDigits": 2
                                    },
                                    "totalGross": {
                                        "type": "centPrecision",
                                        "currencyCode": "EUR",
                                        "centAmount": 36000,
                                        "fractionDigits": 2
                                    },
                                    "taxPortions": [
                                        {
                                            "rate": 0.2,
                                            "amount": {
                                                "type": "centPrecision",
                                                "currencyCode": "EUR",
                                                "centAmount": 6000,
                                                "fractionDigits": 2
                                            },
                                            "name": "MwSt"
                                        }
                                    ],
                                    "totalTax": {
                                        "type": "centPrecision",
                                        "currencyCode": "EUR",
                                        "centAmount": 6000,
                                        "fractionDigits": 2
                                    }
                                },
                                "country": "AT",
                                "orderState": "Open",
                                "paymentState": "Pending",
                                "syncInfo": [],
                                "returnInfo": [],
                                "taxMode": "Platform",
                                "inventoryMode": "None",
                                "taxRoundingMode": "HalfEven",
                                "taxCalculationMode": "LineItemLevel",
                                "origin": "Customer",
                                "shippingMode": "Single",
                                "shippingAddress": {
                                    "country": "AT"
                                },
                                "shipping": [],
                                "lineItems": [
                                    {
                                        "id": "ba42eac8-a0ad-4707-9376-63f896ba106a",
                                        "productId": "6d8ab4be-e24b-473b-a6a5-f1228380b86c",
                                        "productKey": "hydraulic_hub",
                                        "name": {
                                            "en": "Hydraulic Hub",
                                            "de": "Hydraulic Hub"
                                        },
                                        "productType": {
                                            "typeId": "product-type",
                                            "id": "a46f1d35-7553-4813-8a20-993f1e84b9f4",
                                            "version": 23
                                        },
                                        "productSlug": {
                                            "en": "hydraulic-hub"
                                        },
                                        "variant": {
                                            "id": 3,
                                            "sku": "DDCIH_hydraulic_hub_yearly_team",
                                            "key": "DDCIH_hydraulic_hub_yearly_team",
                                            "prices": [],
                                            "images": [],
                                            "attributes": [
                                                {
                                                    "name": "name",
                                                    "value": {
                                                        "en": "Yearly Team",
                                                        "de": "Jährlich für Teams"
                                                    }
                                                },
                                                {
                                                    "name": "features",
                                                    "value": {
                                                        "en": "feature 2\\nfeature 3\\nfeature 4",
                                                        "de": "Funktionalität 1\\nFunktionalität 2"
                                                    }
                                                },
                                                {
                                                    "name": "bundleAmount",
                                                    "value": 5
                                                },
                                                {
                                                    "name": "licenseType",
                                                    "value": {
                                                        "key": "SUBSCRIPTION",
                                                        "label": "subscription license (SUBSCRIPTION), this will be renewed automatically after runtime"
                                                    }
                                                },
                                                {
                                                    "name": "runtime",
                                                    "value": "P1Y"
                                                },
                                                {
                                                    "name": "noticePeriod",
                                                    "value": "P1M"
                                                },
                                                {
                                                    "name": "externalProductId",
                                                    "value": "54987P984313213"
                                                },
                                                {
                                                    "name": "description",
                                                    "value": {
                                                        "en": "this is the description for the team size yearly subscription",
                                                        "de": "gültig für ein Team von 5"
                                                    }
                                                },
                                                {
                                                    "name": "agreements",
                                                    "value": [
                                                        [
                                                            {
                                                                "name": "name",
                                                                "value": {
                                                                    "en": "License agreements",
                                                                    "de": "Lizenzbedingungen"
                                                                }
                                                            },
                                                            {
                                                                "name": "link",
                                                                "value": {
                                                                    "en": "http://azena.com/someotherdocument_en.pdf",
                                                                    "de": "http://azena.com/someotherdocument_de.pdf"
                                                                }
                                                            }
                                                        ],
                                                        [
                                                            {
                                                                "name": "link",
                                                                "value": {
                                                                    "en": "http://azena.com/somedocument_en.pdf",
                                                                    "de": "http://azena.com/somedocument_de.pdf"
                                                                }
                                                            },
                                                            {
                                                                "name": "name",
                                                                "value": {
                                                                    "en": "Terms and conditions",
                                                                    "de": "Geschäftsbedingungen"
                                                                }
                                                            }
                                                        ]
                                                    ]
                                                },
                                                {
                                                    "name": "sellerCompanyId",
                                                    "value": "fc82b363-c3f7-4797-a97c-af37711ac1fd"
                                                },
                                                {
                                                    "name": "tenant",
                                                    "value": {
                                                        "key": "rexroth",
                                                        "label": "rexroth"
                                                    }
                                                }
                                            ],
                                            "assets": []
                                        },
                                        "price": {
                                            "id": "f1d61740-0b5d-4f5c-88a5-ff9c4105ccb3",
                                            "value": {
                                                "type": "centPrecision",
                                                "currencyCode": "EUR",
                                                "centAmount": 30000,
                                                "fractionDigits": 2
                                            },
                                            "key": "pGeCA1gQXIvlHKrCKP8zNl"
                                        },
                                        "quantity": 1,
                                        "discountedPricePerQuantity": [],
                                        "taxRate": {
                                            "name": "MwSt",
                                            "amount": 0.2,
                                            "includedInPrice": false,
                                            "country": "AT",
                                            "id": "tr8WoQjy",
                                            "subRates": []
                                        },
                                        "perMethodTaxRate": [],
                                        "addedAt": "2024-07-04T11:33:26.219Z",
                                        "lastModifiedAt": "2024-07-04T11:33:26.219Z",
                                        "state": [
                                            {
                                                "quantity": 1,
                                                "state": {
                                                    "typeId": "state",
                                                    "id": "3ee09d93-9d61-477b-b6c7-83b94c9e7d2e"
                                                }
                                            }
                                        ],
                                        "priceMode": "Platform",
                                        "lineItemMode": "Standard",
                                        "totalPrice": {
                                            "type": "centPrecision",
                                            "currencyCode": "EUR",
                                            "centAmount": 30000,
                                            "fractionDigits": 2
                                        },
                                        "taxedPrice": {
                                            "totalNet": {
                                                "type": "centPrecision",
                                                "currencyCode": "EUR",
                                                "centAmount": 30000,
                                                "fractionDigits": 2
                                            },
                                            "totalGross": {
                                                "type": "centPrecision",
                                                "currencyCode": "EUR",
                                                "centAmount": 36000,
                                                "fractionDigits": 2
                                            },
                                            "taxPortions": [
                                                {
                                                    "rate": 0.2,
                                                    "amount": {
                                                        "type": "centPrecision",
                                                        "currencyCode": "EUR",
                                                        "centAmount": 6000,
                                                        "fractionDigits": 2
                                                    },
                                                    "name": "MwSt"
                                                }
                                            ],
                                            "totalTax": {
                                                "type": "centPrecision",
                                                "currencyCode": "EUR",
                                                "centAmount": 6000,
                                                "fractionDigits": 2
                                            }
                                        },
                                        "taxedPricePortions": []
                                    }
                                ],
                                "customLineItems": [],
                                "transactionFee": true,
                                "discountCodes": [],
                                "directDiscounts": [],
                                "cart": {
                                    "typeId": "cart",
                                    "id": "f662fd6a-de9d-4bac-aebe-064d953df0da"
                                },
                                "custom": {
                                    "type": {
                                        "typeId": "type",
                                        "id": "c2abdde7-bee4-4af6-8276-cc18e45b6ce2"
                                    },
                                    "fields": {
                                        "companyId": "52967218-7c7e-4471-8391-4962e7bbe537",
                                        "userId": "a6b333dc-eb75-42a2-a847-6451816f6050"
                                    }
                                },
                                "paymentInfo": {
                                    "payments": [
                                        {
                                            "typeId": "payment",
                                            "id": "8aaa8277-fd5b-4bd6-b0ff-dbb261d907b1"
                                        }
                                    ]
                                },
                                "itemShippingAddresses": [],
                                "refusedGifts": []
                            }
                        ]
                    }
                    """)
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withOrdersSearchEmptyResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/orders"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody("""
                    {
                        "limit": 20,
                        "offset": 0,
                        "count": 0,
                        "total": 0,
                        "results": []
                    }
                    """)
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withCreateOrderResponse() {
        instance.stubFor(WireMock.post(urlPathMatching("/.*/orders"))
                .willReturn(WireMock.aResponse()
                        .withStatus(201)
                        .withHeader("content-type", "application/json;charset=UTF-8")
                        .withBodyFile("commercetools/order-create-response.json")
                        .withTransformers("response-template")));
        return instance;
    }

    public static CommercetoolsMockExtension withCreateOrderWithAddonsResponse() {
        instance.stubFor(WireMock.post(urlPathMatching("/.*/orders"))
                .willReturn(WireMock.aResponse()
                        .withStatus(201)
                        .withHeader("content-type", "application/json;charset=UTF-8")
                        .withBodyFile("commercetools/order-create-response-addons.json")
                        .withTransformers("response-template")));
        return instance;
    }

    public static CommercetoolsMockExtension withCreateOrderFailure() {
        instance.stubFor(WireMock.post(urlPathMatching("/.*/orders"))
                .willReturn(WireMock.aResponse()
                        .withStatus(500)
                        .withHeader("content-type", "application/json;charset=UTF-8")
                        .withBody("{}")));
        return instance;
    }

    public static CommercetoolsMockExtension withUpdateOrderResponse() {
        instance.stubFor(WireMock.post(urlPathMatching("/.*/orders/.*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("commercetools/order-update-response.json")
                .withTransformers("response-template")));

        return instance;
    }

    public static CommercetoolsMockExtension withGetTypesResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/.*/product-types"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody("""
                    {
                        "limit": 20,
                        "offset": 0,
                        "count": 3,
                        "total": 3,
                        "results": [
                            {
                                "id": "a46f1d35-7553-4813-8a20-993f1e84b9f4",
                                "version": 54,
                                "versionModifiedAt": "2024-11-20T15:21:23.888Z",
                                "createdAt": "2024-06-13T11:57:10.661Z",
                                "lastModifiedAt": "2024-11-20T15:21:23.888Z",
                                "lastModifiedBy": {
                                    "clientId": "Dimh0TYzpM0NHPQZ5qSPUgA_",
                                    "isPlatformClient": false
                                },
                                "createdBy": {
                                    "clientId": "Dimh0TYzpM0NHPQZ5qSPUgA_",
                                    "isPlatformClient": false
                                },
                                "name": "default-product",
                                "description": "default-product",
                                "classifier": "Complex",
                                "attributes": [],
                                "key": "default-product"
                            },
                            {
                                "id": "a3752668-2b5b-4aae-9503-0ca218a3d211",
                                "version": 2,
                                "versionModifiedAt": "2024-08-07T05:00:53.916Z",
                                "createdAt": "2024-06-18T12:52:01.543Z",
                                "lastModifiedAt": "2024-08-07T05:00:53.916Z",
                                "lastModifiedBy": {
                                    "clientId": "Dimh0TYzpM0NHPQZ5qSPUgA_",
                                    "isPlatformClient": false
                                },
                                "createdBy": {
                                    "clientId": "Dimh0TYzpM0NHPQZ5qSPUgA_",
                                    "isPlatformClient": false
                                },
                                "name": "document link properties",
                                "description": "this is referenced from the product type default-product",
                                "classifier": "Complex",
                                "attributes": [],
                                "key": "agreement"
                            },
                            {
                                "id": "75a25e43-ec58-4581-90f4-367b4cae1cfc",
                                "version": 2,
                                "versionModifiedAt": "2024-11-18T17:24:29.455Z",
                                "createdAt": "2024-11-18T16:43:48.604Z",
                                "lastModifiedAt": "2024-11-18T17:24:29.455Z",
                                "lastModifiedBy": {
                                    "clientId": "Dimh0TYzpM0NHPQZ5qSPUgA_",
                                    "isPlatformClient": false
                                },
                                "createdBy": {
                                    "clientId": "Dimh0TYzpM0NHPQZ5qSPUgA_",
                                    "isPlatformClient": false
                                },
                                "name": "addon to regular products",
                                "description": "addon",
                                "classifier": "Complex",
                                "attributes": [],
                                "key": "addon"
                            }
                        ]
                    }
                    """)
                .withTransformers("response-template")));

        return instance;
    }



    @TestConfiguration
    public static class CommercetoolsMockClientConfig {
        private static final Logger LOG = LoggerFactory.getLogger(CommercetoolsMockClientConfig.class);

        private static final String MOCK_ENDPOINT = "http://localhost:40000";

        @Bean
        @Primary
        public ProjectApiRoot commercetoolsMockClient(final CommercetoolsConfiguration appConfiguration,
                                                      final CommercetoolsRestMetricsJerseyClientInterceptor metricsInterceptor) {
            LOG.warn("using {} as commercetools api endpoint", MOCK_ENDPOINT);
            return ApiRootBuilder.of().defaultClient(MOCK_ENDPOINT)
                    .addMiddleware(metricsInterceptor)
                    .withApiBaseUrl(MOCK_ENDPOINT)
                    .build(appConfiguration.projectKey());
        }
    }
}
