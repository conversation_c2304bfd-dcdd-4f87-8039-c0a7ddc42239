package com.sast.store.ordermanagement.rest;

import com.sast.store.external.ump.test.UmpMockExtension;
import com.sast.store.ordermanagement.AbstractIntegrationTest;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import org.junit.jupiter.api.Test;

import static org.hamcrest.Matchers.notNullValue;

class CartRestServiceIntegrationTest extends AbstractIntegrationTest {

    @Test
    void testGetCart() {
        UmpMockExtension.withDefaultResponse();
        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/cart")
            .then().statusCode(200)
            .body("id", notNullValue())
            .body("lineItems", notNullValue())
            .body("totalPrice", notNullValue())
            .body("totalPrice.value", notNullValue())
            .body("totalPrice.currencyCode", notNullValue())
            .body("billingAddress", notNullValue());
    }
}
