package com.sast.store.ordermanagement.rest;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.sast.store.external.countriesservice.util.CountriesServiceMockExtension;
import com.sast.store.external.ump.test.UmpMockExtension;
import com.sast.store.ordermanagement.AbstractComponentTest;
import com.sast.store.ordermanagement.util.CommercetoolsMockExtension;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import org.junit.jupiter.api.Test;

import static com.github.tomakehurst.wiremock.client.WireMock.matchingJsonPath;
import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.not;

class PaymentRestServiceTest extends AbstractComponentTest {

    @Test
    void testGetPaymentExistingOrder() {
        CommercetoolsMockExtension.withDefaultResponse();
        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/payments/{paymentId}", "8aaa8277-fd5b-4bd6-b0ff-dbb261d907b1")
            .then().statusCode(200)
            .body("paymentId", equalTo("8aaa8277-fd5b-4bd6-b0ff-dbb261d907b1"))
            .body("paymentStatus", equalTo("PENDING"))
            .body("orderId", equalTo("0334895531464"));

        CommercetoolsMockExtension.get().verify(0, postRequestedFor(urlMatching("/.*/orders")));
    }

    @Test
    void testGetPaymentWithBoschTransferOrderWillBeCreated() {
        CommercetoolsMockExtension.withDefaultResponse();
        CommercetoolsMockExtension.withOrdersSearchEmptyResponse();
        CommercetoolsMockExtension.withCartsNonEmptyResponse();
        UmpMockExtension.withDefaultResponse();
        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/payments/{paymentId}", "8aaa8277-fd5b-4bd6-b0ff-dbb261d907b1")
            .then().statusCode(200)
            .body("paymentId", equalTo("8aaa8277-fd5b-4bd6-b0ff-dbb261d907b1"))
            .body("paymentStatus", equalTo("PENDING"));

        CommercetoolsMockExtension.get().verify(1, postRequestedFor(urlMatching("/.*/orders"))
            .withRequestBody(matchingJsonPath("cart.id", WireMock.equalTo("9b06dd8c-4f0d-4d09-ba4c-903af7165d24")))
            .withRequestBody(matchingJsonPath("orderNumber", WireMock.matching("LRX_OR_[0-9]+")))
        );
    }

    @Test
    void testGetNotExistingPayment() {
        CommercetoolsMockExtension.withGetPaymentNotFoundResponse();
        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/payments/{paymentId}", "1234567")
            .then().statusCode(404);
    }

    @Test
    void testGetPaymentUnauthorized() {
        CommercetoolsMockExtension.withDefaultResponse();
        RestAssured
            .given()
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/payments/{paymentId}", "1234567")
            .then().statusCode(401);
    }

    @Test
    void testGetPaymentForbidden() {
        CommercetoolsMockExtension.withDefaultResponse();
        setupKeycloakToken("VIEW_PRICE");
        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/payments/{paymentId}", "1234567")
            .then().statusCode(403);
    }

    @Test
    void testGetPaymentConfigForCart() {
        CommercetoolsMockExtension.withDefaultResponse();
        UmpMockExtension.withDefaultResponse();
        CountriesServiceMockExtension.withListAllCountriesResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN).contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/payments/paymentConfig/")
            .then().statusCode(200)
            .body("paymentMethods", hasSize(2))
            .body("paymentMethods[0].paymentMethodId", equalTo("BOSCH_TRANSFER/SEPA_CREDIT"))
            .body("paymentMethods[1].paymentMethodId", equalTo("BOSCH_TRANSFER/ACH_CREDIT"));
    }

    @Test
    void zeroSumCartSupportsOnlyZeroPaymentMethod() {
        CommercetoolsMockExtension.withDefaultResponse();
        CommercetoolsMockExtension.withCartRecalculateResponse(10, 0);
        UmpMockExtension.withDefaultResponse();
        CountriesServiceMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/payments/paymentConfig")
            .then().statusCode(200)
            .body("paymentMethods", hasSize(1))
            .body("paymentMethods[0].paymentMethodId", equalTo("ZERO/ZERO"));
    }

    @Test
    void nonZeroSumCartDoesNotSupportZeroPaymentMethod() {
        CommercetoolsMockExtension.withCartsNonEmptyResponse();
        CommercetoolsMockExtension.withCartRecalculateResponse(10, 1);
        UmpMockExtension.withDefaultResponse();
        CountriesServiceMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/payments/paymentConfig")
            .then().statusCode(200)
            .body("paymentMethods", not(empty()))
            .body("paymentMethods.paymentMethodId", not(contains("ZERO/ZERO")));
    }

    @Test
    void cartExceedingMaximumTotalQuantityFails() {
        CommercetoolsMockExtension.withCartsNonEmptyResponse();
        CommercetoolsMockExtension.withCartRecalculateResponse(101, 1);
        CommercetoolsMockExtension.withCartDeleteResponse();
        UmpMockExtension.withDefaultResponse();
        CountriesServiceMockExtension.withDefaultResponse();

        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .accept(ContentType.JSON)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/payments/paymentConfig")
            .then().statusCode(400)
            .body("errorCode", not(empty()));
    }
}
