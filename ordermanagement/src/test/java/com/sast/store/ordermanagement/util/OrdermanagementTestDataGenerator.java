package com.sast.store.ordermanagement.util;

import com.sast.store.ordermanagement.invoice.model.InvoiceEntity;
import com.sast.store.ordermanagement.invoice.model.InvoiceRepository;
import com.sast.store.testing.commons.AbstractTestDataGenerator;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import org.jeasy.random.EasyRandom;
import org.jeasy.random.EasyRandomParameters;
import org.jeasy.random.randomizers.number.BigDecimalRandomizer;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.function.Consumer;

@Component
public class OrdermanagementTestDataGenerator extends AbstractTestDataGenerator<OrdermanagementTestDataGenerator> {

    protected OrdermanagementTestDataGenerator generator;

    @Inject
    private InvoiceRepository invoiceRepository;

    private InvoiceGenerator invoiceGenerator;

    private final EasyRandom rnd = new EasyRandom(new EasyRandomParameters()
        .objectPoolSize(10000)
        // dynamodb only supports numbers up to 38 digits
        .randomize(BigDecimal.class, new BigDecimalRandomizer(Integer.valueOf(30))));

    @PostConstruct
    public void init() {
        invoiceGenerator = this.new InvoiceGenerator(this);
        this.generator = this;
    }

    public InvoiceGenerator withInvoice() {
        final InvoiceEntity contract = generator.rnd.nextObject(InvoiceEntity.class);
        contract.setVersion(null);
        generator.invoiceRepository.save(contract);
        generator.setData(contract);
        return generator.invoiceGenerator;
    }

    public class InvoiceGenerator extends OrdermanagementTestDataGenerator {

        public InvoiceGenerator(final OrdermanagementTestDataGenerator generator) {
            this.generator = generator;
        }

        public InvoiceGenerator havingValue(final Consumer<InvoiceEntity> consumer) {
            consumer.accept(generator.getInvoice());

            final InvoiceEntity key = new InvoiceEntity();
            key.setDocumentNumber(generator.getInvoice().getDocumentNumber());

            generator.invoiceRepository.delete(key);

            generator.invoiceRepository.save(generator.getInvoice());
            return this;
        }

    }

    public InvoiceEntity getInvoice() {
        return generator.getData(InvoiceEntity.class).getLast();
    }

    public LinkedList<InvoiceEntity> getInvoices() {
        return generator.getData(InvoiceEntity.class);
    }
}
