package com.sast.store.ordermanagement.cartvalidation.rules;

import com.commercetools.api.models.cart.Cart;
import com.commercetools.api.models.cart.LineItem;
import com.commercetools.api.models.product.Attribute;
import com.commercetools.api.models.product.ProductVariant;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.gen.client.ump.api.UmpExternalCompanyDto;
import com.sast.store.ordermanagement.cartvalidation.CartErrorCode;
import com.sast.store.ordermanagement.cartvalidation.CartValidationContext;
import com.sast.store.ordermanagement.cartvalidation.CartValidationViolation;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SameSellerIdRuleUnitTest {

    @InjectMocks
    private SameSellerIdRule sameSellerIdRule;

    @Mock
    private Cart mockCart;

    private CartValidationContext cartValidationContext;

    @BeforeEach
    void setup() {
        cartValidationContext = CartValidationContext.builder()
                .tenant(Tenant.REXROTH)
                .umpCompany(new UmpExternalCompanyDto())
                .build();
    }

    @Test
    void givenEmptyCart_validation_passes() {
        when(mockCart.getLineItems()).thenReturn(List.of());

        final Set<CartValidationViolation> actualViolations = sameSellerIdRule.apply(mockCart, cartValidationContext);

        assertThat(actualViolations).isEmpty();
    }

    @Test
    void givenLineItemWithSellerId_validation_passes() {
        when(mockCart.getLineItems()).thenReturn(List.of(
            LineItem.builder()
                .variant(ProductVariant.builder()
                    .attributes(Attribute.builder()
                        .name("sellerId")
                        .value("UMTeter_47")
                        .build())
                    .buildUnchecked())
                .buildUnchecked()
        ));

        final Set<CartValidationViolation> actualViolations = sameSellerIdRule.apply(mockCart, cartValidationContext);

        assertThat(actualViolations).isEmpty();
    }

    @Test
    void givenLineItemWithoutSellerId_validation_passes() {
        when(mockCart.getLineItems()).thenReturn(List.of(
            LineItem.builder()
                .variant(ProductVariant.builder()
                    .attributes(List.of())
                    .buildUnchecked())
                .buildUnchecked()
        ));

        final Set<CartValidationViolation> actualViolations = sameSellerIdRule.apply(mockCart, cartValidationContext);

        assertThat(actualViolations).isEmpty();
    }

    @Test
    void givenLineItemsWithoutSellerId_validation_passes() {
        when(mockCart.getLineItems()).thenReturn(List.of(
            LineItem.builder()
                .variant(ProductVariant.builder()
                    .attributes(List.of())
                    .buildUnchecked())
                .buildUnchecked(),
            LineItem.builder()
                .variant(ProductVariant.builder()
                    .attributes(List.of())
                    .buildUnchecked())
                .buildUnchecked()
        ));

        final Set<CartValidationViolation> actualViolations = sameSellerIdRule.apply(mockCart, cartValidationContext);

        assertThat(actualViolations).isEmpty();
    }

    @Test
    void givenLineItemsWithSameSellerId_validation_passes() {
        when(mockCart.getLineItems()).thenReturn(List.of(
            LineItem.builder()
                .variant(ProductVariant.builder()
                    .attributes(Attribute.builder()
                        .name("sellerId")
                        .value("UMTeter_47")
                        .build())
                    .buildUnchecked())
                .buildUnchecked(),
            LineItem.builder()
                .variant(ProductVariant.builder()
                    .attributes(Attribute.builder()
                        .name("sellerId")
                        .value("UMTeter_47")
                        .build())
                    .buildUnchecked())
                .buildUnchecked()
        ));

        final Set<CartValidationViolation> actualViolations = sameSellerIdRule.apply(mockCart, cartValidationContext);

        assertThat(actualViolations).isEmpty();
    }

    @Test
    void givenLineItemsWithDifferentSellerIds_validation_fails() {
        when(mockCart.getLineItems()).thenReturn(List.of(
            LineItem.builder()
                .variant(ProductVariant.builder()
                    .attributes(Attribute.builder()
                        .name("sellerId")
                        .value("UMTeter_47")
                        .build())
                    .buildUnchecked())
                .buildUnchecked(),
            LineItem.builder()
                .variant(ProductVariant.builder()
                    .attributes(Attribute.builder()
                        .name("sellerId")
                        .value("UMTeter_05")
                        .build())
                    .buildUnchecked())
                .buildUnchecked()
        ));

        final Set<CartValidationViolation> actualViolations = sameSellerIdRule.apply(mockCart, cartValidationContext);

        assertThat(actualViolations)
            .hasSize(1)
            .first()
            .extracting(CartValidationViolation::error)
            .isEqualTo(CartErrorCode.MULTIPLE_SELLER_IDS);
    }

    @Test
    void givenLineItemsWithOneMissingSellerId_validation_fails() {
        when(mockCart.getLineItems()).thenReturn(List.of(
            LineItem.builder()
                .variant(ProductVariant.builder()
                    .attributes(Attribute.builder()
                        .name("sellerId")
                        .value("UMTeter_47")
                        .build())
                    .buildUnchecked())
                .buildUnchecked(),
            LineItem.builder()
                .variant(ProductVariant.builder()
                    .attributes(List.of())
                    .buildUnchecked())
                .buildUnchecked()
        ));

        final Set<CartValidationViolation> actualViolations = sameSellerIdRule.apply(mockCart, cartValidationContext);

        assertThat(actualViolations)
            .hasSize(1)
            .first()
            .extracting(CartValidationViolation::error)
            .isEqualTo(CartErrorCode.MULTIPLE_SELLER_IDS);
    }
}
