package com.sast.store.ordermanagement.rest;

import com.sast.store.ordermanagement.AbstractIntegrationTest;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import org.junit.jupiter.api.Test;

import static org.hamcrest.Matchers.greaterThan;
import static org.hamcrest.Matchers.hasSize;

class OrderRestServiceIntegrationTest extends AbstractIntegrationTest {

    @Test
    void testGetOrders() {
        RestAssured
            .given(VALID_KEYCLOAK_TOKEN)
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/orders")
            .then().statusCode(200)
            .body("$", hasSize(greaterThan(0)));
    }

    @Test
    void testGetOrdersUnauthorized() {
        RestAssured
            .given()
            .contentType(ContentType.JSON)
            .header("X-Tenant", "rexroth")
            .when().get(host + "/rest/orders")
            .then().statusCode(401);
    }
}
