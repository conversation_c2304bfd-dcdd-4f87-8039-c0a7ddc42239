server:
  port: 8083

logging:
  level:
    com.sast.store.commons.jerseyclient.ClientRequestResponseLogger: TRACE

spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://sso.mp-dc-d.com/auth/realms/rexroth
  cloud:
    aws:
      region.static: eu-central-1
      credentials:
        access-key: fakekey
        secret-key: fakekey
      sqs:
        endpoint: http://localhost:40123
      s3:
        endpoint: http://localhost:9327
        path-style-access-enabled: true
      dynamodb:
        endpoint: http://localhost:9324

bossstore:
  commercetools:
    clientId: SSAIdVhBpKt80ChCMjZwxi4c
    clientSecret: XtXHlio2S_z0UbW4KSfFniBO3dTklMfW
    projectKey: sast-dev
  hazelcast:
    enabled: local
  pgw:
    apiKey: dummy-api-key
    url: http://localhost:40001
  ump:
    url: http://localhost:40101
  countriesservice:
    url: http://localhost:40002

