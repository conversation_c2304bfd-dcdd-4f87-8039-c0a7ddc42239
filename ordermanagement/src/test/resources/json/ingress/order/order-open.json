{"header": {"version": "V1", "id": {"eventId": "BrimService_1CDHoMCM1YEpshtkDJmncu", "senderName": "brim-service"}, "tenant": "rex<PERSON>", "timestamp": "2024-07-17T15:36:44.873Z", "cause": {"eventId": "create-platform-order", "senderName": "<PERSON>"}}, "data": {"@type": "ORDER", "orderNumber": "928374", "billingSyncStatus": "IN_SYNC", "billingOrderStatus": "OPEN", "placedAt": "2024-07-16T13:37:12Z", "buyerCompanyId": "********-7c7e-4471-8391-4962e7bbe537", "payment": {"@type": "PGW/SEPA_DIRECTDEBIT", "iban": "*****************", "mandateReference": "KJLHKJ8998767KJH7KJHO", "accountHolderName": "<PERSON>", "dateOfSignature": "2024-07-16"}, "items": [{"productId": "DC_12345678Z100", "quantity": 4, "amountAtOrderDate": 14.99, "contracts": [{"contractId": "BC_1000800001", "billingSyncStatus": "NEW", "startDate": "2024-07-16T15:37:12+02:00", "payment": {"@type": "PGW/SEPA_DIRECTDEBIT", "iban": "*****************", "mandateReference": "KJLHKJ8998767KJH7KJHO", "accountHolderName": "<PERSON>", "dateOfSignature": "2024-07-16"}, "productId": "DC_12345678Z100", "contractTypeConfiguration": {"@type": "SUBSCRIPTION", "contractPeriod": "QUARTERLY", "noticePeriod": "P1M"}}, {"contractId": "BC_10000000002", "billingSyncStatus": "NEW", "startDate": "2024-07-16T15:37:12+02:00", "payment": {"@type": "PGW/SEPA_DIRECTDEBIT", "iban": "*****************", "mandateReference": "KJLHKJ8998767KJH7KJHO", "accountHolderName": "<PERSON>", "dateOfSignature": "2024-07-16"}, "productId": "DC_12345678Z100", "contractTypeConfiguration": {"@type": "SUBSCRIPTION", "contractPeriod": "QUARTERLY", "noticePeriod": "P1M"}}, {"contractId": "BC_10000000003", "billingSyncStatus": "NEW", "startDate": "2024-07-16T15:37:12+02:00", "payment": {"@type": "PGW/SEPA_DIRECTDEBIT", "iban": "*****************", "mandateReference": "KJLHKJ8998767KJH7KJHO", "accountHolderName": "<PERSON>", "dateOfSignature": "2024-07-16"}, "productId": "DC_12345678Z100", "contractTypeConfiguration": {"@type": "SUBSCRIPTION", "contractPeriod": "QUARTERLY", "noticePeriod": "P1M"}}, {"contractId": "BC_10000000004", "billingSyncStatus": "NEW", "startDate": "2024-07-16T15:37:12+02:00", "payment": {"@type": "PGW/SEPA_DIRECTDEBIT", "iban": "*****************", "mandateReference": "KJLHKJ8998767KJH7KJHO", "accountHolderName": "<PERSON>", "dateOfSignature": "2024-07-16"}, "productId": "DC_12345678Z100", "contractTypeConfiguration": {"@type": "SUBSCRIPTION", "contractPeriod": "QUARTERLY", "noticePeriod": "P1M"}}]}, {"productId": "DC_12345678Z101", "quantity": 2, "amountAtOrderDate": 17.99, "contracts": [{"contractId": "BC_10000000005", "billingSyncStatus": "NEW", "startDate": "2024-07-16T15:37:12+02:00", "payment": {"@type": "PGW/SEPA_DIRECTDEBIT", "iban": "*****************", "mandateReference": "KJLHKJ8998767KJH7KJHO", "accountHolderName": "<PERSON>", "dateOfSignature": "2024-07-16"}, "productId": "DC_12345678Z101", "contractTypeConfiguration": {"@type": "FIXED_TERM", "duration": "YEARLY"}}, {"contractId": "BC_10000000006", "billingSyncStatus": "NEW", "startDate": "2024-07-16T15:37:12+02:00", "payment": {"@type": "PGW/SEPA_DIRECTDEBIT", "iban": "*****************", "mandateReference": "KJLHKJ8998767KJH7KJHO", "accountHolderName": "<PERSON>", "dateOfSignature": "2024-07-16"}, "productId": "DC_12345678Z101", "contractTypeConfiguration": {"@type": "FIXED_TERM", "duration": "YEARLY"}}]}, {"productId": "DC_12345678Z199", "quantity": 50, "amountAtOrderDate": 17.99, "contracts": [{"contractId": "BC_10000000007", "billingSyncStatus": "NEW", "startDate": "2024-07-16T15:37:12+02:00", "payment": {"@type": "PGW/SEPA_DIRECTDEBIT", "iban": "*****************", "mandateReference": "KJLHKJ8998767KJH7KJHO", "accountHolderName": "<PERSON>", "dateOfSignature": "2024-07-16"}, "productId": "DC_12345678Z199", "contractTypeConfiguration": {"@type": "ONE_TIME"}}]}]}, "errors": []}