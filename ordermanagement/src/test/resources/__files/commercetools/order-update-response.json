{"type": "Order", "id": "{{request.pathSegments.2}}", "version": 4, "versionModifiedAt": "2024-07-18T09:47:20.395Z", "lastMessageSequenceNumber": 4, "createdAt": "2024-07-18T09:10:43.693Z", "lastModifiedAt": "2024-07-18T09:47:20.395Z", "lastModifiedBy": {"clientId": "SSAIdVhBpKt80ChCMjZwxi4c", "isPlatformClient": false}, "createdBy": {"clientId": "SSAIdVhBpKt80ChCMjZwxi4c", "isPlatformClient": false}, "orderNumber": "{{randomValue length=10 type='NUMERIC'}}", "customerGroup": {"typeId": "customer-group", "id": "36318b6d-7e67-4a6f-9cf1-65932a5c65f2"}, "totalPrice": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 60000, "fractionDigits": 2}, "taxedPrice": {"totalNet": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 60000, "fractionDigits": 2}, "totalGross": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 72000, "fractionDigits": 2}, "taxPortions": [{"rate": 0.2, "amount": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 12000, "fractionDigits": 2}, "name": "MwSt"}], "totalTax": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 12000, "fractionDigits": 2}}, "country": "AT", "orderState": "Confirmed", "paymentState": "Pending", "syncInfo": [], "returnInfo": [], "taxMode": "Platform", "inventoryMode": "None", "taxRoundingMode": "HalfEven", "taxCalculationMode": "LineItemLevel", "origin": "Customer", "shippingMode": "Single", "shippingAddress": {"streetName": "Berg", "streetNumber": "18", "postalCode": "5652", "city": "Dienten am Hochkönig", "country": "AT"}, "shipping": [], "lineItems": [{"id": "0afb5609-e676-4a8b-ab49-5ed8065a5ef0", "productId": "6d8ab4be-e24b-473b-a6a5-f1228380b86c", "productKey": "hydraulic_hub", "name": {"en": "Hydraulic <PERSON>", "de": "Hydraulic <PERSON>"}, "productType": {"typeId": "product-type", "id": "a46f1d35-7553-4813-8a20-993f1e84b9f4", "version": 23}, "productSlug": {"en": "hydraulic-hub"}, "variant": {"id": 2, "sku": "DDCIH_hydraulic_hub_yearly", "key": "DDCIH_hydraulic_hub_yearly", "prices": [], "images": [], "attributes": [{"name": "name", "value": {"en": "Yearly", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"name": "description", "value": {"en": "this renews yearly", "de": "verlä<PERSON>t sich jährlich"}}, {"name": "bundleAmount", "value": 1}, {"name": "licenseType", "value": {"key": "SUBSCRIPTION", "label": "subscription license (SUBSCRIPTION), this will be renewed automatically after runtime"}}, {"name": "runtime", "value": "P1Y"}, {"name": "notice<PERSON><PERSON>od", "value": "P1M"}, {"name": "externalProductId", "value": "54987P984313212"}, {"name": "features", "value": {"en": "feature 1\nfeature 2", "de": "funktionalität 1\nfunktionalität 2"}}, {"name": "agreements", "value": [[{"name": "name", "value": {"en": "License agreements", "de": "Lizenzbedingungen"}}, {"name": "link", "value": {"en": "http://azena.com/someotherdocument_en.pdf", "de": "http://azena.com/someotherdocument_de.pdf"}}], [{"name": "link", "value": {"en": "http://azena.com/somedocument_en.pdf", "de": "http://azena.com/somedocument_de.pdf"}}, {"name": "name", "value": {"en": "Terms and conditions", "de": "Geschäftsbedingungen"}}]]}, {"name": "sellerCompanyId", "value": "fc82b363-c3f7-4797-a97c-af37711ac1fd"}, {"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}], "assets": []}, "price": {"id": "2388cf61-0eb6-4223-a8e7-6502e20dbadb", "value": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 20000, "fractionDigits": 2}, "key": "2nIlvBqbaNhQmC1jg7ubJU"}, "quantity": 3, "discountedPricePerQuantity": [], "taxRate": {"name": "MwSt", "amount": 0.2, "includedInPrice": false, "country": "AT", "id": "tr8WoQjy", "subRates": []}, "perMethodTaxRate": [], "addedAt": "2024-07-18T09:10:42.848Z", "lastModifiedAt": "2024-07-18T09:10:42.848Z", "state": [{"quantity": 3, "state": {"typeId": "state", "id": "3ee09d93-9d61-477b-b6c7-83b94c9e7d2e"}}], "priceMode": "Platform", "lineItemMode": "Standard", "totalPrice": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 60000, "fractionDigits": 2}, "taxedPrice": {"totalNet": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 60000, "fractionDigits": 2}, "totalGross": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 72000, "fractionDigits": 2}, "taxPortions": [{"rate": 0.2, "amount": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 12000, "fractionDigits": 2}, "name": "MwSt"}], "totalTax": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 12000, "fractionDigits": 2}}, "taxedPricePortions": []}], "customLineItems": [], "transactionFee": true, "discountCodes": [], "directDiscounts": [], "cart": {"typeId": "cart", "id": "deb2d33c-cd50-4c6f-bc53-27fd9d1df4c7"}, "custom": {"type": {"typeId": "type", "id": "184f9fa2-740d-43b8-b8eb-f49e6e5f672c"}, "fields": {"companyId": "9c8b8096-6733-4a4b-b77a-e2857aae66b9", "userId": "7cb0fdd4-9503-41e0-a279-84ad098c8a00", "tenant": "rex<PERSON>"}}, "paymentInfo": {"payments": [{"typeId": "payment", "id": "900691eb-1db4-4c04-b622-0c5fdb1e6990"}]}, "billingAddress": {"streetName": "Bistdudeppertgassl", "streetNumber": "12", "postalCode": "4010", "city": "Linz", "region": "Linz-Land", "state": "Oberösterreich", "country": "AT"}, "itemShippingAddresses": [], "refusedGifts": []}