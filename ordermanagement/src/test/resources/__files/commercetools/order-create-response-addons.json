{"type": "Order", "id": "42bf7a1a-bf0d-4b9a-b447-70ccdac27c7f", "version": 1, "versionModifiedAt": "2024-12-17T08:38:10.889Z", "lastMessageSequenceNumber": 1, "createdAt": "2024-12-17T08:38:10.862Z", "lastModifiedAt": "2024-12-17T08:38:10.862Z", "lastModifiedBy": {"clientId": "Dimh0TYzpM0NHPQZ5qSPUgA_", "isPlatformClient": false}, "createdBy": {"clientId": "Dimh0TYzpM0NHPQZ5qSPUgA_", "isPlatformClient": false}, "orderNumber": "{{jsonPath request.body '$.orderNumber'}}", "customerGroup": {"typeId": "customer-group", "id": "6101a6c4-e032-4b45-a3ad-40bddaf3e8c4"}, "totalPrice": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 149400, "fractionDigits": 2}, "taxedPrice": {"totalNet": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 149400, "fractionDigits": 2}, "totalGross": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 177786, "fractionDigits": 2}, "taxPortions": [{"rate": 0.19, "amount": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 28386, "fractionDigits": 2}, "name": "MwSt"}], "totalTax": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 28386, "fractionDigits": 2}}, "country": "DE", "orderState": "Open", "paymentState": "Pending", "syncInfo": [], "returnInfo": [], "taxMode": "Platform", "inventoryMode": "None", "taxRoundingMode": "HalfEven", "taxCalculationMode": "LineItemLevel", "origin": "Customer", "shippingMode": "Single", "shippingAddress": {"streetName": "Moosacher Str.", "streetNumber": "80", "postalCode": "80809", "city": "München", "country": "DE"}, "shipping": [], "lineItems": [{"id": "ef14a82d-7fbe-4d31-b423-77bbcc4861c8", "key": "3c1db8b4-df41-425c-ba15-e38d7e41d1f7", "productId": "b21d8b73-debd-4f44-9377-8ba93208d7ae", "productKey": "bodas_connect_5", "name": {"en": "BODAS Connect Lite", "de": "BODAS Connect Lite"}, "productType": {"typeId": "product-type", "id": "a46f1d35-7553-4813-8a20-993f1e84b9f4", "version": 54}, "productSlug": {"en": "rcu-series-5"}, "variant": {"id": 1, "sku": "DRX_PR_BODAS_LITE", "key": "DRX_PR_BODAS_LITE", "prices": [], "images": [{"url": "https://images.cdn.europe-west1.gcp.commercetools.com/b4800138-a539-4ad0-9511-9260c02d29bb/image-2024-12-2_13-5-Lxw4Uv88.png", "dimensions": {"w": 2000, "h": 2000}}, {"url": "https://images.cdn.europe-west1.gcp.commercetools.com/b4800138-a539-4ad0-9511-9260c02d29bb/bodasconnect_icon-7iOIvcEi.jpeg", "dimensions": {"w": 224, "h": 224}}], "attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "name", "value": {"en": "Basic Device Management (RCU Series 5)", "de": "BODAS Connect for RCU 5"}}, {"name": "description", "value": {"en": "Access to your dedicated cloud instance for basic Device management, SOTA & cellular coverage. Cellular data usage limited via CAN signal configuration. No ECU FOTA possible. ", "de": ""}}, {"name": "bundleAmount", "value": 1}, {"name": "licenseType", "value": {"key": "CONSUMPTION", "label": "product with postpaid billing and consumption pricing (CONSUMPTION)"}}, {"name": "runtime", "value": "P1M"}, {"name": "notice<PERSON><PERSON>od", "value": "P28D"}, {"name": "externalProductId", "value": "R917015756"}, {"name": "sellerCompanyId", "value": "f01d4e3c-ab09-4cd2-b5ff-312de81ab7bf"}, {"name": "addons", "value": [{"typeId": "product", "id": "990cdc6a-13f5-4832-a7da-ab44df4b2f19"}]}, {"name": "externalDocuments", "value": [[{"name": "link", "value": {"en": "https://www.boschrexroth.com/media/251a97f6-b44e-476a-aaa8-f9bba06fb6ae"}}, {"name": "name", "value": {"en": "Fact Sheet RCU Lite Services"}}]]}, {"name": "features", "value": {"en": "First 20 claimed RCU (Series 5) are included in base fee."}}], "assets": []}, "price": {"id": "d3bbd4ad-c2a2-4f92-8934-d1fc8fd869cb", "value": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 24900, "fractionDigits": 2}, "key": "DRX_PR_BODAS_LITE_PRICE"}, "quantity": 1, "discountedPricePerQuantity": [], "taxRate": {"name": "MwSt", "amount": 0.19, "includedInPrice": false, "country": "DE", "id": "CAynvyIK", "subRates": []}, "perMethodTaxRate": [], "addedAt": "2024-12-16T14:12:09.932Z", "lastModifiedAt": "2024-12-16T14:12:09.932Z", "state": [{"quantity": 1, "state": {"typeId": "state", "id": "3ee09d93-9d61-477b-b6c7-83b94c9e7d2e"}}], "priceMode": "Platform", "lineItemMode": "Standard", "totalPrice": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 24900, "fractionDigits": 2}, "taxedPrice": {"totalNet": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 24900, "fractionDigits": 2}, "totalGross": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 29631, "fractionDigits": 2}, "taxPortions": [{"rate": 0.19, "amount": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 4731, "fractionDigits": 2}, "name": "MwSt"}], "totalTax": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 4731, "fractionDigits": 2}}, "taxedPricePortions": [], "custom": {"type": {"typeId": "type", "id": "ad6426e5-f34d-46d3-9943-d27e8c62a56c"}, "fields": {"addonItemKeys": ["c300fa8c-bd1a-40a8-a136-66efd9771b45"]}}}, {"id": "dfae272c-480a-49f4-bce8-240495d90cd3", "key": "c300fa8c-bd1a-40a8-a136-66efd9771b45", "productId": "990cdc6a-13f5-4832-a7da-ab44df4b2f19", "productKey": "data-management", "name": {"en": "Data Management", "de": "Data Management"}, "productType": {"typeId": "product-type", "id": "75a25e43-ec58-4581-90f4-367b4cae1cfc", "version": 2}, "productSlug": {"en": "data-management"}, "variant": {"id": 2, "sku": "DRX_PR_BODAS_DATA_3GB", "key": "DRX_PR_BODAS_DATA_3GB", "prices": [], "images": [], "attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "name", "value": {"en": "3GB per RCU"}}, {"name": "description", "value": {"en": "⭐️ Recommended"}}], "assets": []}, "price": {"id": "560ccedf-5f75-42f2-9f8e-c6747ea026e8", "value": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}}, "quantity": 1, "discountedPricePerQuantity": [], "taxRate": {"name": "MwSt", "amount": 0.19, "includedInPrice": false, "country": "DE", "id": "CAynvyIK", "subRates": []}, "perMethodTaxRate": [], "addedAt": "2024-12-16T14:12:09.940Z", "lastModifiedAt": "2024-12-16T14:12:09.940Z", "state": [{"quantity": 1, "state": {"typeId": "state", "id": "3ee09d93-9d61-477b-b6c7-83b94c9e7d2e"}}], "priceMode": "ExternalPrice", "lineItemMode": "Standard", "totalPrice": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}, "taxedPrice": {"totalNet": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}, "totalGross": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}, "taxPortions": [{"rate": 0.19, "amount": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}, "name": "MwSt"}], "totalTax": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}}, "taxedPricePortions": [], "custom": {"type": {"typeId": "type", "id": "d1217476-774e-4cb8-a53a-25d597a91cc3"}, "fields": {"parentLineItemKey": "3c1db8b4-df41-425c-ba15-e38d7e41d1f7"}}}, {"id": "b83d1b8f-526e-4f6c-8111-1fa8a2427861", "key": "1a6cc048-1c5e-4ccb-85cd-7ba91a82e18b", "productId": "b21d8b73-debd-4f44-9377-8ba93208d7ae", "productKey": "bodas_connect_5", "name": {"en": "BODAS Connect Lite", "de": "BODAS Connect Lite"}, "productType": {"typeId": "product-type", "id": "a46f1d35-7553-4813-8a20-993f1e84b9f4", "version": 54}, "productSlug": {"en": "rcu-series-5"}, "variant": {"id": 1, "sku": "DRX_PR_BODAS_LITE", "key": "DRX_PR_BODAS_LITE", "prices": [], "images": [{"url": "https://images.cdn.europe-west1.gcp.commercetools.com/b4800138-a539-4ad0-9511-9260c02d29bb/image-2024-12-2_13-5-Lxw4Uv88.png", "dimensions": {"w": 2000, "h": 2000}}, {"url": "https://images.cdn.europe-west1.gcp.commercetools.com/b4800138-a539-4ad0-9511-9260c02d29bb/bodasconnect_icon-7iOIvcEi.jpeg", "dimensions": {"w": 224, "h": 224}}], "attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "name", "value": {"en": "Basic Device Management (RCU Series 5)", "de": "BODAS Connect for RCU 5"}}, {"name": "description", "value": {"en": "Access to your dedicated cloud instance for basic Device management, SOTA & cellular coverage. Cellular data usage limited via CAN signal configuration. No ECU FOTA possible. ", "de": ""}}, {"name": "bundleAmount", "value": 1}, {"name": "licenseType", "value": {"key": "CONSUMPTION", "label": "product with postpaid billing and consumption pricing (CONSUMPTION)"}}, {"name": "runtime", "value": "P1M"}, {"name": "notice<PERSON><PERSON>od", "value": "P28D"}, {"name": "externalProductId", "value": "R917015756"}, {"name": "sellerCompanyId", "value": "f01d4e3c-ab09-4cd2-b5ff-312de81ab7bf"}, {"name": "addons", "value": [{"typeId": "product", "id": "990cdc6a-13f5-4832-a7da-ab44df4b2f19"}]}, {"name": "externalDocuments", "value": [[{"name": "link", "value": {"en": "https://www.boschrexroth.com/media/251a97f6-b44e-476a-aaa8-f9bba06fb6ae"}}, {"name": "name", "value": {"en": "Fact Sheet RCU Lite Services"}}]]}, {"name": "features", "value": {"en": "First 20 claimed RCU (Series 5) are included in base fee."}}], "assets": []}, "price": {"id": "d3bbd4ad-c2a2-4f92-8934-d1fc8fd869cb", "value": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 24900, "fractionDigits": 2}, "key": "DRX_PR_BODAS_LITE_PRICE"}, "quantity": 1, "discountedPricePerQuantity": [], "taxRate": {"name": "MwSt", "amount": 0.19, "includedInPrice": false, "country": "DE", "id": "CAynvyIK", "subRates": []}, "perMethodTaxRate": [], "addedAt": "2024-12-16T15:12:46.553Z", "lastModifiedAt": "2024-12-16T15:12:46.553Z", "state": [{"quantity": 1, "state": {"typeId": "state", "id": "3ee09d93-9d61-477b-b6c7-83b94c9e7d2e"}}], "priceMode": "Platform", "lineItemMode": "Standard", "totalPrice": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 24900, "fractionDigits": 2}, "taxedPrice": {"totalNet": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 24900, "fractionDigits": 2}, "totalGross": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 29631, "fractionDigits": 2}, "taxPortions": [{"rate": 0.19, "amount": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 4731, "fractionDigits": 2}, "name": "MwSt"}], "totalTax": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 4731, "fractionDigits": 2}}, "taxedPricePortions": [], "custom": {"type": {"typeId": "type", "id": "ad6426e5-f34d-46d3-9943-d27e8c62a56c"}, "fields": {"addonItemKeys": ["a8355a11-792b-44f7-b379-a8f0f1fb4f8d"]}}}, {"id": "daa7eeed-615f-4ddc-a6d8-5ccfff3d1b4c", "key": "a8355a11-792b-44f7-b379-a8f0f1fb4f8d", "productId": "990cdc6a-13f5-4832-a7da-ab44df4b2f19", "productKey": "data-management", "name": {"en": "Data Management", "de": "Data Management"}, "productType": {"typeId": "product-type", "id": "75a25e43-ec58-4581-90f4-367b4cae1cfc", "version": 2}, "productSlug": {"en": "data-management"}, "variant": {"id": 2, "sku": "DRX_PR_BODAS_DATA_3GB", "key": "DRX_PR_BODAS_DATA_3GB", "prices": [], "images": [], "attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "name", "value": {"en": "3GB per RCU"}}, {"name": "description", "value": {"en": "⭐️ Recommended"}}], "assets": []}, "price": {"id": "d125d0e4-89b0-4002-8422-84888c649286", "value": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}}, "quantity": 1, "discountedPricePerQuantity": [], "taxRate": {"name": "MwSt", "amount": 0.19, "includedInPrice": false, "country": "DE", "id": "CAynvyIK", "subRates": []}, "perMethodTaxRate": [], "addedAt": "2024-12-16T15:12:46.565Z", "lastModifiedAt": "2024-12-16T15:12:46.565Z", "state": [{"quantity": 1, "state": {"typeId": "state", "id": "3ee09d93-9d61-477b-b6c7-83b94c9e7d2e"}}], "priceMode": "ExternalPrice", "lineItemMode": "Standard", "totalPrice": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}, "taxedPrice": {"totalNet": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}, "totalGross": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}, "taxPortions": [{"rate": 0.19, "amount": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}, "name": "MwSt"}], "totalTax": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}}, "taxedPricePortions": [], "custom": {"type": {"typeId": "type", "id": "d1217476-774e-4cb8-a53a-25d597a91cc3"}, "fields": {"parentLineItemKey": "1a6cc048-1c5e-4ccb-85cd-7ba91a82e18b"}}}, {"id": "16c66627-fbe7-4a46-bcbb-ab92de9155d5", "key": "aaa6fc0b-f3d0-47c0-85ed-5ac1b07fa052", "productId": "b21d8b73-debd-4f44-9377-8ba93208d7ae", "productKey": "bodas_connect_5", "name": {"en": "BODAS Connect Lite", "de": "BODAS Connect Lite"}, "productType": {"typeId": "product-type", "id": "a46f1d35-7553-4813-8a20-993f1e84b9f4", "version": 54}, "productSlug": {"en": "rcu-series-5"}, "variant": {"id": 1, "sku": "DRX_PR_BODAS_LITE", "key": "DRX_PR_BODAS_LITE", "prices": [], "images": [{"url": "https://images.cdn.europe-west1.gcp.commercetools.com/b4800138-a539-4ad0-9511-9260c02d29bb/image-2024-12-2_13-5-Lxw4Uv88.png", "dimensions": {"w": 2000, "h": 2000}}, {"url": "https://images.cdn.europe-west1.gcp.commercetools.com/b4800138-a539-4ad0-9511-9260c02d29bb/bodasconnect_icon-7iOIvcEi.jpeg", "dimensions": {"w": 224, "h": 224}}], "attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "name", "value": {"en": "Basic Device Management (RCU Series 5)", "de": "BODAS Connect for RCU 5"}}, {"name": "description", "value": {"en": "Access to your dedicated cloud instance for basic Device management, SOTA & cellular coverage. Cellular data usage limited via CAN signal configuration. No ECU FOTA possible. ", "de": ""}}, {"name": "bundleAmount", "value": 1}, {"name": "licenseType", "value": {"key": "CONSUMPTION", "label": "product with postpaid billing and consumption pricing (CONSUMPTION)"}}, {"name": "runtime", "value": "P1M"}, {"name": "notice<PERSON><PERSON>od", "value": "P28D"}, {"name": "externalProductId", "value": "R917015756"}, {"name": "sellerCompanyId", "value": "f01d4e3c-ab09-4cd2-b5ff-312de81ab7bf"}, {"name": "addons", "value": [{"typeId": "product", "id": "990cdc6a-13f5-4832-a7da-ab44df4b2f19"}]}, {"name": "externalDocuments", "value": [[{"name": "link", "value": {"en": "https://www.boschrexroth.com/media/251a97f6-b44e-476a-aaa8-f9bba06fb6ae"}}, {"name": "name", "value": {"en": "Fact Sheet RCU Lite Services"}}]]}, {"name": "features", "value": {"en": "First 20 claimed RCU (Series 5) are included in base fee."}}], "assets": []}, "price": {"id": "d3bbd4ad-c2a2-4f92-8934-d1fc8fd869cb", "value": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 24900, "fractionDigits": 2}, "key": "DRX_PR_BODAS_LITE_PRICE"}, "quantity": 1, "discountedPricePerQuantity": [], "taxRate": {"name": "MwSt", "amount": 0.19, "includedInPrice": false, "country": "DE", "id": "CAynvyIK", "subRates": []}, "perMethodTaxRate": [], "addedAt": "2024-12-16T15:13:04.664Z", "lastModifiedAt": "2024-12-16T15:13:04.664Z", "state": [{"quantity": 1, "state": {"typeId": "state", "id": "3ee09d93-9d61-477b-b6c7-83b94c9e7d2e"}}], "priceMode": "Platform", "lineItemMode": "Standard", "totalPrice": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 24900, "fractionDigits": 2}, "taxedPrice": {"totalNet": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 24900, "fractionDigits": 2}, "totalGross": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 29631, "fractionDigits": 2}, "taxPortions": [{"rate": 0.19, "amount": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 4731, "fractionDigits": 2}, "name": "MwSt"}], "totalTax": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 4731, "fractionDigits": 2}}, "taxedPricePortions": [], "custom": {"type": {"typeId": "type", "id": "ad6426e5-f34d-46d3-9943-d27e8c62a56c"}, "fields": {"addonItemKeys": []}}}, {"id": "e942ba6a-499f-49ed-90e0-cbef7a3b8c99", "key": "f8ebb24d-657e-4ffc-b244-19acc20f593f", "productId": "5abd3673-2110-4aa0-bbf9-c97cd1e24c89", "productKey": "bodas_connect_10", "name": {"en": "BODAS Connect Pro", "de": "BODAS Connect Pro"}, "productType": {"typeId": "product-type", "id": "a46f1d35-7553-4813-8a20-993f1e84b9f4", "version": 54}, "productSlug": {"en": "bodas-connect-for-rcu-series-10-20"}, "variant": {"id": 1, "sku": "DRX_PR_BODAS_COMPACT", "key": "DRX_PR_BODAS_COMPACT", "prices": [], "images": [{"url": "https://images.cdn.europe-west1.gcp.commercetools.com/b4800138-a539-4ad0-9511-9260c02d29bb/image-2024-12-2_13-5-fhvg6WdO.png", "dimensions": {"w": 2000, "h": 2000}}, {"url": "https://images.cdn.europe-west1.gcp.commercetools.com/b4800138-a539-4ad0-9511-9260c02d29bb/bodasconnect_icon-dbWuJ_-R.jpeg", "dimensions": {"w": 224, "h": 224}}], "attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "name", "value": {"en": "Device Management Compact (RCU Series 10/20)"}}, {"name": "bundleAmount", "value": 1}, {"name": "licenseType", "value": {"key": "CONSUMPTION", "label": "product with postpaid billing and consumption pricing (CONSUMPTION)"}}, {"name": "externalProductId", "value": "R917014006"}, {"name": "sellerCompanyId", "value": "f01d4e3c-ab09-4cd2-b5ff-312de81ab7bf"}, {"name": "description", "value": {"en": "Access to a dedicated cloud instance for basic Device management, administration and software updates for the RCU. First 20 claimed RCUs (Series 10/20) included in base fee."}}, {"name": "features", "value": {"en": "RCU base software customized for off-highway needs\nRCU software updates for optimal cyber security\nWrite & deploy your own custom snaps"}}, {"name": "runtime", "value": "P1M"}, {"name": "notice<PERSON><PERSON>od", "value": "P28D"}, {"name": "addons", "value": [{"typeId": "product", "id": "d74b86e4-233c-4bec-b137-d7ef4b630b64"}, {"typeId": "product", "id": "990cdc6a-13f5-4832-a7da-ab44df4b2f19"}, {"typeId": "product", "id": "da41df90-baa5-415c-bbda-3c4c0d337645"}]}, {"name": "externalDocuments", "value": [[{"name": "link", "value": {"en": "https://dc-mkt-prod.cloud.bosch.tech/xrm/media/global/documents/legal_notice/tc_for_iot_services.pdf"}}, {"name": "name", "value": {"en": "IoT Services T&Cs"}}, {"name": "category", "value": {"key": "SOFTWARE_LICENSE", "label": "SOFTWARE_LICENSE, this will be grouped as software license in the ui"}}], [{"name": "link", "value": {"en": "https://www.boschrexroth.com/de/de/media-details/c8a94c1f-d8bc-4ead-afd2-1ff93fb27233"}}, {"name": "name", "value": {"en": "Fact Sheet All-In-One Connectivity"}}], [{"name": "link", "value": {"en": "https://www.boschrexroth.com/de/de/media-details/29d12ccd-61cb-455c-9378-8a58440cf4ae", "de": ""}}, {"name": "name", "value": {"en": "Fact Sheet Device Connectivity"}}]]}], "assets": []}, "price": {"id": "9c50dda8-7bca-43a8-9b22-63057ea8eab6", "value": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 24900, "fractionDigits": 2}, "key": "DRX_PR_BODAS_COMPACT_PRICE"}, "quantity": 1, "discountedPricePerQuantity": [], "taxRate": {"name": "MwSt", "amount": 0.19, "includedInPrice": false, "country": "DE", "id": "CAynvyIK", "subRates": []}, "perMethodTaxRate": [], "addedAt": "2024-12-16T15:35:47.604Z", "lastModifiedAt": "2024-12-16T15:35:47.604Z", "state": [{"quantity": 1, "state": {"typeId": "state", "id": "3ee09d93-9d61-477b-b6c7-83b94c9e7d2e"}}], "priceMode": "Platform", "lineItemMode": "Standard", "totalPrice": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 24900, "fractionDigits": 2}, "taxedPrice": {"totalNet": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 24900, "fractionDigits": 2}, "totalGross": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 29631, "fractionDigits": 2}, "taxPortions": [{"rate": 0.19, "amount": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 4731, "fractionDigits": 2}, "name": "MwSt"}], "totalTax": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 4731, "fractionDigits": 2}}, "taxedPricePortions": [], "custom": {"type": {"typeId": "type", "id": "ad6426e5-f34d-46d3-9943-d27e8c62a56c"}, "fields": {"addonItemKeys": ["f27b9399-e7c8-4d3c-9f72-dd420450ccfe", "7dcbdf04-7d50-4fc8-bf3e-ff9d47eb984b"]}}}, {"id": "913131bc-516a-4257-bbd9-f48aaa71b5f0", "key": "f27b9399-e7c8-4d3c-9f72-dd420450ccfe", "productId": "d74b86e4-233c-4bec-b137-d7ef4b630b64", "productKey": "cellular-connection", "name": {"en": "Cellular Services (RCU Series 10/20)", "de": "Cellular Connection"}, "productType": {"typeId": "product-type", "id": "75a25e43-ec58-4581-90f4-367b4cae1cfc", "version": 2}, "productSlug": {"en": "cellular-connection"}, "variant": {"id": 4, "sku": "DRX_PR_BODAS_CONNECTION_1024MB", "key": "DRX_PR_BODAS_CONNECTION_1024MB", "prices": [], "images": [], "attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "name", "value": {"en": "1024 MB per RCU /month"}}, {"name": "description", "value": {"en": "⭐️ Recommended"}}], "assets": []}, "price": {"id": "093646f5-4a1a-401a-a68b-5710ec305c3f", "value": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}}, "quantity": 1, "discountedPricePerQuantity": [], "taxRate": {"name": "MwSt", "amount": 0.19, "includedInPrice": false, "country": "DE", "id": "CAynvyIK", "subRates": []}, "perMethodTaxRate": [], "addedAt": "2024-12-16T15:35:47.616Z", "lastModifiedAt": "2024-12-16T15:35:47.616Z", "state": [{"quantity": 1, "state": {"typeId": "state", "id": "3ee09d93-9d61-477b-b6c7-83b94c9e7d2e"}}], "priceMode": "ExternalPrice", "lineItemMode": "Standard", "totalPrice": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}, "taxedPrice": {"totalNet": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}, "totalGross": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}, "taxPortions": [{"rate": 0.19, "amount": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}, "name": "MwSt"}], "totalTax": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}}, "taxedPricePortions": [], "custom": {"type": {"typeId": "type", "id": "d1217476-774e-4cb8-a53a-25d597a91cc3"}, "fields": {"parentLineItemKey": "f8ebb24d-657e-4ffc-b244-19acc20f593f"}}}, {"id": "89372ecc-de1b-4779-9ca0-05e20627dd05", "key": "7dcbdf04-7d50-4fc8-bf3e-ff9d47eb984b", "productId": "990cdc6a-13f5-4832-a7da-ab44df4b2f19", "productKey": "data-management", "name": {"en": "Data Management", "de": "Data Management"}, "productType": {"typeId": "product-type", "id": "75a25e43-ec58-4581-90f4-367b4cae1cfc", "version": 2}, "productSlug": {"en": "data-management"}, "variant": {"id": 2, "sku": "DRX_PR_BODAS_DATA_3GB", "key": "DRX_PR_BODAS_DATA_3GB", "prices": [], "images": [], "attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "name", "value": {"en": "3GB per RCU"}}, {"name": "description", "value": {"en": "⭐️ Recommended"}}], "assets": []}, "price": {"id": "839f7549-59c7-46ac-9ba9-c3f6ae2d1a6d", "value": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}}, "quantity": 1, "discountedPricePerQuantity": [], "taxRate": {"name": "MwSt", "amount": 0.19, "includedInPrice": false, "country": "DE", "id": "CAynvyIK", "subRates": []}, "perMethodTaxRate": [], "addedAt": "2024-12-16T15:35:47.623Z", "lastModifiedAt": "2024-12-16T15:35:47.623Z", "state": [{"quantity": 1, "state": {"typeId": "state", "id": "3ee09d93-9d61-477b-b6c7-83b94c9e7d2e"}}], "priceMode": "ExternalPrice", "lineItemMode": "Standard", "totalPrice": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}, "taxedPrice": {"totalNet": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}, "totalGross": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}, "taxPortions": [{"rate": 0.19, "amount": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}, "name": "MwSt"}], "totalTax": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}}, "taxedPricePortions": [], "custom": {"type": {"typeId": "type", "id": "d1217476-774e-4cb8-a53a-25d597a91cc3"}, "fields": {"parentLineItemKey": "f8ebb24d-657e-4ffc-b244-19acc20f593f"}}}, {"id": "422a5d5d-f3ea-47a9-95fb-ba79692f912a", "key": "2711c3cc-5954-4066-bef4-face4c64b508", "productId": "5abd3673-2110-4aa0-bbf9-c97cd1e24c89", "productKey": "bodas_connect_10", "name": {"en": "BODAS Connect Pro", "de": "BODAS Connect Pro"}, "productType": {"typeId": "product-type", "id": "a46f1d35-7553-4813-8a20-993f1e84b9f4", "version": 54}, "productSlug": {"en": "bodas-connect-for-rcu-series-10-20"}, "variant": {"id": 1, "sku": "DRX_PR_BODAS_COMPACT", "key": "DRX_PR_BODAS_COMPACT", "prices": [], "images": [{"url": "https://images.cdn.europe-west1.gcp.commercetools.com/b4800138-a539-4ad0-9511-9260c02d29bb/image-2024-12-2_13-5-fhvg6WdO.png", "dimensions": {"w": 2000, "h": 2000}}, {"url": "https://images.cdn.europe-west1.gcp.commercetools.com/b4800138-a539-4ad0-9511-9260c02d29bb/bodasconnect_icon-dbWuJ_-R.jpeg", "dimensions": {"w": 224, "h": 224}}], "attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "name", "value": {"en": "Device Management Compact (RCU Series 10/20)"}}, {"name": "bundleAmount", "value": 1}, {"name": "licenseType", "value": {"key": "CONSUMPTION", "label": "product with postpaid billing and consumption pricing (CONSUMPTION)"}}, {"name": "externalProductId", "value": "R917014006"}, {"name": "sellerCompanyId", "value": "f01d4e3c-ab09-4cd2-b5ff-312de81ab7bf"}, {"name": "description", "value": {"en": "Access to a dedicated cloud instance for basic Device management, administration and software updates for the RCU. First 20 claimed RCUs (Series 10/20) included in base fee."}}, {"name": "features", "value": {"en": "RCU base software customized for off-highway needs\nRCU software updates for optimal cyber security\nWrite & deploy your own custom snaps"}}, {"name": "runtime", "value": "P1M"}, {"name": "notice<PERSON><PERSON>od", "value": "P28D"}, {"name": "addons", "value": [{"typeId": "product", "id": "d74b86e4-233c-4bec-b137-d7ef4b630b64"}, {"typeId": "product", "id": "990cdc6a-13f5-4832-a7da-ab44df4b2f19"}, {"typeId": "product", "id": "da41df90-baa5-415c-bbda-3c4c0d337645"}]}, {"name": "externalDocuments", "value": [[{"name": "link", "value": {"en": "https://dc-mkt-prod.cloud.bosch.tech/xrm/media/global/documents/legal_notice/tc_for_iot_services.pdf"}}, {"name": "name", "value": {"en": "IoT Services T&Cs"}}, {"name": "category", "value": {"key": "SOFTWARE_LICENSE", "label": "SOFTWARE_LICENSE, this will be grouped as software license in the ui"}}], [{"name": "link", "value": {"en": "https://www.boschrexroth.com/de/de/media-details/c8a94c1f-d8bc-4ead-afd2-1ff93fb27233"}}, {"name": "name", "value": {"en": "Fact Sheet All-In-One Connectivity"}}], [{"name": "link", "value": {"en": "https://www.boschrexroth.com/de/de/media-details/29d12ccd-61cb-455c-9378-8a58440cf4ae", "de": ""}}, {"name": "name", "value": {"en": "Fact Sheet Device Connectivity"}}]]}], "assets": []}, "price": {"id": "9c50dda8-7bca-43a8-9b22-63057ea8eab6", "value": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 24900, "fractionDigits": 2}, "key": "DRX_PR_BODAS_COMPACT_PRICE"}, "quantity": 2, "discountedPricePerQuantity": [], "taxRate": {"name": "MwSt", "amount": 0.19, "includedInPrice": false, "country": "DE", "id": "CAynvyIK", "subRates": []}, "perMethodTaxRate": [], "addedAt": "2024-12-16T15:36:14.431Z", "lastModifiedAt": "2024-12-16T15:36:28.241Z", "state": [{"quantity": 2, "state": {"typeId": "state", "id": "3ee09d93-9d61-477b-b6c7-83b94c9e7d2e"}}], "priceMode": "Platform", "lineItemMode": "Standard", "totalPrice": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 49800, "fractionDigits": 2}, "taxedPrice": {"totalNet": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 49800, "fractionDigits": 2}, "totalGross": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 59262, "fractionDigits": 2}, "taxPortions": [{"rate": 0.19, "amount": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 9462, "fractionDigits": 2}, "name": "MwSt"}], "totalTax": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 9462, "fractionDigits": 2}}, "taxedPricePortions": [], "custom": {"type": {"typeId": "type", "id": "ad6426e5-f34d-46d3-9943-d27e8c62a56c"}, "fields": {"addonItemKeys": []}}}], "customLineItems": [], "transactionFee": true, "discountCodes": [], "directDiscounts": [], "cart": {"typeId": "cart", "id": "4b72522a-12c4-47a5-85c5-0f0bf4100900"}, "custom": {"type": {"typeId": "type", "id": "184f9fa2-740d-43b8-b8eb-f49e6e5f672c"}, "fields": {"companyId": "d9a1491f-8657-4cdc-a13f-a0a9d2745248", "userId": "c09fbeca-dd9b-4155-a8ea-16f2d44f7539", "tenant": "rex<PERSON>", "notes1": "A great first note", "notes2": "A fantastic second note"}}, "paymentInfo": {"payments": [{"typeId": "payment", "id": "e482ef33-084b-4b38-90a8-5070bfe13873"}]}, "billingAddress": {"streetName": "Moosacher Str.", "streetNumber": "80", "postalCode": "80809", "city": "München", "country": "DE", "email": "<EMAIL>"}, "itemShippingAddresses": [], "refusedGifts": []}