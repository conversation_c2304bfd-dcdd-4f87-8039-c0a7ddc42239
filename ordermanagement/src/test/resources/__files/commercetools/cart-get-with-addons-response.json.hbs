{"type": "<PERSON><PERSON>", "id": "0bcc1353-f5c6-4a4b-984c-a90d6c02d632", "version": 7, "versionModifiedAt": "2024-12-06T15:26:15.662Z", "lastMessageSequenceNumber": 1, "createdAt": "2024-12-03T14:34:59.081Z", "lastModifiedAt": "2024-12-06T15:26:15.662Z", "lastModifiedBy": {"clientId": "SSAIdVhBpKt80ChCMjZwxi4c", "isPlatformClient": false}, "createdBy": {"clientId": "SSAIdVhBpKt80ChCMjZwxi4c", "isPlatformClient": false}, "key": "ca49c858-eed5-4afc-8052-3e27b46ff13e", "customerGroup": {"typeId": "customer-group", "id": "36318b6d-7e67-4a6f-9cf1-65932a5c65f2"}, "lineItems": [{"id": "2baee68c-eefa-41a4-9094-5ff2a8150876", "key": "0f38b6ea-22c0-4d78-b60a-7dcd59395354", "productId": "5abd3673-2110-4aa0-bbf9-c97cd1e24c89", "productKey": "bodas_connect_10", "name": {"en": "RCU Series 10 & 20", "de": "RCU Serie 10 & 20"}, "productType": {"typeId": "product-type", "id": "a46f1d35-7553-4813-8a20-993f1e84b9f4", "version": 54}, "productSlug": {"en": "bodas-connect-for-rcu-series-10-20"}, "variant": {"id": 1, "sku": "DRX_PR_BODAS_COMPACT", "key": "DRX_PR_BODAS_COMPACT", "prices": [], "images": [{"url": "https://images.cdn.europe-west1.gcp.commercetools.com/b4800138-a539-4ad0-9511-9260c02d29bb/bodasconnect-ooNblHOt.jpeg", "dimensions": {"w": 1600, "h": 1600}}, {"url": "https://images.cdn.europe-west1.gcp.commercetools.com/b4800138-a539-4ad0-9511-9260c02d29bb/bodasconnect_icon-dbWuJ_-R.jpeg", "dimensions": {"w": 224, "h": 224}}], "attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "name", "value": {"en": "Device Management Compact"}}, {"name": "bundleAmount", "value": 1}, {"name": "licenseType", "value": {"key": "CONSUMPTION", "label": "product with postpaid billing and consumption pricing (CONSUMPTION)"}}, {"name": "externalProductId", "value": "R917014006"}, {"name": "sellerCompanyId", "value": "f01d4e3c-ab09-4cd2-b5ff-312de81ab7bf"}, {"name": "description", "value": {"en": "The Telematics solution for machine OEMs to gain internal R&D and customer support efficiency & establish external data driven business models. Includes integrated Device Management, Cellular Connectivity and Data Management and offers Over-the-Air functionality for updating ECUs."}}, {"name": "features", "value": {"en": "SOTA\nCustom Snaps\nCybersecurity"}}, {"name": "runtime", "value": "P1M"}, {"name": "notice<PERSON><PERSON>od", "value": "P28D"}, {"name": "addons", "value": [{"typeId": "product", "id": "d74b86e4-233c-4bec-b137-d7ef4b630b64"}]}], "assets": []}, "price": {"id": "9c50dda8-7bca-43a8-9b22-63057ea8eab6", "value": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 24900, "fractionDigits": 2}, "key": "DRX_PR_BODAS_COMPACT"}, "quantity": 2, "discountedPricePerQuantity": [], "taxRate": {"name": "MwSt", "amount": 0.2, "includedInPrice": false, "country": "AT", "id": "tr8WoQjy", "subRates": []}, "perMethodTaxRate": [], "addedAt": "2024-12-06T15:23:51.429Z", "lastModifiedAt": "2024-12-06T15:26:15.442Z", "state": [{"quantity": 2, "state": {"typeId": "state", "id": "3ee09d93-9d61-477b-b6c7-83b94c9e7d2e"}}], "priceMode": "Platform", "lineItemMode": "Standard", "totalPrice": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 49800, "fractionDigits": 2}, "taxedPrice": {"totalNet": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 49800, "fractionDigits": 2}, "totalGross": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 59760, "fractionDigits": 2}, "taxPortions": [{"rate": 0.2, "amount": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 9960, "fractionDigits": 2}, "name": "MwSt"}], "totalTax": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 9960, "fractionDigits": 2}}, "taxedPricePortions": []}, {"id": "fc66ca6d-ea09-442b-b39a-b5e91fd84ca1", "key": "8a5a66e1-f3c2-4861-9ef6-f62b909ad726", "productId": "d74b86e4-233c-4bec-b137-d7ef4b630b64", "productKey": "cellular-connection", "name": {"en": "Cellular Connection"}, "productType": {"typeId": "product-type", "id": "75a25e43-ec58-4581-90f4-367b4cae1cfc", "version": 2}, "productSlug": {"en": "cellular-connection"}, "variant": {"id": 1, "sku": "DRX_PR_BODAS_CONNECTION_50MB", "key": "DRX_PR_BODAS_CONNECTION_50MB", "prices": [], "images": [], "attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "name", "value": {"en": "50 MB"}}], "assets": []}, "price": {"id": "1bdefe63-c27a-4e6c-80f6-c6b459be3afe", "value": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}}, "quantity": 1, "discountedPricePerQuantity": [], "taxRate": {"name": "MwSt", "amount": 0.2, "includedInPrice": false, "country": "AT", "id": "tr8WoQjy", "subRates": []}, "perMethodTaxRate": [], "addedAt": "2024-12-06T15:23:51.437Z", "lastModifiedAt": "2024-12-06T15:23:51.437Z", "state": [{"quantity": 1, "state": {"typeId": "state", "id": "3ee09d93-9d61-477b-b6c7-83b94c9e7d2e"}}], "priceMode": "ExternalPrice", "lineItemMode": "Standard", "totalPrice": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}, "taxedPrice": {"totalNet": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}, "totalGross": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}, "taxPortions": [{"rate": 0.2, "amount": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}, "name": "MwSt"}], "totalTax": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}}, "taxedPricePortions": [], "custom": {"type": {"typeId": "type", "id": "d1217476-774e-4cb8-a53a-25d597a91cc3"}, "fields": {"parentLineItemKey": "0f38b6ea-22c0-4d78-b60a-7dcd59395354"}}}, {"id": "29cee239-bffc-4d9d-8d34-a106e0048305", "key": "7f15d8e3-886f-4f61-a715-673003c7c99c", "productId": "d74b86e4-233c-4bec-b137-d7ef4b630b64", "productKey": "cellular-connection", "name": {"en": "Cellular Connection"}, "productType": {"typeId": "product-type", "id": "75a25e43-ec58-4581-90f4-367b4cae1cfc", "version": 2}, "productSlug": {"en": "cellular-connection"}, "variant": {"id": 1, "sku": "DRX_PR_BODAS_CONNECTION_50MB", "key": "DRX_PR_BODAS_CONNECTION_50MB", "prices": [], "images": [], "attributes": [{"name": "tenant", "value": {"key": "rex<PERSON>", "label": "rex<PERSON>"}}, {"name": "name", "value": {"en": "50 MB"}}], "assets": []}, "price": {"id": "2194252e-67aa-454f-924f-ec9f932d047b", "value": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}}, "quantity": 1, "discountedPricePerQuantity": [], "taxRate": {"name": "MwSt", "amount": 0.2, "includedInPrice": false, "country": "AT", "id": "tr8WoQjy", "subRates": []}, "perMethodTaxRate": [], "addedAt": "2024-12-06T15:26:15.442Z", "lastModifiedAt": "2024-12-06T15:26:15.442Z", "state": [{"quantity": 1, "state": {"typeId": "state", "id": "3ee09d93-9d61-477b-b6c7-83b94c9e7d2e"}}], "priceMode": "ExternalPrice", "lineItemMode": "Standard", "totalPrice": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}, "taxedPrice": {"totalNet": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}, "totalGross": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}, "taxPortions": [{"rate": 0.2, "amount": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}, "name": "MwSt"}], "totalTax": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 0, "fractionDigits": 2}}, "taxedPricePortions": [], "custom": {"type": {"typeId": "type", "id": "d1217476-774e-4cb8-a53a-25d597a91cc3"}, "fields": {"parentLineItemKey": "8f03ec8f-2bca-41b2-967b-32f16a5dbc79"}}}], "cartState": "Active", "totalPrice": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 49800, "fractionDigits": 2}, "taxedPrice": {"totalNet": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 49800, "fractionDigits": 2}, "totalGross": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 59760, "fractionDigits": 2}, "taxPortions": [{"rate": 0.2, "amount": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 9960, "fractionDigits": 2}, "name": "MwSt"}], "totalTax": {"type": "centPrecision", "currencyCode": "EUR", "centAmount": 9960, "fractionDigits": 2}}, "country": "AT", "shippingMode": "Single", "shippingAddress": {"streetName": "Berg", "streetNumber": "18", "postalCode": "5652", "city": "Dienten am Hochkönig", "country": "AT"}, "shipping": [], "customLineItems": [], "discountCodes": [], "directDiscounts": [], "custom": {"type": {"typeId": "type", "id": "184f9fa2-740d-43b8-b8eb-f49e6e5f672c"}, "fields": {"companyId": "d9a1491f-8657-4cdc-a13f-a0a9d2745248", "userId": "7cb0fdd4-9503-41e0-a279-84ad098c8a00", "tenant": "rex<PERSON>"}}, "inventoryMode": "None", "taxMode": "Platform", "taxRoundingMode": "HalfEven", "taxCalculationMode": "LineItemLevel", "refusedGifts": [], "origin": "Customer", "billingAddress": {"streetName": "Bistdudeppertgassl", "streetNumber": "12", "postalCode": "4010", "city": "Linz", "region": "Linz-Land", "state": "Oberösterreich", "country": "AT", "email": "<EMAIL>"}, "itemShippingAddresses": [], "totalLineItemQuantity": 4}