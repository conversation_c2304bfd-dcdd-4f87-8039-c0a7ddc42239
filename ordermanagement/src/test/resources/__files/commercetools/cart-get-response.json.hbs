{
  "type": "Cart",
  "id": "b895ec91-a988-4420-8af1-392782f68d13",
  "version": 3,
  "versionModifiedAt": "2024-10-28T06:29:09.651Z",
  "lastMessageSequenceNumber": 1,
  "createdAt": "2024-10-28T06:29:09.526Z",
  "lastModifiedAt": "2024-10-28T06:29:09.651Z",
  "lastModifiedBy": {
    "clientId": "SSAIdVhBpKt80ChCMjZwxi4c",
    "isPlatformClient": false
  },
  "createdBy": {
    "clientId": "SSAIdVhBpKt80ChCMjZwxi4c",
    "isPlatformClient": false
  },
  "key": "47114579-15d6-4414-b59b-ee56e5f10354",
  "customerGroup": {
    "typeId": "customer-group",
    "id": "36318b6d-7e67-4a6f-9cf1-65932a5c65f2"
  },
  "lineItems": [
    {
      "id": "af6e50db-e1f3-4c56-9599-a7fafafb8efa",
      "productId": "6d8ab4be-e24b-473b-a6a5-f1228380b86c",
      "productKey": "hydraulic_hub_itests",
      "name": {
        "en": "Hydraulic Hub Wrong Seller",
        "de": "Hydraulic Hub Falscher Verkäufer"
      },
      "productType": {
        "typeId": "product-type",
        "id": "a46f1d35-7553-4813-8a20-993f1e84b9f4",
        "version": 34
      },
      "productSlug": {
        "en": "hydraulic-hub"
      },
      "variant": {
        "id": 2,
        "sku": "DDCIH_hydraulic_hub_yearly",
        "key": "DDCIH_hydraulic_hub_yearly",
        "prices": [],
        "images": [],
        "attributes": [
          {
            "name": "name",
            "value": {
              "en": "Yearly",
              "de": "Jährlich"
            }
          },
          {
            "name": "description",
            "value": {
              "en": "this renews yearly",
              "de": "verlängert sich jährlich"
            }
          },
          {
            "name": "bundleAmount",
            "value": 1
          },
          {
            "name": "licenseType",
            "value": {
              "key": "SUBSCRIPTION",
              "label": "subscription license (SUBSCRIPTION), this will be renewed automatically after runtime"
            }
          },
          {
            "name": "runtime",
            "value": "P1Y"
          },
          {
            "name": "noticePeriod",
            "value": "P1M"
          },
          {
            "name": "externalProductId",
            "value": "54987P984313212"
          },
          {
            "name": "features",
            "value": {
              "en": "feature 1\nfeature 2",
              "de": "funktionalität 1\nfunktionalität 2"
            }
          },
          {
            "name": "agreements",
            "value": [
              [
                {
                  "name": "name",
                  "value": {
                    "en": "Device Management T&C",
                    "de": "Device Management T&C"
                  }
                },
                {
                  "name": "link",
                  "value": {
                    "en": "https://dc-mkt-prod.cloud.bosch.tech/xrm/media/global/documents/legal_notice/tc_devicemanagement_saas.pdf",
                    "de": "https://dc-mkt-prod.cloud.bosch.tech/xrm/media/global/documents/legal_notice/tc_devicemanagement_saas.pdf"
                  }
                }
              ],
              [
                {
                  "name": "link",
                  "value": {
                    "en": "https://dc-mkt-prod.cloud.bosch.tech/xrm/media/global/documents/legal_notice/tc_for_data_management_services.pdf",
                    "de": "https://dc-mkt-prod.cloud.bosch.tech/xrm/media/global/documents/legal_notice/tc_for_data_management_services.pdf"
                  }
                },
                {
                  "name": "name",
                  "value": {
                    "en": "Data Management T&C",
                    "de": "Data Management T&C"
                  }
                }
              ]
            ]
          },
          {
            "name": "sellerCompanyId",
            "value": "fc82b363-c3f7-4797-a97c-af37711ac1fd"
          },
          {
            "name": "tenant",
            "value": {
              "key": "rexroth",
              "label": "rexroth"
            }
          },
          {
            "name": "externalDocuments",
            "value": [
              [
                {
                  "name": "link",
                  "value": {
                    "en": "https://worksonmymachine.lol/document_en.pdf",
                    "de": "https://worksonmymachine.lol/document_de.pdf"
                  }
                },
                {
                  "name": "name",
                  "value": {
                    "en": "Fact Sheet",
                    "de": "Datenblatt"
                  }
                }
              ]
            ]
          },
          {
            "name": "entitlements",
            "value": [
              "DCKEYCLOAK:PREMIUM",
              "LITMOS"
            ]
          }
        ],
        "assets": []
      },
      "price": {
        "id": "2388cf61-0eb6-4223-a8e7-6502e20dbadb",
        "value": {
          "type": "centPrecision",
          "currencyCode": "EUR",
          "centAmount": 20000,
          "fractionDigits": 2
        },
        "key": "2nIlvBqbaNhQmC1jg7ubJU"
      },
      "quantity": 3,
      "discountedPricePerQuantity": [],
      "taxRate": {
        "name": "MwSt",
        "amount": 0.2,
        "includedInPrice": false,
        "country": "AT",
        "id": "tr8WoQjy",
        "subRates": []
      },
      "perMethodTaxRate": [],
      "addedAt": "2024-10-28T06:29:09.637Z",
      "lastModifiedAt": "2024-10-28T06:29:09.637Z",
      "state": [
        {
          "quantity": 3,
          "state": {
            "typeId": "state",
            "id": "3ee09d93-9d61-477b-b6c7-83b94c9e7d2e"
          }
        }
      ],
      "priceMode": "Platform",
      "lineItemMode": "Standard",
      "totalPrice": {
        "type": "centPrecision",
        "currencyCode": "EUR",
        "centAmount": 60000,
        "fractionDigits": 2
      },
      "taxedPrice": {
        "totalNet": {
          "type": "centPrecision",
          "currencyCode": "EUR",
          "centAmount": 60000,
          "fractionDigits": 2
        },
        "totalGross": {
          "type": "centPrecision",
          "currencyCode": "EUR",
          "centAmount": 72000,
          "fractionDigits": 2
        },
        "taxPortions": [
          {
            "rate": 0.2,
            "amount": {
              "type": "centPrecision",
              "currencyCode": "EUR",
              "centAmount": 12000,
              "fractionDigits": 2
            },
            "name": "MwSt"
          }
        ],
        "totalTax": {
          "type": "centPrecision",
          "currencyCode": "EUR",
          "centAmount": 12000,
          "fractionDigits": 2
        }
      },
      "taxedPricePortions": []
    }
  ],
  "cartState": "Active",
  "totalPrice": {
    "type": "centPrecision",
    "currencyCode": "EUR",
    "centAmount": {{val parameters.totalAmount default='60000'}},
    "fractionDigits": 2
  },
  "taxedPrice": {
    "totalNet": {
      "type": "centPrecision",
      "currencyCode": "EUR",
      "centAmount": 60000,
      "fractionDigits": 2
    },
    "totalGross": {
      "type": "centPrecision",
      "currencyCode": "EUR",
      "centAmount": 72000,
      "fractionDigits": 2
    },
    "taxPortions": [
      {
        "rate": 0.2,
        "amount": {
          "type": "centPrecision",
          "currencyCode": "EUR",
          "centAmount": 12000,
          "fractionDigits": 2
        },
        "name": "MwSt"
      }
    ],
    "totalTax": {
      "type": "centPrecision",
      "currencyCode": "EUR",
      "centAmount": 12000,
      "fractionDigits": 2
    }
  },
  "country": "AT",
  "shippingMode": "Single",
  "shippingAddress": {
    "streetName": "Berg",
    "streetNumber": "18",
    "postalCode": "5652",
    "city": "Dienten am Hochkönig",
    "country": "AT"
  },
  "shipping": [],
  "customLineItems": [],
  "discountCodes": [],
  "directDiscounts": [],
  "custom": {
    "type": {
      "typeId": "type",
      "id": "b35e6a79-4424-4332-8647-31c8d8585edc"
    },
    "fields": {
      "companyId": "0db1341c-7782-4819-a24e-6eb6d3c6c272",
      "userId": "7cb0fdd4-9503-41e0-a279-84ad098c8a00",
      "tenant": "rexroth"
    }
  },
  "inventoryMode": "None",
  "taxMode": "Platform",
  "taxRoundingMode": "HalfEven",
  "taxCalculationMode": "LineItemLevel",
  "refusedGifts": [],
  "origin": "Customer",
  "billingAddress": {
    "streetName": "Bistdudeppertgassl",
    "streetNumber": "12",
    "postalCode": "4010",
    "city": "Linz",
    "region": "Linz-Land",
    "state": "Oberösterreich",
    "country": "AT",
    "email": "<EMAIL>"
  },
  "itemShippingAddresses": [],
  "totalLineItemQuantity": {{val parameters.totalQuantity default='3'}}
}