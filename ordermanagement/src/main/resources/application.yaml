bossstore:
   environment-prefix: L
   commercetools:
      clientId: gp83Agv3Z8RMX0tmNtljlFbo
      clientSecret: nED0ZFVUhUQq0QxzvsrIO5yJLH9USTXO
      projectKey: glorious-new-store
   pgw:
      apiKey: dummy-api-key
      url: http://localhost:1234
   mattermost:
      url: https://mattermost.sastdev.net/
      apiKey: testkey
      channelId: testchannel
   tenants:
      rexroth:
         ump:
            tenant: baam
         public-urls:
           contract-management-url: https://store.mp-dc-d.com/subscriptions
         cart-limits-by-currency:
           EUR: 10000
           USD: 10000

   sellers:
      fc82b363-c3f7-4797-a97c-af37711ac1fd:
         boschTransfer:
            accountHolder: Bosch GmbH
            bankName: Deutsche Bank
            iban: **********************
            bic: BELADEBEXXX
         boschTransferAch:
            accountHolder: Bosch Rexroth
            routingNumber: *********
            accountNumber: **********
            bic: BOFAUS3N
            bankName: Bank of America, New York
   emails:
     queueName: EmailserviceMail
     fromAddress: <EMAIL>
   platform-messaging:
     egress-order-topic: bossstore-order-events.fifo

logging:
    level:
        com.sast: DEBUG

management:
    endpoint:
        health:
            show-details: always
            probes.enabled: true
    endpoints.web.exposure.include: health, info, prometheus
    prometheus.metrics.export.enabled: true

spring:
   cache:
      type: caffeine
      cache-names: companies,companyDetails,users,user,companyManagers,subCompanies,companyInformation,listAllCountries,getCountry
      caffeine.spec: maximumSize=100000,expireAfterWrite=300s
   security.oauth2.resourceserver.jwt.issuer-uri: http://localhost:8000/auth/realms/baam
   cloud.aws.dynamodb.table-prefix: bossstore_   
 