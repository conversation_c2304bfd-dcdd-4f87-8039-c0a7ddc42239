<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="CONSOLE_LOG_PATTERN" value="${CONSOLE_LOG_PATTERN:-%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}}){faint} %X{app-tracing-id} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>

    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <include resource="org/springframework/boot/logging/logback/console-appender.xml" />

    <conversionRule conversionWord="wEx" converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter" />
    <property name="FILE_LOG_PATTERN" value="%d{'yyyy-MM-dd HH:mm:ss,SSS'} %X{app-tracing-id} %-5p [%c] \\(%t\\) %replace(%msg){'[\r\n]', ' '}%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>

    <property name="ROOT_LOG_FILE" value="target/ordermanagement.log"/>
    <appender name="root-file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <file>${ROOT_LOG_FILE}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${ROOT_LOG_FILE}.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>256MB</maxFileSize>
        </rollingPolicy>
    </appender>

    <root level="WARN">
        <appender-ref ref="root-file"/>
        <appender-ref ref="CONSOLE" />
    </root>
</configuration>
