package com.sast.store.ordermanagement.service;

import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.models.cart.Cart;
import com.commercetools.api.models.cart.CartUpdateAction;
import com.commercetools.api.models.cart.CartUpdateBuilder;
import com.commercetools.api.models.common.MoneyBuilder;
import com.commercetools.api.models.payment.Payment;
import com.commercetools.api.models.payment.PaymentDraftBuilder;
import com.commercetools.api.models.payment.PaymentMethodInfoBuilder;
import com.commercetools.api.models.payment.PaymentResourceIdentifierBuilder;
import com.commercetools.api.models.payment.TransactionDraftBuilder;
import com.commercetools.api.models.payment.TransactionState;
import com.commercetools.api.models.payment.TransactionType;
import com.sast.store.external.commercetools.util.CustomFieldProvider;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.UUID;

@Component
@Slf4j
@RequiredArgsConstructor
public class CommercetoolsPaymentFactory {
    private final SellerService sellerService;
    private final ProjectApiRoot commercetoolsClient;

    public Payment createAuthorizationPayment(@NonNull final Cart cart,
                                              @NonNull final TransactionState transactionState,
                                              @NonNull final PaymentMethodService paymentMethodService) {
        final String sellerCompanyId = sellerService.getSellerCompanyId(cart)
                .orElseThrow(() -> new IllegalStateException(
                        "Cart %s has no seller company".formatted(cart.getKey())));

        final Payment payment = commercetoolsClient.payments()
                .create(PaymentDraftBuilder.of()
                        .interfaceId(UUID.randomUUID().toString())
                        .paymentMethodInfo(PaymentMethodInfoBuilder.of()
                                .method(paymentMethodService.getPaymentMethodId())
                                .paymentInterface(paymentMethodService.getPaymentProviderName().name())
                                .build())
                        .amountPlanned(cart.getTotalPrice())
                        .custom(CustomFieldProvider.defaultPayment(sellerCompanyId))
                        .transactions(TransactionDraftBuilder.of()
                                .amount(MoneyBuilder.of()
                                        .centAmount(cart.getTotalPrice().getCentAmount())
                                        .currencyCode(cart.getTotalPrice().getCurrencyCode())
                                        .build())
                                .timestamp(ZonedDateTime.now())
                                .type(TransactionType.AUTHORIZATION)
                                .state(transactionState)
                                .build())
                        .build())
                .executeBlocking().getBody().get();
        LOG.info("created payment: {}", payment);
        // attach payment to cart
        commercetoolsClient.carts().withId(cart.getId())
                .post(CartUpdateBuilder.of()
                        .version(cart.getVersion())
                        .plusActions(CartUpdateAction.addPaymentBuilder()
                                .payment(PaymentResourceIdentifierBuilder.of()
                                        .id(payment.getId())
                                        .build())
                                .build())
                        .build())
                .executeBlocking();
        return payment;
    }
}
