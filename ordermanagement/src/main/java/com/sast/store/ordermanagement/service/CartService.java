package com.sast.store.ordermanagement.service;

import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.models.cart.Cart;
import com.commercetools.api.models.cart.CartDraft;
import com.commercetools.api.models.cart.CartRemoveLineItemAction;
import com.commercetools.api.models.cart.CartSetBillingAddressAction;
import com.commercetools.api.models.cart.CartSetCustomerGroupAction;
import com.commercetools.api.models.cart.CartUpdate;
import com.commercetools.api.models.cart.CartUpdateAction;
import com.commercetools.api.models.cart.CartUpdateBuilder;
import com.commercetools.api.models.cart.LineItem;
import com.commercetools.api.models.cart.TaxMode;
import com.commercetools.api.models.common.BaseAddress;
import com.commercetools.api.models.customer_group.CustomerGroup;
import com.commercetools.api.models.customer_group.CustomerGroupResourceIdentifierBuilder;
import com.commercetools.api.models.product.ProductVariant;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.external.commercetools.service.CustomerGroupProvider;
import com.sast.store.external.commercetools.util.CustomFieldProvider;
import com.sast.store.external.countriesservice.api.CountriesServiceClient;
import com.sast.store.external.ump.UmpClient;
import com.sast.store.gen.client.ump.api.UmpAddressDto;
import com.sast.store.gen.client.ump.api.UmpExternalCompanyDto;
import com.sast.store.gen.client.ump.api.UmpInternalCustomerGroupConfigurationDto;
import com.sast.store.ordermanagement.api.AuthorizationInformationDto;
import com.sast.store.ordermanagement.cartvalidation.CartValidationViolation;
import com.sast.store.ordermanagement.cartvalidation.CartValidator;
import com.sast.store.ordermanagement.config.AppConfiguration;
import io.vrap.rmf.base.client.error.NotFoundException;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.BadRequestException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Stream;

@Component
@RequiredArgsConstructor
@Slf4j
public class CartService {
    private final ProjectApiRoot commercetoolsClient;

    private final UmpClient umpClient;

    private final PaymentMethodRegistry paymentMethodRegistry;

    private final AppConfiguration appConfiguration;

    private final CartValidator cartValidator;

    private final CountriesServiceClient countriesServiceClient;

    private final CustomerGroupProvider customerGroupProvider;

    public Cart createCart(final Tenant tenant, final String companyId, final String userId) {
        final String umpTenant = appConfiguration.tenant(tenant).ump().tenant();
        final UmpExternalCompanyDto umpCompanyDetails = umpClient.getCompanyDetails(umpTenant, companyId);
        final String country = umpCompanyDetails.getCompanyCountry();
        final Optional<String> customerGroup = fetchCommercetoolsCustomerGroup(
            umpCompanyDetails.getCustomerGroup().getCustomizationId());

        return commercetoolsClient.carts().create(CartDraft.builder()
            .key(UUID.randomUUID().toString())
            .currency(getCurrencyOfCountry(tenant, country))
            .taxMode(TaxMode.DISABLED)
            .billingAddress(
                asBaseAddress(
                    ObjectUtils.defaultIfNull(umpCompanyDetails.getBillingAddress(), umpCompanyDetails.getBusinessAddress()), country,
                    umpCompanyDetails.getCompanyEmail()))
            .country(country)
            .customerGroup(customerGroup.map(g -> CustomerGroupResourceIdentifierBuilder.of().id(g).build()).orElse(null))
            .custom(CustomFieldProvider.defaultOrder(tenant, companyId, userId))
            .shippingAddress(asBaseAddress(umpCompanyDetails.getBusinessAddress(), country, null))
            .build()).executeBlocking().getBody();
    }

    private BaseAddress asBaseAddress(final UmpAddressDto umpAddressDto, final String countryCode, final String email) {
        if (umpAddressDto == null) {
            return null;
        }
        return BaseAddress.builder()
            .city(umpAddressDto.getCity())
            .postalCode(umpAddressDto.getPostalCode())
            .streetName(umpAddressDto.getStreet())
            .streetNumber(umpAddressDto.getHouseNumber())
            .state(umpAddressDto.getState())
            .region(umpAddressDto.getRegion())
            .email(email)
            .country(countryCode)
            .build();
    }

    private Optional<String> fetchCommercetoolsCustomerGroup(final String g) {
        try {
            return Optional.of(commercetoolsClient.customerGroups().withKey(g).get().executeBlocking().getBody().get().getId());
        } catch (final NotFoundException e) {
            LOG.info("customer group {} not found in commercetools", g);
            return Optional.empty();
        }
    }

    public Cart getCart(final String cartKey) {
        return commercetoolsClient.carts().withKey(cartKey).get()
            .executeBlocking().getBody();
    }

    public Optional<Cart> findCartByPaymentId(final String paymentId) {
        final List<Cart> carts = commercetoolsClient.carts().get()
            .withWhere("paymentInfo(payments(id=:paymentId)) and cartState=\"Active\"")
            .addPredicateVar("paymentId", paymentId)
            .executeBlocking().getBody().getResults().stream().toList();
        if (carts.size() > 1) {
            throw new IllegalStateException("More than one cart found for paymentId " + paymentId);
        }
        return carts.stream().findFirst();
    }

    public List<Cart> findCartsWithPayments() {
        return commercetoolsClient.carts().get()
            .withWhere("""
                paymentInfo(payments(id is defined)) \
                and lastModifiedAt > :start \
                and lastModifiedAt < :end \
                and cartState="Active\"""")
            .addPredicateVar("start", ZonedDateTime.now().minusDays(14).toInstant())
            .addPredicateVar("end", ZonedDateTime.now().minusMinutes(15).toInstant())
            .withLimit(100).executeBlocking().getBody()
            .getResults();
    }

    public Cart getOrCreateCart(final Tenant tenant, final String companyId, final String userId) {
        return commercetoolsClient.carts().get()
            .withWhere(
                """
                    cartState="Active" \
                    and custom(fields(tenant=:tenant)) \
                    and custom(fields(companyId=:companyId)) \
                    and custom(fields(userId=:userId))""")
            .addPredicateVar("tenant", tenant.id())
            .addPredicateVar("companyId", companyId)
            .addPredicateVar("userId", userId)
            .executeBlocking().getBody().getResults()
            .stream().findFirst()
            .orElseGet(() -> createCart(tenant, companyId, userId));
    }

    public Cart recalculate(final Cart cart) {
        final Cart recalculatedCart = commercetoolsClient.carts().withKey(cart.getKey())
            .post(CartUpdate.builder()
                .version(cart.getVersion())
                .actions(CartUpdateAction.recalculateBuilder().build())
                .build())
            .executeBlocking()
            .getBody();

        final Set<CartValidationViolation> violations = cartValidator.validate(recalculatedCart);
        if (!violations.isEmpty()) {
            commercetoolsClient.carts().withKey(cart.getKey())
                .delete()
                .executeBlocking();
            LOG.warn("Cart {} became invalid after recalculation and will be removed.", cart.getKey());
            throw new BadRequestException("Cart became invalid after recalculation");
        }
        return recalculatedCart;
    }

    public Cart addToCart(final Cart cart, final String sku, final Long quantity, final List<String> addons) {
        if (addons.isEmpty() && !addonsExist(cart, sku) && itemExists(cart, sku)) {
            // if item already exists in cart, update quantity
            final LineItem existingItem = cart.getLineItems().stream().filter(li -> li.getVariant().getSku().equals(sku)).findFirst()
                .orElseThrow();
            return updateCart(cart, existingItem.getId(), existingItem.getQuantity() + quantity);
        }
        final String parentKey = UUID.randomUUID().toString();
        final List<String> addonKeys = addons.stream().map(addon -> UUID.randomUUID().toString()).toList();
        final Iterator<String> addonKeysIterator = addonKeys.iterator();
        final CartUpdateAction[] actions = Stream.concat(Stream
            .of(CartUpdateAction.addLineItemBuilder()
                .sku(sku)
                .key(parentKey)
                .quantity(quantity)
                .custom(CustomFieldProvider.defaultLineItem(addonKeys))
                .build()),
            addons.stream().map(s -> CartUpdateAction.addLineItemBuilder()
                .sku(s)
                .key(addonKeysIterator.next())
                .quantity(1L)
                .custom(CustomFieldProvider.addonLineItem(parentKey))
                .build()))
            .toArray(CartUpdateAction[]::new);

        return updateCartWithRetry(cart, actions);
    }

    public void syncCompanyBilling(Tenant tenant, UmpExternalCompanyDto company) {
        List<Cart> carts = commercetoolsClient.carts().get()
            .withWhere("custom(fields(companyId=:cid)) and cartState=\"Active\"")
            .addPredicateVar("cid", company.getCompanyId())
            .executeBlocking()
            .getBody()
            .getResults();

        BaseAddress newAddr = asBaseAddress(
            company.getBillingAddress(),
            company.getCompanyCountry(),
            company.getCompanyEmail()
        );

        for (Cart cart : carts) {
            updateCartWithRetry(
                cart,
                CartSetBillingAddressAction.builder().address(newAddr).build(),
                CartUpdateAction.recalculateBuilder().build()
            );
        }
    }

public void syncUserGroup(final Tenant tenant, final String companyId, final UmpInternalCustomerGroupConfigurationDto groupDto) {

        final Optional<String> customizationIdOptional = Optional.ofNullable(groupDto)
                    .map(UmpInternalCustomerGroupConfigurationDto::getCustomizationId);

        final Optional<String> customerGroupIdOptional = customizationIdOptional
                .flatMap(customerGroupProvider::findByKey)
                .map(CustomerGroup::getId);

        if (customerGroupIdOptional.isEmpty()) {
            LOG.error("No commercetools customer-group found for customizationId {}",
                      customizationIdOptional.orElse("<null>"));
            return;
        }
        String customerGroupId = customerGroupIdOptional.get();

        List<Cart> carts = commercetoolsClient.carts().get()
            .withWhere("custom(fields(companyId=:cid)) and cartState=\"Active\"")
            .addPredicateVar("cid", companyId)
            .executeBlocking()
            .getBody()
            .getResults();

        for (Cart cart : carts) {
            updateCartWithRetry(
                cart,
                CartSetCustomerGroupAction.builder()
                    .customerGroup(
                        CustomerGroupResourceIdentifierBuilder.of()
                            .id(customerGroupId)
                            .build()
                    )
                    .build(),
                CartUpdateAction.recalculateBuilder().build()
            );
        }

        LOG.info("Applied customer-group {} to {} carts for company {}", 
                customerGroupId, carts.size(), companyId);
    }

    private boolean itemExists(final Cart cart, final String sku) {
        return cart.getLineItems().stream().map(LineItem::getVariant).map(ProductVariant::getSku).anyMatch(sku::equals);
    }

    private boolean itemExists(final Cart cart, final String sku, final String key) {
        return cart.getLineItems().stream().anyMatch(li -> key.equals(li.getKey()) && sku.equals(li.getVariant().getSku()));
    }

    private boolean addonsExist(final Cart cart, final String sku) {
        return cart.getLineItems().stream().anyMatch(
            li -> CustomFieldProvider.getParentLineItemKey(li).map(key -> itemExists(cart, sku, key)).orElse(false));
    }

    public Cart updateCart(final Cart cart, final String lineItemId, final Long quantity) {
        final CartUpdateAction[] cartActions = Stream
            .concat(buildUpdateAddonsAction(cart, lineItemId, quantity).stream(), Stream.of(CartUpdateAction
                .changeLineItemQuantityBuilder()
                .lineItemId(lineItemId)
                .quantity(quantity)
                .build()))
            .toArray(CartUpdateAction[]::new);
        return updateCartWithRetry(cart, cartActions);
    }

    private List<CartRemoveLineItemAction> buildUpdateAddonsAction(final Cart cart, final String lineItemId, final Long quantity) {
        if (quantity != 0) {
            return List.of();
        }
        final Optional<LineItem> lineItemWithAddons = cart.getLineItems().stream()
            .filter(li -> li.getId().equals(lineItemId))
            .filter(li -> addonsExist(cart, li.getVariant().getSku()))
            .findFirst();
        if (lineItemWithAddons.isEmpty()) {
            return List.of();
        }

        return cart.getLineItems().stream()
            .filter(addon -> CustomFieldProvider
                .getParentLineItemKey(addon).map(lineItemWithAddons.get().getKey()::equals).orElse(false))
            .map(LineItem::getId)
            .map(addonKey -> CartUpdateAction.removeLineItemBuilder()
                .lineItemId(addonKey)
                .build())
            .toList();
    }

    private Cart updateCartWithRetry(final Cart cart, final CartUpdateAction... cartAction) {
        try {
            return updateCart(cart, cartAction);
        } catch (final io.vrap.rmf.base.client.error.GatewayTimeoutException ex) {
            // in case of validator timeout we retry once, if this doesn't happen we should probably remove this code
            LOG.warn("update cart timed out, retry");
            return updateCart(cart, cartAction);
        }

    }

    private Cart updateCart(final Cart cart, final CartUpdateAction... cartAction) {
        try {
            return commercetoolsClient.carts().withKey(cart.getKey())
                .post(CartUpdate.builder()
                    .version(cart.getVersion())
                    .actions(cartAction)
                    .build())
                .executeBlocking()
                .getBody();
        } catch (final com.commercetools.api.client.error.BadRequestException ex) {
            LOG.info("update cart rejected", ex);
            throw new BadRequestException("Cart update rejected");
        }
    }

    public Cart deleteCart(final Cart cart) {
        return commercetoolsClient.carts().withKey(cart.getKey())
            .delete().withVersion(cart.getVersion())
            .executeBlocking().getBody();
    }

    private String getCurrencyOfCountry(final Tenant tenant, final String countryCode) {
        return countriesServiceClient
            .getCountry(com.sast.store.external.countriesservice.api.Tenant.valueOf(tenant.id()), countryCode)
            .getTenantConfigurations().stream().findAny().orElseThrow().getCurrency();
    }

    public AuthorizationInformationDto checkout(final Cart cart, @NotNull final String paymentMethodId) {
        final PaymentMethodService paymentMethod = paymentMethodRegistry.getService(paymentMethodId);

        if (!paymentMethod.supportsCart(cart)) {
            throw new IllegalArgumentException("Payment method not supported for cart");
        }

        return paymentMethod.authorize(cart);
    }

    public AuthorizationInformationDto checkout(final Cart cart, @NotNull final String paymentMethodId, final List<String> notes) {
        final PaymentMethodService paymentMethod = paymentMethodRegistry.getService(paymentMethodId);
        if (!paymentMethod.supportsCart(cart)) {
            throw new IllegalArgumentException("Payment method not supported for cart");
        }

        if (notes == null || notes.isEmpty()) {
            return paymentMethod.authorize(cart);
        }

        // attach notes to cart
        final CartUpdateBuilder updateActions = CartUpdateBuilder.of()
            .version(cart.getVersion())
            .plusActions(CartUpdateAction.setCustomFieldBuilder()
                .name("notes1")
                .value(notes.get(0))
                .build());
        if (notes.size() > 1) {
            updateActions.plusActions(CartUpdateAction.setCustomFieldBuilder()
                .name("notes2")
                .value(notes.get(1))
                .build());
        }
        final Cart cart2 = commercetoolsClient.carts().withId(cart.getId())
            .post(updateActions.build())
            .executeBlocking().getBody();

        return paymentMethod.authorize(cart2);
    }
}
