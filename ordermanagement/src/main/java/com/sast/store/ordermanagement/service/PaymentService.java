package com.sast.store.ordermanagement.service;

import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.models.cart.Cart;
import com.commercetools.api.models.order.Order;
import com.commercetools.api.models.payment.Payment;
import com.commercetools.api.models.payment.TransactionState;
import com.sast.store.ordermanagement.api.CheckoutInformationDto;
import com.sast.store.ordermanagement.api.PaymentDto;
import com.sast.store.ordermanagement.dto.AuthorizationResultDto;
import com.sast.store.ordermanagement.dto.AuthorizationResultDto.Status;
import jakarta.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;

import static java.util.function.BinaryOperator.maxBy;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;

@Component
public class PaymentService {
    private static final Logger LOG = LoggerFactory.getLogger(PaymentService.class);

    @Inject
    private ProjectApiRoot commercetoolsClient;
    @Inject
    private OrderService orderService;
    @Inject
    private CartService cartService;
    @Inject
    private PaymentMethodRegistry paymentMethodRegistry;
    @Inject
    private List<PaymentMethodService> paymentMethodServices;
    @Inject
    private OrderWorkflowService orderWorkflowService;

    public PaymentDto getPayment(final String paymentId) {
        final Payment payment = commercetoolsClient.payments().withId(paymentId).get().executeBlocking().getBody();
        final Optional<Order> orderAlreadyExists = orderService.findOrderByPaymentId(paymentId);
        if (orderAlreadyExists.isPresent()) {
            return PaymentDto.builder()
                .paymentId(paymentId)
                .orderId(orderAlreadyExists.get().getOrderNumber())
                .paymentStatus(payment.getTransactions().getFirst().getState().name())
                .build();
        }

        placeOrderIfPaymentSuccessful(payment);

        final Payment payment2 = commercetoolsClient.payments().withId(paymentId).get().executeBlocking().getBody();
        final Optional<Order> order = orderService.findOrderByPaymentId(paymentId);
        return order.map(value -> PaymentDto.builder()
            .paymentId(payment.getId())
            .orderId(value.getOrderNumber())
            .paymentStatus(payment2.getTransactions().getFirst().getState().name())
            .build())
            .orElseGet(() -> PaymentDto.builder()
                .paymentId(payment.getId())
                .paymentStatus(payment2.getTransactions().getFirst().getState().name())
                .build());
    }

    private void placeOrderIfPaymentSuccessful(final Payment payment) {
        final String paymentMethod = payment.getPaymentMethodInfo().getMethod();
        final PaymentMethodService paymentMethodService = paymentMethodRegistry.getService(paymentMethod);
        final AuthorizationResultDto result = paymentMethodService.getAuthorizationResult(payment);

        // convert shopping cart to order in case of successful payment
        if (result.status() == Status.SUCCESS) {
            final Cart cart = cartService.findCartByPaymentId(payment.getId())
                .orElseThrow(() -> new IllegalStateException("No cart found for payment id: " + payment.getId()));
            LOG.info("Payment {} successful, placing order for cart {}", payment.getId(), cart.getId());
            orderWorkflowService.placeOrder(cart, result);
        }
    }

    private void placeOrderIfPaymentSuccessful(final Cart cart) {
        cart.getPaymentInfo().getPayments().stream()
            .map(p -> commercetoolsClient.payments().withId(p.getId()).get().executeBlocking().getBody())
            .filter(p -> p.getTransactions() != null && !p.getTransactions().isEmpty())
            .filter(p -> p.getTransactions().getFirst().getState() != TransactionState.FAILURE)
            .max(Comparator.comparing(Payment::getCreatedAt))
            .ifPresent(this::placeOrderIfPaymentSuccessful);
    }

    public List<CheckoutInformationDto> fetchCheckoutInformation(final Cart cart) {
        return paymentMethodServices.stream()
            .filter(paymentMethodService -> paymentMethodService.supportsCart(cart))
            // if multiple psps provide the same payment method, choose it based on compareTo
            .collect(toMap(PaymentMethodService::getPaymentMethodType, identity(), maxBy(PaymentMethodService::compareTo)))
            .values().stream()
            .sorted(PaymentMethodService::compareTo)
            .map(paymentMethodService -> paymentMethodService.prepareCheckout(cart))
            .toList();
    }

    public void checkForLostCarts() {
        // find carts with payments and convert them to orders if payment was successful
        // so we don't lose orders if the client could not be redirected back to the shop after payment
        cartService.findCartsWithPayments().stream()
            .peek(cart -> LOG.info("Found cart with payment {} ", cart))
            .forEach(cart -> {
                try {
                    placeOrderIfPaymentSuccessful(cart);
                } catch (final RuntimeException e) {
                    LOG.info("Failed to place order for cart {}", cart, e);
                }
            });
    }
}
