package com.sast.store.ordermanagement.service;

import com.commercetools.api.models.cart.Cart;
import com.commercetools.api.models.payment.Payment;
import com.commercetools.api.models.payment.TransactionState;
import com.sast.store.brimtegration.apimodel.common.payment.BoschTransferAchCreditPayment;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.external.commercetools.util.CustomFieldProvider;
import com.sast.store.ordermanagement.api.AuthorizationInformationDto;
import com.sast.store.ordermanagement.api.CheckoutInformationDto;
import com.sast.store.ordermanagement.api.PaymentMethodType;
import com.sast.store.ordermanagement.api.PaymentProviderEnum;
import com.sast.store.ordermanagement.config.AppConfiguration;
import com.sast.store.ordermanagement.config.AppConfiguration.SellerCompanyConfiguration;
import com.sast.store.ordermanagement.dto.AuthorizationResultDto;
import com.sast.store.ordermanagement.payment.SupportedCartPredicates;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@RequiredArgsConstructor
@Slf4j
public class BoschTransferAchPaymentService implements PaymentMethodService {
    private final CountriesService countriesService;
    private final AppConfiguration appConfiguration;
    private final SellerService sellerService;
    private final CommercetoolsPaymentFactory commercetoolsPaymentFactory;
    private final SupportedCartPredicates supportedCartPredicates;

    @Override
    public boolean supportsCart(final Cart cart) {
        if (supportedCartPredicates.hasZeroTotalPrice(cart) && !supportedCartPredicates.containsLicenseTypes(cart, "CONSUMPTION")) {
            LOG.info("cart {} not supported by {} because total price is zero and cart contains no consumption product", cart,
                getPaymentMethodId());
            return false;
        }

        // TODO move the following predicates into `SupportedCardPredicates`
        final String country = cart.getCountry();
        final String currencyCode = cart.getTotalPrice().getCurrencyCode();
        final Tenant tenant = CustomFieldProvider.getTenant(cart).orElseThrow();
        if (!countriesService.isEnabled(tenant, country, currencyCode, this)) {
            LOG.info("cart {} not supported by {} because it is disabled in countriesservice", cart, getPaymentMethodId());
            return false;
        }
        final Optional<String> sellerCompanyId = sellerService.getSellerCompanyId(cart);
        if (sellerCompanyId.isEmpty()) {
            LOG.info("cart {} not supported by {} because sellercompany is not configured", cart, getPaymentMethodId());
            return false;
        }
        if (getSellerConfiguration(sellerCompanyId.get()).isEmpty()) {
            LOG.info("cart {} not supported by {} because payment method is not configured for seller", cart, getPaymentMethodId());
            return false;
        }
        LOG.info("cart {} supported by {}", cart.getId(), getPaymentMethodId());
        return true;
    }

    @Override
    public CheckoutInformationDto prepareCheckout(final Cart cart) {
        final String sellerCompanyId = sellerService.getSellerCompanyId(cart)
            .orElseThrow(() -> new IllegalStateException(
                "Cart %s has no seller company".formatted(cart.getKey())));
        final AppConfiguration.BoschTransferAchConfiguration achConfiguration = getSellerConfiguration(sellerCompanyId)
            .orElseThrow(() -> new IllegalStateException(
                "Seller %s has no configuration for BOSCH_TRANSFER/ACH_CREDIT".formatted(sellerCompanyId)));
        return CheckoutInformationDto.builder()
            .paymentMethodId(getPaymentMethodId())
            .boschAchCreditInformation(CheckoutInformationDto.BoschAchCreditInformation.builder()
                .accountHolder(achConfiguration.accountHolder())
                .routingNumber(achConfiguration.routingNumber())
                .accountNumber(achConfiguration.accountNumber())
                .bic(achConfiguration.bic())
                .bankName(achConfiguration.bankName())
                .build())
            .build();
    }

    @Override
    public AuthorizationInformationDto authorize(final Cart cart) {
        final Payment payment = commercetoolsPaymentFactory
            .createAuthorizationPayment(cart, TransactionState.PENDING, this);

        return AuthorizationInformationDto.builder()
            .paymentId(payment.getId())
            .build();
    }

    @Override
    public AuthorizationResultDto getAuthorizationResult(final Payment payment) {
        final String sellerCompanyId = CustomFieldProvider.getSellerCompanyId(payment)
            .orElseThrow(() -> new IllegalStateException("Cannot determine sellerCompanyId for payment %s"
                .formatted(payment.getId())));

        final AppConfiguration.BoschTransferAchConfiguration achConfiguration = getSellerConfiguration(sellerCompanyId)
            .orElseThrow(() -> new IllegalStateException(
                "Seller %s has no configuration for BOSCH_TRANSFER/ACH_CREDIT".formatted(sellerCompanyId)));

        return AuthorizationResultDto.builder()
            .brimPaymentData(BoschTransferAchCreditPayment.builder()
                .routingNumber(achConfiguration.routingNumber())
                .accountNumber(achConfiguration.accountNumber())
                .bic(achConfiguration.bic())
                .bankName(achConfiguration.bankName())
                .build())
            .status(AuthorizationResultDto.Status.SUCCESS)
            .build();
    }

    @Override
    public PaymentProviderEnum getPaymentProviderName() {
        return PaymentProviderEnum.BOSCH_TRANSFER;
    }

    @Override
    public PaymentMethodType getPaymentMethodType() {
        return PaymentMethodType.ACH_CREDIT;
    }

    private Optional<AppConfiguration.BoschTransferAchConfiguration> getSellerConfiguration(@NonNull final String companyId) {
        return appConfiguration.seller(companyId)
            .map(SellerCompanyConfiguration::boschTransferAch);
    }
}
