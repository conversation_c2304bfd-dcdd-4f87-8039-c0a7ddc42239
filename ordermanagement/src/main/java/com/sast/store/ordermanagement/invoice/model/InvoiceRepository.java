package com.sast.store.ordermanagement.invoice.model;

import com.sast.store.commons.tenant.api.Tenant;
import io.awspring.cloud.dynamodb.DynamoDbTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.stream.Stream;

@Component
@Slf4j
public class InvoiceRepository extends AbstractDynamodbDao<InvoiceEntity> {

    public InvoiceRepository(final DynamoDbTemplate dynamoDbTemplate) {
        super(dynamoDbTemplate, "", InvoiceEntity.class);
    }

    public Optional<InvoiceEntity> findByDocumentNumber(final String documentNumber) {
        return findByPartitionValue(documentNumber);
    }

    public Stream<InvoiceEntity> findByCompanyId(final Tenant tenant, final String companyId) {
        return findByIndexPartitionKey(companyId, "bossstore_invoice_entity_secondaryindex0").filter(p -> p.getTenant().equals(tenant));
    }
}
