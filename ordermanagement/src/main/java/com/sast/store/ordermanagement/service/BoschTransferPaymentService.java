package com.sast.store.ordermanagement.service;

import com.commercetools.api.models.cart.Cart;
import com.commercetools.api.models.payment.Payment;
import com.commercetools.api.models.payment.TransactionState;
import com.sast.store.brimtegration.apimodel.common.payment.BoschTransferSepaCreditPayment;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.external.commercetools.util.CustomFieldProvider;
import com.sast.store.ordermanagement.api.AuthorizationInformationDto;
import com.sast.store.ordermanagement.api.CheckoutInformationDto;
import com.sast.store.ordermanagement.api.CheckoutInformationDto.BoschSepaCreditInformation;
import com.sast.store.ordermanagement.api.PaymentMethodType;
import com.sast.store.ordermanagement.api.PaymentProviderEnum;
import com.sast.store.ordermanagement.config.AppConfiguration;
import com.sast.store.ordermanagement.config.AppConfiguration.BoschTransferConfiguration;
import com.sast.store.ordermanagement.config.AppConfiguration.SellerCompanyConfiguration;
import com.sast.store.ordermanagement.dto.AuthorizationResultDto;
import com.sast.store.ordermanagement.payment.SupportedCartPredicates;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@RequiredArgsConstructor
@Slf4j
public class BoschTransferPaymentService implements PaymentMethodService {
    private final CountriesService countriesService;
    private final AppConfiguration appConfiguration;
    private final SellerService sellerService;
    private final CommercetoolsPaymentFactory commercetoolsPaymentFactory;
    private final SupportedCartPredicates supportedCartPredicates;

    @Override
    public boolean supportsCart(final Cart cart) {
        if (supportedCartPredicates.hasZeroTotalPrice(cart) && !supportedCartPredicates.containsLicenseTypes(cart, "CONSUMPTION")) {
            LOG.info("cart {} not supported by {} because total price is zero and cart contains no consumption product", cart,
                getPaymentMethodId());
            return false;
        }

        // TODO move the following predicates into `SupportedCardPredicates`
        final String country = cart.getCountry();
        final String currencyCode = cart.getTotalPrice().getCurrencyCode();
        final Tenant tenant = CustomFieldProvider.getTenant(cart).orElseThrow();
        if (!countriesService.isEnabled(tenant, country, currencyCode, this)) {
            LOG.info("cart {} not supported by {} because it is disabled in countriesservice", cart, getPaymentMethodId());
            return false;
        }
        final Optional<String> sellerCompanyId = sellerService.getSellerCompanyId(cart);
        if (sellerCompanyId.isEmpty()) {
            LOG.info("cart {} not supported by {} because sellercompany is not configured", cart, getPaymentMethodId());
            return false;
        }
        if (getSellerConfiguration(sellerCompanyId.get()).isEmpty()) {
            LOG.info("cart {} not supported by {} because payment method is not configured for seller", cart, getPaymentMethodId());
            return false;
        }
        LOG.info("cart {} supported by {}", cart.getId(), getPaymentMethodId());
        return true;
    }

    @Override
    public CheckoutInformationDto prepareCheckout(final Cart cart) {
        final String sellerCompanyId = sellerService.getSellerCompanyId(cart)
            .orElseThrow(() -> new IllegalStateException(
                "Cart %s has no seller company".formatted(cart.getKey())));
        final BoschTransferConfiguration boschTransferConfiguration = getSellerConfiguration(sellerCompanyId)
            .orElseThrow(() -> new IllegalStateException(
                "Seller %s has no configuration for BOSCH_TRANSFER/SEPA_CREDIT".formatted(sellerCompanyId)));
        return CheckoutInformationDto.builder()
            .paymentMethodId(getPaymentMethodId())
            .boschSepaCreditInformation(BoschSepaCreditInformation.builder()
                .accountHolder(boschTransferConfiguration.accountHolder())
                .bankName(boschTransferConfiguration.bankName())
                .iban(boschTransferConfiguration.iban())
                .bic(boschTransferConfiguration.bic())
                .build())
            .build();
    }

    @Override
    public AuthorizationInformationDto authorize(final Cart cart) {
        final Payment payment = commercetoolsPaymentFactory
            .createAuthorizationPayment(cart, TransactionState.PENDING, this);

        return AuthorizationInformationDto.builder()
            .paymentId(payment.getId())
            .build();
    }

    @Override
    public AuthorizationResultDto getAuthorizationResult(final Payment payment) {
        final String sellerCompanyId = CustomFieldProvider.getSellerCompanyId(payment)
            .orElseThrow(() -> new IllegalStateException("Cannot determine sellerCompanyId for payment %s"
                .formatted(payment.getId())));

        final BoschTransferConfiguration boschTransferConfiguration = getSellerConfiguration(sellerCompanyId)
            .orElseThrow(() -> new IllegalStateException(
                "Seller %s has no configuration for BOSCH_TRANSFER/SEPA_CREDIT".formatted(sellerCompanyId)));

        return AuthorizationResultDto.builder()
            .brimPaymentData(BoschTransferSepaCreditPayment.builder()
                .iban(boschTransferConfiguration.iban())
                .bic(boschTransferConfiguration.bic())
                .bankName(boschTransferConfiguration.bankName())
                .build())
            .status(AuthorizationResultDto.Status.SUCCESS)
            .build();
    }

    @Override
    public PaymentProviderEnum getPaymentProviderName() {
        return PaymentProviderEnum.BOSCH_TRANSFER;
    }

    @Override
    public PaymentMethodType getPaymentMethodType() {
        return PaymentMethodType.SEPA_CREDIT;
    }

    private Optional<AppConfiguration.BoschTransferConfiguration> getSellerConfiguration(@NonNull final String companyId) {
        return appConfiguration.seller(companyId)
            .map(SellerCompanyConfiguration::boschTransfer);
    }
}
