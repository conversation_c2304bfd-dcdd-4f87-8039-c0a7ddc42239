package com.sast.store.ordermanagement.service.sync;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.gen.client.ump.api.UmpExternalCompanyDto;
import com.sast.store.ordermanagement.service.CartService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class CompanySyncService {

    private final CartService cartService;

    public void handleCompanyUpdate(final UmpExternalCompanyDto companyInfo,
                                    final Tenant tenant) {

        cartService.syncCompanyBilling(tenant, companyInfo);

        LOG.info("Successfully synchronized billing for UMP company {} across all carts",
                 companyInfo.getCompanyId());
    }
}
