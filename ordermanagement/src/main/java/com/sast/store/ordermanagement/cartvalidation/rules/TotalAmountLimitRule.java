package com.sast.store.ordermanagement.cartvalidation.rules;

import com.commercetools.api.models.cart.Cart;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.ordermanagement.cartvalidation.CartErrorCode;
import com.sast.store.ordermanagement.cartvalidation.CartValidationContext;
import com.sast.store.ordermanagement.cartvalidation.CartValidationRule;
import com.sast.store.ordermanagement.cartvalidation.CartValidationViolation;
import com.sast.store.ordermanagement.config.AppConfiguration;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.javamoney.moneta.Money;
import org.springframework.stereotype.Component;

import javax.money.CurrencyUnit;
import javax.money.MonetaryAmount;
import java.math.BigDecimal;
import java.util.Optional;
import java.util.Set;

@Component
@RequiredArgsConstructor
@Slf4j
public class TotalAmountLimitRule implements CartValidationRule {
    private final AppConfiguration appConfiguration;

    @Override
    public Set<CartValidationViolation> apply(@NonNull final Cart cart,
                                              @NonNull final CartValidationContext context) {
        if (cart.getTotalPrice() != null) {
            final MonetaryAmount cartAmountLimit = getCartAmountLimit(context.tenant(), cart.getTotalPrice().getCurrency());

            if (cart.getTotalPrice().isGreaterThan(cartAmountLimit)) {
                return Set.of(CartValidationViolation.builder()
                                .error(CartErrorCode.TOTAL_AMOUNT_LIMIT_EXCEEDED)
                                .message("Cart amount %s exceeds configured cart amount limit of %s"
                                        .formatted(cart.getTotalPrice(), cartAmountLimit))
                        .build());
            }
        }
        return Set.of();
    }

    private MonetaryAmount getCartAmountLimit(final Tenant tenant, final CurrencyUnit currencyUnit) {
        final BigDecimal cartAmountLimit = Optional.ofNullable(appConfiguration.tenant(tenant))
                .orElseThrow(() -> new IllegalStateException("Tenant %s is not configured" .formatted(tenant)))
                .cartLimitsByCurrency()
                .get(currencyUnit.getCurrencyCode());

        if (cartAmountLimit == null) {
            throw new IllegalStateException("Tenant %s has no configured cart amount limit for currency %s"
                    .formatted(tenant, currencyUnit.getCurrencyCode()));
        }

        return Money.of(cartAmountLimit, currencyUnit);
    }
}
