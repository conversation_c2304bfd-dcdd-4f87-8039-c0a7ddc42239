package com.sast.store.ordermanagement.cartvalidation.rules;

import com.commercetools.api.models.cart.Cart;
import com.sast.store.ordermanagement.cartvalidation.CartErrorCode;
import com.sast.store.ordermanagement.cartvalidation.CartValidationContext;
import com.sast.store.ordermanagement.cartvalidation.CartValidationRule;
import com.sast.store.ordermanagement.cartvalidation.CartValidationViolation;
import lombok.NonNull;
import org.springframework.stereotype.Component;

import java.util.Set;

@Component
public class TotalItemQuantityLimitRule implements CartValidationRule {
    // This is due to a BRIM limitation of 100 contracts per order. Don't raise this unless you know what you're doing.
    private static final long ITEM_QUANTITY_LIMIT = 100;

    @Override
    public Set<CartValidationViolation> apply(@NonNull final Cart cart,
                                              @NonNull final CartValidationContext context) {
        if (cart.getTotalLineItemQuantity() != null && cart.getTotalLineItemQuantity() > ITEM_QUANTITY_LIMIT) {
            return Set.of(CartValidationViolation.builder()
                    .error(CartErrorCode.TOTAL_QUANTITY_LIMIT_EXCEEDED)
                    .message("Total quantity limit of %s items exceeded".formatted(ITEM_QUANTITY_LIMIT))
                    .build());
        }

        return Set.of();
    }
}
