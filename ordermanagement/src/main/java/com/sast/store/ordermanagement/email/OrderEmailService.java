package com.sast.store.ordermanagement.email;

import com.commercetools.api.models.cart.LineItem;
import com.commercetools.api.models.order.Order;
import com.sast.store.brimtegration.apimodel.common.payment.BoschTransferAchCreditPayment;
import com.sast.store.brimtegration.apimodel.common.payment.BoschTransferSepaCreditPayment;
import com.sast.store.brimtegration.apimodel.common.payment.PaymentData;
import com.sast.store.brimtegration.apimodel.common.payment.PgwSepaDirectDebitPayment;
import com.sast.store.brimtegration.apimodel.common.payment.ZeroPayment;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.external.commercetools.util.CustomAttributeProvider;
import com.sast.store.external.commercetools.util.CustomFieldProvider;
import com.sast.store.external.commercetools.util.LocalizedFieldProvider;
import com.sast.store.external.email.EmailServiceClient;
import com.sast.store.external.email.data.EmailRecipient;
import com.sast.store.external.email.data.EmailTenant;
import com.sast.store.external.email.data.TemplatedEmail;
import com.sast.store.external.ump.UmpClient;
import com.sast.store.gen.client.ump.api.UmpExternalCompanyDto;
import com.sast.store.ordermanagement.api.PaymentMethodType;
import com.sast.store.ordermanagement.config.AppConfiguration;
import com.sast.store.ordermanagement.email.data.OrderItemEmailDto;
import com.sast.store.ordermanagement.email.data.PaymentEmailDto;
import com.sast.store.ordermanagement.email.data.RexrothOrderConfirmationDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Locale;

@Component
public class OrderEmailService {
    private final EmailServiceClient emailServiceClient;
    private final AppConfiguration appConfiguration;
    private final UmpClient umpClient;

    public OrderEmailService(final EmailServiceClient emailServiceClient,
                             final AppConfiguration appConfiguration,
                             final UmpClient umpClient) {
        this.emailServiceClient = emailServiceClient;
        this.appConfiguration = appConfiguration;
        this.umpClient = umpClient;
    }

    // We'll likely enjoy slight differences for this email per tenant. Better just not to create a common order confirm
    // template.
    public void sendRexrothOrderConfirmation(final Order order, final PaymentData paymentData) {
        final Tenant tenant = CustomFieldProvider.getTenant(order).orElseThrow();
        final String userId = CustomFieldProvider.getUserId(order).orElseThrow();
        final UmpExternalCompanyDto umpCompany = fetchCompanyForOrder(tenant, order);
        final Locale locale = Locale.forLanguageTag(umpCompany.getCommunicationLanguage());

        final RexrothOrderConfirmationDto rexrothOrderConfirmation = RexrothOrderConfirmationDto.builder()
                .orderNumber(order.getOrderNumber())
                .billingEmail(umpCompany.getCompanyEmail())
                .payment(getPaymentEmailDto(paymentData))
                .orderItems(order.getLineItems().stream().map(lineItem -> getOrderItemEmailDto(lineItem, locale)).toList())
                .contractManagementUrl(appConfiguration.tenant(tenant).publicUrls().contractManagementUrl())
                .build();

        emailServiceClient.sendIgnoringFailures(TemplatedEmail.builder()
                .to(EmailRecipient.forUmpUserId(userId))
                .tenant(EmailTenant.rexroth)
                .templateData(rexrothOrderConfirmation)
                .build());

        emailServiceClient.sendIgnoringFailures(TemplatedEmail.builder()
                .to(EmailRecipient.forUmpCompanyId(umpCompany.getCompanyId()))
                .tenant(EmailTenant.rexroth)
                .templateData(rexrothOrderConfirmation)
                .build());
    }

    private OrderItemEmailDto getOrderItemEmailDto(final LineItem lineItem, final Locale locale) {
        return OrderItemEmailDto.builder()
                .productName(LocalizedFieldProvider.getWithFallback(lineItem.getName(), locale).orElse(null))
                .variantName(CustomAttributeProvider.getVariantName(lineItem.getVariant(), locale).orElse(null))
                .amount(CustomAttributeProvider.moneyToBigDecimal(lineItem.getPrice().getValue()))
                .currency(lineItem.getPrice().getValue().getCurrencyCode())
                .billingCycle(CustomAttributeProvider.getRuntime(lineItem.getVariant()).orElse(null))
                .noticePeriod(CustomAttributeProvider.getNoticePeriod(lineItem.getVariant()).orElse(null))
                .contractType(CustomAttributeProvider.getLicenseType(lineItem.getVariant()).orElse(null))
                .quantity(lineItem.getQuantity())
                .build();
    }

    private PaymentEmailDto getPaymentEmailDto(final PaymentData paymentData) {
        return switch (paymentData) {
            case final PgwSepaDirectDebitPayment sepaDd -> new PaymentEmailDto(PaymentMethodType.SEPA_DIRECTDEBIT,
                    "····" + StringUtils.right(sepaDd.iban(), 4));
            case final BoschTransferSepaCreditPayment ignored ->
                    new PaymentEmailDto(PaymentMethodType.SEPA_CREDIT, null);
            case final BoschTransferAchCreditPayment ignored ->
                    new PaymentEmailDto(PaymentMethodType.ACH_CREDIT, null);
            case final ZeroPayment ignored ->
                    new PaymentEmailDto(PaymentMethodType.ZERO, null);
            default -> new PaymentEmailDto(null, null);
        };
    }

    private UmpExternalCompanyDto fetchCompanyForOrder(final Tenant tenant, final Order order) {
        final String companyId = CustomFieldProvider.getCompanyId(order)
                .orElseThrow(() -> new IllegalStateException("Order %s does not have a companyId".formatted(order.getOrderNumber())));
        final String umpTenant = appConfiguration.tenant(tenant).ump().tenant();
        return umpClient.getCompanyDetails(umpTenant, companyId);
    }
}
