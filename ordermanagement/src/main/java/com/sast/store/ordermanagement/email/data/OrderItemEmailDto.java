package com.sast.store.ordermanagement.email.data;

import java.math.BigDecimal;
import java.time.Period;

public record OrderItemEmailDto(
        String productName,
        String variantName,
        BigDecimal amount,
        String currency,
        Period billingCycle,
        Period noticePeriod,
        String contractType,
        long quantity
) {
    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {
        private String productName;
        private String variantName;
        private BigDecimal amount;
        private String currency;
        private Period billingCycle;
        private Period noticePeriod;
        private String contractType;
        private long quantity;

        private Builder() {
        }

        public static Builder anOrderItemEmailDto() {
            return new Builder();
        }

        public Builder productName(final String productName) {
            this.productName = productName;
            return this;
        }

        public Builder variantName(final String variantName) {
            this.variantName = variantName;
            return this;
        }

        public Builder amount(final BigDecimal amount) {
            this.amount = amount;
            return this;
        }

        public Builder currency(final String currency) {
            this.currency = currency;
            return this;
        }

        public Builder billingCycle(final Period billingCycle) {
            this.billingCycle = billingCycle;
            return this;
        }

        public Builder noticePeriod(final Period noticePeriod) {
            this.noticePeriod = noticePeriod;
            return this;
        }

        public Builder contractType(final String contractType) {
            this.contractType = contractType;
            return this;
        }

        public Builder quantity(final long quantity) {
            this.quantity = quantity;
            return this;
        }

        public OrderItemEmailDto build() {
            return new OrderItemEmailDto(productName, variantName, amount, currency, billingCycle, noticePeriod, contractType, quantity);
        }
    }
}
