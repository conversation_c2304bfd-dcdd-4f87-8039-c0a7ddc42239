package com.sast.store.ordermanagement.invoice.service;

import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.models.order.Order;
import com.sast.store.external.commercetools.util.CustomFieldProvider;
import com.sast.store.external.email.EmailServiceClient;
import com.sast.store.external.email.data.EmailRecipient;
import com.sast.store.external.email.data.EmailTenant;
import com.sast.store.external.email.data.S3Attachment;
import com.sast.store.external.email.data.TemplatedEmail;
import com.sast.store.ordermanagement.invoice.email.InvoiceEmailDto;
import com.sast.store.ordermanagement.invoice.model.InvoiceEntity;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class InvoiceEmailService {
    private final EmailServiceClient emailServiceClient;
    private final ProjectApiRoot commercetoolsClient;

    public void sendInvoiceNotification(@NonNull final InvoiceEntity invoiceEntity) {
        emailServiceClient.sendIgnoringFailures(TemplatedEmail.builder()
            .to(EmailRecipient.forUmpCompanyId(invoiceEntity.getCompany()))
            .tenant(EmailTenant.valueOf(invoiceEntity.getTenant().id()))
            .templateData(InvoiceEmailDto.builder()
                .orderNumber(String.join(", ", invoiceEntity.getOrderNumbers()))
                .build())
            .attachments(List.of(toAttachment(invoiceEntity)))
            .build());

        sendOrderUserMail(invoiceEntity);
    }

    private S3Attachment toAttachment(@NonNull final InvoiceEntity invoiceEntity) {
        final var s3File = invoiceEntity.getS3File();
        return S3Attachment.builder()
            .bucket(s3File.getBucket())
            .key(s3File.getKey())
            .build();
    }

    private void sendOrderUserMail(@NonNull final InvoiceEntity invoiceEntity) {
        if (invoiceEntity.getOrderNumbers().size() > 1) {
            throw new IllegalStateException("Invoice %s has more than one order"
                .formatted(invoiceEntity.getDocumentNumber()));
        }
        final String orderNumber = invoiceEntity.getOrderNumbers().stream().findFirst()
            .orElseThrow(() -> new IllegalStateException("Invoice %s has no order"
                .formatted(invoiceEntity.getDocumentNumber())));
        final String userId = getOrderUserId(orderNumber);

        emailServiceClient.sendIgnoringFailures(TemplatedEmail.builder()
            .to(EmailRecipient.forUmpUserId(userId))
            .tenant(EmailTenant.valueOf(invoiceEntity.getTenant().id()))
            .templateData(InvoiceEmailDto.builder()
                .orderNumber(orderNumber)
                .build())
            .attachments(List.of(toAttachment(invoiceEntity)))
            .build());
    }

    private String getOrderUserId(final String orderNumber) {
        final Order order = commercetoolsClient.orders()
            .withOrderNumber(orderNumber).get()
            .executeBlocking().getBody();

        return CustomFieldProvider.getUserId(order)
            .orElseThrow(() -> new IllegalStateException("Order {} has no userId"));
    }
}
