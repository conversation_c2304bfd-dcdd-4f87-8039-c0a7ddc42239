package com.sast.store.ordermanagement.rest;

import com.commercetools.api.models.cart.Cart;
import com.commercetools.api.models.cart.LineItem;
import com.commercetools.api.models.common.CentPrecisionMoney;
import com.commercetools.api.models.common.Price;
import com.commercetools.api.models.product.ProductVariant;
import com.sast.store.commons.basewebapp.keycloak.AuthenticationService;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.external.commercetools.util.CustomAttributeProvider;
import com.sast.store.external.commercetools.util.CustomFieldProvider;
import com.sast.store.external.ump.UmpClient;
import com.sast.store.gen.client.ump.api.UmpCompanyInfoDto;
import com.sast.store.ordermanagement.api.AddonLineItemDto;
import com.sast.store.ordermanagement.api.AddressDto;
import com.sast.store.ordermanagement.api.AuthorizationInformationDto;
import com.sast.store.ordermanagement.api.CartAddDto;
import com.sast.store.ordermanagement.api.CartAddDto.Addons;
import com.sast.store.ordermanagement.api.CartApi;
import com.sast.store.ordermanagement.api.CartCheckoutDto;
import com.sast.store.ordermanagement.api.CartDto;
import com.sast.store.ordermanagement.api.CartUpdateDto;
import com.sast.store.ordermanagement.api.CompanyDto;
import com.sast.store.ordermanagement.api.LineItemDto;
import com.sast.store.ordermanagement.api.LocalizedLinkDto;
import com.sast.store.ordermanagement.api.LocalizedLinkDto.LinkType;
import com.sast.store.ordermanagement.api.MoneyDto;
import com.sast.store.ordermanagement.api.ProductVariantDto;
import com.sast.store.ordermanagement.service.CartService;
import com.sast.store.ordermanagement.service.ProductTypeService;
import com.sast.store.ordermanagement.service.SellerService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Locale;
import java.util.Optional;

@Component
@RequiredArgsConstructor
public class CartRestService implements CartApi {
    private static final Logger LOG = LoggerFactory.getLogger(CartRestService.class);

    private final CartService cartService;

    private final AuthenticationService authenticationService;

    private final SellerService sellerService;

    private final UmpClient umpClient;

    private final ProductTypeService productTypeService;

    @Override
    @PreAuthorize("hasRole('DEFAULT')")
    public CartDto getCurrentCart(@NotNull final Tenant tenant, final String languageCode, final String countryCode) {
        final String userId = authenticationService.getUserId();
        final String companyId = authenticationService.getCompanyId();
        final String language = authenticationService.getCommunicationLanguage().orElse(languageCode);

        final Cart cart = cartService.getOrCreateCart(tenant, companyId, userId);
        return buildCartDto(tenant, cart, language);
    }

    private CartDto buildCartDto(final Tenant tenant, final Cart cart, final String language) {
        return CartDto.builder()
            .id(cart.getKey())
            .lineItems(cart.getLineItems().stream()
                .filter(li -> productTypeService.isDefaultProduct(li.getProductType()))
                .map(li -> LineItemDto.builder()
                    .lineItemId(li.getId())
                    .productId(li.getProductId())
                    .sku(li.getVariant().getSku())
                    .variant(buildVariant(li.getVariant(), language))
                    .name(li.getName().get(language))
                    .itemPrice(buildPrice(li.getPrice()))
                    .quantity(li.getQuantity())
                    .totalPrice(buildPrice(li.getTotalPrice()))
                    .build())
                .toList())
            .totalPrice(MoneyDto.builder()
                .value(cart.getTotalPrice().getNumber().doubleValue())
                .currencyCode(cart.getTotalPrice().getCurrencyCode())
                .build())
            .sellerCompany(buildSellerCompany(tenant, cart))
            .billingAddress(Optional.ofNullable(cart.getBillingAddress())
                .map(billingAddress -> AddressDto.builder()
                    .city(billingAddress.getCity())
                    .postalCode(billingAddress.getPostalCode())
                    .streetName(billingAddress.getStreetName())
                    .streetNumber(billingAddress.getStreetNumber())
                    .state(billingAddress.getState())
                    .region(billingAddress.getRegion())
                    .country(billingAddress.getCountry())
                    .email(billingAddress.getEmail())
                    .build())
                .orElse(null))
            .addons(cart.getLineItems().stream()
                .filter(li -> productTypeService.isAddon(li.getProductType()))
                .map(li -> AddonLineItemDto.builder()
                    .addonLineItemId(li.getId())
                    .name(li.getName().get(language))
                    .parentLineItemId(getParentLineItemId(cart, li))
                    .addonVariant(AddonLineItemDto.AddonVariant.builder()
                        .sku(li.getVariant().getSku())
                        .name(CustomAttributeProvider.getVariantName(li.getVariant(), Locale.forLanguageTag(language)).orElse(null))
                        .build())
                    .itemPrice(buildPrice(li.getPrice()))
                    .totalPrice(buildPrice(li.getTotalPrice()))
                    .build())
                .toList())
            .build();
    }

    private MoneyDto buildPrice(final @NotNull @Valid Price price) {
        return MoneyDto.builder()
            .value(price.getValue().getNumber().doubleValue())
            .currencyCode(price.getValue().getCurrency().getCurrencyCode())
            .build();
    }

    private MoneyDto buildPrice(@NotNull @Valid final CentPrecisionMoney centPrecisionMoney) {
        return MoneyDto.builder()
            .value(centPrecisionMoney.getNumber().doubleValue())
            .currencyCode(centPrecisionMoney.getCurrencyCode())
            .build();
    }

    private String getParentLineItemId(final Cart cart, final LineItem li) {
        return CustomFieldProvider.getParentLineItemKey(li)
            .flatMap(key -> cart.getLineItems().stream().filter(i -> key.equals(i.getKey())).findFirst())
            .map(LineItem::getId)
            .orElse(null);
    }

    @Override
    @PreAuthorize("hasRole('DEFAULT')")
    public CartDto addToCurrentCart(@NotNull final Tenant tenantId, @Valid final CartAddDto cartUpdateDto) {
        final String userId = authenticationService.getUserId();
        final String companyId = authenticationService.getCompanyId();
        final String communicationLanguage = authenticationService.getCommunicationLanguage().orElse("DE");

        final Cart cart = cartService.getOrCreateCart(tenantId, companyId, userId);
        final List<String> list = cartUpdateDto.addons() == null ? List.of() : cartUpdateDto.addons().stream().map(Addons::sku).toList();
        final Cart updatedCart = cartService.addToCart(cart, cartUpdateDto.sku(), cartUpdateDto.quantity(), list);
        return buildCartDto(tenantId, updatedCart, communicationLanguage);
    }

    @Override
    @PreAuthorize("hasRole('DEFAULT')")
    public CartDto updateCurrentCart(@NotNull final Tenant tenantId, @Valid final CartUpdateDto cartUpdateDto) {
        final String userId = authenticationService.getUserId();
        final String companyId = authenticationService.getCompanyId();
        final String communicationLanguage = authenticationService.getCommunicationLanguage().orElse("DE");

        final Cart cart = cartService.getOrCreateCart(tenantId, companyId, userId);
        final Cart updatedCart = cartService.updateCart(cart, cartUpdateDto.lineItemId(), cartUpdateDto.quantity());
        return buildCartDto(tenantId, updatedCart, communicationLanguage);
    }

    @Override
    @PreAuthorize("hasRole('DEFAULT')")
    public void clearCurrentCart(@NotNull final Tenant tenantId, final String countryCode) {
        final String userId = authenticationService.getUserId();
        final String companyId = authenticationService.getCompanyId();

        final Cart cart = cartService.getOrCreateCart(tenantId, companyId, userId);
        cartService.deleteCart(cart);
    }

    @Override
    @PreAuthorize("hasRole('DEFAULT')")
    public AuthorizationInformationDto checkoutCurrentCart(@NotNull final Tenant tenantId, final String countryCode,
        @NotNull final String paymentMethodId) {
        final String userId = authenticationService.getUserId();
        final String companyId = authenticationService.getCompanyId();

        final Cart cart = cartService.getOrCreateCart(tenantId, companyId, userId);
        return cartService.checkout(cart, paymentMethodId);
    }

    @Override
    @PreAuthorize("hasRole('DEFAULT')")
    public AuthorizationInformationDto checkoutCart(@NotNull final Tenant tenantId, @Valid final CartCheckoutDto cartCheckoutDto) {
        final String userId = authenticationService.getUserId();
        final String companyId = authenticationService.getCompanyId();
        final String paymentMethodId = cartCheckoutDto.paymentMethodId();
        final List<String> notes = cartCheckoutDto.notes();

        final Cart cart = cartService.getOrCreateCart(tenantId, companyId, userId);
        return cartService.checkout(cart, paymentMethodId, notes);
    }

    private ProductVariantDto buildVariant(final ProductVariant variant, final String language) {
        final Locale locale = Locale.forLanguageTag(language);
        return ProductVariantDto.builder()
            .sku(variant.getSku())
            .name(CustomAttributeProvider.getVariantName(variant, locale).orElse(null))
            .licenseType(CustomAttributeProvider.getLicenseType(variant).orElse(null))
            .runtime(CustomAttributeProvider.getRuntime(variant).orElse(null))
            .agreements(buildAgreements(variant, locale))
            .priceList(CustomAttributeProvider.getPriceList(variant, locale))
            .addons(CustomAttributeProvider.getAddonsAsReference(variant).stream()
                .map(reference -> ProductVariantDto.Addon.builder()
                    .id(reference.getId())
                    .build())
                .toList())
            .build();
    }

    private List<LocalizedLinkDto> buildAgreements(final ProductVariant variant, final Locale locale) {
        return CustomAttributeProvider.getAgreements(variant).stream()
            .map(attribute -> LocalizedLinkDto.builder()
                .name(CustomAttributeProvider.getAgreementName(attribute, locale))
                .url(CustomAttributeProvider.getAgreementLink(attribute, locale))
                .linkType(LinkType.valueOf(CustomAttributeProvider.getAgreementCategory(attribute)
                    .orElse(LinkType.SOFTWARE_LICENSE.name())))
                .build())
            .toList();
    }

    private CompanyDto buildSellerCompany(final Tenant tenant, final Cart cart) {
        return sellerService.getSellerCompanyId(cart)
            .map(companyId -> getCompanyInformationOrNull(tenant, companyId))
            .map(UmpCompanyInfoDto::getCompanyName)
            .map(name -> CompanyDto.builder().name(name).build())
            .orElse(null);
    }

    private UmpCompanyInfoDto getCompanyInformationOrNull(final Tenant tenant, final String companyId) {
        try {
            return umpClient.getCompanyInformation(tenant.id(), companyId);
        } catch (final Exception e) {
            LOG.warn("Cannot get company for tenant={}, companyId={}: {}", tenant, companyId, e.getMessage(), e);
            return null;
        }
    }
}
