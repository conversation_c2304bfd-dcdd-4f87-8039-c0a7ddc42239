package com.sast.store.ordermanagement.company;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.external.ump.messaging.CompanyUpdateNotification;
import com.sast.store.external.ump.messaging.UmpNotification;
import com.sast.store.gen.client.ump.api.UmpExternalCompanyDto;
import com.sast.store.ordermanagement.service.sync.CompanySyncService;
import com.sast.store.ordermanagement.service.sync.UserGroupSyncService;
import io.awspring.cloud.sqs.annotation.SqsListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.EnumSet;
import java.util.Set;

@Component
@Slf4j
@RequiredArgsConstructor
public class UmpNotificationConsumer {
    public static final Set<Tenant> IGNORED_TENANTS = EnumSet.of(Tenant.BAAM, Tenant.AZENA);

    private final CompanySyncService companySyncService;
    private final UserGroupSyncService userGroupSyncService;

    @SqsListener("bossstore-ordermanagement-account-events")
    public void consume(final UmpNotification umpNotification) {
        LOG.info("Received UMP notification: {}", umpNotification);
        if (IGNORED_TENANTS.contains(Tenant.fromString(umpNotification.tenant()))) {
            LOG.info("Ignoring UMP notification for tenant={}", umpNotification.tenant());
            return;
        }

        if (umpNotification instanceof final CompanyUpdateNotification companyUpdateNotification) {
            LOG.info("company update notification for company id={}", companyUpdateNotification.companyId());

            final UmpExternalCompanyDto umpExternalCompanyDto = companyUpdateNotification.asExternalCompanyDto();
            final Tenant tenant = Tenant.fromString(companyUpdateNotification.tenant());

            LOG.debug("External company payload: {}", umpExternalCompanyDto);

            companySyncService.handleCompanyUpdate(umpExternalCompanyDto, tenant);

            userGroupSyncService.handleUserGroupChange(
                companyUpdateNotification.companyId(),
                companyUpdateNotification.customerGroup(),
                tenant
            );

            LOG.info("Successfully synchronized billing and customer-group for UMP company {} across all carts",
                     companyUpdateNotification.companyId());
        } else {
            LOG.info("Ignoring unhandled UMP notification: {}", umpNotification);
        }
    }
}
