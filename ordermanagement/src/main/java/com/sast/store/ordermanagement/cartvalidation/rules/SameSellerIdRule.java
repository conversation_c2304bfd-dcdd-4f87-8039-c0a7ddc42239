package com.sast.store.ordermanagement.cartvalidation.rules;

import com.commercetools.api.models.cart.Cart;
import com.commercetools.api.models.cart.LineItem;
import com.sast.store.external.commercetools.util.CustomAttributeProvider;
import com.sast.store.ordermanagement.cartvalidation.CartErrorCode;
import com.sast.store.ordermanagement.cartvalidation.CartValidationContext;
import com.sast.store.ordermanagement.cartvalidation.CartValidationRule;
import com.sast.store.ordermanagement.cartvalidation.CartValidationViolation;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Set;

import static java.util.stream.Collectors.toSet;

@Component
@Slf4j
public class SameSellerIdRule implements CartValidationRule {

    @Override
    public Set<CartValidationViolation> apply(@NonNull final Cart cart, @NonNull final CartValidationContext context) {
        final var sellerIds = cart.getLineItems().stream()
            .map(LineItem::getVariant)
            .map(CustomAttributeProvider::getSellerId)
            .collect(toSet());

        if (sellerIds.size() > 1) {
            LOG.info("Multiple seller ids {} found in cart {}", sellerIds, cart);
            return Set.of(CartValidationViolation.builder()
                .error(CartErrorCode.MULTIPLE_SELLER_IDS)
                .message("Cart can only contain items from the same seller id")
                .build());
        }

        return Set.of();
    }
}
