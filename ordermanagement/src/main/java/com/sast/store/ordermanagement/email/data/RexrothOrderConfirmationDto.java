package com.sast.store.ordermanagement.email.data;

import com.sast.store.external.email.data.EmailTemplateData;
import com.sast.store.external.email.data.EmailTenant;

import java.net.URI;
import java.util.List;

public record RexrothOrderConfirmationDto(
    String orderNumber,
    String billingEmail,
    PaymentEmailDto payment,
    List<OrderItemEmailDto> orderItems,
    URI contractManagementUrl
) implements EmailTemplateData {
    @Override
    public String getTemplateName(final EmailTenant emailTenant) {
        return "rexroth/store/orderConfirmation";
    }

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {
        private String orderNumber;
        private String billingEmail;
        private PaymentEmailDto payment;
        private List<OrderItemEmailDto> orderItems;
        private URI contractManagementUrl;

        private Builder() {
        }

        public Builder orderNumber(final String orderNumber) {
            this.orderNumber = orderNumber;
            return this;
        }

        public Builder billingEmail(final String billingEmail) {
            this.billingEmail = billingEmail;
            return this;
        }

        public Builder payment(final PaymentEmailDto payment) {
            this.payment = payment;
            return this;
        }

        public Builder orderItems(final List<OrderItemEmailDto> orderItems) {
            this.orderItems = orderItems;
            return this;
        }

        public Builder contractManagementUrl(final URI contractManagementUrl) {
            this.contractManagementUrl = contractManagementUrl;
            return this;
        }

        public RexrothOrderConfirmationDto build() {
            return new RexrothOrderConfirmationDto(orderNumber, billingEmail, payment, orderItems, contractManagementUrl);
        }
    }
}
