package com.sast.store.ordermanagement.rest;

import com.commercetools.api.models.cart.Cart;
import com.commercetools.api.models.cart.CartReference;
import com.commercetools.api.models.extension.ExtensionInput;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sast.store.ordermanagement.api.CommercetoolsExtensionApi;
import com.sast.store.ordermanagement.cartvalidation.CartValidationViolation;
import com.sast.store.ordermanagement.cartvalidation.CartValidator;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.ws.rs.BadRequestException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;

import java.util.Set;

@Component
public class CommercetoolsExtensionRestService implements CommercetoolsExtensionApi {
    private static final Logger LOG = LoggerFactory.getLogger(CommercetoolsExtensionRestService.class);

    @Inject
    @Named("commercetoolsObjectMapper")
    private ObjectMapper commercetoolsObjectMapper;

    @Inject
    private CartValidator cartValidator;

    @Override
    @PreAuthorize("hasRole('DEFAULT')")
    public void validateCart(final String payload) {
        final ExtensionInput extensionAction = parseExtensionInput(payload);

        if (!(extensionAction.getResource() instanceof final CartReference cartResource)) {
            LOG.warn("Unsupported resource type: {}", extensionAction.getResource());
            throw new BadRequestException();
        }

        final Cart cart = cartResource.getObj();
        final Set<CartValidationViolation> violations = cartValidator.validate(cart);
        if (!violations.isEmpty()) {
            throw new BadRequestException(violations.stream().map(CartValidationViolation::error).toList().toString());
        }

    }

    /**
     * payload conversion for commercetools extension input
     * 
     * we don't want to leak the whole commercetools api into the ordermanagement api and also we need the commerce
     * tools object mapper to parse the payload
     */
    private ExtensionInput parseExtensionInput(final String cart) {
        try {
            return commercetoolsObjectMapper.readValue(cart, ExtensionInput.class);
        } catch (final JsonProcessingException e) {
            LOG.warn("Cannot parse cart validation payload: {}", cart, e);
            throw new BadRequestException();
        }
    }
}
