package com.sast.store.ordermanagement.cartvalidation;

import com.commercetools.api.models.cart.Cart;
import lombok.NonNull;

import java.util.Set;

/**
 * cart validation rules - implementations are concrete rules to be enforced on any cart
 */
public interface CartValidationRule {
    /**
     * Validates the given cart according to a specific validation rule.
     * Implementers must return violations if the given cart is to be considered invalid, otherwise an empty
     * set must be returned. Exceptions are only to be thrown if the validation cannot be performed.
     * <br/><br/>
     * Cart validation is performance-critical, therefore calls to third party systems must be avoided,
     * and calls to other platform components must be minimized. In order to avoid a predictable explosion
     * of calls to UMP or other components and potential inconsistencies due to cache invalidation, the results
     * of such calls should be taken from the provided validation context.
     *
     * @param cart cart to be validated
     * @param context validation context
     * @return A set of violations if cart is invalid, otherwise empty set.
     */
    Set<CartValidationViolation> apply(@NonNull Cart cart, @NonNull CartValidationContext context);
}
