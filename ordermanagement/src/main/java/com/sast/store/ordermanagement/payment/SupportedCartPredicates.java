package com.sast.store.ordermanagement.payment;

import com.commercetools.api.models.cart.Cart;
import com.commercetools.api.models.cart.LineItem;
import com.sast.store.external.commercetools.util.CustomAttributeProvider;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
public class SupportedCartPredicates {
    public boolean containsLicenseTypes(final Cart cart, final String... licenseTypes) {
        return cart.getLineItems().stream()
            .map(LineItem::getVariant)
            .map(CustomAttributeProvider::getLicenseType)
            .flatMap(Optional::stream)
            .anyMatch(licenseType -> List.of(licenseTypes).contains(licenseType));
    }

    public boolean hasZeroTotalPrice(final Cart cart) {
        return cart.getTotalPrice().isZero();
    }
}
