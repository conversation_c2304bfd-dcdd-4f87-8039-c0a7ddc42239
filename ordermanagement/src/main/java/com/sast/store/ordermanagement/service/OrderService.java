package com.sast.store.ordermanagement.service;

import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.models.cart.Cart;
import com.commercetools.api.models.cart.CartResourceIdentifier;
import com.commercetools.api.models.order.Order;
import com.commercetools.api.models.order.OrderFromCartDraft;
import com.commercetools.api.models.order.OrderState;
import com.commercetools.api.models.order.OrderUpdate;
import com.commercetools.api.models.order.OrderUpdateAction;
import com.commercetools.api.models.order.PaymentState;
import com.sast.store.commons.basewebapp.keycloak.AuthenticationService;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.external.commercetools.util.CustomFieldProvider;
import com.sast.store.ordermanagement.config.AppConfiguration;
import io.vrap.rmf.base.client.error.NotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.RandomStringGenerator;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
@RequiredArgsConstructor
@Slf4j
public class OrderService {
    private final ProjectApiRoot commercetoolsClient;
    private final AuthenticationService authenticationService;
    private final AppConfiguration appConfiguration;

    // avoid leading zeros
    private final RandomStringGenerator firstDigitOrderNumberGenerator = new RandomStringGenerator.Builder()
        .withinRange('1', '9')
        .get();
    private final RandomStringGenerator orderNumberGenerator = new RandomStringGenerator.Builder()
        .withinRange('0', '9')
        .get();

    public Optional<Order> findOrder(final String orderId) {
        try {
            return Optional.of(commercetoolsClient.orders()
                .withId(orderId).get()
                .executeBlocking().getBody());
        } catch (final NotFoundException ex) {
            return Optional.empty();
        }
    }

    public Optional<Order> findOrderByOrderNumber(final String orderNumber) {
        try {
            return Optional.of(commercetoolsClient.orders()
                .withOrderNumber(orderNumber).get()
                .executeBlocking().getBody());
        } catch (final NotFoundException ex) {
            return Optional.empty();
        }
    }

    public List<Order> getOrders() {
        final String companyId = authenticationService.getCompanyId();
        return commercetoolsClient.orders().get()
            .withWhere("custom(fields(companyId=:companyId))")
            .addPredicateVar("companyId", companyId)
            .addSort("createdAt desc")
            .addLimit(30)
            .executeBlocking().getBody().getResults();
    }

    public void deleteOrder(final Order order) {
        commercetoolsClient.orders().withId(order.getId()).delete()
            .withVersion(order.getVersion())
            .executeBlocking();
    }

    public Optional<Order> findOrderByPaymentId(final String paymentId) {
        final List<Order> orders = commercetoolsClient.orders().get()
            .withWhere("paymentInfo(payments(id=:paymentId))")
            .addPredicateVar("paymentId", paymentId)
            .executeBlocking().getBody().getResults().stream().toList();
        if (orders.size() > 1) {
            throw new IllegalStateException("More than one order found for paymentId " + paymentId);
        }
        return orders.stream().findFirst();
    }

    public Order updateOrderState(final Order order, final OrderState orderState) {
        final Order updatedOrder = commercetoolsClient.orders().withId(order.get().getId())
            .post(OrderUpdate.builder()
                .version(order.get().getVersion())
                .plusActions(OrderUpdateAction.changeOrderStateBuilder().orderState(orderState).build())
                .build())
            .executeBlocking().getBody();
        LOG.info("Order state updated to {} for order {}", orderState, updatedOrder);
        return updatedOrder;
    }

    public Order createOrder(final Cart cart) {
        final Tenant tenant = CustomFieldProvider.getTenant(cart)
                .orElseThrow(() -> new IllegalStateException("Cart %s has no tenant".formatted(cart.getKey())));
        return commercetoolsClient.orders().create(OrderFromCartDraft.builder()
            .version(cart.get().getVersion())
            .cart(CartResourceIdentifier.builder()
                .id(cart.get().getId())
                .build())
            .orderNumber(generateOrderNumber(tenant))
            .paymentState(PaymentState.PENDING)
            .build())
            .executeBlocking().getBody();
    }
    private String generateOrderNumber(final Tenant tenant) {
        final String environmentPrefix = appConfiguration.environmentPrefix();

        final String tenantPrefix = switch (tenant) {
            case Tenant.REXROTH -> "RX";
            case Tenant.BAAM -> "AA";
            default -> throw new IllegalStateException("Unexpected tenant value: " + tenant);
        };

        return "%s%s_OR_%s".formatted(
                environmentPrefix, tenantPrefix,
                firstDigitOrderNumberGenerator.generate(1) + orderNumberGenerator.generate(14)
        );
    }
}
