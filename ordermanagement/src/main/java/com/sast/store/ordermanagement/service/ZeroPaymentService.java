package com.sast.store.ordermanagement.service;

import com.commercetools.api.models.cart.Cart;
import com.commercetools.api.models.payment.Payment;
import com.commercetools.api.models.payment.TransactionState;
import com.sast.store.brimtegration.apimodel.common.payment.ZeroPayment;
import com.sast.store.ordermanagement.api.AuthorizationInformationDto;
import com.sast.store.ordermanagement.api.CheckoutInformationDto;
import com.sast.store.ordermanagement.api.PaymentMethodType;
import com.sast.store.ordermanagement.api.PaymentProviderEnum;
import com.sast.store.ordermanagement.dto.AuthorizationResultDto;
import com.sast.store.ordermanagement.payment.SupportedCartPredicates;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class ZeroPaymentService implements PaymentMethodService {
    private final CommercetoolsPaymentFactory commercetoolsPaymentFactory;
    private final SupportedCartPredicates supportedCartPredicates;

    @Override
    public boolean supportsCart(final Cart cart) {
        if (!supportedCartPredicates.hasZeroTotalPrice(cart)) {
            LOG.info("cart {} not supported by {} because total price is not zero", cart, getPaymentMethodId());
            return false;
        }

        if (supportedCartPredicates.containsLicenseTypes(cart, "CONSUMPTION")) {
            LOG.info("cart {} not supported by {} because it contains a consumption product", cart, getPaymentMethodId());
            return false;
        }

        LOG.info("cart {} supported by {}", cart.getId(), getPaymentMethodId());
        return true;
    }

    @Override
    public CheckoutInformationDto prepareCheckout(final Cart cart) {
        return CheckoutInformationDto.builder()
            .paymentMethodId(getPaymentMethodId())
            .build();
    }

    @Override
    public AuthorizationInformationDto authorize(final Cart cart) {
        final var payment = commercetoolsPaymentFactory
                .createAuthorizationPayment(cart, TransactionState.SUCCESS, this);

        return AuthorizationInformationDto.builder()
            .paymentId(payment.getId())
            .build();
    }

    @Override
    public AuthorizationResultDto getAuthorizationResult(final Payment payment) {
        return AuthorizationResultDto.builder()
            .status(AuthorizationResultDto.Status.SUCCESS)
            .brimPaymentData(ZeroPayment.builder().build())
            .build();
    }

    @Override
    public PaymentProviderEnum getPaymentProviderName() {
        return PaymentProviderEnum.ZERO;
    }

    @Override
    public PaymentMethodType getPaymentMethodType() {
        return PaymentMethodType.ZERO;
    }
}
