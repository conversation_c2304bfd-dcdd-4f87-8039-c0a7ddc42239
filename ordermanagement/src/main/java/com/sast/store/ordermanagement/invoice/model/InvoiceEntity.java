package com.sast.store.ordermanagement.invoice.model;

import com.sast.store.commons.tenant.api.Tenant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import software.amazon.awssdk.enhanced.dynamodb.extensions.annotations.DynamoDbVersionAttribute;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@DynamoDbBean
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = false, fluent = false)
public class InvoiceEntity {
    @Getter(onMethod_ = @DynamoDbPartitionKey)
    private String documentNumber;
    private Tenant tenant;
    @Getter(onMethod_ = @DynamoDbSecondaryPartitionKey(indexNames = "bossstore_invoice_entity_secondaryindex0"))
    private String company;
    @Builder.Default
    private List<String> orderNumbers = new ArrayList<>();
    @Builder.Default
    private List<LineItem> lineItems = new ArrayList<>();
    private InvoiceTotalAmounts totalAmounts;
    @Getter(onMethod_ = @DynamoDbSecondarySortKey(indexNames = "bossstore_invoice_entity_secondaryindex0"))
    private LocalDate documentDate;
    private LocalDate creationDate;
    @Builder.Default
    private List<CreditNote> creditNotes = new ArrayList<>();
    private S3File s3File;

    @Getter(onMethod_ = @DynamoDbVersionAttribute)
    private Long version;

    public void addCreditNote(final CreditNote creditNote) {
        creditNotes.add(creditNote);
    }

    public enum CreditNoteType {
        CREDIT_NOTE, REVERSAL
    }

    @DynamoDbBean
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LineItem {
        private Integer position;
        private String productId;
        private BigDecimal quantity;
        private BigDecimal netAmount;
    }

    @DynamoDbBean
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InvoiceTotalAmounts {
        private String currency;
        private BigDecimal netAmount;
        private BigDecimal grossAmount;
        private BigDecimal taxAmount;
    }

    @DynamoDbBean
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class S3File {
        private String bucket;
        private String key;
        private String sha256sum;
        private String contentType;
    }

    @DynamoDbBean
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreditNote {
        private String documentNumber;
        private CreditNoteType creditNoteType;
        @Builder.Default
        private List<LineItem> lineItems = new ArrayList<>();
        private InvoiceTotalAmounts totalAmounts;
        private LocalDate documentDate;
        private LocalDate creationDate;
        private S3File s3File;
    }
}
