package com.sast.store.ordermanagement.invoice.rest;

import com.sast.store.commons.basewebapp.keycloak.AuthenticationService;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.ordermanagement.api.InvoiceApi;
import com.sast.store.ordermanagement.api.InvoiceDto;
import com.sast.store.ordermanagement.api.InvoiceStatus;
import com.sast.store.ordermanagement.api.MoneyDto;
import com.sast.store.ordermanagement.invoice.service.InvoiceService;
import jakarta.inject.Inject;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.StreamingOutput;
import org.apache.commons.codec.binary.Base64OutputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class InvoiceRestService implements InvoiceApi {
    private static final Logger LOG = LoggerFactory.getLogger(InvoiceRestService.class);

    @Inject
    private InvoiceService invoiceService;

    @Inject
    private AuthenticationService authenticationService;

    @Override
    @PreAuthorize("hasRole('DEFAULT')")
    public List<InvoiceDto> listAllInvoices(@NotNull final Tenant tenant) {
        final String companyId = authenticationService.getCompanyId();
        return invoiceService.getInvoices(tenant, companyId)
            .map(i -> InvoiceDto.builder()
                .invoiceNumber(i.getDocumentNumber())
                .invoiceDate(i.getCreationDate())
                .orderIds(List.copyOf(i.getOrderNumbers()))
                .status(InvoiceStatus.ISSUED)
                .totalAmount(MoneyDto.builder()
                    .value(i.getTotalAmounts().getGrossAmount().doubleValue())
                    .currencyCode(i.getTotalAmounts().getCurrency())
                    .build())
                .build())
            .toList();
    }

    @Override
    @PreAuthorize("hasRole('DEFAULT')")
    public Response download(@NotNull final Tenant tenantId, @NotNull final String documentNumber) {
        LOG.info("download document {} for tenant {}", documentNumber, tenantId);
        final String companyId = authenticationService.getCompanyId();
        return Response.ok((StreamingOutput) out -> {
            invoiceService.getInvoiceDocument(tenantId, companyId, documentNumber, out);
        }).build();
    }

    @Override
    @PreAuthorize("hasRole('DEFAULT')")
    public Response download64(@NotNull final Tenant tenantId, @NotNull final String documentNumber) {
        LOG.info("download document {} for tenant {}", documentNumber, tenantId);
        final String companyId = authenticationService.getCompanyId();
        return Response.ok((StreamingOutput) out -> {
            try (var base64 = new Base64OutputStream(out)) {
                invoiceService.getInvoiceDocument(tenantId, companyId, documentNumber, base64);
            }
        }).build();
    }
}
