package com.sast.store.ordermanagement.cartvalidation;

import lombok.Getter;

@Getter
public enum CartErrorCode {
    TOTAL_AMOUNT_LIMIT_EXCEEDED("totalAmountLimitExceeded"),
    TOTAL_QUANTITY_LIMIT_EXCEEDED("totalQuantityLimitExceeded"),
    ADDON_PARENT_NOT_FOUND("addonParentNotFound"),
    MULTIPLE_SELLER_IDS("multipleSellerIds");

    private final String apiErrorCode;

    CartErrorCode(final String apiErrorCode) {
        this.apiErrorCode = apiErrorCode;
    }
}
