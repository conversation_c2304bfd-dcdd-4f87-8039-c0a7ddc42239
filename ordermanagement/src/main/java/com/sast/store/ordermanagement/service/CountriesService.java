package com.sast.store.ordermanagement.service;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.external.countriesservice.api.CountriesServiceClient;
import com.sast.store.external.countriesservice.api.CountryDto;
import com.sast.store.ordermanagement.api.PaymentMethodType;
import jakarta.inject.Inject;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class CountriesService {
    private static final Map<PaymentMethodType, String> COUNTRIES_SERVICE_PAYMENT_METHODS = Map.of(
        PaymentMethodType.SEPA_CREDIT, "SEPA",
        PaymentMethodType.SEPA_DIRECTDEBIT, "SEPA_DIRECTDEBIT",
        PaymentMethodType.ACH_CREDIT, "ACH",
        PaymentMethodType.CREDIT_CARD, "CREDIT_CARD",
        PaymentMethodType.INVOICE_BY_SELLER, "INVOICE_BY_SELLER");

    @Inject
    private CountriesServiceClient countriesService;

    public CountryDto getCountry(final Tenant tenant, final String country) {
        return countriesService.getCountry(com.sast.store.external.countriesservice.api.Tenant.valueOf(tenant.id()), country);
    }

    public boolean isEnabled(final Tenant tenant, final String country, final String currencyCode,
        final PaymentMethodService paymentMethod) {
        final CountryDto countryDto = getCountry(tenant, country);
        return countryDto.getPaymentProviders().contains(paymentMethod.getPaymentProviderName().name())
            && countryDto.getPaymentMethods().contains(paymentMethodType(paymentMethod.getPaymentMethodType()));
    }

    private String paymentMethodType(final PaymentMethodType paymentMethodType) {
        return COUNTRIES_SERVICE_PAYMENT_METHODS.get(paymentMethodType);
    }
}
