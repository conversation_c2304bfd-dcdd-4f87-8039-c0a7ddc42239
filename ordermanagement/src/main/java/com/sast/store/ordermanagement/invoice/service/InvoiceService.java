package com.sast.store.ordermanagement.invoice.service;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.ordermanagement.invoice.model.InvoiceEntity;
import com.sast.store.ordermanagement.invoice.model.InvoiceRepository;
import jakarta.inject.Inject;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.NotFoundException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;

import java.io.IOException;
import java.io.OutputStream;
import java.util.stream.Stream;

@Component
public class InvoiceService {
    private static final Logger LOG = LoggerFactory.getLogger(InvoiceService.class);

    @Inject
    private InvoiceRepository invoiceRepository;

    @Inject
    private S3Client s3Client;

    public Stream<InvoiceEntity> getInvoices(@NotNull final Tenant tenant, final String companyId) {
        return invoiceRepository.findByCompanyId(tenant, companyId);
    }

    public void getInvoiceDocument(@NotNull final Tenant tenant, @NotNull final String companyId,
        @NotNull final String documentNumber, final OutputStream out) {
        final InvoiceEntity invoiceEntity = invoiceRepository.findByDocumentNumber(documentNumber)
            .filter(invoice -> invoice.getTenant().equals(tenant))
            .filter(invoice -> invoice.getCompany().equals(companyId))
            .orElseThrow(() -> {
                LOG.warn("Invoice not found or wrong tenant/company {}/{} {}", tenant, companyId, documentNumber);
                return new NotFoundException("Invoice not found");
            });

        final GetObjectRequest getObjectRequest = GetObjectRequest.builder()
            .bucket(invoiceEntity.getS3File().getBucket())
            .key(invoiceEntity.getS3File().getKey())
            .build();
        try (ResponseInputStream<GetObjectResponse> response = s3Client.getObject(getObjectRequest)) {
            response.transferTo(out);
        } catch (final IOException e) {
            throw new RuntimeException("Failed to write invoice document", e);
        }

    }

}
