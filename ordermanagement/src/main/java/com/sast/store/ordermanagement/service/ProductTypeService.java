package com.sast.store.ordermanagement.service;

import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.models.product_type.ProductType;
import com.commercetools.api.models.product_type.ProductTypeReference;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * product types are created in terraform and only referenced by uuid in most responses, so here is a mapping beteween
 * product-type-uuid and product-type-key
 */
@Component
public class ProductTypeService {
    private static final Logger LOG = LoggerFactory.getLogger(ProductTypeService.class);

    @Inject
    private ProjectApiRoot commercetoolsClient;

    private final Cache<String, ProductType> productTypeCache = Caffeine.newBuilder()
        .maximumSize(100)
        .build(this::fetchOne);

    @PostConstruct
    public void initializeCache() {
        try {
            commercetoolsClient.productTypes().get().executeBlocking().getBody().getResults().forEach(productType -> {
                productTypeCache.put(productType.getId(), productType);
            });
            LOG.info("ProductType cache initialized with {} entries", productTypeCache.estimatedSize());
        } catch (final Exception e) {
            LOG.warn("Failed to initialize ProductType cache");
        }
    }

    public Optional<String> getProductTypeKey(final String productTypeId) {
        return Optional.ofNullable(productTypeCache.getIfPresent(productTypeId)).map(ProductType::getKey);
    }

    public boolean isDefaultProduct(final ProductTypeReference productTypeReference) {
        return isType(productTypeReference, "default-product");
    }

    public boolean isAddon(final ProductTypeReference productTypeReference) {
        return isType(productTypeReference, "addon");
    }

    private boolean isType(final ProductTypeReference productTypeId, final String type) {
        if (productTypeId == null) {
            return false;
        }
        final ProductType productType = productTypeCache.getIfPresent(productTypeId.getId());
        return productType != null && type.equals(productType.getKey());
    }

    private ProductType fetchOne(final String id) {
        return commercetoolsClient.productTypes().withId(id).get().executeBlocking().getBody();
    }
}
