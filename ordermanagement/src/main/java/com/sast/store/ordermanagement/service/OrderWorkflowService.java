package com.sast.store.ordermanagement.service;

import com.commercetools.api.models.cart.Cart;
import com.commercetools.api.models.cart.LineItem;
import com.commercetools.api.models.order.Order;
import com.commercetools.api.models.order.OrderState;
import com.google.common.base.Preconditions;
import com.sast.store.brimtegration.apimodel.events.EventHeader;
import com.sast.store.brimtegration.apimodel.events.ingress.order.data.BillingOrder;
import com.sast.store.brimtegration.apimodel.events.ingress.order.data.BillingOrderStatus;
import com.sast.store.external.commercetools.util.CustomAttributeProvider;
import com.sast.store.external.commercetools.util.LocalizedFieldProvider;
import com.sast.store.external.mattermost.MattermostClient;
import com.sast.store.external.ump.UmpClient;
import com.sast.store.gen.client.ump.api.UmpExternalCompanyDto;
import com.sast.store.ordermanagement.brim.BrimOrderExportService;
import com.sast.store.ordermanagement.cartvalidation.CartValidationViolation;
import com.sast.store.ordermanagement.cartvalidation.CartValidator;
import com.sast.store.ordermanagement.dto.AuthorizationResultDto;
import com.sast.store.ordermanagement.email.OrderEmailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.Locale;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

@Component
@Slf4j
@RequiredArgsConstructor
public class OrderWorkflowService {
    private final OrderService orderService;
    private final OrderEmailService orderEmailService;
    private final BrimOrderExportService brimOrderExportService;
    private final CartValidator cartValidator;
    private final MattermostClient mattermostClient;
    private final UmpClient umpClient;

    public Order placeOrder(final Cart cart, final AuthorizationResultDto authorizationResultDto) {
        Preconditions.checkArgument(cart != null, "Parameter cart may not be null");
        Preconditions.checkArgument(authorizationResultDto != null, "Parameter authorizationResultDto may not be null");

        final Set<CartValidationViolation> violations = cartValidator.validate(cart);
        if (!violations.isEmpty()) {
            throw new IllegalStateException("Validation failed for cart %s. Cannot place order."
                .formatted(cart.getKey()));
        }

        final Order order = orderService.createOrder(cart);
        LOG.info("order {} created from cart {}", order.getOrderNumber(), cart.getId());

        // Workflow step "export order"
        brimOrderExportService.exportOrder(order, authorizationResultDto.brimPaymentData());
        LOG.info("order {} exported to brim", order.getOrderNumber());

        return order;
    }

    public void processOrderImportedInBrim(final EventHeader eventHeader, final BillingOrder orderData) {
        LOG.info("Processing order: {}", orderData);
        final BillingOrderStatus billingOrderStatus = orderData.billingOrderStatus();
        // Workflow step "await order exported"
        if (billingOrderStatus != BillingOrderStatus.OPEN && billingOrderStatus != BillingOrderStatus.COMPLETED
            && billingOrderStatus != BillingOrderStatus.REJECTED) {
            LOG.info("Ignoring brim order because status is: {} order: {}", billingOrderStatus, orderData);
            return;
        }
        final Optional<Order> order = orderService.findOrderByOrderNumber(orderData.orderNumber());
        if (order.isEmpty()) {
            LOG.warn("Order not found: {}", orderData.orderNumber());
            return;
        }
        // Workflow step "update order status"
        final Order updatedOrder = orderService.updateOrderState(order.get(), getOrderState(billingOrderStatus));
        // Workflow step "send order confirmation"
        if (Objects.equals(order.get().getOrderState(), OrderState.OPEN)) {
            // Retrieve stored payment method instead of passing around the BRIM payment
            orderEmailService.sendRexrothOrderConfirmation(order.get(), orderData.payment());
            sendMattermostNotification(eventHeader, orderData, updatedOrder);
        }
    }

    private void sendMattermostNotification(final EventHeader eventHeader, final BillingOrder orderData, final Order updatedOrder) {
        try {
            final String tenant = eventHeader.tenant().getJsonValue();
            final UmpExternalCompanyDto buyerCompanyDetails = umpClient.getCompanyDetails(tenant, orderData.buyerCompanyId());
            final Locale buyerCompanyLocale = Locale.of("en", buyerCompanyDetails.getCompanyCountry());
            final String sellerCompanyName = updatedOrder.getLineItems().stream()
                .map(LineItem::getVariant)
                .map(CustomAttributeProvider::getSellerCompanyId).filter(Optional::isPresent).map(Optional::get).findAny()
                .map(sellerCompanyId -> umpClient.getCompanyDetails(tenant, sellerCompanyId))
                .map(UmpExternalCompanyDto::getCompanyName).orElse("unknown");
            final String message = MessageFormat.format(
                """
                    :moneybag: The company {0} ({1} :flag-{2}:) successfully purchased {3} {3,choice,1#product|1<products} {4} \
                    from seller {10} in store {5}. They bought {11} {11,choice,1#item|1<items} with a total price of {6} {7} \
                    and paid via {8}. The order ID is {9}.""",
                buyerCompanyDetails.getCompanyName(),
                buyerCompanyLocale.getDisplayCountry(),
                buyerCompanyLocale.getCountry().toLowerCase(),
                updatedOrder.getLineItems().size(),
                updatedOrder.getLineItems().stream().map(LineItem::getName).map(LocalizedFieldProvider::getEnglish)
                    .map(x -> x.orElse("unknown")).toList(),
                eventHeader.tenant().getJsonValue(),
                updatedOrder.getTotalPrice().getNumber().doubleValue(),
                updatedOrder.getTotalPrice().getCurrencyCode(),
                orderData.payment().getPaymentMethod(),
                orderData.orderNumber(),
                sellerCompanyName,
                updatedOrder.getLineItems().stream().map(LineItem::getQuantity).mapToLong(Long::longValue).sum());
            mattermostClient.send(message);
        } catch (final RuntimeException e) {
            LOG.info("Failed to send mattermost notification", e);
        }
    }

    private OrderState getOrderState(final BillingOrderStatus billingOrderStatus) {
        return switch (billingOrderStatus) {
            case BillingOrderStatus.OPEN -> OrderState.CONFIRMED;
            case BillingOrderStatus.COMPLETED -> OrderState.COMPLETE;
            case BillingOrderStatus.REJECTED -> OrderState.CANCELLED;
            default -> throw new IllegalArgumentException("Unexpected value: " + billingOrderStatus);
        };
    }
}
