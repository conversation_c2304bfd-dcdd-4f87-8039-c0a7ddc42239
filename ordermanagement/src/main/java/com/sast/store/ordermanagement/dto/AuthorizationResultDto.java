package com.sast.store.ordermanagement.dto;

import com.sast.store.brimtegration.apimodel.common.payment.PaymentData;

public record AuthorizationResultDto(
        Status status,
        // TODO BOSS-630: Remove when payment method is stored
        PaymentData brimPaymentData
) {

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {
        private Status status;
        private PaymentData brimPaymentData;

        private Builder() {
        }

        public Builder status(final Status status) {
            this.status = status;
            return this;
        }

        public Builder brimPaymentData(final PaymentData brimPaymentData) {
            this.brimPaymentData = brimPaymentData;
            return this;
        }

        public AuthorizationResultDto build() {
            return new AuthorizationResultDto(this.status, this.brimPaymentData);
        }
    }

    public enum Status {
        SUCCESS, FAILURE
    }
}