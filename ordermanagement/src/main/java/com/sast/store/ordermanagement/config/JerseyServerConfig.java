package com.sast.store.ordermanagement.config;

import com.sast.store.commons.basewebapp.rest.CommonJerseyServerConfig;
import com.sast.store.ordermanagement.invoice.rest.InvoiceRestService;
import com.sast.store.ordermanagement.me.rest.MeRestService;
import com.sast.store.ordermanagement.rest.CartRestService;
import com.sast.store.ordermanagement.rest.CommercetoolsExtensionRestService;
import com.sast.store.ordermanagement.rest.OrderRestService;
import com.sast.store.ordermanagement.rest.PaymentRestService;
import jakarta.annotation.PostConstruct;
import jakarta.ws.rs.ApplicationPath;
import org.glassfish.jersey.server.ResourceConfig;
import org.springframework.context.annotation.Configuration;

@Configuration
@ApplicationPath("/rest")
public class JerseyServerConfig extends ResourceConfig {

    @PostConstruct
    public void init() {
        register(CartRestService.class);
        register(OrderRestService.class);
        register(PaymentRestService.class);
        register(InvoiceRestService.class);
        register(CommercetoolsExtensionRestService.class);
        register(MeRestService.class);

        CommonJerseyServerConfig.defaultRegistrationsAndProperties(clazz -> register(clazz), (prop, val) -> property(prop, val));
    }
}
