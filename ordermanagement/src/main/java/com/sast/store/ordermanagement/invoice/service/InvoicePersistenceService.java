package com.sast.store.ordermanagement.invoice.service;

import com.sast.store.brimtegration.apimodel.common.document.DocumentLineItem;
import com.sast.store.brimtegration.apimodel.common.document.PlatformDocumentType;
import com.sast.store.brimtegration.apimodel.common.document.S3File;
import com.sast.store.brimtegration.apimodel.events.ingress.document.data.BillingBuyerDocumentCreated;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.ordermanagement.invoice.model.InvoiceEntity;
import com.sast.store.ordermanagement.invoice.model.InvoiceEntity.CreditNote;
import com.sast.store.ordermanagement.invoice.model.InvoiceRepository;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class InvoicePersistenceService {
    private final InvoiceRepository invoiceRepository;

    public InvoiceEntity saveInvoice(@NonNull final Tenant tenant,
        @NonNull final BillingBuyerDocumentCreated buyerDocumentCreated) {
        final InvoiceEntity invoiceEntity = InvoiceEntity.builder()
            .documentNumber(buyerDocumentCreated.documentNumber())
            .tenant(tenant)
            .company(buyerDocumentCreated.recipientCompanyId())
            .orderNumbers(List.copyOf(buyerDocumentCreated.orderNumbers()))
            .lineItems(toLineItems(buyerDocumentCreated.lineItems()))
            .totalAmounts(toTotalAmounts(buyerDocumentCreated))
            .documentDate(buyerDocumentCreated.documentDate())
            .creationDate(buyerDocumentCreated.creationDate())
            .s3File(toS3File(buyerDocumentCreated.file()))
            .build();

        invoiceRepository.save(invoiceEntity);
        LOG.info("Saved invoice {}", invoiceEntity.getDocumentNumber());
        return invoiceEntity;
    }

    public InvoiceEntity.CreditNote saveCreditNote(@NonNull final InvoiceEntity invoice,
        @NonNull final BillingBuyerDocumentCreated buyerDocumentCreated) {

        final CreditNote creditNote = InvoiceEntity.CreditNote.builder()
            .documentNumber(buyerDocumentCreated.documentNumber())
            .creditNoteType(toCreditNoteType(buyerDocumentCreated.documentType()))
            .totalAmounts(toTotalAmounts(buyerDocumentCreated))
            .lineItems(toLineItems(buyerDocumentCreated.lineItems()))
            .documentDate(buyerDocumentCreated.documentDate())
            .creationDate(buyerDocumentCreated.creationDate())
            .s3File(toS3File(buyerDocumentCreated.file()))
            .build();

        invoice.addCreditNote(creditNote);
        invoiceRepository.update(invoice);
        LOG.info("Saved credit note {} for invoice {}", creditNote.getDocumentNumber(), invoice.getDocumentNumber());
        return creditNote;
    }

    private InvoiceEntity.InvoiceTotalAmounts toTotalAmounts(final BillingBuyerDocumentCreated buyerDocumentCreated) {
        return InvoiceEntity.InvoiceTotalAmounts.builder()
            .currency(buyerDocumentCreated.currency().getCurrencyCode())
            .netAmount(buyerDocumentCreated.netAmount())
            .grossAmount(buyerDocumentCreated.grossAmount())
            .taxAmount(buyerDocumentCreated.taxAmount())
            .build();
    }

    private InvoiceEntity.S3File toS3File(final S3File brimS3File) {
        return InvoiceEntity.S3File.builder()
            .key(brimS3File.key())
            .bucket(brimS3File.bucket())
            .contentType(brimS3File.contentType())
            .sha256sum(brimS3File.sha256sum())
            .build();
    }

    private InvoiceEntity.CreditNoteType toCreditNoteType(final PlatformDocumentType platformDocumentType) {
        return switch (platformDocumentType) {
            case CREDIT_NOTE -> InvoiceEntity.CreditNoteType.CREDIT_NOTE;
            case REVERSAL -> InvoiceEntity.CreditNoteType.REVERSAL;
            default -> throw new IllegalArgumentException("Invalid credit note type %s".formatted(platformDocumentType));
        };
    }

    private List<InvoiceEntity.LineItem> toLineItems(final List<DocumentLineItem> brimLineItems) {
        return brimLineItems.stream()
            .map(lineItem -> InvoiceEntity.LineItem.builder()
                .position(lineItem.position())
                .quantity(lineItem.quantity())
                .netAmount(lineItem.netAmount())
                .productId(lineItem.productId())
                .build())
            .toList();
    }
}
