package com.sast.store.ordermanagement.cartvalidation.rules;

import com.commercetools.api.models.cart.Cart;
import com.commercetools.api.models.cart.LineItem;
import com.commercetools.api.models.product.ProductReference;
import com.sast.store.external.commercetools.util.CustomAttributeProvider;
import com.sast.store.external.commercetools.util.CustomFieldProvider;
import com.sast.store.ordermanagement.cartvalidation.CartErrorCode;
import com.sast.store.ordermanagement.cartvalidation.CartValidationContext;
import com.sast.store.ordermanagement.cartvalidation.CartValidationRule;
import com.sast.store.ordermanagement.cartvalidation.CartValidationViolation;
import lombok.NonNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Component
public class AddonsValidationRule implements CartValidationRule {
    private static final Logger LOG = LoggerFactory.getLogger(AddonsValidationRule.class);

    @Override
    public Set<CartValidationViolation> apply(@NonNull final Cart cart, @NonNull final CartValidationContext context) {
        if (cart.getLineItems() == null) {
            return Set.of();
        }

        final boolean orphanedAddonsFound = cart.getLineItems().stream()
            .filter(li -> CustomFieldProvider.getParentLineItemKey(li).isPresent())
            .anyMatch(li -> !itemExists(cart, li));

        if (orphanedAddonsFound) {
            LOG.info("Invalid addon structure found in cart {}", cart);
            return Set.of(CartValidationViolation.builder()
                .error(CartErrorCode.ADDON_PARENT_NOT_FOUND)
                .message("Parent line item not found")
                .build());
        }

        return Set.of();
    }

    private boolean itemExists(final Cart cart, final LineItem addonLineItem) {
        final String parentKey = CustomFieldProvider.getParentLineItemKey(addonLineItem).get();
        final Optional<LineItem> parent = cart.getLineItems().stream()
            .filter(li -> li.getKey() != null)
            .filter(li -> li.getKey().equals(parentKey))
            .findAny();
        if (parent.isEmpty()) {
            LOG.info("parent with key {} does not exist", parentKey);
            return false;
        }
        final LineItem parentLineItem = parent.get();
        if (CustomFieldProvider.getParentLineItemKey(parentLineItem).isPresent()) {
            LOG.info("parent with key {} is not allowed to reference another line item {}", parentKey,
                CustomFieldProvider.getParentLineItemKey(parentLineItem).get());
            return false;
        }

        final List<String> addons = Optional.ofNullable(parentLineItem.getVariant())
            .map(CustomAttributeProvider::getAddonsAsReference)
            .stream()
            .flatMap(List::stream)
            .map(ProductReference::getId)
            .toList();
        if (!addons.contains(addonLineItem.getProductId())) {
            LOG.info("addon is referencing an invalid parent, addon: {} parent: {}", addonLineItem, parentLineItem);
            return false;
        }
        return true;
    }
}
