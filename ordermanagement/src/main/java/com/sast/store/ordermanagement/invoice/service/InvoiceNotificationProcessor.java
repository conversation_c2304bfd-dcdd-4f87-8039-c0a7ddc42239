package com.sast.store.ordermanagement.invoice.service;

import com.sast.store.brimtegration.apimodel.events.EventHeader;
import com.sast.store.brimtegration.apimodel.events.ingress.document.data.BillingBuyerDocumentCreated;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.ordermanagement.invoice.model.InvoiceEntity;
import com.sast.store.ordermanagement.invoice.model.InvoiceRepository;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@Slf4j
@RequiredArgsConstructor
public class InvoiceNotificationProcessor {
    private final InvoiceRepository invoiceRepository;
    private final InvoicePersistenceService invoicePersistenceService;
    private final InvoiceEmailService invoiceEmailService;

    public void process(@NonNull final EventHeader header,
        @NonNull final BillingBuyerDocumentCreated buyerDocumentCreated) {
        final Tenant tenant = Tenant.fromString(header.tenant().getJsonValue());
        switch (buyerDocumentCreated.documentType()) {
            case INVOICE -> handleInvoice(tenant, buyerDocumentCreated);
            case CREDIT_NOTE, REVERSAL -> handleCreditNote(tenant, buyerDocumentCreated);
            default -> throw new IllegalStateException("Unexpected document type: " + buyerDocumentCreated.documentType());
        }
    }

    private void handleInvoice(@NonNull final Tenant tenant,
        @NonNull final BillingBuyerDocumentCreated buyerDocumentCreated) {
        final Optional<InvoiceEntity> invoice = invoiceRepository.findByDocumentNumber(buyerDocumentCreated.documentNumber());
        if (invoice.isPresent()) {
            LOG.warn("Ignoring already known invoice {}", buyerDocumentCreated.documentNumber());
            return;
        }

        final InvoiceEntity invoiceEntity = invoicePersistenceService.saveInvoice(tenant, buyerDocumentCreated);
        try {
            invoiceEmailService.sendInvoiceNotification(invoiceEntity);
        } catch (final Exception e) {
            LOG.error("Error while sending invoice notifications for invoice {}: {}",
                invoiceEntity.getDocumentNumber(), e.getMessage(), e);
        }
    }

    private void handleCreditNote(@NonNull final Tenant tenant,
        @NonNull final BillingBuyerDocumentCreated buyerDocumentCreated) {
        final InvoiceEntity invoice = invoiceRepository.findByDocumentNumber(buyerDocumentCreated.parentDocumentNumber())
            .orElseThrow(() -> new IllegalArgumentException("Could not find parent document %s"
                .formatted(buyerDocumentCreated.parentDocumentNumber())));

        if (invoice.getCreditNotes() == null) {
            throw new IllegalStateException("Invoice credit notes must not be null");
        }
        if (invoice.getCreditNotes().stream()
            .anyMatch(creditNote -> creditNote.getDocumentNumber().equals(buyerDocumentCreated.documentNumber()))) {
            LOG.warn("Ignoring already known credit note {}", buyerDocumentCreated.documentNumber());
            return;
        }

        final InvoiceEntity.CreditNote creditNote = invoicePersistenceService
            .saveCreditNote(invoice, buyerDocumentCreated);
        // TODO: Send email here
    }
}
