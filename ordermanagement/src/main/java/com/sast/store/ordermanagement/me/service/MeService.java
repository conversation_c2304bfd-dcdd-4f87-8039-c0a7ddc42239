package com.sast.store.ordermanagement.me.service;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.external.ump.UmpClient;
import com.sast.store.gen.client.ump.api.UmpExternalCompanyDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@Slf4j
@RequiredArgsConstructor
public class MeService {

    private final UmpClient umpClient;

    public Optional<UmpExternalCompanyDto> fetchCompany(final Tenant tenant, final String companyId) {
        try {
            return Optional.ofNullable(umpClient.getCompanyDetails(tenant.id(), companyId));
        } catch (final Exception e) {
            LOG.warn("Failed to query UMP for company (companyId={}, tenant={}): {}", companyId, tenant, e.getMessage(), e);
            return Optional.empty();
        }
    }
}
