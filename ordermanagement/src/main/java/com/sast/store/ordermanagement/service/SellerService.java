package com.sast.store.ordermanagement.service;

import com.commercetools.api.models.cart.Cart;
import com.sast.store.external.commercetools.util.CustomAttributeProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
public class SellerService {
    private static final Logger LOG = LoggerFactory.getLogger(SellerService.class);

    public Optional<String> getSellerCompanyId(final Cart cart) {
        final List<String> list = cart.getLineItems().stream()
            .map(lineItem -> CustomAttributeProvider.getSellerCompanyId(lineItem.getVariant()))
            .filter(Optional::isPresent)
            .map(Optional::get)
            .distinct()
            .toList();
        if (list.size() != 1) {
            LOG.info("cart {} has multiple or no sellers: {}", cart.getId(), list);
            return Optional.empty();
        }
        return Optional.of(list.getFirst());
    }
}
