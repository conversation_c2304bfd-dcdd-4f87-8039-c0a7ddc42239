package com.sast.store.ordermanagement.cartvalidation;

import com.commercetools.api.models.cart.Cart;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.external.commercetools.util.CustomFieldProvider;
import com.sast.store.external.ump.UmpClient;
import com.sast.store.gen.client.ump.api.UmpExternalCompanyDto;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class CartValidationContextProvider {
    private final UmpClient umpClient;

    public CartValidationContext cartContext(@NonNull final Cart cart) {
        final Tenant tenant = CustomFieldProvider.getTenant(cart)
            .orElseThrow(() -> new IllegalStateException("Cart %s has no tenant".formatted(cart.getId())));
        final String companyId = CustomFieldProvider.getCompanyId(cart)
            .orElseThrow(() -> new IllegalStateException("Cart %s has no companyId".formatted(cart.getId())));

        final UmpExternalCompanyDto umpCompany = umpClient.getCompanyDetails(tenant.id(), companyId);

        return CartValidationContext.builder()
            .umpCompany(umpCompany)
            .tenant(tenant)
            .build();
    }
}
