package com.sast.store.ordermanagement.rest;

import com.sast.store.commons.basewebapp.keycloak.AuthenticationService;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.ordermanagement.api.LineItemDto;
import com.sast.store.ordermanagement.api.MoneyDto;
import com.sast.store.ordermanagement.api.OrderApi;
import com.sast.store.ordermanagement.api.OrderDto;
import com.sast.store.ordermanagement.service.OrderService;
import jakarta.inject.Inject;
import jakarta.validation.constraints.NotNull;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;

@Component
public class OrderRestService implements OrderApi {

    @Inject
    private OrderService orderService;
    @Inject
    private AuthenticationService authenticationService;

    @Override
    @PreAuthorize("hasRole('DEFAULT')")
    public List<OrderDto> listAllOrders(@NotNull final Tenant tenantId) {
        final String languageCode = authenticationService.getCommunicationLanguage().get();

        return orderService.getOrders().stream()
            .map(order -> OrderDto.builder()
                .orderId(order.getOrderNumber())
                .createdAt(order.getCreatedAt())
                .lineItems(order.getLineItems().stream().map(li -> LineItemDto.builder()
                    .productId(li.getProductId())
                    .sku(li.getVariant().getSku())
                    .name(li.getName().get(languageCode))
                    .itemPrice(MoneyDto.builder()
                            .value(li.getPrice().getValue().getNumber().doubleValue())
                            .currencyCode(li.getPrice().getValue().getCurrency().getCurrencyCode())
                            .build())
                    .quantity(li.getQuantity())
                    .totalPrice(MoneyDto.builder().value(li.getTotalPrice().getNumber().doubleValue())
                        .currencyCode(li.getTotalPrice().getCurrencyCode())
                        .build())
                    .build()).toList())
                .totalPrice(MoneyDto.builder().value(order.getTotalPrice().getNumber().doubleValue())
                    .currencyCode(order.getTotalPrice().getCurrencyCode())
                    .build())
                .build())
            .sorted(Comparator.comparing(OrderDto::createdAt))
            .toList();
    }
}
