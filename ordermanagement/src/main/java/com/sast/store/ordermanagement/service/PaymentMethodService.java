package com.sast.store.ordermanagement.service;

import com.commercetools.api.models.cart.Cart;
import com.commercetools.api.models.payment.Payment;
import com.sast.store.ordermanagement.api.AuthorizationInformationDto;
import com.sast.store.ordermanagement.api.CheckoutInformationDto;
import com.sast.store.ordermanagement.api.PaymentMethodType;
import com.sast.store.ordermanagement.api.PaymentProviderEnum;
import com.sast.store.ordermanagement.dto.AuthorizationResultDto;

import java.util.Comparator;

public interface PaymentMethodService {

    /**
     * 
     * Executed before rendering the checkout page to determine if the payment method should be displayed to the user.
     * 
     * Checks if this payment method supports the the given cart. This should also verify if the payment method is
     * enabled in countrieservice
     * 
     */
    boolean supportsCart(Cart cart);

    /**
     * Executed before rendering of the checkout page to fetch additonal information that should be displayed to the
     * user
     * 
     * Prepares the checkout before any payment method is selected and return the necessary information to initialize
     * the user interface. E.g. for bank transfer this could be the target bank account that is already displayed before
     * placing the order.
     * 
     */
    CheckoutInformationDto prepareCheckout(Cart cart);

    /**
     * Executed after the user has selected the payment method and clicked on the checkout button.
     * 
     * Starts the authorization process. This should initialize the payment e.g. generate a redirect link in case the
     * payment is done on an external page.
     * 
     * The implementation of this method should create a payment object and attach it to the cart.
     *
     */
    AuthorizationInformationDto authorize(Cart cart);

    /**
     * Finalizes the payment. This is called after the user has confirmed the payment on the external page and should
     * fetch the status of the payment. If the dto returns status=SUCCESS the order will be created.
     * 
     * This method should update the payment and attach a transaction object to it.
     *
     */
    AuthorizationResultDto getAuthorizationResult(Payment payment);

    /**
     * Name of the payment provider, this will be used to group payment methods, the combination of payment provider and
     * payment method needs to be unique
     * 
     * payment providers can be enabled and disabled in the countries service
     * 
     * e.g. PGW
     */
    PaymentProviderEnum getPaymentProviderName();

    /**
     * Payment method, only one payment method per payment method type will be displayed to the user. If multiple
     * payment methods of the same type are enabled only one will be chosen based on the order in PaymentProviderEnum
     * 
     * payment methods can be enabled and disabled in the countries service
     *
     * e.g. SEPA_DIRECTDEBIT
     */
    PaymentMethodType getPaymentMethodType();

    /**
     * the unique payment method id, this should be used internally for persistence and lookup of the payment service
     * implementation
     * 
     * e.g. PGW/SEPA_DIRECTDEBIT
     *
     */
    default String getPaymentMethodId() {
        return getPaymentProviderName() + "/" + getPaymentMethodType();
    }

    /**
     * By default sorted based on position in the PaymentProviderEnum and PaymentMethodType enums
     */
    default int compareTo(final PaymentMethodService other) {
        return Comparator.comparing(PaymentMethodService::getPaymentProviderName)
            .thenComparing(PaymentMethodService::getPaymentMethodType)
            .compare(this, other);
    }

}
