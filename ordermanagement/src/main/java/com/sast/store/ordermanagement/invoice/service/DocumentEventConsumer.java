package com.sast.store.ordermanagement.invoice.service;

import com.sast.store.brimtegration.apimodel.events.ingress.document.IngressDocumentEvent;
import com.sast.store.brimtegration.apimodel.events.ingress.document.IngressDocumentEventData;
import com.sast.store.brimtegration.apimodel.events.ingress.document.data.BillingBuyerDocumentCreated;
import io.awspring.cloud.sqs.annotation.SqsListener;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class DocumentEventConsumer {
    private final InvoiceNotificationProcessor invoiceNotificationProcessor;

    @SqsListener("bossstore-businessprocessing-invoice-events.fifo")
    public void consume(final @Valid IngressDocumentEvent<? extends IngressDocumentEventData> ingressDocumentEvent) {
        LOG.info("Processing document event {}", ingressDocumentEvent);
        switch (ingressDocumentEvent.data()) {
            case final BillingBuyerDocumentCreated buyerDocumentCreated -> invoiceNotificationProcessor
                .process(ingressDocumentEvent.header(), buyerDocumentCreated);
            default -> LOG.info("Ignoring message of type {}", ingressDocumentEvent.data().getClass().getSimpleName());
        }
    }
}