package com.sast.store.ordermanagement.invoice.email;

import com.sast.store.external.email.data.EmailTemplateData;
import com.sast.store.external.email.data.EmailTenant;
import lombok.Builder;

@Builder
public record InvoiceEmailDto(
    String orderNumber) implements EmailTemplateData {
    @Override
    public String getTemplateName(final EmailTenant emailTenant) {
        return "rexroth/store/invoiceNotification";
    }
}
