package com.sast.store.ordermanagement.me.rest;

import com.sast.store.commons.basewebapp.keycloak.AuthenticationService;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.gen.client.ump.api.UmpExternalCompanyDto;
import com.sast.store.ordermanagement.api.CompanyDto;
import com.sast.store.ordermanagement.api.MeApi;
import com.sast.store.ordermanagement.me.service.MeService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class MeRestService implements MeApi {

    private final AuthenticationService authenticationService;
    private final MeService meService;

    @Override
    @PreAuthorize("hasRole('DEFAULT')")
    public CompanyDto getCompany(final Tenant tenant) {
        final var companyId = authenticationService.getCompanyId();

        return meService.fetchCompany(tenant, companyId)
            .map(this::toCompanyDto)
            .orElseThrow(() -> new IllegalStateException("Company not found"));
    }

    private CompanyDto toCompanyDto(final UmpExternalCompanyDto company) {
        return CompanyDto.builder()
            .name(company.getCompanyName())
            .country(company.getCompanyCountry())
            .build();
    }
}
