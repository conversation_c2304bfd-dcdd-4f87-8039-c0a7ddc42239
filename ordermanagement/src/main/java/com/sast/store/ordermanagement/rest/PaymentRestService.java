package com.sast.store.ordermanagement.rest;

import com.commercetools.api.models.cart.Cart;
import com.sast.store.commons.basewebapp.keycloak.AuthenticationService;
import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.ordermanagement.api.CheckoutInformationDto;
import com.sast.store.ordermanagement.api.PaymentApi;
import com.sast.store.ordermanagement.api.PaymentDto;
import com.sast.store.ordermanagement.api.PaymentMethodConfigDto;
import com.sast.store.ordermanagement.service.CartService;
import com.sast.store.ordermanagement.service.PaymentService;
import jakarta.inject.Inject;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.NotFoundException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PaymentRestService implements PaymentApi {

    @Inject
    private PaymentService paymentService;

    @Inject
    private CartService cartService;

    @Inject
    private AuthenticationService authenticationService;

    @Override
    @PreAuthorize("hasRole('DEFAULT')")
    public PaymentDto getPayment(@NotNull final Tenant tenantId, final String paymentId) {
        try {
            return paymentService.getPayment(paymentId);
        } catch (final io.vrap.rmf.base.client.error.NotFoundException e) {
            throw new NotFoundException();
        }
    }

    @Override
    @PreAuthorize("hasRole('DEFAULT')")
    public PaymentMethodConfigDto getPaymentConfig(final Tenant tenantId) {
        final String userId = authenticationService.getUserId();
        final String companyId = authenticationService.getCompanyId();

        final Cart cart = cartService.getOrCreateCart(tenantId, companyId, userId);
        final Cart recalculatedCart = cartService.recalculate(cart);

        final List<CheckoutInformationDto> list = paymentService.fetchCheckoutInformation(recalculatedCart);

        return PaymentMethodConfigDto.builder().paymentMethods(list).build();
    }

}
