// src/main/java/com/sast/store/ordermanagement/service/sync/UserGroupSyncService.java
package com.sast.store.ordermanagement.service.sync;

import com.sast.store.commons.tenant.api.Tenant;
import com.sast.store.gen.client.ump.api.UmpInternalCustomerGroupConfigurationDto;
import com.sast.store.ordermanagement.service.CartService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserGroupSyncService {

    private final CartService cartService;

    public void handleUserGroupChange(final String companyId,
                                      final UmpInternalCustomerGroupConfigurationDto groupDto,
                                      final Tenant tenant) {
        cartService.syncUserGroup(tenant, companyId, groupDto);
        LOG.info("Synchronized customer-group change for company {} → customizationId {} across all carts",
                 companyId, groupDto.getCustomizationId());
    }
}