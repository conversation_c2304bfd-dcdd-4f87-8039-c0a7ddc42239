package com.sast.store.ordermanagement.cartvalidation;

import com.commercetools.api.models.cart.Cart;
import jakarta.annotation.PostConstruct;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
@RequiredArgsConstructor
public class CartValidator {
    private final Set<CartValidationRule> cartValidationRules;
    private final CartValidationContextProvider cartValidationContextProvider;

    @PostConstruct
    public void logActiveValidationRules() {
        final Set<String> validatorClassNames = cartValidationRules.stream()
                .map(rule -> rule.getClass().getSimpleName())
                .collect(Collectors.toSet());

        LOG.info("Active cart validation rules: {}", String.join(", ", validatorClassNames));
    }

    /**
     * Perform validation on given cart
     *
     * @param cart Cart to be validated
     * @return set of violations. if the returned set is not empty, the provided cart must be considered invalid.
     *         The violations of the given cart for all active rules are cumulative.
     */
    public Set<CartValidationViolation> validate(@NonNull final Cart cart) {
        final CartValidationContext context = cartValidationContextProvider.cartContext(cart);

        final Set<CartValidationViolation> violations = cartValidationRules.stream()
                .flatMap(rule -> rule.apply(cart, context).stream())
                .collect(Collectors.toSet());

        if (!violations.isEmpty()) {
            LOG.warn("Cart {} is invalid. Violations: {}", cart.getKey(), violations);
        }

        return violations;
    }
}
