package com.sast.store.ordermanagement.invoice.model;

import io.awspring.cloud.dynamodb.DynamoDbTemplate;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.PageIterable;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;

import java.util.Optional;
import java.util.stream.Stream;

public abstract class AbstractDynamodbDao<T> {

    private final DynamoDbTemplate dynamoDbTemplate;
    private final Class<T> entityClass;

    public AbstractDynamodbDao(final DynamoDbTemplate dynamoDbTemplate, final String tableName,
        final Class<T> entityClass) {
        this.dynamoDbTemplate = dynamoDbTemplate;
        this.entityClass = entityClass;
    }

    public T save(final T entity) {
        return dynamoDbTemplate.save(entity);
    }

    public T update(final T entity) {
        return dynamoDbTemplate.update(entity);
    }

    public T delete(final T entity) {
        return dynamoDbTemplate.delete(entity);
    }

    public void save(final Iterable<T> entity) {
        entity.forEach(this::save);
    }

    public Stream<T> findAll() {
        return dynamoDbTemplate.scanAll(entityClass).items().stream();
    }

    protected Optional<T> findByPartitionValue(final String partitionValue) {
        return Optional.ofNullable(dynamoDbTemplate.load(Key.builder()
            .partitionValue(partitionValue)
            .build(), entityClass));
    }

    protected Optional<T> findByPartitionValueAndSortValue(final String partitionValue, final String sortValue) {
        return Optional.ofNullable(dynamoDbTemplate.load(Key.builder()
            .partitionValue(partitionValue)
            .sortValue(sortValue)
            .build(), entityClass));
    }

    protected Stream<T> findAllByPartitionValue(final String partitionValue) {
        final PageIterable<T> result = dynamoDbTemplate.query(QueryEnhancedRequest.builder()
            .queryConditional(QueryConditional
                .keyEqualTo(Key.builder()
                    .partitionValue(partitionValue)
                    .build()))
            .build(), entityClass);
        return result.items().stream();
    }

    protected Stream<T> findByIndexPartitionKey(final String partitionValue, final String indexName) {
        final PageIterable<T> result = dynamoDbTemplate.query(QueryEnhancedRequest.builder()
            .queryConditional(QueryConditional
                .keyEqualTo(Key.builder()
                    .partitionValue(partitionValue)
                    .build()))
            .build(), entityClass, indexName);
        return result.items().stream();
    }

    protected Stream<T> findByIndexPartitionKeyAndSortValue(final String partitionValue, final String sortValue,
        final String indexName) {
        final PageIterable<T> result = dynamoDbTemplate.query(QueryEnhancedRequest.builder()
            .queryConditional(QueryConditional
                .keyEqualTo(Key.builder()
                    .partitionValue(partitionValue)
                    .sortValue(sortValue)
                    .build()))
            .build(), entityClass, indexName);
        return result.items().stream();
    }
}