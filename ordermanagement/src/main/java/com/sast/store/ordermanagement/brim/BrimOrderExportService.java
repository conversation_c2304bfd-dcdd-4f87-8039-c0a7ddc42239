package com.sast.store.ordermanagement.brim;

import com.commercetools.api.models.cart.LineItem;
import com.commercetools.api.models.common.Price;
import com.commercetools.api.models.common.TypedMoney;
import com.commercetools.api.models.order.Order;
import com.google.common.base.Preconditions;
import com.google.common.primitives.Ints;
import com.sast.store.brimtegration.apimodel.common.Tenant;
import com.sast.store.brimtegration.apimodel.common.order.InvoiceNotes;
import com.sast.store.brimtegration.apimodel.common.payment.PaymentData;
import com.sast.store.brimtegration.apimodel.events.egress.order.data.PlatformAddonItem;
import com.sast.store.brimtegration.apimodel.events.egress.order.data.PlatformOrderItem;
import com.sast.store.brimtegration.apimodel.events.egress.order.data.PlatformOrderPlaced;
import com.sast.store.external.brim.BrimMessagePublisher;
import com.sast.store.external.commercetools.util.CustomAttributeProvider;
import com.sast.store.external.commercetools.util.CustomFieldProvider;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static java.time.temporal.ChronoUnit.DAYS;

@Component
public class BrimOrderExportService {
    private final BrimMessagePublisher brimMessagePublisher;

    public BrimOrderExportService(final BrimMessagePublisher brimMessagePublisher) {
        this.brimMessagePublisher = brimMessagePublisher;
    }

    public void exportOrder(final Order order, final PaymentData brimPaymentData) {
        Preconditions.checkArgument(order != null, "Parameter order cannot be null");
        Preconditions.checkArgument(brimPaymentData != null, "Parameter brimPaymentData cannot be null");

        final PlatformOrderPlaced platformOrderPlaced = PlatformOrderPlaced.builder()
                .orderNumber(order.getOrderNumber())
                .placedAt(order.getCreatedAt())
                .buyerCompanyId(getBuyerCompanyId(order))
                .items(getOrderItems(order))
                .invoiceNotes(getInvoiceNotes(order))
                .payment(brimPaymentData)
                .build();

        brimMessagePublisher.publishPlatformOrderEvent(Tenant.REXROTH.getJsonValue(), platformOrderPlaced);
    }

    private List<PlatformOrderItem> getOrderItems(final Order commercetoolsOrder) {
        final Map<String, List<LineItem>> addonItemsByParentKey = commercetoolsOrder.getLineItems().stream()
                .filter(lineItem -> CustomFieldProvider.getParentLineItemKey(lineItem).isPresent())
                .collect(Collectors.groupingBy(
                        lineItem -> CustomFieldProvider.getParentLineItemKey(lineItem).orElseThrow()));

        return commercetoolsOrder.getLineItems().stream()
                .filter(lineItem -> CustomFieldProvider.getParentLineItemKey(lineItem).isEmpty())
                .map(lineItem -> asOrderItem(lineItem, addonItemsByParentKey.get(lineItem.getKey())))
                .toList();
    }

    private PlatformOrderItem asOrderItem(final LineItem lineItem, final List<LineItem> addonItems) {
        return PlatformOrderItem.builder()
                .productId(lineItem.getVariant().getSku())
                .quantity(Ints.checkedCast(lineItem.getQuantity()))
                .amountAtOrderDate(asBigDecimal(lineItem.getPrice().getValue()))
                .addons(CollectionUtils.emptyIfNull(addonItems).stream().map(this::asAddonItem).toList())
                .requestedContractStart(getRequestedContractStart(lineItem).orElse(null))
                .priceStorefrontId(Optional.ofNullable(lineItem.getPrice()).map(Price::getKey).orElse(null))
                .build();
    }

    private Optional<ZonedDateTime> getRequestedContractStart(final LineItem lineItem) {
        final var contractStart = CustomAttributeProvider.getContractStart(lineItem.getVariant())
            .map(ContractStart::valueOf)
            .orElse(ContractStart.IMMEDIATE);

        return switch (contractStart) {
            case IMMEDIATE -> Optional.empty();
            case NEXT_FIRST -> Optional.of(ZonedDateTime.now(ZoneOffset.UTC).plusMonths(1).withDayOfMonth(1).truncatedTo(DAYS));
        };
    }

    private PlatformAddonItem asAddonItem(final LineItem addonItem) {
        return PlatformAddonItem.builder()
                .productId(addonItem.getVariant().getSku())
                .amountAtOrderDate(asBigDecimal(addonItem.getPrice().getValue()))
                .priceStorefrontId(Optional.ofNullable(addonItem.getPrice()).map(Price::getKey).orElse(null))
                .build();
    }

    private InvoiceNotes getInvoiceNotes(final Order commercetoolsOrder) {
        final InvoiceNotes.InvoiceNotesBuilder invoiceNotesBuilder = InvoiceNotes.builder();
        CustomFieldProvider.getInvoiceNotes1(commercetoolsOrder).ifPresent(invoiceNotesBuilder::firstNote);
        CustomFieldProvider.getInvoiceNotes2(commercetoolsOrder).ifPresent(invoiceNotesBuilder::secondNote);
        return invoiceNotesBuilder.build();
    }

    private String getBuyerCompanyId(final Order commercetoolsOrder) {
        return CustomFieldProvider.getCompanyId(commercetoolsOrder)
                .orElseThrow(() -> new IllegalArgumentException("Commercetools order %s has no companyId field"
                        .formatted(commercetoolsOrder.getOrderNumber())));
    }

    private BigDecimal asBigDecimal(final TypedMoney typedMoney) {
        return BigDecimal.valueOf(typedMoney.getCentAmount())
                .movePointLeft(typedMoney.getFractionDigits());
    }
}
