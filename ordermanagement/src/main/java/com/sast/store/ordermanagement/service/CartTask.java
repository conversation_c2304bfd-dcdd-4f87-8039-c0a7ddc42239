package com.sast.store.ordermanagement.service;

import jakarta.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class CartTask {
    private static final Logger LOG = LoggerFactory.getLogger(CartTask.class);

    @Inject
    private PaymentService paymentService;

    // random initial delay to avoid running at the same time with other pods
    @Scheduled(fixedRateString = "PT30M", initialDelayString = "PT${random.int[5,25]}M")
    public void scheduledTask() {
        LOG.info("started cartTask");
        try {
            paymentService.checkForLostCarts();
        } catch (final RuntimeException e) {
            LOG.warn("cartTask failed", e);
        }
    }
}
