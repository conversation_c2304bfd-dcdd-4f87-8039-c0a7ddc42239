package com.sast.store.ordermanagement.config;

import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.net.URI;
import java.util.Map;
import java.util.Optional;

@ConfigurationProperties(prefix = "bossstore")
@Validated
public record AppConfiguration(
    @NotBlank @Size(min = 1, max = 1) String environmentPrefix,
    Map<com.sast.store.commons.tenant.api.Tenant, Tenant> tenants,
    Map<String, SellerCompanyConfiguration> sellers,
    PlatformMessagingConfiguration platformMessaging
) {
    public Tenant tenant(final com.sast.store.commons.tenant.api.Tenant tenant) {
        return tenants.get(tenant);
    }

    public Optional<SellerCompanyConfiguration> seller(final String sellerCompanyId) {
        return Optional.ofNullable(sellers.get(sellerCompanyId));
    }

    public record Tenant(
        @NotNull PublicUrlConfig publicUrls,
        @NotNull Ump ump,
        Map<String, BigDecimal> cartLimitsByCurrency
    ) {
        public Tenant {
            if (cartLimitsByCurrency == null) {
                cartLimitsByCurrency = Map.of();
            }
        }
    }

    public record Pgw(
        URI successUri,
        URI failureUri,
        URI cancelUri
    ) { }

    public record SellerCompanyConfiguration(
        @Nullable BoschTransferConfiguration boschTransfer,
        @Nullable BoschTransferAchConfiguration boschTransferAch
    ) { }

    public record BoschTransferConfiguration(
        // in brim the accountholder is actually taken from bpmd master data so maybe we should take it from ump
        // instead?
        @NotBlank String accountHolder,
        @NotBlank String bankName,
        @NotBlank String iban,
        @Nullable String bic
    ) { }

    public record BoschTransferAchConfiguration(
        @NotBlank String accountHolder,
        @NotBlank String routingNumber,
        @NotBlank String accountNumber,
        @NotBlank String bic,
        @NotBlank String bankName
    ) { }

    public record PlatformMessagingConfiguration(
        @NotBlank String egressOrderTopic
    ) { }

    public record PublicUrlConfig(
        @NotNull URI contractManagementUrl
    ) { }

    public record Ump(
        String tenant
    ) { }
}
