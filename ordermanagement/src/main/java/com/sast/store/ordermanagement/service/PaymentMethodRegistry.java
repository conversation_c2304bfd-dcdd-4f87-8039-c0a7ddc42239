package com.sast.store.ordermanagement.service;

import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class PaymentMethodRegistry {
    private static final Logger LOG = LoggerFactory.getLogger(PaymentMethodRegistry.class);
    @Inject
    private List<PaymentMethodService> paymentMethods;

    private Map<String, PaymentMethodService> registry;

    @PostConstruct
    void setup() {
        registry = paymentMethods.stream().collect(Collectors.toMap(PaymentMethodService::getPaymentMethodId, Function.identity()));
        LOG.info("registered payment methods {}", registry);
        if (registry.isEmpty()) {
            LOG.error("No payment methods registered");
        }
        if (registry.size() != paymentMethods.size()) {
            LOG.error("Not all payment methods could be registered. Registered: {}, expected: {}", registry, paymentMethods);
        }
    }

    public PaymentMethodService getService(final String paymentMethodId) {
        final PaymentMethodService service = registry.get(paymentMethodId);
        if (service == null) {
            LOG.warn("Payment method not found: {}", paymentMethodId);
            throw new IllegalArgumentException();
        }
        return service;
    }

    public Collection<PaymentMethodService> getAll() {
        return registry.values();
    }

}
