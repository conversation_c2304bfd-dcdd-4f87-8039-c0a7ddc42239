# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10

"@ampproject/remapping@npm:^2.2.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10/f3451525379c68a73eb0a1e65247fbf28c0cccd126d93af21c75fceff77773d43c0d4a2d51978fb131aff25b5f2cb41a9fe48cc296e61ae65e679c4f6918b0ab
  languageName: node
  linkType: hard

"@ardatan/relay-compiler@npm:12.0.0":
  version: 12.0.0
  resolution: "@ardatan/relay-compiler@npm:12.0.0"
  dependencies:
    "@babel/core": "npm:^7.14.0"
    "@babel/generator": "npm:^7.14.0"
    "@babel/parser": "npm:^7.14.0"
    "@babel/runtime": "npm:^7.0.0"
    "@babel/traverse": "npm:^7.14.0"
    "@babel/types": "npm:^7.0.0"
    babel-preset-fbjs: "npm:^3.4.0"
    chalk: "npm:^4.0.0"
    fb-watchman: "npm:^2.0.0"
    fbjs: "npm:^3.0.0"
    glob: "npm:^7.1.1"
    immutable: "npm:~3.7.6"
    invariant: "npm:^2.2.4"
    nullthrows: "npm:^1.1.1"
    relay-runtime: "npm:12.0.0"
    signedsource: "npm:^1.0.0"
    yargs: "npm:^15.3.1"
  peerDependencies:
    graphql: "*"
  bin:
    relay-compiler: bin/relay-compiler
  checksum: 10/60896560fd282ccc9e705fa18c685d23783f97670fa44be287beaf9d49acfd1a6bbc19daf3e55d9cffdf385ef883be36f7acf5bdcf61c46483e31db9e4e71884
  languageName: node
  linkType: hard

"@ardatan/sync-fetch@npm:^0.0.1":
  version: 0.0.1
  resolution: "@ardatan/sync-fetch@npm:0.0.1"
  dependencies:
    node-fetch: "npm:^2.6.1"
  checksum: 10/ee21741badecb18fb9a18a404275e25272f67ade914f98885de79ccecba3403b8a6357e6b033a028e24f0d902197dd541655309d7789ebacd7ad981bf1f12618
  languageName: node
  linkType: hard

"@asamuzakjp/css-color@npm:^2.8.2":
  version: 2.8.2
  resolution: "@asamuzakjp/css-color@npm:2.8.2"
  dependencies:
    "@csstools/css-calc": "npm:^2.1.1"
    "@csstools/css-color-parser": "npm:^3.0.7"
    "@csstools/css-parser-algorithms": "npm:^3.0.4"
    "@csstools/css-tokenizer": "npm:^3.0.3"
    lru-cache: "npm:^11.0.2"
  checksum: 10/998885b5deae79d26719befe9cc7e6877ae55818226c1da7c3e901107eb9a2d961b8797cc0961372a23e72b8484899a2b7f06879e34ff7f49c1c35e55eb695d3
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.25.9, @babel/code-frame@npm:^7.26.0, @babel/code-frame@npm:^7.26.2":
  version: 7.26.2
  resolution: "@babel/code-frame@npm:7.26.2"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.25.9"
    js-tokens: "npm:^4.0.0"
    picocolors: "npm:^1.0.0"
  checksum: 10/db2c2122af79d31ca916755331bb4bac96feb2b334cdaca5097a6b467fdd41963b89b14b6836a14f083de7ff887fc78fa1b3c10b14e743d33e12dbfe5ee3d223
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.20.5":
  version: 7.26.2
  resolution: "@babel/compat-data@npm:7.26.2"
  checksum: 10/ed9eed6b62ce803ef4a320b1dac76b0302abbb29c49dddf96f3e3207d9717eb34e299a8651bb1582e9c3346ead74b6d595ffced5b3dae718afa08b18741f8402
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.25.9":
  version: 7.26.3
  resolution: "@babel/compat-data@npm:7.26.3"
  checksum: 10/0bf4e491680722aa0eac26f770f2fae059f92e2ac083900b241c90a2c10f0fc80e448b1feccc2b332687fab4c3e33e9f83dee9ef56badca1fb9f3f71266d9ebf
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.26.8":
  version: 7.26.8
  resolution: "@babel/compat-data@npm:7.26.8"
  checksum: 10/bdddf577f670e0e12996ef37e134856c8061032edb71a13418c3d4dae8135da28910b7cd6dec6e668ab3a41e42089ef7ee9c54ef52fe0860b54cb420b0d14948
  languageName: node
  linkType: hard

"@babel/core@npm:^7.14.0, @babel/core@npm:^7.22.9":
  version: 7.26.0
  resolution: "@babel/core@npm:7.26.0"
  dependencies:
    "@ampproject/remapping": "npm:^2.2.0"
    "@babel/code-frame": "npm:^7.26.0"
    "@babel/generator": "npm:^7.26.0"
    "@babel/helper-compilation-targets": "npm:^7.25.9"
    "@babel/helper-module-transforms": "npm:^7.26.0"
    "@babel/helpers": "npm:^7.26.0"
    "@babel/parser": "npm:^7.26.0"
    "@babel/template": "npm:^7.25.9"
    "@babel/traverse": "npm:^7.25.9"
    "@babel/types": "npm:^7.26.0"
    convert-source-map: "npm:^2.0.0"
    debug: "npm:^4.1.0"
    gensync: "npm:^1.0.0-beta.2"
    json5: "npm:^2.2.3"
    semver: "npm:^6.3.1"
  checksum: 10/65767bfdb1f02e80d3af4f138066670ef8fdd12293de85ef151758a901c191c797e86d2e99b11c4cdfca33c72385ecaf38bbd7fa692791ec44c77763496b9b93
  languageName: node
  linkType: hard

"@babel/core@npm:^7.26.10":
  version: 7.26.10
  resolution: "@babel/core@npm:7.26.10"
  dependencies:
    "@ampproject/remapping": "npm:^2.2.0"
    "@babel/code-frame": "npm:^7.26.2"
    "@babel/generator": "npm:^7.26.10"
    "@babel/helper-compilation-targets": "npm:^7.26.5"
    "@babel/helper-module-transforms": "npm:^7.26.0"
    "@babel/helpers": "npm:^7.26.10"
    "@babel/parser": "npm:^7.26.10"
    "@babel/template": "npm:^7.26.9"
    "@babel/traverse": "npm:^7.26.10"
    "@babel/types": "npm:^7.26.10"
    convert-source-map: "npm:^2.0.0"
    debug: "npm:^4.1.0"
    gensync: "npm:^1.0.0-beta.2"
    json5: "npm:^2.2.3"
    semver: "npm:^6.3.1"
  checksum: 10/68f6707eebd6bb8beed7ceccf5153e35b86c323e40d11d796d75c626ac8f1cc4e1f795584c5ab5f886bc64150c22d5088123d68c069c63f29984c4fc054d1dab
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.14.0, @babel/generator@npm:^7.18.13, @babel/generator@npm:^7.25.9":
  version: 7.26.2
  resolution: "@babel/generator@npm:7.26.2"
  dependencies:
    "@babel/parser": "npm:^7.26.2"
    "@babel/types": "npm:^7.26.0"
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    jsesc: "npm:^3.0.2"
  checksum: 10/71ace82b5b07a554846a003624bfab93275ccf73cdb9f1a37a4c1094bf9dc94bb677c67e8b8c939dbd6c5f0eda2e8f268aa2b0d9c3b9511072565660e717e045
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.26.0, @babel/generator@npm:^7.26.3":
  version: 7.26.3
  resolution: "@babel/generator@npm:7.26.3"
  dependencies:
    "@babel/parser": "npm:^7.26.3"
    "@babel/types": "npm:^7.26.3"
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    jsesc: "npm:^3.0.2"
  checksum: 10/c1d8710cc1c52af9d8d67f7d8ea775578aa500887b327d2a81e27494764a6ef99e438dd7e14cf7cd3153656492ee27a8362980dc438087c0ca39d4e75532c638
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.26.10, @babel/generator@npm:^7.27.0":
  version: 7.27.0
  resolution: "@babel/generator@npm:7.27.0"
  dependencies:
    "@babel/parser": "npm:^7.27.0"
    "@babel/types": "npm:^7.27.0"
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    jsesc: "npm:^3.0.2"
  checksum: 10/5447c402b1d841132534a0a9715e89f4f28b6f2886a23e70aaa442150dba4a1e29e4e2351814f439ee1775294dccdef9ab0a4192b6e6a5ad44e24233b3611da2
  languageName: node
  linkType: hard

"@babel/helper-annotate-as-pure@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-annotate-as-pure@npm:7.25.9"
  dependencies:
    "@babel/types": "npm:^7.25.9"
  checksum: 10/41edda10df1ae106a9b4fe617bf7c6df77db992992afd46192534f5cff29f9e49a303231733782dd65c5f9409714a529f215325569f14282046e9d3b7a1ffb6c
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.20.7, @babel/helper-compilation-targets@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-compilation-targets@npm:7.25.9"
  dependencies:
    "@babel/compat-data": "npm:^7.25.9"
    "@babel/helper-validator-option": "npm:^7.25.9"
    browserslist: "npm:^4.24.0"
    lru-cache: "npm:^5.1.1"
    semver: "npm:^6.3.1"
  checksum: 10/8053fbfc21e8297ab55c8e7f9f119e4809fa7e505268691e1bedc2cf5e7a5a7de8c60ad13da2515378621b7601c42e101d2d679904da395fa3806a1edef6b92e
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.26.5":
  version: 7.27.0
  resolution: "@babel/helper-compilation-targets@npm:7.27.0"
  dependencies:
    "@babel/compat-data": "npm:^7.26.8"
    "@babel/helper-validator-option": "npm:^7.25.9"
    browserslist: "npm:^4.24.0"
    lru-cache: "npm:^5.1.1"
    semver: "npm:^6.3.1"
  checksum: 10/32224b512e813fc808539b4ca7fca8c224849487c365abcef8cb8b0eea635c65375b81429f82d076e9ec1f3f3b3db1d0d56aac4d482a413f58d5ad608f912155
  languageName: node
  linkType: hard

"@babel/helper-create-class-features-plugin@npm:^7.18.6":
  version: 7.25.9
  resolution: "@babel/helper-create-class-features-plugin@npm:7.25.9"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.25.9"
    "@babel/helper-member-expression-to-functions": "npm:^7.25.9"
    "@babel/helper-optimise-call-expression": "npm:^7.25.9"
    "@babel/helper-replace-supers": "npm:^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.25.9"
    "@babel/traverse": "npm:^7.25.9"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/d1d47a7b5fd317c6cb1446b0e4f4892c19ddaa69ea0229f04ba8bea5f273fc8168441e7114ad36ff919f2d310f97310cec51adc79002e22039a7e1640ccaf248
  languageName: node
  linkType: hard

"@babel/helper-member-expression-to-functions@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-member-expression-to-functions@npm:7.25.9"
  dependencies:
    "@babel/traverse": "npm:^7.25.9"
    "@babel/types": "npm:^7.25.9"
  checksum: 10/ef8cc1c1e600b012b312315f843226545a1a89f25d2f474ce2503fd939ca3f8585180f291a3a13efc56cf13eddc1d41a3a040eae9a521838fd59a6d04cc82490
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-module-imports@npm:7.25.9"
  dependencies:
    "@babel/traverse": "npm:^7.25.9"
    "@babel/types": "npm:^7.25.9"
  checksum: 10/e090be5dee94dda6cd769972231b21ddfae988acd76b703a480ac0c96f3334557d70a965bf41245d6ee43891e7571a8b400ccf2b2be5803351375d0f4e5bcf08
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.25.9, @babel/helper-module-transforms@npm:^7.26.0":
  version: 7.26.0
  resolution: "@babel/helper-module-transforms@npm:7.26.0"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.25.9"
    "@babel/helper-validator-identifier": "npm:^7.25.9"
    "@babel/traverse": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/9841d2a62f61ad52b66a72d08264f23052d533afc4ce07aec2a6202adac0bfe43014c312f94feacb3291f4c5aafe681955610041ece2c276271adce3f570f2f5
  languageName: node
  linkType: hard

"@babel/helper-optimise-call-expression@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-optimise-call-expression@npm:7.25.9"
  dependencies:
    "@babel/types": "npm:^7.25.9"
  checksum: 10/f09d0ad60c0715b9a60c31841b3246b47d67650c512ce85bbe24a3124f1a4d66377df793af393273bc6e1015b0a9c799626c48e53747581c1582b99167cc65dc
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.12.13, @babel/helper-plugin-utils@npm:^7.18.6, @babel/helper-plugin-utils@npm:^7.20.2, @babel/helper-plugin-utils@npm:^7.25.9, @babel/helper-plugin-utils@npm:^7.8.0":
  version: 7.25.9
  resolution: "@babel/helper-plugin-utils@npm:7.25.9"
  checksum: 10/e347d87728b1ab10b6976d46403941c8f9008c045ea6d99997a7ffca7b852dc34b6171380f7b17edf94410e0857ff26f3a53d8618f11d73744db86e8ca9b8c64
  languageName: node
  linkType: hard

"@babel/helper-replace-supers@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-replace-supers@npm:7.25.9"
  dependencies:
    "@babel/helper-member-expression-to-functions": "npm:^7.25.9"
    "@babel/helper-optimise-call-expression": "npm:^7.25.9"
    "@babel/traverse": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/8ebf787016953e4479b99007bac735c9c860822fafc51bc3db67bc53814539888797238c81fa8b948b6da897eb7b1c1d4f04df11e501a7f0596b356be02de2ab
  languageName: node
  linkType: hard

"@babel/helper-simple-access@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-simple-access@npm:7.25.9"
  dependencies:
    "@babel/traverse": "npm:^7.25.9"
    "@babel/types": "npm:^7.25.9"
  checksum: 10/a16a6cfa5e8ac7144e856bcdaaf0022cf5de028fc0c56ce21dd664a6e900999a4285c587a209f2acf9de438c0d60bfb497f5f34aa34cbaf29da3e2f8d8d7feb7
  languageName: node
  linkType: hard

"@babel/helper-skip-transparent-expression-wrappers@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-skip-transparent-expression-wrappers@npm:7.25.9"
  dependencies:
    "@babel/traverse": "npm:^7.25.9"
    "@babel/types": "npm:^7.25.9"
  checksum: 10/fdbb5248932198bc26daa6abf0d2ac42cab9c2dbb75b7e9f40d425c8f28f09620b886d40e7f9e4e08ffc7aaa2cefe6fc2c44be7c20e81f7526634702fb615bdc
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-string-parser@npm:7.25.9"
  checksum: 10/c28656c52bd48e8c1d9f3e8e68ecafd09d949c57755b0d353739eb4eae7ba4f7e67e92e4036f1cd43378cc1397a2c943ed7bcaf5949b04ab48607def0258b775
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-validator-identifier@npm:7.25.9"
  checksum: 10/3f9b649be0c2fd457fa1957b694b4e69532a668866b8a0d81eabfa34ba16dbf3107b39e0e7144c55c3c652bf773ec816af8df4a61273a2bb4eb3145ca9cf478e
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-validator-option@npm:7.25.9"
  checksum: 10/9491b2755948ebbdd68f87da907283698e663b5af2d2b1b02a2765761974b1120d5d8d49e9175b167f16f72748ffceec8c9cf62acfbee73f4904507b246e2b3d
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.26.0":
  version: 7.26.0
  resolution: "@babel/helpers@npm:7.26.0"
  dependencies:
    "@babel/template": "npm:^7.25.9"
    "@babel/types": "npm:^7.26.0"
  checksum: 10/fd4757f65d10b64cfdbf4b3adb7ea6ffff9497c53e0786452f495d1f7794da7e0898261b4db65e1c62bbb9a360d7d78a1085635c23dfc3af2ab6dcba06585f86
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.26.10":
  version: 7.27.0
  resolution: "@babel/helpers@npm:7.27.0"
  dependencies:
    "@babel/template": "npm:^7.27.0"
    "@babel/types": "npm:^7.27.0"
  checksum: 10/0dd40ba1e5ba4b72d1763bb381384585a56f21a61a19dc1b9a03381fe8e840207fdaa4da645d14dc028ad768087d41aad46347cc6573bd69d82f597f5a12dc6f
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.14.0, @babel/parser@npm:^7.16.8, @babel/parser@npm:^7.26.2":
  version: 7.26.2
  resolution: "@babel/parser@npm:7.26.2"
  dependencies:
    "@babel/types": "npm:^7.26.0"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 10/8baee43752a3678ad9f9e360ec845065eeee806f1fdc8e0f348a8a0e13eef0959dabed4a197c978896c493ea205c804d0a1187cc52e4a1ba017c7935bab4983d
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.25.3, @babel/parser@npm:^7.25.9, @babel/parser@npm:^7.26.0, @babel/parser@npm:^7.26.3":
  version: 7.26.3
  resolution: "@babel/parser@npm:7.26.3"
  dependencies:
    "@babel/types": "npm:^7.26.3"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 10/e7e3814b2dc9ee3ed605d38223471fa7d3a84cbe9474d2b5fa7ac57dc1ddf75577b1fd3a93bf7db8f41f28869bda795cddd80223f980be23623b6434bf4c88a8
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.26.10, @babel/parser@npm:^7.27.0":
  version: 7.27.0
  resolution: "@babel/parser@npm:7.27.0"
  dependencies:
    "@babel/types": "npm:^7.27.0"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 10/0fee9f05c6db753882ca9d10958301493443da9f6986d7020ebd7a696b35886240016899bc0b47d871aea2abcafd64632343719742e87432c8145e0ec2af2a03
  languageName: node
  linkType: hard

"@babel/plugin-proposal-class-properties@npm:^7.0.0":
  version: 7.18.6
  resolution: "@babel/plugin-proposal-class-properties@npm:7.18.6"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.18.6"
    "@babel/helper-plugin-utils": "npm:^7.18.6"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/49a78a2773ec0db56e915d9797e44fd079ab8a9b2e1716e0df07c92532f2c65d76aeda9543883916b8e0ff13606afeffa67c5b93d05b607bc87653ad18a91422
  languageName: node
  linkType: hard

"@babel/plugin-proposal-object-rest-spread@npm:^7.0.0":
  version: 7.20.7
  resolution: "@babel/plugin-proposal-object-rest-spread@npm:7.20.7"
  dependencies:
    "@babel/compat-data": "npm:^7.20.5"
    "@babel/helper-compilation-targets": "npm:^7.20.7"
    "@babel/helper-plugin-utils": "npm:^7.20.2"
    "@babel/plugin-syntax-object-rest-spread": "npm:^7.8.3"
    "@babel/plugin-transform-parameters": "npm:^7.20.7"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/cb0f8f2ff98d7bb64ee91c28b20e8ab15d9bc7043f0932cbb9e51e1bbfb623b12f206a1171e070299c9cf21948c320b710d6d72a42f68a5bfd2702354113a1c5
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-properties@npm:^7.0.0":
  version: 7.12.13
  resolution: "@babel/plugin-syntax-class-properties@npm:7.12.13"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.12.13"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/24f34b196d6342f28d4bad303612d7ff566ab0a013ce89e775d98d6f832969462e7235f3e7eaf17678a533d4be0ba45d3ae34ab4e5a9dcbda5d98d49e5efa2fc
  languageName: node
  linkType: hard

"@babel/plugin-syntax-flow@npm:^7.0.0, @babel/plugin-syntax-flow@npm:^7.25.9":
  version: 7.26.0
  resolution: "@babel/plugin-syntax-flow@npm:7.26.0"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/fdc0d0a7b512e00d933e12cf93c785ea4645a193f4b539230b7601cfaa8c704410199318ce9ea14e5fca7d13e9027822f7d81a7871d3e854df26b6af04cc3c6c
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-assertions@npm:^7.20.0, @babel/plugin-syntax-import-assertions@npm:^7.26.0":
  version: 7.26.0
  resolution: "@babel/plugin-syntax-import-assertions@npm:7.26.0"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/b58f2306df4a690ca90b763d832ec05202c50af787158ff8b50cdf3354359710bce2e1eb2b5135fcabf284756ac8eadf09ca74764aa7e76d12a5cac5f6b21e67
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:^7.0.0, @babel/plugin-syntax-jsx@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-syntax-jsx@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/bb609d1ffb50b58f0c1bac8810d0e46a4f6c922aa171c458f3a19d66ee545d36e782d3bffbbc1fed0dc65a558bdce1caf5279316583c0fff5a2c1658982a8563
  languageName: node
  linkType: hard

"@babel/plugin-syntax-object-rest-spread@npm:^7.0.0, @babel/plugin-syntax-object-rest-spread@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-object-rest-spread@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/fddcf581a57f77e80eb6b981b10658421bc321ba5f0a5b754118c6a92a5448f12a0c336f77b8abf734841e102e5126d69110a306eadb03ca3e1547cab31f5cbf
  languageName: node
  linkType: hard

"@babel/plugin-transform-arrow-functions@npm:^7.0.0":
  version: 7.25.9
  resolution: "@babel/plugin-transform-arrow-functions@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/c29f081224859483accf55fb4d091db2aac0dcd0d7954bac5ca889030cc498d3f771aa20eb2e9cd8310084ec394d85fa084b97faf09298b6bc9541182b3eb5bb
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoped-functions@npm:^7.0.0":
  version: 7.25.9
  resolution: "@babel/plugin-transform-block-scoped-functions@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/bf31896556b33a80f017af3d445ceb532ec0f5ca9d69bc211a963ac92514d172d5c24c5ac319f384d9dfa7f1a4d8dc23032c2fe3e74f98a59467ecd86f7033ae
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoping@npm:^7.0.0":
  version: 7.25.9
  resolution: "@babel/plugin-transform-block-scoping@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/89dcdd7edb1e0c2f44e3c568a8ad8202e2574a8a8308248550a9391540bc3f5c9fbd8352c60ae90769d46f58d3ab36f2c3a0fbc1c3620813d92ff6fccdfa79c8
  languageName: node
  linkType: hard

"@babel/plugin-transform-classes@npm:^7.0.0":
  version: 7.25.9
  resolution: "@babel/plugin-transform-classes@npm:7.25.9"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.25.9"
    "@babel/helper-compilation-targets": "npm:^7.25.9"
    "@babel/helper-plugin-utils": "npm:^7.25.9"
    "@babel/helper-replace-supers": "npm:^7.25.9"
    "@babel/traverse": "npm:^7.25.9"
    globals: "npm:^11.1.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/1914ebe152f35c667fba7bf17ce0d9d0f33df2fb4491990ce9bb1f9ec5ae8cbd11d95b0dc371f7a4cc5e7ce4cf89467c3e34857302911fc6bfb6494a77f7b37e
  languageName: node
  linkType: hard

"@babel/plugin-transform-computed-properties@npm:^7.0.0":
  version: 7.25.9
  resolution: "@babel/plugin-transform-computed-properties@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
    "@babel/template": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/aa1a9064d6a9d3b569b8cae6972437315a38a8f6553ee618406da5122500a06c2f20b9fa93aeed04dd895923bf6f529c09fc79d4be987ec41785ceb7d2203122
  languageName: node
  linkType: hard

"@babel/plugin-transform-destructuring@npm:^7.0.0":
  version: 7.25.9
  resolution: "@babel/plugin-transform-destructuring@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/51b24fbead910ad0547463b2d214dd08076b22a66234b9f878b8bac117603dd23e05090ff86e9ffc373214de23d3e5bf1b095fe54cce2ca16b010264d90cf4f5
  languageName: node
  linkType: hard

"@babel/plugin-transform-flow-strip-types@npm:^7.0.0":
  version: 7.25.9
  resolution: "@babel/plugin-transform-flow-strip-types@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
    "@babel/plugin-syntax-flow": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/a3ffc76bbc922720debe973bccb501ccbda0d6d32d80c9efd599ab1b683fd72cae3198975d8609b37070fc32f921a9eb7d2db17b7b719395468773be41011822
  languageName: node
  linkType: hard

"@babel/plugin-transform-for-of@npm:^7.0.0":
  version: 7.25.9
  resolution: "@babel/plugin-transform-for-of@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/63a2db7fe06c2e3f5fc1926f478dac66a5f7b3eaeb4a0ffae577e6f3cb3d822cb1ed2ed3798f70f5cb1aa06bc2ad8bcd1f557342f5c425fd83c37a8fc1cfd2ba
  languageName: node
  linkType: hard

"@babel/plugin-transform-function-name@npm:^7.0.0":
  version: 7.25.9
  resolution: "@babel/plugin-transform-function-name@npm:7.25.9"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.25.9"
    "@babel/helper-plugin-utils": "npm:^7.25.9"
    "@babel/traverse": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/a8d7c8d019a6eb57eab5ca1be3e3236f175557d55b1f3b11f8ad7999e3fbb1cf37905fd8cb3a349bffb4163a558e9f33b63f631597fdc97c858757deac1b2fd7
  languageName: node
  linkType: hard

"@babel/plugin-transform-literals@npm:^7.0.0":
  version: 7.25.9
  resolution: "@babel/plugin-transform-literals@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/3cca75823a38aab599bc151b0fa4d816b5e1b62d6e49c156aa90436deb6e13649f5505973151a10418b64f3f9d1c3da53e38a186402e0ed7ad98e482e70c0c14
  languageName: node
  linkType: hard

"@babel/plugin-transform-member-expression-literals@npm:^7.0.0":
  version: 7.25.9
  resolution: "@babel/plugin-transform-member-expression-literals@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/db92041ae87b8f59f98b50359e0bb172480f6ba22e5e76b13bdfe07122cbf0daa9cd8ad2e78dcb47939938fed88ad57ab5989346f64b3a16953fc73dea3a9b1f
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-commonjs@npm:^7.0.0":
  version: 7.25.9
  resolution: "@babel/plugin-transform-modules-commonjs@npm:7.25.9"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.25.9"
    "@babel/helper-plugin-utils": "npm:^7.25.9"
    "@babel/helper-simple-access": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/a7390ca999373ccdef91075f274d1ace3a5cb79f9b9118ed6f76e94867ed454cf798a6f312ce2c4cdc1e035a25d810d754e4cb2e4d866acb4219490f3585de60
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-super@npm:^7.0.0":
  version: 7.25.9
  resolution: "@babel/plugin-transform-object-super@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
    "@babel/helper-replace-supers": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/1817b5d8b80e451ae1ad9080cca884f4f16df75880a158947df76a2ed8ab404d567a7dce71dd8051ef95f90fbe3513154086a32aba55cc76027f6cbabfbd7f98
  languageName: node
  linkType: hard

"@babel/plugin-transform-parameters@npm:^7.0.0, @babel/plugin-transform-parameters@npm:^7.20.7":
  version: 7.25.9
  resolution: "@babel/plugin-transform-parameters@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/014009a1763deb41fe9f0dbca2c4489ce0ac83dd87395f488492e8eb52399f6c883d5bd591bae3b8836f2460c3937fcebd07e57dce1e0bfe30cdbc63fdfc9d3a
  languageName: node
  linkType: hard

"@babel/plugin-transform-property-literals@npm:^7.0.0":
  version: 7.25.9
  resolution: "@babel/plugin-transform-property-literals@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/436046ab07d54a9b44a384eeffec701d4e959a37a7547dda72e069e751ca7ff753d1782a8339e354b97c78a868b49ea97bf41bf5a44c6d7a3c0a05ad40eeb49c
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-display-name@npm:^7.0.0":
  version: 7.25.9
  resolution: "@babel/plugin-transform-react-display-name@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/dc7affde0ed98e40f629ee92a2fc44fbd8008aabda1ddb3f5bd2632699d3289b08dff65b26cf3b89dab46397ec440f453d19856bbb3a9a83df5b4ac6157c5c39
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx@npm:^7.0.0":
  version: 7.25.9
  resolution: "@babel/plugin-transform-react-jsx@npm:7.25.9"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.25.9"
    "@babel/helper-module-imports": "npm:^7.25.9"
    "@babel/helper-plugin-utils": "npm:^7.25.9"
    "@babel/plugin-syntax-jsx": "npm:^7.25.9"
    "@babel/types": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/eb179ecdf0ae19aed254105cf78fbac35f9983f51ed04b7b67c863a4820a70a879bd5da250ac518321f86df20eac010e53e3411c8750c386d51da30e4814bfb6
  languageName: node
  linkType: hard

"@babel/plugin-transform-shorthand-properties@npm:^7.0.0":
  version: 7.25.9
  resolution: "@babel/plugin-transform-shorthand-properties@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/f774995d58d4e3a992b732cf3a9b8823552d471040e280264dd15e0735433d51b468fef04d75853d061309389c66bda10ce1b298297ce83999220eb0ad62741d
  languageName: node
  linkType: hard

"@babel/plugin-transform-spread@npm:^7.0.0":
  version: 7.25.9
  resolution: "@babel/plugin-transform-spread@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/fe72c6545267176cdc9b6f32f30f9ced37c1cafa1290e4436b83b8f377b4f1c175dad404228c96e3efdec75da692f15bfb9db2108fcd9ad260bc9968778ee41e
  languageName: node
  linkType: hard

"@babel/plugin-transform-template-literals@npm:^7.0.0":
  version: 7.25.9
  resolution: "@babel/plugin-transform-template-literals@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10/92eb1d6e2d95bd24abbb74fa7640d02b66ff6214e0bb616d7fda298a7821ce15132a4265d576a3502a347a3c9e94b6c69ed265bb0784664592fa076785a3d16a
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.0.0, @babel/runtime@npm:^7.14.0":
  version: 7.26.0
  resolution: "@babel/runtime@npm:7.26.0"
  dependencies:
    regenerator-runtime: "npm:^0.14.0"
  checksum: 10/9f4ea1c1d566c497c052d505587554e782e021e6ccd302c2ad7ae8291c8e16e3f19d4a7726fb64469e057779ea2081c28b7dbefec6d813a22f08a35712c0f699
  languageName: node
  linkType: hard

"@babel/template@npm:^7.18.10, @babel/template@npm:^7.20.7, @babel/template@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/template@npm:7.25.9"
  dependencies:
    "@babel/code-frame": "npm:^7.25.9"
    "@babel/parser": "npm:^7.25.9"
    "@babel/types": "npm:^7.25.9"
  checksum: 10/e861180881507210150c1335ad94aff80fd9e9be6202e1efa752059c93224e2d5310186ddcdd4c0f0b0fc658ce48cb47823f15142b5c00c8456dde54f5de80b2
  languageName: node
  linkType: hard

"@babel/template@npm:^7.26.9, @babel/template@npm:^7.27.0":
  version: 7.27.0
  resolution: "@babel/template@npm:7.27.0"
  dependencies:
    "@babel/code-frame": "npm:^7.26.2"
    "@babel/parser": "npm:^7.27.0"
    "@babel/types": "npm:^7.27.0"
  checksum: 10/7159ca1daea287ad34676d45a7146675444d42c7664aca3e617abc9b1d9548c8f377f35a36bb34cf956e1d3610dcb7acfcfe890aebf81880d35f91a7bd273ee5
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.14.0, @babel/traverse@npm:^7.16.8":
  version: 7.25.9
  resolution: "@babel/traverse@npm:7.25.9"
  dependencies:
    "@babel/code-frame": "npm:^7.25.9"
    "@babel/generator": "npm:^7.25.9"
    "@babel/parser": "npm:^7.25.9"
    "@babel/template": "npm:^7.25.9"
    "@babel/types": "npm:^7.25.9"
    debug: "npm:^4.3.1"
    globals: "npm:^11.1.0"
  checksum: 10/7431614d76d4a053e429208db82f2846a415833f3d9eb2e11ef72eeb3c64dfd71f4a4d983de1a4a047b36165a1f5a64de8ca2a417534cc472005c740ffcb9c6a
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.25.9":
  version: 7.26.4
  resolution: "@babel/traverse@npm:7.26.4"
  dependencies:
    "@babel/code-frame": "npm:^7.26.2"
    "@babel/generator": "npm:^7.26.3"
    "@babel/parser": "npm:^7.26.3"
    "@babel/template": "npm:^7.25.9"
    "@babel/types": "npm:^7.26.3"
    debug: "npm:^4.3.1"
    globals: "npm:^11.1.0"
  checksum: 10/30c81a80d66fc39842814bc2e847f4705d30f3859156f130d90a0334fe1d53aa81eed877320141a528ecbc36448acc0f14f544a7d410fa319d1c3ab63b50b58f
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.26.10":
  version: 7.27.0
  resolution: "@babel/traverse@npm:7.27.0"
  dependencies:
    "@babel/code-frame": "npm:^7.26.2"
    "@babel/generator": "npm:^7.27.0"
    "@babel/parser": "npm:^7.27.0"
    "@babel/template": "npm:^7.27.0"
    "@babel/types": "npm:^7.27.0"
    debug: "npm:^4.3.1"
    globals: "npm:^11.1.0"
  checksum: 10/b0675bc16bd87187e8b090557b0650135de56a621692ad8614b20f32621350ae0fc2e1129b73b780d64a9ed4beab46849a17f90d5267b6ae6ce09ec8412a12c7
  languageName: node
  linkType: hard

"@babel/types@npm:^7.0.0, @babel/types@npm:^7.25.9, @babel/types@npm:^7.26.0, @babel/types@npm:^7.26.3":
  version: 7.26.3
  resolution: "@babel/types@npm:7.26.3"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.25.9"
    "@babel/helper-validator-identifier": "npm:^7.25.9"
  checksum: 10/c31d0549630a89abfa11410bf82a318b0c87aa846fbf5f9905e47ba5e2aa44f41cc746442f105d622c519e4dc532d35a8d8080460ff4692f9fc7485fbf3a00eb
  languageName: node
  linkType: hard

"@babel/types@npm:^7.16.8, @babel/types@npm:^7.18.13":
  version: 7.26.0
  resolution: "@babel/types@npm:7.26.0"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.25.9"
    "@babel/helper-validator-identifier": "npm:^7.25.9"
  checksum: 10/40780741ecec886ed9edae234b5eb4976968cc70d72b4e5a40d55f83ff2cc457de20f9b0f4fe9d858350e43dab0ea496e7ef62e2b2f08df699481a76df02cd6e
  languageName: node
  linkType: hard

"@babel/types@npm:^7.26.10, @babel/types@npm:^7.27.0":
  version: 7.27.0
  resolution: "@babel/types@npm:7.27.0"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.25.9"
    "@babel/helper-validator-identifier": "npm:^7.25.9"
  checksum: 10/2c322bce107c8a534dc4a23be60d570e6a4cc7ca2e44d4f0eee08c0b626104eb7e60ab8de03463bc5da1773a2f69f1e6edec1648d648d65461d6520a7f3b0770
  languageName: node
  linkType: hard

"@bd/cd-system3@npm:^11.3.2":
  version: 11.3.2
  resolution: "@bd/cd-system3@npm:11.3.2"
  dependencies:
    "@mdi/font": "npm:^7.4.47"
    "@mdi/js": "npm:^7.4.47"
    "@vueuse/core": "npm:^10.11.1"
    vue-component-type-helpers: "npm:^2.2.0"
    vue-i18n: "npm:^10.0.5"
    webfontloader: "npm:^1.6.28"
  peerDependencies:
    vue: ^3.5.13
    vuetify: ^3.7.7
  checksum: 10/88cebe9751841e8776174c562625c27a131623d5af3c8c7e965c3389dc00d206b3b828d97fa97265ab021555f1c5eb5e085dfd3cd49e10f550776eb06c793218
  languageName: node
  linkType: hard

"@csstools/color-helpers@npm:^5.0.1":
  version: 5.0.1
  resolution: "@csstools/color-helpers@npm:5.0.1"
  checksum: 10/4cb25b34997c9b0e9f401833e27942636494bc3c7fda5c6633026bc3fdfdda1c67be68ea048058bfba449a86ec22332e23b4ec5982452c50b67880c4cb13a660
  languageName: node
  linkType: hard

"@csstools/css-calc@npm:^2.1.1":
  version: 2.1.2
  resolution: "@csstools/css-calc@npm:2.1.2"
  peerDependencies:
    "@csstools/css-parser-algorithms": ^3.0.4
    "@csstools/css-tokenizer": ^3.0.3
  checksum: 10/23ba633b15ba733f9da6d65e6a97a34116d10add7df15f6b05df93f00bb47b335a2268fcfd93c442da5d4678706f7bb26ffcc26a74621e34fe0d399bb27e53d3
  languageName: node
  linkType: hard

"@csstools/css-color-parser@npm:^3.0.7":
  version: 3.0.7
  resolution: "@csstools/css-color-parser@npm:3.0.7"
  dependencies:
    "@csstools/color-helpers": "npm:^5.0.1"
    "@csstools/css-calc": "npm:^2.1.1"
  peerDependencies:
    "@csstools/css-parser-algorithms": ^3.0.4
    "@csstools/css-tokenizer": ^3.0.3
  checksum: 10/efceb60608f3fc2b6da44d5be7720a8b302e784f05c1c12f17a1da4b4b9893b2e20d0ea74ac2c2d6d5ca9b64ee046d05f803c7b78581fd5a3f85e78acfc5d98e
  languageName: node
  linkType: hard

"@csstools/css-parser-algorithms@npm:^3.0.4":
  version: 3.0.4
  resolution: "@csstools/css-parser-algorithms@npm:3.0.4"
  peerDependencies:
    "@csstools/css-tokenizer": ^3.0.3
  checksum: 10/dfb6926218d9f8ba25d8b43ea46c03863c819481f8c55e4de4925780eaab9e6bcd6bead1d56b4ef82d09fcd9d69a7db2750fa9db08eece9470fd499dc76d0edb
  languageName: node
  linkType: hard

"@csstools/css-tokenizer@npm:^3.0.3":
  version: 3.0.3
  resolution: "@csstools/css-tokenizer@npm:3.0.3"
  checksum: 10/6baa3160e426e1f177b8f10d54ec7a4a596090f65a05f16d7e9e4da049962a404eabc5f885f4867093702c259cd4080ac92a438326e22dea015201b3e71f5bbb
  languageName: node
  linkType: hard

"@esbuild/aix-ppc64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/aix-ppc64@npm:0.24.2"
  conditions: os=aix & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/android-arm64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/android-arm64@npm:0.24.2"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/android-arm@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/android-arm@npm:0.24.2"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/android-x64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/android-x64@npm:0.24.2"
  conditions: os=android & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/darwin-arm64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/darwin-arm64@npm:0.24.2"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/darwin-x64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/darwin-x64@npm:0.24.2"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/freebsd-arm64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/freebsd-arm64@npm:0.24.2"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/freebsd-x64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/freebsd-x64@npm:0.24.2"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/linux-arm64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/linux-arm64@npm:0.24.2"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/linux-arm@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/linux-arm@npm:0.24.2"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/linux-ia32@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/linux-ia32@npm:0.24.2"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/linux-loong64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/linux-loong64@npm:0.24.2"
  conditions: os=linux & cpu=loong64
  languageName: node
  linkType: hard

"@esbuild/linux-mips64el@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/linux-mips64el@npm:0.24.2"
  conditions: os=linux & cpu=mips64el
  languageName: node
  linkType: hard

"@esbuild/linux-ppc64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/linux-ppc64@npm:0.24.2"
  conditions: os=linux & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/linux-riscv64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/linux-riscv64@npm:0.24.2"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"@esbuild/linux-s390x@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/linux-s390x@npm:0.24.2"
  conditions: os=linux & cpu=s390x
  languageName: node
  linkType: hard

"@esbuild/linux-x64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/linux-x64@npm:0.24.2"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/netbsd-arm64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/netbsd-arm64@npm:0.24.2"
  conditions: os=netbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/netbsd-x64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/netbsd-x64@npm:0.24.2"
  conditions: os=netbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/openbsd-arm64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/openbsd-arm64@npm:0.24.2"
  conditions: os=openbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/openbsd-x64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/openbsd-x64@npm:0.24.2"
  conditions: os=openbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/sunos-x64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/sunos-x64@npm:0.24.2"
  conditions: os=sunos & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/win32-arm64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/win32-arm64@npm:0.24.2"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/win32-ia32@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/win32-ia32@npm:0.24.2"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/win32-x64@npm:0.24.2":
  version: 0.24.2
  resolution: "@esbuild/win32-x64@npm:0.24.2"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0, @eslint-community/eslint-utils@npm:^4.4.0":
  version: 4.4.1
  resolution: "@eslint-community/eslint-utils@npm:4.4.1"
  dependencies:
    eslint-visitor-keys: "npm:^3.4.3"
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: 10/ae92a11412674329b4bd38422518601ec9ceae28e251104d1cad83715da9d38e321f68c817c39b64e66d0af7d98df6f9a10ad2dc638911254b47fb8932df00ef
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.10.0, @eslint-community/regexpp@npm:^4.12.1":
  version: 4.12.1
  resolution: "@eslint-community/regexpp@npm:4.12.1"
  checksum: 10/c08f1dd7dd18fbb60bdd0d85820656d1374dd898af9be7f82cb00451313402a22d5e30569c150315b4385907cdbca78c22389b2a72ab78883b3173be317620cc
  languageName: node
  linkType: hard

"@eslint/compat@npm:1.2.4":
  version: 1.2.4
  resolution: "@eslint/compat@npm:1.2.4"
  peerDependencies:
    eslint: ^9.10.0
  peerDependenciesMeta:
    eslint:
      optional: true
  checksum: 10/872ac21e3f430575ba70916d83f5a4e7e9cc7fa953111c99ecef225d1ed05b66fbdb5034761dd9035f00c3f0d7ca7657f8cbfa4ff7ead3967f630c8c783d2beb
  languageName: node
  linkType: hard

"@eslint/config-array@npm:^0.19.0":
  version: 0.19.1
  resolution: "@eslint/config-array@npm:0.19.1"
  dependencies:
    "@eslint/object-schema": "npm:^2.1.5"
    debug: "npm:^4.3.1"
    minimatch: "npm:^3.1.2"
  checksum: 10/1243b01f463de85c970c18f0994f9d1850dafe8cc8c910edb64105d845edd3cacaa0bbf028bf35a6daaf5a179021140b6a8b1dc7a2f915b42c2d35f022a9c201
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.9.0":
  version: 0.9.1
  resolution: "@eslint/core@npm:0.9.1"
  dependencies:
    "@types/json-schema": "npm:^7.0.15"
  checksum: 10/f2263f8f94fdf84fc34573e027de98f1fce6287120513ae672ddf0652c75b9fa77c314d565628fc58e0a6f959766acc34c8191f9b94f1757b910408ffa04adde
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^3.0.0, @eslint/eslintrc@npm:^3.2.0":
  version: 3.2.0
  resolution: "@eslint/eslintrc@npm:3.2.0"
  dependencies:
    ajv: "npm:^6.12.4"
    debug: "npm:^4.3.2"
    espree: "npm:^10.0.1"
    globals: "npm:^14.0.0"
    ignore: "npm:^5.2.0"
    import-fresh: "npm:^3.2.1"
    js-yaml: "npm:^4.1.0"
    minimatch: "npm:^3.1.2"
    strip-json-comments: "npm:^3.1.1"
  checksum: 10/b32dd90ce7da68e89b88cd729db46b27aac79a2e6cb1fa75d25a6b766d586b443bfbf59622489efbd3c6f696f147b51111e81ec7cd23d70f215c5d474cad0261
  languageName: node
  linkType: hard

"@eslint/js@npm:9.17.0":
  version: 9.17.0
  resolution: "@eslint/js@npm:9.17.0"
  checksum: 10/1a89e62f5c50e75d44565b7f3b91701455a999132c991e10bac59c118fbb54bdd54be22b9bda1ac730f78a2e64604403d65ce5dd7726d80b2632982cfc3d84ac
  languageName: node
  linkType: hard

"@eslint/object-schema@npm:^2.1.5":
  version: 2.1.5
  resolution: "@eslint/object-schema@npm:2.1.5"
  checksum: 10/bb07ec53357047f20de923bcd61f0306d9eee83ef41daa32e633e154a44796b5bd94670169eccb8fd8cb4ff42228a43b86953a6321f789f98194baba8207b640
  languageName: node
  linkType: hard

"@eslint/plugin-kit@npm:^0.2.3":
  version: 0.2.4
  resolution: "@eslint/plugin-kit@npm:0.2.4"
  dependencies:
    levn: "npm:^0.4.1"
  checksum: 10/e34d02ea1dccd716e51369620263a4b2167aff3c0510ed776e21336cc3ad7158087449a76931baf07cdc33810cb6919db375f2e9f409435d2c6e0dd5f4786b25
  languageName: node
  linkType: hard

"@graphql-codegen/add@npm:^5.0.3":
  version: 5.0.3
  resolution: "@graphql-codegen/add@npm:5.0.3"
  dependencies:
    "@graphql-codegen/plugin-helpers": "npm:^5.0.3"
    tslib: "npm:~2.6.0"
  peerDependencies:
    graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0
  checksum: 10/5e4ea9b5d76e6f472015185d0d007abf38ba7b27fb613b163ef1ca6c732695951111721896ee36894d098b27a67c55a84d000b503db93b9d9e6713e34d2fa5a6
  languageName: node
  linkType: hard

"@graphql-codegen/cli@npm:5.0.3":
  version: 5.0.3
  resolution: "@graphql-codegen/cli@npm:5.0.3"
  dependencies:
    "@babel/generator": "npm:^7.18.13"
    "@babel/template": "npm:^7.18.10"
    "@babel/types": "npm:^7.18.13"
    "@graphql-codegen/client-preset": "npm:^4.4.0"
    "@graphql-codegen/core": "npm:^4.0.2"
    "@graphql-codegen/plugin-helpers": "npm:^5.0.3"
    "@graphql-tools/apollo-engine-loader": "npm:^8.0.0"
    "@graphql-tools/code-file-loader": "npm:^8.0.0"
    "@graphql-tools/git-loader": "npm:^8.0.0"
    "@graphql-tools/github-loader": "npm:^8.0.0"
    "@graphql-tools/graphql-file-loader": "npm:^8.0.0"
    "@graphql-tools/json-file-loader": "npm:^8.0.0"
    "@graphql-tools/load": "npm:^8.0.0"
    "@graphql-tools/prisma-loader": "npm:^8.0.0"
    "@graphql-tools/url-loader": "npm:^8.0.0"
    "@graphql-tools/utils": "npm:^10.0.0"
    "@whatwg-node/fetch": "npm:^0.9.20"
    chalk: "npm:^4.1.0"
    cosmiconfig: "npm:^8.1.3"
    debounce: "npm:^1.2.0"
    detect-indent: "npm:^6.0.0"
    graphql-config: "npm:^5.1.1"
    inquirer: "npm:^8.0.0"
    is-glob: "npm:^4.0.1"
    jiti: "npm:^1.17.1"
    json-to-pretty-yaml: "npm:^1.2.2"
    listr2: "npm:^4.0.5"
    log-symbols: "npm:^4.0.0"
    micromatch: "npm:^4.0.5"
    shell-quote: "npm:^1.7.3"
    string-env-interpolation: "npm:^1.0.1"
    ts-log: "npm:^2.2.3"
    tslib: "npm:^2.4.0"
    yaml: "npm:^2.3.1"
    yargs: "npm:^17.0.0"
  peerDependencies:
    "@parcel/watcher": ^2.1.0
    graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0
  peerDependenciesMeta:
    "@parcel/watcher":
      optional: true
  bin:
    gql-gen: cjs/bin.js
    graphql-code-generator: cjs/bin.js
    graphql-codegen: cjs/bin.js
    graphql-codegen-esm: esm/bin.js
  checksum: 10/c3359668f824246e78656d26af506b5b279d50e08a56f54db87da492bd4d0a8e8b6540a6119402d7f5026c137babfd79e628897c6038e199ee6322f688eec757
  languageName: node
  linkType: hard

"@graphql-codegen/client-preset@npm:4.5.1, @graphql-codegen/client-preset@npm:^4.4.0":
  version: 4.5.1
  resolution: "@graphql-codegen/client-preset@npm:4.5.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.20.2"
    "@babel/template": "npm:^7.20.7"
    "@graphql-codegen/add": "npm:^5.0.3"
    "@graphql-codegen/gql-tag-operations": "npm:4.0.12"
    "@graphql-codegen/plugin-helpers": "npm:^5.1.0"
    "@graphql-codegen/typed-document-node": "npm:^5.0.12"
    "@graphql-codegen/typescript": "npm:^4.1.2"
    "@graphql-codegen/typescript-operations": "npm:^4.4.0"
    "@graphql-codegen/visitor-plugin-common": "npm:^5.6.0"
    "@graphql-tools/documents": "npm:^1.0.0"
    "@graphql-tools/utils": "npm:^10.0.0"
    "@graphql-typed-document-node/core": "npm:3.2.0"
    tslib: "npm:~2.6.0"
  peerDependencies:
    graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0
  checksum: 10/4f8160f471609356829ccaccbb5f13fdef2da93ef074adb339acd82c7894b6ce51f997b21fd673be58358953ebab22fc6d2a2a4e21543d4713e42d7adbdfec5e
  languageName: node
  linkType: hard

"@graphql-codegen/core@npm:^4.0.2":
  version: 4.0.2
  resolution: "@graphql-codegen/core@npm:4.0.2"
  dependencies:
    "@graphql-codegen/plugin-helpers": "npm:^5.0.3"
    "@graphql-tools/schema": "npm:^10.0.0"
    "@graphql-tools/utils": "npm:^10.0.0"
    tslib: "npm:~2.6.0"
  peerDependencies:
    graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0
  checksum: 10/09aa9d5b3215b7c8a81e07d6c826fa9697e4d20c7fa4333905aa89afe88044ce5c733633a59c6590fc997f03a6f62f9aecf76d6c1efa4f1a16c5ad2b0b6f665b
  languageName: node
  linkType: hard

"@graphql-codegen/gql-tag-operations@npm:4.0.12":
  version: 4.0.12
  resolution: "@graphql-codegen/gql-tag-operations@npm:4.0.12"
  dependencies:
    "@graphql-codegen/plugin-helpers": "npm:^5.1.0"
    "@graphql-codegen/visitor-plugin-common": "npm:5.6.0"
    "@graphql-tools/utils": "npm:^10.0.0"
    auto-bind: "npm:~4.0.0"
    tslib: "npm:~2.6.0"
  peerDependencies:
    graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0
  checksum: 10/21445bebc7759e1da5044647c70fb5c62441f5064595bf2b7f27d832ff6a08d40bd5363fb69a280639936eddb8ec620da3d12b01457b368ea4b015bc6f52de49
  languageName: node
  linkType: hard

"@graphql-codegen/plugin-helpers@npm:^5.0.3, @graphql-codegen/plugin-helpers@npm:^5.1.0":
  version: 5.1.0
  resolution: "@graphql-codegen/plugin-helpers@npm:5.1.0"
  dependencies:
    "@graphql-tools/utils": "npm:^10.0.0"
    change-case-all: "npm:1.0.15"
    common-tags: "npm:1.8.2"
    import-from: "npm:4.0.0"
    lodash: "npm:~4.17.0"
    tslib: "npm:~2.6.0"
  peerDependencies:
    graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0
  checksum: 10/415e79be90a1f5d289c9cd7f0a581c277d544be1f7136d7f74f5f067c205eb35fd6cd522455866fa8105f241eec4c77bebe02eef007d5021a7b7a453b85b2001
  languageName: node
  linkType: hard

"@graphql-codegen/schema-ast@npm:^4.0.2":
  version: 4.1.0
  resolution: "@graphql-codegen/schema-ast@npm:4.1.0"
  dependencies:
    "@graphql-codegen/plugin-helpers": "npm:^5.0.3"
    "@graphql-tools/utils": "npm:^10.0.0"
    tslib: "npm:~2.6.0"
  peerDependencies:
    graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0
  checksum: 10/cddec7723d708990ac8e33eb8935e72545b60ed7b772452ba45b60e577af950d23503de83f0919d1730f7d52dcb970900d3587d9a54202032164ba3c246d4c10
  languageName: node
  linkType: hard

"@graphql-codegen/typed-document-node@npm:^5.0.12":
  version: 5.0.12
  resolution: "@graphql-codegen/typed-document-node@npm:5.0.12"
  dependencies:
    "@graphql-codegen/plugin-helpers": "npm:^5.1.0"
    "@graphql-codegen/visitor-plugin-common": "npm:5.6.0"
    auto-bind: "npm:~4.0.0"
    change-case-all: "npm:1.0.15"
    tslib: "npm:~2.6.0"
  peerDependencies:
    graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0
  checksum: 10/68d5e284649e7c545910d3cfb479c26803124a2b777caf50e365e2495614d4e1c3cce533dd0186e53fce0f7487d609c69514f392572280cd6d40f4b1ba0c75c2
  languageName: node
  linkType: hard

"@graphql-codegen/typescript-operations@npm:^4.4.0":
  version: 4.4.0
  resolution: "@graphql-codegen/typescript-operations@npm:4.4.0"
  dependencies:
    "@graphql-codegen/plugin-helpers": "npm:^5.1.0"
    "@graphql-codegen/typescript": "npm:^4.1.2"
    "@graphql-codegen/visitor-plugin-common": "npm:5.6.0"
    auto-bind: "npm:~4.0.0"
    tslib: "npm:~2.6.0"
  peerDependencies:
    graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0
  checksum: 10/9b4d3dfe2641ee8b8f06a004af733fca05f93a8b8274f44296f61a43e313d94954cd2fcfeb9dc63e852116fc7e017b93cce94ca49fa433025412efc185a61323
  languageName: node
  linkType: hard

"@graphql-codegen/typescript@npm:^4.1.2":
  version: 4.1.2
  resolution: "@graphql-codegen/typescript@npm:4.1.2"
  dependencies:
    "@graphql-codegen/plugin-helpers": "npm:^5.1.0"
    "@graphql-codegen/schema-ast": "npm:^4.0.2"
    "@graphql-codegen/visitor-plugin-common": "npm:5.6.0"
    auto-bind: "npm:~4.0.0"
    tslib: "npm:~2.6.0"
  peerDependencies:
    graphql: ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0
  checksum: 10/a0a853a403df6b5a4e4a3d342fad86bb5daaa6aaa3b10c922529e43efe8b38e9bf95f17d4086698dfa30efc8d94aef85f4ac890f80107ce11a67aa1db76e1ca4
  languageName: node
  linkType: hard

"@graphql-codegen/visitor-plugin-common@npm:5.6.0, @graphql-codegen/visitor-plugin-common@npm:^5.6.0":
  version: 5.6.0
  resolution: "@graphql-codegen/visitor-plugin-common@npm:5.6.0"
  dependencies:
    "@graphql-codegen/plugin-helpers": "npm:^5.1.0"
    "@graphql-tools/optimize": "npm:^2.0.0"
    "@graphql-tools/relay-operation-optimizer": "npm:^7.0.0"
    "@graphql-tools/utils": "npm:^10.0.0"
    auto-bind: "npm:~4.0.0"
    change-case-all: "npm:1.0.15"
    dependency-graph: "npm:^0.11.0"
    graphql-tag: "npm:^2.11.0"
    parse-filepath: "npm:^1.0.2"
    tslib: "npm:~2.6.0"
  peerDependencies:
    graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0
  checksum: 10/3e398546cc219575a8a4cd10f554148a3db94aef4752c28cc6897b81b40caa7e42899680b65f7b57288b61f076f2aca71d90f7acdd5e05b47b8aca3c9e0f0146
  languageName: node
  linkType: hard

"@graphql-eslint/eslint-plugin@npm:^4.4.0":
  version: 4.4.0
  resolution: "@graphql-eslint/eslint-plugin@npm:4.4.0"
  dependencies:
    "@graphql-tools/code-file-loader": "npm:^8.0.0"
    "@graphql-tools/graphql-tag-pluck": "npm:^8.3.4"
    "@graphql-tools/utils": "npm:^10.0.0"
    debug: "npm:^4.3.4"
    fast-glob: "npm:^3.2.12"
    graphql-config: "npm:^5.1.3"
    graphql-depth-limit: "npm:^1.1.0"
    lodash.lowercase: "npm:^4.3.0"
  peerDependencies:
    "@apollo/subgraph": ^2
    eslint: ">=8.44.0"
    graphql: ^16
    json-schema-to-ts: ^3
  peerDependenciesMeta:
    "@apollo/subgraph":
      optional: true
    json-schema-to-ts:
      optional: true
  checksum: 10/37c16d0690cd345562a8896da3511883c5811e44d1eec0230a01d59682e7231687c685c7c713cd9c9f5ef4181e576bcb645844a3e49328d70aaffeb49a29e327
  languageName: node
  linkType: hard

"@graphql-tools/apollo-engine-loader@npm:^8.0.0":
  version: 8.0.6
  resolution: "@graphql-tools/apollo-engine-loader@npm:8.0.6"
  dependencies:
    "@ardatan/sync-fetch": "npm:^0.0.1"
    "@graphql-tools/utils": "npm:^10.6.1"
    "@whatwg-node/fetch": "npm:^0.10.0"
    tslib: "npm:^2.4.0"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/f50a3eb1f0e7eddce15c7b70a8d053afdcb0ccdb59e9784f05ac3236e56019894ea29ba4862e7fed47c46d2a548d9828260dd8b62ecb0e7401979cd511ac1251
  languageName: node
  linkType: hard

"@graphql-tools/batch-execute@npm:^9.0.7":
  version: 9.0.7
  resolution: "@graphql-tools/batch-execute@npm:9.0.7"
  dependencies:
    "@graphql-tools/utils": "npm:^10.6.0"
    dataloader: "npm:^2.2.2"
    tslib: "npm:^2.4.0"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/1a24485a86ff27aa7a77085b07f9277fa61b76565eb48c207574afeeaad80106600c5b74f2f129ac01f7a9523c0504ad62c311b9152677a130e33d63c939d2d3
  languageName: node
  linkType: hard

"@graphql-tools/code-file-loader@npm:^8.0.0":
  version: 8.1.7
  resolution: "@graphql-tools/code-file-loader@npm:8.1.7"
  dependencies:
    "@graphql-tools/graphql-tag-pluck": "npm:8.3.6"
    "@graphql-tools/utils": "npm:^10.6.1"
    globby: "npm:^11.0.3"
    tslib: "npm:^2.4.0"
    unixify: "npm:^1.0.0"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/c365b7862cd682e3dc68552a16a4c625b883d247ef52facd38574fa6e0f3efb678f92a4576df342b807f981c9fbfb9deede269c59f332285765cdc5c4337b90e
  languageName: node
  linkType: hard

"@graphql-tools/delegate@npm:^10.2.3":
  version: 10.2.3
  resolution: "@graphql-tools/delegate@npm:10.2.3"
  dependencies:
    "@graphql-tools/batch-execute": "npm:^9.0.7"
    "@graphql-tools/executor": "npm:^1.3.3"
    "@graphql-tools/schema": "npm:^10.0.8"
    "@graphql-tools/utils": "npm:^10.6.0"
    "@repeaterjs/repeater": "npm:^3.0.6"
    dataloader: "npm:^2.2.2"
    dset: "npm:^3.1.2"
    tslib: "npm:^2.5.0"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/d37759315f28e8f7fd7e1bafeadee0cf6a97aee661ded3cf1f77b695eab2f8f7fb14ca9d026c788677586658f9498dd7e681e054d226a6e90545fd58bcb43204
  languageName: node
  linkType: hard

"@graphql-tools/documents@npm:^1.0.0":
  version: 1.0.1
  resolution: "@graphql-tools/documents@npm:1.0.1"
  dependencies:
    lodash.sortby: "npm:^4.7.0"
    tslib: "npm:^2.4.0"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/6af5cc1a5ab88fc2ef08d97c1190c4857ea894ea41672f9f94889ed817664524972c8f234bed023b0746fd2f358b96ca1cc753f0af127d0b8076fa7c6f3e27e5
  languageName: node
  linkType: hard

"@graphql-tools/executor-graphql-ws@npm:^1.3.2":
  version: 1.3.3
  resolution: "@graphql-tools/executor-graphql-ws@npm:1.3.3"
  dependencies:
    "@graphql-tools/utils": "npm:^10.6.0"
    "@whatwg-node/disposablestack": "npm:^0.0.5"
    graphql-ws: "npm:^5.14.0"
    isomorphic-ws: "npm:^5.0.0"
    tslib: "npm:^2.4.0"
    ws: "npm:^8.17.1"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/6f3bef5b2819da31a553984d8ea0a3db38f127718d6aade86c7294137825827182b931103b75568253bde37bf468e0ffa2185a424130a910942fb04c989b1d88
  languageName: node
  linkType: hard

"@graphql-tools/executor-http@npm:^1.1.9":
  version: 1.1.11
  resolution: "@graphql-tools/executor-http@npm:1.1.11"
  dependencies:
    "@graphql-tools/utils": "npm:^10.6.0"
    "@repeaterjs/repeater": "npm:^3.0.4"
    "@whatwg-node/disposablestack": "npm:^0.0.5"
    "@whatwg-node/fetch": "npm:^0.10.1"
    extract-files: "npm:^11.0.0"
    meros: "npm:^1.2.1"
    tslib: "npm:^2.4.0"
    value-or-promise: "npm:^1.0.12"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/5ac3df6fb52515aed84ef047dfe00b6986dcafaf91fdc59637969054445801659aa20fbcce915ad3098988d02554e3ed26e0b0edfb195bfc262a80534425d603
  languageName: node
  linkType: hard

"@graphql-tools/executor-legacy-ws@npm:^1.1.4":
  version: 1.1.4
  resolution: "@graphql-tools/executor-legacy-ws@npm:1.1.4"
  dependencies:
    "@graphql-tools/utils": "npm:^10.6.1"
    "@types/ws": "npm:^8.0.0"
    isomorphic-ws: "npm:^5.0.0"
    tslib: "npm:^2.4.0"
    ws: "npm:^8.17.1"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/e98b2f599f03847036c79f27df349ecaab8da9f8c84b6ab54762e16b91e682079d6a4e33826d9701c924a45676a165dd94a62960c2724df9fb275fee55f6bfd6
  languageName: node
  linkType: hard

"@graphql-tools/executor@npm:^1.3.3":
  version: 1.3.4
  resolution: "@graphql-tools/executor@npm:1.3.4"
  dependencies:
    "@graphql-tools/utils": "npm:^10.6.0"
    "@graphql-typed-document-node/core": "npm:3.2.0"
    "@repeaterjs/repeater": "npm:^3.0.4"
    tslib: "npm:^2.4.0"
    value-or-promise: "npm:^1.0.12"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/4564f7069685081a169d9d3fb72e7b9ca7d9dd6e2d702ae73849225d3b962b5ea2accffdce2328eccf00fbbe0fb68894e3d21c3ab4331e451499762acc91f4b0
  languageName: node
  linkType: hard

"@graphql-tools/git-loader@npm:^8.0.0":
  version: 8.0.11
  resolution: "@graphql-tools/git-loader@npm:8.0.11"
  dependencies:
    "@graphql-tools/graphql-tag-pluck": "npm:8.3.6"
    "@graphql-tools/utils": "npm:^10.6.1"
    is-glob: "npm:4.0.3"
    micromatch: "npm:^4.0.8"
    tslib: "npm:^2.4.0"
    unixify: "npm:^1.0.0"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/fb615a75c0493946d3b1479a18fc8e0812b40781de11895b0e314ca24b5436e96b1fc55c7e130b6313726b5f240b235dae0fefc670b8babf09c69d3423a4240b
  languageName: node
  linkType: hard

"@graphql-tools/github-loader@npm:^8.0.0":
  version: 8.0.6
  resolution: "@graphql-tools/github-loader@npm:8.0.6"
  dependencies:
    "@ardatan/sync-fetch": "npm:^0.0.1"
    "@graphql-tools/executor-http": "npm:^1.1.9"
    "@graphql-tools/graphql-tag-pluck": "npm:^8.3.6"
    "@graphql-tools/utils": "npm:^10.6.1"
    "@whatwg-node/fetch": "npm:^0.10.0"
    tslib: "npm:^2.4.0"
    value-or-promise: "npm:^1.0.12"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/9ba05d5f0589951fb968fd8181561750f9e2f4a2805455e09fc5069110667bcc8191958e687a9aa587d3bdd8b28504f8101bc73eec1838aea1e37c408c45f8d8
  languageName: node
  linkType: hard

"@graphql-tools/graphql-file-loader@npm:^8.0.0":
  version: 8.0.5
  resolution: "@graphql-tools/graphql-file-loader@npm:8.0.5"
  dependencies:
    "@graphql-tools/import": "npm:7.0.5"
    "@graphql-tools/utils": "npm:^10.6.1"
    globby: "npm:^11.0.3"
    tslib: "npm:^2.4.0"
    unixify: "npm:^1.0.0"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/25da2efaae7b92acd2bc52ef5d0cc022d21435239fc40334f869b633235ef189094941e7cec83298f836dc9dee71c8a861c8a8b39d7e0c5bab0c57166624c7ef
  languageName: node
  linkType: hard

"@graphql-tools/graphql-tag-pluck@npm:8.3.6, @graphql-tools/graphql-tag-pluck@npm:^8.3.6":
  version: 8.3.6
  resolution: "@graphql-tools/graphql-tag-pluck@npm:8.3.6"
  dependencies:
    "@babel/core": "npm:^7.22.9"
    "@babel/parser": "npm:^7.16.8"
    "@babel/plugin-syntax-import-assertions": "npm:^7.20.0"
    "@babel/traverse": "npm:^7.16.8"
    "@babel/types": "npm:^7.16.8"
    "@graphql-tools/utils": "npm:^10.6.1"
    tslib: "npm:^2.4.0"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/5fa7a0eddc727eb042ec92f9c285f5b5e50909d6d81d988f8e18733621878c9e7bd2c17d489909096777354205154bc7b769e3daef716f4c1400b2124f74c12b
  languageName: node
  linkType: hard

"@graphql-tools/graphql-tag-pluck@npm:^8.3.4":
  version: 8.3.19
  resolution: "@graphql-tools/graphql-tag-pluck@npm:8.3.19"
  dependencies:
    "@babel/core": "npm:^7.26.10"
    "@babel/parser": "npm:^7.26.10"
    "@babel/plugin-syntax-import-assertions": "npm:^7.26.0"
    "@babel/traverse": "npm:^7.26.10"
    "@babel/types": "npm:^7.26.10"
    "@graphql-tools/utils": "npm:^10.8.6"
    tslib: "npm:^2.4.0"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/e4318a6b5561dd174f6d6667f5072aa23483cd44c81d253e341119c29466c579adff90dfa592e02a62b65db55d169b151cfe8ac7fc93677e6b9502bb9006e0f3
  languageName: node
  linkType: hard

"@graphql-tools/import@npm:7.0.5":
  version: 7.0.5
  resolution: "@graphql-tools/import@npm:7.0.5"
  dependencies:
    "@graphql-tools/utils": "npm:^10.6.1"
    resolve-from: "npm:5.0.0"
    tslib: "npm:^2.4.0"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/e9010a9797ca7dbbf9669f956bf92ff53e01984688ce3f846fdd798e1250e9051c516a91225e67d04a6de55e11d0f1195c20f6ae44072c887af06a625eb88579
  languageName: node
  linkType: hard

"@graphql-tools/json-file-loader@npm:^8.0.0":
  version: 8.0.5
  resolution: "@graphql-tools/json-file-loader@npm:8.0.5"
  dependencies:
    "@graphql-tools/utils": "npm:^10.6.1"
    globby: "npm:^11.0.3"
    tslib: "npm:^2.4.0"
    unixify: "npm:^1.0.0"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/d4fa21565ab72a756f8ba199c6132666c13bb59459ac054dbe7defef0b84f3ebc119b59cf48112fbc47b01630477859b75561243ecdafe54379f026b9fa05d22
  languageName: node
  linkType: hard

"@graphql-tools/load@npm:^8.0.0":
  version: 8.0.6
  resolution: "@graphql-tools/load@npm:8.0.6"
  dependencies:
    "@graphql-tools/schema": "npm:^10.0.10"
    "@graphql-tools/utils": "npm:^10.6.1"
    p-limit: "npm:3.1.0"
    tslib: "npm:^2.4.0"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/bdf17e2a4c19dc636df0eadd7e372fd8325084193fd3539e599d5d1f2dc8034ffed05f8821edc16a1e26db06d2be2a28e636f3d1def2410fb4cbc2a3468f9c0c
  languageName: node
  linkType: hard

"@graphql-tools/merge@npm:^9.0.0":
  version: 9.0.17
  resolution: "@graphql-tools/merge@npm:9.0.17"
  dependencies:
    "@graphql-tools/utils": "npm:^10.7.2"
    tslib: "npm:^2.4.0"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/85d6f256151cbe7c061e544dd9f68ce3b0a801ec358bb13b7e61ba654ff143d4cbf48a0f0c4c31c078b8c0c8a8f97b15f75ad4dabe7b5f171aaa4b4dd89072a4
  languageName: node
  linkType: hard

"@graphql-tools/merge@npm:^9.0.10":
  version: 9.0.10
  resolution: "@graphql-tools/merge@npm:9.0.10"
  dependencies:
    "@graphql-tools/utils": "npm:^10.6.0"
    tslib: "npm:^2.4.0"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/59f38a487edd22a87c7b6ca52c4445f08ee257b6312487e59a432e70bd09d26ac0b9b68abcfa0e83bb3df0406076c46be94e16e24c54f07ff066f366ec726868
  languageName: node
  linkType: hard

"@graphql-tools/merge@npm:^9.0.11":
  version: 9.0.11
  resolution: "@graphql-tools/merge@npm:9.0.11"
  dependencies:
    "@graphql-tools/utils": "npm:^10.6.1"
    tslib: "npm:^2.4.0"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/476130e8cb68c7e45d7f82b70d3fb6c44579727071f1fa9efda155471fc3e1c0094f5e8de3a66ef715d49d02d4e5290d4a72c89fb28c904aa145ad06b6ca2c13
  languageName: node
  linkType: hard

"@graphql-tools/optimize@npm:^2.0.0":
  version: 2.0.0
  resolution: "@graphql-tools/optimize@npm:2.0.0"
  dependencies:
    tslib: "npm:^2.4.0"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/7f79c0e1852abc571308e887d27d613da5b797256c8c6eb6c5fe7ca77f09e61524778ae281cebc0b698c51d4fe1074e2b8e0d0627b8e2dcf505aa6ed09b49a2f
  languageName: node
  linkType: hard

"@graphql-tools/prisma-loader@npm:^8.0.0":
  version: 8.0.17
  resolution: "@graphql-tools/prisma-loader@npm:8.0.17"
  dependencies:
    "@graphql-tools/url-loader": "npm:^8.0.15"
    "@graphql-tools/utils": "npm:^10.5.6"
    "@types/js-yaml": "npm:^4.0.0"
    "@whatwg-node/fetch": "npm:^0.10.0"
    chalk: "npm:^4.1.0"
    debug: "npm:^4.3.1"
    dotenv: "npm:^16.0.0"
    graphql-request: "npm:^6.0.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.0"
    jose: "npm:^5.0.0"
    js-yaml: "npm:^4.0.0"
    lodash: "npm:^4.17.20"
    scuid: "npm:^1.1.0"
    tslib: "npm:^2.4.0"
    yaml-ast-parser: "npm:^0.0.43"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/264472d2a9ab2567ce9b47bf84eaa1d4f56090c85d84bd2c237e49abf809350677eff8b3d3980118c4fa8ac8ebba1aab6e48d5feefa121ca6eb9f2cf1d838768
  languageName: node
  linkType: hard

"@graphql-tools/relay-operation-optimizer@npm:^7.0.0":
  version: 7.0.5
  resolution: "@graphql-tools/relay-operation-optimizer@npm:7.0.5"
  dependencies:
    "@ardatan/relay-compiler": "npm:12.0.0"
    "@graphql-tools/utils": "npm:^10.6.1"
    tslib: "npm:^2.4.0"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/4129fdca07fd98f58f765c1e76c8c3e6816274a463ca3fcda59f61094855819ce6b9d1b943d96de30845c3c0168d21e064ac0aa689e5ed6f15a12ba2d64c2da5
  languageName: node
  linkType: hard

"@graphql-tools/schema@npm:^10.0.0, @graphql-tools/schema@npm:^10.0.10, @graphql-tools/schema@npm:^10.0.7":
  version: 10.0.10
  resolution: "@graphql-tools/schema@npm:10.0.10"
  dependencies:
    "@graphql-tools/merge": "npm:^9.0.11"
    "@graphql-tools/utils": "npm:^10.6.1"
    tslib: "npm:^2.4.0"
    value-or-promise: "npm:^1.0.12"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/017cb8583db9c1ffd25c2e8376be9d8e10910f84d727dd7f1fa88e9c035242cb8a064ff8235e17d741877252155d6f4c30b505b22615247de375c4972aea106b
  languageName: node
  linkType: hard

"@graphql-tools/schema@npm:^10.0.8":
  version: 10.0.9
  resolution: "@graphql-tools/schema@npm:10.0.9"
  dependencies:
    "@graphql-tools/merge": "npm:^9.0.10"
    "@graphql-tools/utils": "npm:^10.6.0"
    tslib: "npm:^2.4.0"
    value-or-promise: "npm:^1.0.12"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/7f36b62391407bcf16a19421fd338aad71f241d2eb13e6899e0a1db5f2dc1c7859e8b2e29db4d110168bd7ec8d5864a20ad8b95979d59b97c5c1dd62af087340
  languageName: node
  linkType: hard

"@graphql-tools/url-loader@npm:^8.0.0, @graphql-tools/url-loader@npm:^8.0.15":
  version: 8.0.17
  resolution: "@graphql-tools/url-loader@npm:8.0.17"
  dependencies:
    "@ardatan/sync-fetch": "npm:^0.0.1"
    "@graphql-tools/executor-graphql-ws": "npm:^1.3.2"
    "@graphql-tools/executor-http": "npm:^1.1.9"
    "@graphql-tools/executor-legacy-ws": "npm:^1.1.4"
    "@graphql-tools/utils": "npm:^10.6.1"
    "@graphql-tools/wrap": "npm:^10.0.16"
    "@types/ws": "npm:^8.0.0"
    "@whatwg-node/fetch": "npm:^0.10.0"
    isomorphic-ws: "npm:^5.0.0"
    tslib: "npm:^2.4.0"
    value-or-promise: "npm:^1.0.11"
    ws: "npm:^8.17.1"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/e58c9e0fb6b45b6174c845cd03fecf4fcf380900454a40b071f6d36b1145b04d7dbf72b706e87b4c5cab1225afafa97d7cc8e0ce2b2649ca676fccaec6140dd1
  languageName: node
  linkType: hard

"@graphql-tools/utils@npm:^10.0.0, @graphql-tools/utils@npm:^10.6.1":
  version: 10.6.1
  resolution: "@graphql-tools/utils@npm:10.6.1"
  dependencies:
    "@graphql-typed-document-node/core": "npm:^3.1.1"
    cross-inspect: "npm:1.0.1"
    dset: "npm:^3.1.2"
    tslib: "npm:^2.4.0"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/b7ff1207cfc109180bd1d35d2ca3a8b8d23d4b60ecf35105c3bf09b6b3d3597176712fa94411ad900ed1be158db6b0eafca4ae9670a42a96582cf6f293306f92
  languageName: node
  linkType: hard

"@graphql-tools/utils@npm:^10.5.6":
  version: 10.6.0
  resolution: "@graphql-tools/utils@npm:10.6.0"
  dependencies:
    "@graphql-typed-document-node/core": "npm:^3.1.1"
    cross-inspect: "npm:1.0.1"
    dset: "npm:^3.1.2"
    tslib: "npm:^2.4.0"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/198b7e59ec4c333e1d313f7b69d0ef525e957c7e893d13e67ad49dd799aaefc1b550f4bcf6abf8ed5b0fce4755b32ff467840c07e2f898a4e51421475b42f9d5
  languageName: node
  linkType: hard

"@graphql-tools/utils@npm:^10.6.0, @graphql-tools/utils@npm:^10.7.2":
  version: 10.7.2
  resolution: "@graphql-tools/utils@npm:10.7.2"
  dependencies:
    "@graphql-typed-document-node/core": "npm:^3.1.1"
    cross-inspect: "npm:1.0.1"
    dset: "npm:^3.1.4"
    tslib: "npm:^2.4.0"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/b4725b081e5ff5c1441036db76ce907a6fe9b4c94aa9ceb070f75541b2297c3cccaa182f91d214f9abe6d89df33d8df51e055afbc4e382b01e8d8fb7c2f6edf6
  languageName: node
  linkType: hard

"@graphql-tools/utils@npm:^10.8.6":
  version: 10.8.6
  resolution: "@graphql-tools/utils@npm:10.8.6"
  dependencies:
    "@graphql-typed-document-node/core": "npm:^3.1.1"
    "@whatwg-node/promise-helpers": "npm:^1.0.0"
    cross-inspect: "npm:1.0.1"
    dset: "npm:^3.1.4"
    tslib: "npm:^2.4.0"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/98329aef966b489d3674eb086b784f6fb4500afaf9bc46fbe6a14ca32e98fec480c7395d3488c5eb2f450b75a538e98edf0527ed4bf24af352230e850c914389
  languageName: node
  linkType: hard

"@graphql-tools/wrap@npm:^10.0.16":
  version: 10.0.21
  resolution: "@graphql-tools/wrap@npm:10.0.21"
  dependencies:
    "@graphql-tools/delegate": "npm:^10.2.3"
    "@graphql-tools/schema": "npm:^10.0.7"
    "@graphql-tools/utils": "npm:^10.6.0"
    tslib: "npm:^2.4.0"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/4b7c10773a8f1d4526c6a91272951d80e2b2cfe0ddf1f6620ff564799bae70c72fffd6e8cbe1d04dd6b5188fdfecc6bc66c06ab4e11a7fffa3b81a224197bf71
  languageName: node
  linkType: hard

"@graphql-typed-document-node/core@npm:3.2.0, @graphql-typed-document-node/core@npm:^3.1.1, @graphql-typed-document-node/core@npm:^3.2.0":
  version: 3.2.0
  resolution: "@graphql-typed-document-node/core@npm:3.2.0"
  peerDependencies:
    graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10/fa44443accd28c8cf4cb96aaaf39d144a22e8b091b13366843f4e97d19c7bfeaf609ce3c7603a4aeffe385081eaf8ea245d078633a7324c11c5ec4b2011bb76d
  languageName: node
  linkType: hard

"@humanfs/core@npm:^0.19.1":
  version: 0.19.1
  resolution: "@humanfs/core@npm:0.19.1"
  checksum: 10/270d936be483ab5921702623bc74ce394bf12abbf57d9145a69e8a0d1c87eb1c768bd2d93af16c5705041e257e6d9cc7529311f63a1349f3678abc776fc28523
  languageName: node
  linkType: hard

"@humanfs/node@npm:^0.16.6":
  version: 0.16.6
  resolution: "@humanfs/node@npm:0.16.6"
  dependencies:
    "@humanfs/core": "npm:^0.19.1"
    "@humanwhocodes/retry": "npm:^0.3.0"
  checksum: 10/6d43c6727463772d05610aa05c83dab2bfbe78291022ee7a92cb50999910b8c720c76cc312822e2dea2b497aa1b3fef5fe9f68803fc45c9d4ed105874a65e339
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 10/e993950e346331e5a32eefb27948ecdee2a2c4ab3f072b8f566cd213ef485dd50a3ca497050608db91006f5479e43f91a439aef68d2a313bd3ded06909c7c5b3
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.3.0":
  version: 0.3.1
  resolution: "@humanwhocodes/retry@npm:0.3.1"
  checksum: 10/eb457f699529de7f07649679ec9e0353055eebe443c2efe71c6dd950258892475a038e13c6a8c5e13ed1fb538cdd0a8794faa96b24b6ffc4c87fb1fc9f70ad7f
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.4.1":
  version: 0.4.1
  resolution: "@humanwhocodes/retry@npm:0.4.1"
  checksum: 10/39fafc7319e88f61befebd5e1b4f0136534ea6a9bd10d74366698187bd63544210ec5d79a87ed4d91297f1cc64c4c53d45fb0077a2abfdce212cf0d3862d5f04
  languageName: node
  linkType: hard

"@intlify/core-base@npm:10.0.5":
  version: 10.0.5
  resolution: "@intlify/core-base@npm:10.0.5"
  dependencies:
    "@intlify/message-compiler": "npm:10.0.5"
    "@intlify/shared": "npm:10.0.5"
  checksum: 10/d0680074ca5abc26c348976047001fda76045917fabcbe041fa662c2ca66dc6d22570ebf5e198f718e8b98c3d662e29d824f891183f51ff6b888c7c68db5e4fd
  languageName: node
  linkType: hard

"@intlify/core-base@npm:11.1.2":
  version: 11.1.2
  resolution: "@intlify/core-base@npm:11.1.2"
  dependencies:
    "@intlify/message-compiler": "npm:11.1.2"
    "@intlify/shared": "npm:11.1.2"
  checksum: 10/d61d2a30b9fcefec8ce72352fea000d746edf4ccca280f0925e36f7bb0da1ee7cdbed26ba50144de325f13535f4c5fa64bb26f7a10232095e4f749cd4e149c26
  languageName: node
  linkType: hard

"@intlify/core-base@npm:^9.12.0":
  version: 9.14.2
  resolution: "@intlify/core-base@npm:9.14.2"
  dependencies:
    "@intlify/message-compiler": "npm:9.14.2"
    "@intlify/shared": "npm:9.14.2"
  checksum: 10/e07660e529f21369dddb45290e8d5983a78973072fcf91311aec14a730822d2df8fc9e93212365ec55b411439b9bda020d364843c42d350fb2d51d462dfc2d03
  languageName: node
  linkType: hard

"@intlify/eslint-plugin-vue-i18n@npm:3.2.0":
  version: 3.2.0
  resolution: "@intlify/eslint-plugin-vue-i18n@npm:3.2.0"
  dependencies:
    "@eslint/eslintrc": "npm:^3.0.0"
    "@intlify/core-base": "npm:^9.12.0"
    "@intlify/message-compiler": "npm:^9.12.0"
    debug: "npm:^4.3.4"
    eslint-compat-utils: "npm:^0.6.0"
    glob: "npm:^10.3.3"
    globals: "npm:^15.0.0"
    ignore: "npm:^6.0.0"
    import-fresh: "npm:^3.3.0"
    is-language-code: "npm:^3.1.0"
    js-yaml: "npm:^4.1.0"
    json5: "npm:^2.2.3"
    jsonc-eslint-parser: "npm:^2.3.0"
    lodash: "npm:^4.17.21"
    parse5: "npm:^7.1.2"
    semver: "npm:^7.5.4"
    synckit: "npm:^0.9.0"
    vue-eslint-parser: "npm:^9.3.1"
    yaml-eslint-parser: "npm:^1.2.2"
  peerDependencies:
    eslint: ^8.0.0 || ^9.0.0-0
  checksum: 10/fbd10d97ba619e6a584d610cd570c104f5efd4fa71ec5a4864934974775d9b941af3042a9e97933962b7639fa36c34d404e38f6fbae9ef4fdee923bddf60ee1b
  languageName: node
  linkType: hard

"@intlify/message-compiler@npm:10.0.5":
  version: 10.0.5
  resolution: "@intlify/message-compiler@npm:10.0.5"
  dependencies:
    "@intlify/shared": "npm:10.0.5"
    source-map-js: "npm:^1.0.2"
  checksum: 10/3e386b042ac6dd98533b6ea2b4af610999236a1eb7ff4d77762cd06cae1858a53735b0b7e60828c569c715a9f0ae588aaaf8496f9a4b287a44e7f0fd470857ff
  languageName: node
  linkType: hard

"@intlify/message-compiler@npm:11.1.2":
  version: 11.1.2
  resolution: "@intlify/message-compiler@npm:11.1.2"
  dependencies:
    "@intlify/shared": "npm:11.1.2"
    source-map-js: "npm:^1.0.2"
  checksum: 10/150236c5e4a0019e6f6518c6b23600ca00bc6885617cc23f8e846cfb8762d85e4805db9551dc49d471ffc515c17432720f5891659ca3d088b4b0a1341da8f86f
  languageName: node
  linkType: hard

"@intlify/message-compiler@npm:9.14.2, @intlify/message-compiler@npm:^9.12.0":
  version: 9.14.2
  resolution: "@intlify/message-compiler@npm:9.14.2"
  dependencies:
    "@intlify/shared": "npm:9.14.2"
    source-map-js: "npm:^1.0.2"
  checksum: 10/730c3bd655c864309092ea262a42b701ab6d11565d248fb542e2e922b3873a3edc23556315c1e1a38eb486c540b438dca9fc6bdfa5a9dfc98c0ff6e26331ca3d
  languageName: node
  linkType: hard

"@intlify/shared@npm:10.0.5":
  version: 10.0.5
  resolution: "@intlify/shared@npm:10.0.5"
  checksum: 10/64552b8770ad236c11893837e95e4e2ea7c947396cfcee797bf8e26afea6d06f707178f4aa36b0b416d89800ba9cd058e6742bf2ebdd7c7ff7f9fc1ec9bc7b48
  languageName: node
  linkType: hard

"@intlify/shared@npm:11.1.2":
  version: 11.1.2
  resolution: "@intlify/shared@npm:11.1.2"
  checksum: 10/486dc693b444f908533e3e6d555733a4758664f2d6a4bd26becede0651a893b9287125aeb04b51c563174ccf5c7c241495a5a7ecdce0e92734aafe60ef531317
  languageName: node
  linkType: hard

"@intlify/shared@npm:9.14.2":
  version: 9.14.2
  resolution: "@intlify/shared@npm:9.14.2"
  checksum: 10/761eae6d0e883cd03557fd1d7b19341375a58a3486eab543b745a6fff10af4d98a249eef1e06433d807813180e71df0c03f6ed404a207611f0d26ffeeb2dd35f
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10/e9ed5fd27c3aec1095e3a16e0c0cf148d1fee55a38665c35f7b3f86a9b5d00d042ddaabc98e8a1cb7463b9378c15f22a94eb35e99469c201453eb8375191f243
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
  checksum: 10/4412e9e6713c89c1e66d80bb0bb5a2a93192f10477623a27d08f228ba0316bb880affabc5bfe7f838f58a34d26c2c190da726e576cdfc18c49a72e89adabdcf5
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.8
  resolution: "@jridgewell/gen-mapping@npm:0.3.8"
  dependencies:
    "@jridgewell/set-array": "npm:^1.2.1"
    "@jridgewell/sourcemap-codec": "npm:^1.4.10"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10/9d3a56ab3612ab9b85d38b2a93b87f3324f11c5130859957f6500e4ac8ce35f299d5ccc3ecd1ae87597601ecf83cee29e9afd04c18777c24011073992ff946df
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 10/97106439d750a409c22c8bff822d648f6a71f3aa9bc8e5129efdc36343cd3096ddc4eeb1c62d2fe48e9bdd4db37b05d4646a17114ecebd3bbcacfa2de51c3c1d
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.2.1":
  version: 1.2.1
  resolution: "@jridgewell/set-array@npm:1.2.1"
  checksum: 10/832e513a85a588f8ed4f27d1279420d8547743cc37fcad5a5a76fc74bb895b013dfe614d0eed9cb860048e6546b798f8f2652020b4b2ba0561b05caa8c654b10
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14, @jridgewell/sourcemap-codec@npm:^1.5.0":
  version: 1.5.0
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.0"
  checksum: 10/4ed6123217569a1484419ac53f6ea0d9f3b57e5b57ab30d7c267bdb27792a27eb0e4b08e84a2680aa55cc2f2b411ffd6ec3db01c44fdc6dc43aca4b55f8374fd
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.1.0"
    "@jridgewell/sourcemap-codec": "npm:^1.4.14"
  checksum: 10/dced32160a44b49d531b80a4a2159dceab6b3ddf0c8e95a0deae4b0e894b172defa63d5ac52a19c2068e1fe7d31ea4ba931fbeec103233ecb4208953967120fc
  languageName: node
  linkType: hard

"@kamilkisiela/fast-url-parser@npm:^1.1.4":
  version: 1.1.4
  resolution: "@kamilkisiela/fast-url-parser@npm:1.1.4"
  checksum: 10/5b79438235a81817b02b96ddc581c996961cec5b40c7d6ebabd01ac6af8d4a35a43b9b263144af25386cef92c054c3ef6b1723b09eb0d8cf7b4053781a474c5f
  languageName: node
  linkType: hard

"@mdi/font@npm:^7.4.47":
  version: 7.4.47
  resolution: "@mdi/font@npm:7.4.47"
  checksum: 10/ecef7fd514f1970bb257272283dc609800c9e2f86c8388aa10d9e900a29c57a4146f766c7a0775603714799ed715a0a4515aaec6b2c7d28610bea2e95c1fc1a2
  languageName: node
  linkType: hard

"@mdi/js@npm:^7.4.47":
  version: 7.4.47
  resolution: "@mdi/js@npm:7.4.47"
  checksum: 10/c1a8fc82f23030bccc0cf324b13b73a0034d06140e79f8bc7b0e4e59275624c470e5ca6524d6141ad8c4fe3ad0f314c7af99afb3e38df163eb50d3b13b9eab17
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": "npm:2.0.5"
    run-parallel: "npm:^1.1.9"
  checksum: 10/6ab2a9b8a1d67b067922c36f259e3b3dfd6b97b219c540877a4944549a4d49ea5ceba5663905ab5289682f1f3c15ff441d02f0447f620a42e1cb5e1937174d4b
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 10/012480b5ca9d97bff9261571dbbec7bbc6033f69cc92908bc1ecfad0792361a5a1994bc48674b9ef76419d056a03efadfce5a6cf6dbc0a36559571a7a483f6f0
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": "npm:2.1.5"
    fastq: "npm:^1.6.0"
  checksum: 10/40033e33e96e97d77fba5a238e4bba4487b8284678906a9f616b5579ddaf868a18874c0054a75402c9fbaaa033a25ceae093af58c9c30278e35c23c9479e79b0
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: 10/775c9a7eb1f88c195dfb3bce70c31d0fe2a12b28b754e25c08a3edb4bc4816bfedb7ac64ef1e730579d078ca19dacf11630e99f8f3c3e0fd7b23caa5fd6d30a6
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10/405c4490e1ff11cf299775449a3c254a366a4b1ffc79d87159b0ee7d5558ac9f6a2f8c0735fd6ff3873cef014cb1a44a5f9127cb6a1b2dbc408718cca9365b5a
  languageName: node
  linkType: hard

"@one-ini/wasm@npm:0.1.1":
  version: 0.1.1
  resolution: "@one-ini/wasm@npm:0.1.1"
  checksum: 10/673c11518dba2e582e42415cbefe928513616f3af25e12f6e4e6b1b98b52b3e6c14bc251a361654af63cd64f208f22a1f7556fa49da2bf7efcf28cb14f16f807
  languageName: node
  linkType: hard

"@parcel/watcher-android-arm64@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-android-arm64@npm:2.5.0"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@parcel/watcher-darwin-arm64@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-darwin-arm64@npm:2.5.0"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@parcel/watcher-darwin-x64@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-darwin-x64@npm:2.5.0"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@parcel/watcher-freebsd-x64@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-freebsd-x64@npm:2.5.0"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@parcel/watcher-linux-arm-glibc@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-linux-arm-glibc@npm:2.5.0"
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@parcel/watcher-linux-arm-musl@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-linux-arm-musl@npm:2.5.0"
  conditions: os=linux & cpu=arm & libc=musl
  languageName: node
  linkType: hard

"@parcel/watcher-linux-arm64-glibc@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-linux-arm64-glibc@npm:2.5.0"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@parcel/watcher-linux-arm64-musl@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-linux-arm64-musl@npm:2.5.0"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@parcel/watcher-linux-x64-glibc@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-linux-x64-glibc@npm:2.5.0"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@parcel/watcher-linux-x64-musl@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-linux-x64-musl@npm:2.5.0"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@parcel/watcher-win32-arm64@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-win32-arm64@npm:2.5.0"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@parcel/watcher-win32-ia32@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-win32-ia32@npm:2.5.0"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@parcel/watcher-win32-x64@npm:2.5.0":
  version: 2.5.0
  resolution: "@parcel/watcher-win32-x64@npm:2.5.0"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@parcel/watcher@npm:^2.4.1":
  version: 2.5.0
  resolution: "@parcel/watcher@npm:2.5.0"
  dependencies:
    "@parcel/watcher-android-arm64": "npm:2.5.0"
    "@parcel/watcher-darwin-arm64": "npm:2.5.0"
    "@parcel/watcher-darwin-x64": "npm:2.5.0"
    "@parcel/watcher-freebsd-x64": "npm:2.5.0"
    "@parcel/watcher-linux-arm-glibc": "npm:2.5.0"
    "@parcel/watcher-linux-arm-musl": "npm:2.5.0"
    "@parcel/watcher-linux-arm64-glibc": "npm:2.5.0"
    "@parcel/watcher-linux-arm64-musl": "npm:2.5.0"
    "@parcel/watcher-linux-x64-glibc": "npm:2.5.0"
    "@parcel/watcher-linux-x64-musl": "npm:2.5.0"
    "@parcel/watcher-win32-arm64": "npm:2.5.0"
    "@parcel/watcher-win32-ia32": "npm:2.5.0"
    "@parcel/watcher-win32-x64": "npm:2.5.0"
    detect-libc: "npm:^1.0.3"
    is-glob: "npm:^4.0.3"
    micromatch: "npm:^4.0.5"
    node-addon-api: "npm:^7.0.0"
    node-gyp: "npm:latest"
  dependenciesMeta:
    "@parcel/watcher-android-arm64":
      optional: true
    "@parcel/watcher-darwin-arm64":
      optional: true
    "@parcel/watcher-darwin-x64":
      optional: true
    "@parcel/watcher-freebsd-x64":
      optional: true
    "@parcel/watcher-linux-arm-glibc":
      optional: true
    "@parcel/watcher-linux-arm-musl":
      optional: true
    "@parcel/watcher-linux-arm64-glibc":
      optional: true
    "@parcel/watcher-linux-arm64-musl":
      optional: true
    "@parcel/watcher-linux-x64-glibc":
      optional: true
    "@parcel/watcher-linux-x64-musl":
      optional: true
    "@parcel/watcher-win32-arm64":
      optional: true
    "@parcel/watcher-win32-ia32":
      optional: true
    "@parcel/watcher-win32-x64":
      optional: true
  checksum: 10/1e28b1aa9a63456ebfa7af3e41297d088bd31d9e32548604f4f26ed96c5808f4330cd515062e879c24a9eaab7894066c8a3951ee30b59e7cbe6786ab2c790dae
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10/115e8ceeec6bc69dff2048b35c0ab4f8bbee12d8bb6c1f4af758604586d802b6e669dcb02dda61d078de42c2b4ddce41b3d9e726d7daa6b4b850f4adbf7333ff
  languageName: node
  linkType: hard

"@pkgr/core@npm:^0.1.0":
  version: 0.1.1
  resolution: "@pkgr/core@npm:0.1.1"
  checksum: 10/6f25fd2e3008f259c77207ac9915b02f1628420403b2630c92a07ff963129238c9262afc9e84344c7a23b5cc1f3965e2cd17e3798219f5fd78a63d144d3cceba
  languageName: node
  linkType: hard

"@playwright/test@npm:1.52.0":
  version: 1.52.0
  resolution: "@playwright/test@npm:1.52.0"
  dependencies:
    playwright: "npm:1.52.0"
  bin:
    playwright: cli.js
  checksum: 10/e18a4eb626c7bc6cba212ff2e197cf9ae2e4da1c91bfdf08a744d62e27222751173e4b220fa27da72286a89a3b4dea7c09daf384d23708f284b64f98e9a63a88
  languageName: node
  linkType: hard

"@repeaterjs/repeater@npm:^3.0.4, @repeaterjs/repeater@npm:^3.0.6":
  version: 3.0.6
  resolution: "@repeaterjs/repeater@npm:3.0.6"
  checksum: 10/25698e822847b776006428f31e2d31fbcb4faccf30c1c8d68d6e1308e58b49afb08764d1dd15536ddd67775cd01fd6c2fb22f039c05a71865448fbcfb2246af2
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm-eabi@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-android-arm-eabi@npm:4.40.2"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm64@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-android-arm64@npm:4.40.2"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-arm64@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-darwin-arm64@npm:4.40.2"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-x64@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-darwin-x64@npm:4.40.2"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@rollup/rollup-freebsd-arm64@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-freebsd-arm64@npm:4.40.2"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-freebsd-x64@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-freebsd-x64@npm:4.40.2"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm-gnueabihf@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-linux-arm-gnueabihf@npm:4.40.2"
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm-musleabihf@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-linux-arm-musleabihf@npm:4.40.2"
  conditions: os=linux & cpu=arm & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-gnu@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-linux-arm64-gnu@npm:4.40.2"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-musl@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-linux-arm64-musl@npm:4.40.2"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-loongarch64-gnu@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-linux-loongarch64-gnu@npm:4.40.2"
  conditions: os=linux & cpu=loong64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-powerpc64le-gnu@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-linux-powerpc64le-gnu@npm:4.40.2"
  conditions: os=linux & cpu=ppc64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-riscv64-gnu@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-linux-riscv64-gnu@npm:4.40.2"
  conditions: os=linux & cpu=riscv64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-riscv64-musl@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-linux-riscv64-musl@npm:4.40.2"
  conditions: os=linux & cpu=riscv64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-s390x-gnu@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-linux-s390x-gnu@npm:4.40.2"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-gnu@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-linux-x64-gnu@npm:4.40.2"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-musl@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-linux-x64-musl@npm:4.40.2"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-win32-arm64-msvc@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-win32-arm64-msvc@npm:4.40.2"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-win32-ia32-msvc@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-win32-ia32-msvc@npm:4.40.2"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@rollup/rollup-win32-x64-msvc@npm:4.40.2":
  version: 4.40.2
  resolution: "@rollup/rollup-win32-x64-msvc@npm:4.40.2"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@tanstack/match-sorter-utils@npm:^8.1.1":
  version: 8.19.4
  resolution: "@tanstack/match-sorter-utils@npm:8.19.4"
  dependencies:
    remove-accents: "npm:0.5.0"
  checksum: 10/1289bd422da8577e5b1b4f1673505b521745d2edaf249eee2d8854633896dcb8f4bf91909b8e0641d3df2818856ff530db6957ce907fba65c073b7305dee365d
  languageName: node
  linkType: hard

"@tanstack/query-core@npm:4.36.1":
  version: 4.36.1
  resolution: "@tanstack/query-core@npm:4.36.1"
  checksum: 10/7c648872cd491bcab2aa4c18e0b7ca130c072f05c277a5876977fa3bfa87634bbfde46e9d249236587d78c39866889a02e4e202b478dc6074ff96093732ae56d
  languageName: node
  linkType: hard

"@tanstack/vue-query@npm:4.37.1":
  version: 4.37.1
  resolution: "@tanstack/vue-query@npm:4.37.1"
  dependencies:
    "@tanstack/match-sorter-utils": "npm:^8.1.1"
    "@tanstack/query-core": "npm:4.36.1"
    "@vue/devtools-api": "npm:^6.4.2"
    vue-demi: "npm:^0.13.11"
  peerDependencies:
    "@vue/composition-api": ^1.1.2
    vue: ^2.5.0 || ^3.0.0
  peerDependenciesMeta:
    "@vue/composition-api":
      optional: true
  checksum: 10/e3dc0e72997165c6520b7dd7d81db667527de6918e9923a215a7c265d744443e83efb9b81e228c732329d3c710386c9bd873240f1efe423e6903e06627d37711
  languageName: node
  linkType: hard

"@tsconfig/node22@npm:22.0.0":
  version: 22.0.0
  resolution: "@tsconfig/node22@npm:22.0.0"
  checksum: 10/9fc45789304640e1e37e1f1e04c02dca593c290d4f1cb55af7e9cab799fb2c6356e1cc15ee9f140185591a81f491a4c7f2d6c08c1e5a3d758658ba69f1afee6d
  languageName: node
  linkType: hard

"@types/estree@npm:1.0.7":
  version: 1.0.7
  resolution: "@types/estree@npm:1.0.7"
  checksum: 10/419c845ece767ad4b21171e6e5b63dabb2eb46b9c0d97361edcd9cabbf6a95fcadb91d89b5fa098d1336fa0b8fceaea82fca97a2ef3971f5c86e53031e157b21
  languageName: node
  linkType: hard

"@types/estree@npm:^1.0.6":
  version: 1.0.6
  resolution: "@types/estree@npm:1.0.6"
  checksum: 10/9d35d475095199c23e05b431bcdd1f6fec7380612aed068b14b2a08aa70494de8a9026765a5a91b1073f636fb0368f6d8973f518a31391d519e20c59388ed88d
  languageName: node
  linkType: hard

"@types/file-saver@npm:2.0.7":
  version: 2.0.7
  resolution: "@types/file-saver@npm:2.0.7"
  checksum: 10/c3d1cd80eab1214767922cabac97681f3fb688e82b74890450d70deaca49537949bbc96d80d363d91e8f0a4752c7164909cc8902d9721c5c4809baafc42a3801
  languageName: node
  linkType: hard

"@types/js-yaml@npm:^4.0.0":
  version: 4.0.9
  resolution: "@types/js-yaml@npm:4.0.9"
  checksum: 10/a0ce595db8a987904badd21fc50f9f444cb73069f4b95a76cc222e0a17b3ff180669059c763ec314bc4c3ce284379177a9da80e83c5f650c6c1310cafbfaa8e6
  languageName: node
  linkType: hard

"@types/jsdom@npm:21.1.7":
  version: 21.1.7
  resolution: "@types/jsdom@npm:21.1.7"
  dependencies:
    "@types/node": "npm:*"
    "@types/tough-cookie": "npm:*"
    parse5: "npm:^7.0.0"
  checksum: 10/a5ee54aec813ac928ef783f69828213af4d81325f584e1fe7573a9ae139924c40768d1d5249237e62d51b9a34ed06bde059c86c6b0248d627457ec5e5d532dfa
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.15":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 10/1a3c3e06236e4c4aab89499c428d585527ce50c24fe8259e8b3926d3df4cfbbbcf306cfc73ddfb66cbafc973116efd15967020b0f738f63e09e64c7d260519e7
  languageName: node
  linkType: hard

"@types/lodash-es@npm:4.17.12":
  version: 4.17.12
  resolution: "@types/lodash-es@npm:4.17.12"
  dependencies:
    "@types/lodash": "npm:*"
  checksum: 10/56b9a433348b11c31051c6fa9028540a033a08fb80b400c589d740446c19444d73b217cf1471d4036448ef686a83e8cf2a35d1fadcb3f2105f26701f94aebb07
  languageName: node
  linkType: hard

"@types/lodash@npm:*":
  version: 4.17.14
  resolution: "@types/lodash@npm:4.17.14"
  checksum: 10/6ee40725f3e192f5ef1f493caca19210aa7acd7adc3136b8dba84d418a35be0abea0668105aed9f696ad62a54310a9c0d328971ad4b157f5bcda700424ed5aae
  languageName: node
  linkType: hard

"@types/node@npm:*":
  version: 22.9.1
  resolution: "@types/node@npm:22.9.1"
  dependencies:
    undici-types: "npm:~6.19.8"
  checksum: 10/43fadcb3a914a1daff8e559839f235eec65fe80bfef5016b361dbc7952c9bc9d79456c78d89beab275a9e9e5accff37e838c019ab519f821f12c953cd6c24b50
  languageName: node
  linkType: hard

"@types/node@npm:22.10.5":
  version: 22.10.5
  resolution: "@types/node@npm:22.10.5"
  dependencies:
    undici-types: "npm:~6.20.0"
  checksum: 10/a5366961ffa9921e8f15435bc18ea9f8b7a7bb6b3d92dd5e93ebcd25e8af65708872bd8e6fee274b4655bab9ca80fbff9f0e42b5b53857790f13cf68cf4cbbfc
  languageName: node
  linkType: hard

"@types/tough-cookie@npm:*":
  version: 4.0.5
  resolution: "@types/tough-cookie@npm:4.0.5"
  checksum: 10/01fd82efc8202670865928629697b62fe9bf0c0dcbc5b1c115831caeb073a2c0abb871ff393d7df1ae94ea41e256cb87d2a5a91fd03cdb1b0b4384e08d4ee482
  languageName: node
  linkType: hard

"@types/web-bluetooth@npm:^0.0.20":
  version: 0.0.20
  resolution: "@types/web-bluetooth@npm:0.0.20"
  checksum: 10/2faa323e5c994e9468fff4675e3b6d35f3730eb4dc7c761d02267fb6246dbfd659fd8f3583db0872aae05b3ee139799c25655bbe79bf1b56c08c06e665db814b
  languageName: node
  linkType: hard

"@types/ws@npm:^8.0.0":
  version: 8.5.13
  resolution: "@types/ws@npm:8.5.13"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10/21369beafa75c91ae3b00d3a2671c7408fceae1d492ca2abd5ac7c8c8bf4596d513c1599ebbddeae82c27c4a2d248976d0d714c4b3d34362b2ae35b964e2e637
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:8.19.1":
  version: 8.19.1
  resolution: "@typescript-eslint/eslint-plugin@npm:8.19.1"
  dependencies:
    "@eslint-community/regexpp": "npm:^4.10.0"
    "@typescript-eslint/scope-manager": "npm:8.19.1"
    "@typescript-eslint/type-utils": "npm:8.19.1"
    "@typescript-eslint/utils": "npm:8.19.1"
    "@typescript-eslint/visitor-keys": "npm:8.19.1"
    graphemer: "npm:^1.4.0"
    ignore: "npm:^5.3.1"
    natural-compare: "npm:^1.4.0"
    ts-api-utils: "npm:^2.0.0"
  peerDependencies:
    "@typescript-eslint/parser": ^8.0.0 || ^8.0.0-alpha.0
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.8.0"
  checksum: 10/c9a6d3181ec01068075b85ad3ac454910b4452281d60c775cc7229827f6d6a076b7336f5f07a7ad89bf08b3224f6a49aa20342b9438702393bee0aa7315d23b2
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:8.19.1":
  version: 8.19.1
  resolution: "@typescript-eslint/parser@npm:8.19.1"
  dependencies:
    "@typescript-eslint/scope-manager": "npm:8.19.1"
    "@typescript-eslint/types": "npm:8.19.1"
    "@typescript-eslint/typescript-estree": "npm:8.19.1"
    "@typescript-eslint/visitor-keys": "npm:8.19.1"
    debug: "npm:^4.3.4"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.8.0"
  checksum: 10/da3db63ff655cf0fb91745ba8e52d853386f601cf6106d36f4541efcb9e2c6c3b82c6743b15680eff9eafeccaf31c9b26191a955e66ae19de9172f67335463ab
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:8.19.1":
  version: 8.19.1
  resolution: "@typescript-eslint/scope-manager@npm:8.19.1"
  dependencies:
    "@typescript-eslint/types": "npm:8.19.1"
    "@typescript-eslint/visitor-keys": "npm:8.19.1"
  checksum: 10/6ffc78b15367f211eb6650459ca2bb6bfe4c1fa95a3474adc08ee9a20c250b2e0e02fd99be36bd3dad74967ecd9349e792b5d818d85735cba40f1b5c236074d1
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:8.19.1":
  version: 8.19.1
  resolution: "@typescript-eslint/type-utils@npm:8.19.1"
  dependencies:
    "@typescript-eslint/typescript-estree": "npm:8.19.1"
    "@typescript-eslint/utils": "npm:8.19.1"
    debug: "npm:^4.3.4"
    ts-api-utils: "npm:^2.0.0"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.8.0"
  checksum: 10/123ecda88b057d6a4b68226701f435661440a420fda88cba60b49d7fb3e4f49483164ff174f259e28c0beabb0ed04500462a20faefd78331ba202bf54b01e3ef
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:8.19.1":
  version: 8.19.1
  resolution: "@typescript-eslint/types@npm:8.19.1"
  checksum: 10/5833a5f8fdac4a490dd3906a0243a0713fbf138fabb451870c70b0b089c539a9624b467b0913ddc0a225a8284342e7fd31cd506dec53c1a6d8f3c8c8902b9cae
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:8.19.1":
  version: 8.19.1
  resolution: "@typescript-eslint/typescript-estree@npm:8.19.1"
  dependencies:
    "@typescript-eslint/types": "npm:8.19.1"
    "@typescript-eslint/visitor-keys": "npm:8.19.1"
    debug: "npm:^4.3.4"
    fast-glob: "npm:^3.3.2"
    is-glob: "npm:^4.0.3"
    minimatch: "npm:^9.0.4"
    semver: "npm:^7.6.0"
    ts-api-utils: "npm:^2.0.0"
  peerDependencies:
    typescript: ">=4.8.4 <5.8.0"
  checksum: 10/5de467452d5ef1a380d441b06cd0134652a0c98cdb4ce31b93eb589f7dc75ef60364d03fd80ca0a48d0c8b268f7258d4f6528b16fe1b89442d60a4bc960fe5f5
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:8.19.1":
  version: 8.19.1
  resolution: "@typescript-eslint/utils@npm:8.19.1"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.4.0"
    "@typescript-eslint/scope-manager": "npm:8.19.1"
    "@typescript-eslint/types": "npm:8.19.1"
    "@typescript-eslint/typescript-estree": "npm:8.19.1"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.8.0"
  checksum: 10/bb92116a53fe143ee87e830941afb21d4222a64ca3f2b6dac5c2d9984f981408e60e52b04c32d95208896075ac222fb4ee631c5b0c4826b87d4bd8091c421ab1
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:8.19.1":
  version: 8.19.1
  resolution: "@typescript-eslint/visitor-keys@npm:8.19.1"
  dependencies:
    "@typescript-eslint/types": "npm:8.19.1"
    eslint-visitor-keys: "npm:^4.2.0"
  checksum: 10/510eb196e7b7d59d3981d672a75454615159e931fe78e2a64b09607c3cfa45110709b0eb5ac3dd271d757a0d98cf4868ad2f45bf9193f96e9efec3efa92a19c1
  languageName: node
  linkType: hard

"@vitejs/plugin-vue@npm:5.2.1":
  version: 5.2.1
  resolution: "@vitejs/plugin-vue@npm:5.2.1"
  peerDependencies:
    vite: ^5.0.0 || ^6.0.0
    vue: ^3.2.25
  checksum: 10/60edb926bf919aebe5ef527402bbb84902a23bcba57ea718285e4d700abf9718bc9411806440dc7e8ea0cef06d93c0078e2d456137b4eb3fd70d17288e9db081
  languageName: node
  linkType: hard

"@vitest/eslint-plugin@npm:1.1.24":
  version: 1.1.24
  resolution: "@vitest/eslint-plugin@npm:1.1.24"
  peerDependencies:
    "@typescript-eslint/utils": ">= 8.0"
    eslint: ">= 8.57.0"
    typescript: ">= 5.0.0"
    vitest: "*"
  peerDependenciesMeta:
    typescript:
      optional: true
    vitest:
      optional: true
  checksum: 10/2406cdfef6967495f57941c22806ad5aa768ea48734913dbe1a48c567fcaa334e00a9996e6a84bd37d29c684e56d774b34af79bc006235b522a546cb6470c5ba
  languageName: node
  linkType: hard

"@volar/language-core@npm:2.4.11, @volar/language-core@npm:~2.4.11":
  version: 2.4.11
  resolution: "@volar/language-core@npm:2.4.11"
  dependencies:
    "@volar/source-map": "npm:2.4.11"
  checksum: 10/cda5959642eb469ad6c57c3d7ad731503fc3b1f82bc57c3511cc6c925138fe1f6f3ef253ddf1cc6bd3bdd63d8a5943396b337e97ecd8afec8f486ac79db5f258
  languageName: node
  linkType: hard

"@volar/source-map@npm:2.4.11":
  version: 2.4.11
  resolution: "@volar/source-map@npm:2.4.11"
  checksum: 10/689803b2e788bdfda45a7994605798e8ed5ae7bb1116912db5e8f8c8a18000c0c66ebfae5ce33d3ac4cd3a7e171819253511232ed1eb20b1781b4aaed806d100
  languageName: node
  linkType: hard

"@volar/typescript@npm:~2.4.11":
  version: 2.4.11
  resolution: "@volar/typescript@npm:2.4.11"
  dependencies:
    "@volar/language-core": "npm:2.4.11"
    path-browserify: "npm:^1.0.1"
    vscode-uri: "npm:^3.0.8"
  checksum: 10/fe4be5497a8c12ddf25ee681cb47a3acc78cff4190ed6da7763c6a42f4fb5497cd99fb276ac0e849a70e49c05f9fea73127b10ca3b1a62ed3a33a67c4297215c
  languageName: node
  linkType: hard

"@vue/compiler-core@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/compiler-core@npm:3.5.13"
  dependencies:
    "@babel/parser": "npm:^7.25.3"
    "@vue/shared": "npm:3.5.13"
    entities: "npm:^4.5.0"
    estree-walker: "npm:^2.0.2"
    source-map-js: "npm:^1.2.0"
  checksum: 10/22f042bb47c8a1edb9d602e24da8092ab542d5640f0459a9b99ecf35f90e96678f870209dd30f774f5340c6d817d3c5a46ca49cefb9659ee5b228bd42d1f076a
  languageName: node
  linkType: hard

"@vue/compiler-dom@npm:3.5.13, @vue/compiler-dom@npm:^3.5.0":
  version: 3.5.13
  resolution: "@vue/compiler-dom@npm:3.5.13"
  dependencies:
    "@vue/compiler-core": "npm:3.5.13"
    "@vue/shared": "npm:3.5.13"
  checksum: 10/5dc628c52091264a443c2d7326b759d7d3999c7e9c00078c2eb370b778e60b9f2ef258a8decf2fd97c8fa0923f895d449eabc1e5bc3d8a45d3ef99c9eb0599d7
  languageName: node
  linkType: hard

"@vue/compiler-sfc@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/compiler-sfc@npm:3.5.13"
  dependencies:
    "@babel/parser": "npm:^7.25.3"
    "@vue/compiler-core": "npm:3.5.13"
    "@vue/compiler-dom": "npm:3.5.13"
    "@vue/compiler-ssr": "npm:3.5.13"
    "@vue/shared": "npm:3.5.13"
    estree-walker: "npm:^2.0.2"
    magic-string: "npm:^0.30.11"
    postcss: "npm:^8.4.48"
    source-map-js: "npm:^1.2.0"
  checksum: 10/08d55bbdbe86ad0a1fc0501dbf5f535161d35ecb378adb478dd4a75b97e8d21852516966c0ad8aed1d6da11b0d8280b7848ff142b4181cb8f24eaaecd7827f73
  languageName: node
  linkType: hard

"@vue/compiler-ssr@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/compiler-ssr@npm:3.5.13"
  dependencies:
    "@vue/compiler-dom": "npm:3.5.13"
    "@vue/shared": "npm:3.5.13"
  checksum: 10/09f2706455a7d8a5acc67c98120d28d0105d006184402b045636be7791939f5a77fd1c37657047b0129fa431f03437dcab9befc6baa172367ecdef7618407149
  languageName: node
  linkType: hard

"@vue/compiler-vue2@npm:^2.7.16":
  version: 2.7.16
  resolution: "@vue/compiler-vue2@npm:2.7.16"
  dependencies:
    de-indent: "npm:^1.0.2"
    he: "npm:^1.2.0"
  checksum: 10/739ad06be19206b2715707c226a070509bcf28c31b539a6fc932d220eb7b0c09109d71fded573ed0c4073429793a3513ca4a4e69ad4f7afc0c5bc3c28639e871
  languageName: node
  linkType: hard

"@vue/devtools-api@npm:^6.4.2, @vue/devtools-api@npm:^6.5.0, @vue/devtools-api@npm:^6.6.3, @vue/devtools-api@npm:^6.6.4":
  version: 6.6.4
  resolution: "@vue/devtools-api@npm:6.6.4"
  checksum: 10/0fca4912b6ae0185b9375f5d113d417984077db0681c74cf39eb8522eb82c27f662a72e1ae3e0d79e105fdd0a99a7cbd65ed111465d238f60cce10922e02a812
  languageName: node
  linkType: hard

"@vue/eslint-config-prettier@npm:10.1.0":
  version: 10.1.0
  resolution: "@vue/eslint-config-prettier@npm:10.1.0"
  dependencies:
    eslint-config-prettier: "npm:^9.1.0"
    eslint-plugin-prettier: "npm:^5.2.1"
  peerDependencies:
    eslint: ">= 8.21.0"
    prettier: ">= 3.0.0"
  checksum: 10/cba714f54908397c99a8f36979b073cf0292c685e4ca41915ce93680aff143ecff58e185d3fe390b53466de5d55baf7a2c382f968e7141f248fb2b0d4f3c2df1
  languageName: node
  linkType: hard

"@vue/eslint-config-typescript@npm:14.2.0":
  version: 14.2.0
  resolution: "@vue/eslint-config-typescript@npm:14.2.0"
  dependencies:
    fast-glob: "npm:^3.3.2"
    typescript-eslint: "npm:^8.18.1"
    vue-eslint-parser: "npm:^9.4.3"
  peerDependencies:
    eslint: ^9.10.0
    eslint-plugin-vue: ^9.28.0
    typescript: ">=4.8.4"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10/10cc365d97218b0c633b28cd2557a515c4c93bf7024b66a672113316836ef0163d737112e524924b69cbf929545267ae2c175af7af858ed37ddb5fce57b2089c
  languageName: node
  linkType: hard

"@vue/language-core@npm:2.2.8":
  version: 2.2.8
  resolution: "@vue/language-core@npm:2.2.8"
  dependencies:
    "@volar/language-core": "npm:~2.4.11"
    "@vue/compiler-dom": "npm:^3.5.0"
    "@vue/compiler-vue2": "npm:^2.7.16"
    "@vue/shared": "npm:^3.5.0"
    alien-signals: "npm:^1.0.3"
    minimatch: "npm:^9.0.3"
    muggle-string: "npm:^0.4.1"
    path-browserify: "npm:^1.0.1"
  peerDependencies:
    typescript: "*"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10/351f1fe547cc9057313788c866a9af49b7a36c247edc1d8e86201a8f342233e13aec5fe3d804972a141aab663e130f976b23d73a4cc987754aab54f283c6ad56
  languageName: node
  linkType: hard

"@vue/reactivity@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/reactivity@npm:3.5.13"
  dependencies:
    "@vue/shared": "npm:3.5.13"
  checksum: 10/e4db379fad27f9fbf2cb19133b8b1320b72e34dd60ec3654756175c67b1c76b356ec4f4dd58443e3c1b73e2814bec18eb67822b6d27966a7e8e450986e0c56f2
  languageName: node
  linkType: hard

"@vue/runtime-core@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/runtime-core@npm:3.5.13"
  dependencies:
    "@vue/reactivity": "npm:3.5.13"
    "@vue/shared": "npm:3.5.13"
  checksum: 10/55ef3ec9efe59b84c2468abb486ff8ecd717607332182699ff5bbfe646687ee5c16c1bd57f968a4a4a4103289bba70667e3e7ea8b4d5eb0ebc8778411279942a
  languageName: node
  linkType: hard

"@vue/runtime-dom@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/runtime-dom@npm:3.5.13"
  dependencies:
    "@vue/reactivity": "npm:3.5.13"
    "@vue/runtime-core": "npm:3.5.13"
    "@vue/shared": "npm:3.5.13"
    csstype: "npm:^3.1.3"
  checksum: 10/f32e52b08cc1e9daf645a665ff40040ea4a8f7ee2f98c39e5d4f1e959a004cc330770ce2fb1406b3d567745148cf78e9f96935118add91af3e1a2ce2c7c11040
  languageName: node
  linkType: hard

"@vue/server-renderer@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/server-renderer@npm:3.5.13"
  dependencies:
    "@vue/compiler-ssr": "npm:3.5.13"
    "@vue/shared": "npm:3.5.13"
  peerDependencies:
    vue: 3.5.13
  checksum: 10/1da86b265dfc74336fd4212e4b3033c9cf069d024cf9823846e77edab976ec58f3a0b6bd0dbf2449620939811f4959c74a8c3a6ae65392429351d68925d73307
  languageName: node
  linkType: hard

"@vue/shared@npm:3.5.13, @vue/shared@npm:^3.5.0":
  version: 3.5.13
  resolution: "@vue/shared@npm:3.5.13"
  checksum: 10/5c0c24f443533392dde08c3e4272ff2e19af9762f90baeaa808850e05106537bbd9e2d2ad2081d979b8c4bc89902395b46036b67f74c60b76025924de37833b1
  languageName: node
  linkType: hard

"@vue/test-utils@npm:2.4.6":
  version: 2.4.6
  resolution: "@vue/test-utils@npm:2.4.6"
  dependencies:
    js-beautify: "npm:^1.14.9"
    vue-component-type-helpers: "npm:^2.0.0"
  checksum: 10/a3b445f1dae9b663e8cdb048054bb16d0b24b7901bcf45c81e84a8340030651bb7ad9fd309d36f5963addd25883968e0f81da44a06bab99bb9aa89b4235f2880
  languageName: node
  linkType: hard

"@vue/tsconfig@npm:0.7.0":
  version: 0.7.0
  resolution: "@vue/tsconfig@npm:0.7.0"
  peerDependencies:
    typescript: 5.x
    vue: ^3.4.0
  peerDependenciesMeta:
    typescript:
      optional: true
    vue:
      optional: true
  checksum: 10/29b5dd11cc179de707dfd549d0e80e220f4a0f6f7d90b9a52769bc655e599b9166b28af1218819245fbdd30a74d80fdd484538cdda6876d9d369ae623f234834
  languageName: node
  linkType: hard

"@vuetify/loader-shared@npm:^2.0.3":
  version: 2.0.3
  resolution: "@vuetify/loader-shared@npm:2.0.3"
  dependencies:
    upath: "npm:^2.0.1"
  peerDependencies:
    vue: ^3.0.0
    vuetify: ^3.0.0
  checksum: 10/19b74b040820da2e9ca4c077c0693d060ecc6a9266ecaf11d73a4db279cc428a339a34716a00794b04616a6ad1a1447e2822c9b550af79de672b540bb0a7d3c4
  languageName: node
  linkType: hard

"@vueuse/core@npm:12.3.0":
  version: 12.3.0
  resolution: "@vueuse/core@npm:12.3.0"
  dependencies:
    "@types/web-bluetooth": "npm:^0.0.20"
    "@vueuse/metadata": "npm:12.3.0"
    "@vueuse/shared": "npm:12.3.0"
    vue: "npm:^3.5.13"
  checksum: 10/c631aa4b2206b06d0cbd6fba59f08eba3862d1a556f085863446e05df4910d986dd49182fcccfe6d582b58e45bc2046f9985af0bee331cc737f29d0c879c74dc
  languageName: node
  linkType: hard

"@vueuse/core@npm:^10.11.1":
  version: 10.11.1
  resolution: "@vueuse/core@npm:10.11.1"
  dependencies:
    "@types/web-bluetooth": "npm:^0.0.20"
    "@vueuse/metadata": "npm:10.11.1"
    "@vueuse/shared": "npm:10.11.1"
    vue-demi: "npm:>=0.14.8"
  checksum: 10/bbebdcd1ef0a77437cf2432062bb57c13e0afdadc474861ddad7ce343a8638778c5e54ad302c80e1d4351d4bdaf43501dc5937185e08d257fd000bdfea4ecb36
  languageName: node
  linkType: hard

"@vueuse/metadata@npm:10.11.1":
  version: 10.11.1
  resolution: "@vueuse/metadata@npm:10.11.1"
  checksum: 10/20336a05eb4945c1486d5e0a81c91a8ba08137f567b86d2bc1171af36d916c56d30166163016ed64642cce904cad1af09332655f7f0178eb5f2c65e54f648b4f
  languageName: node
  linkType: hard

"@vueuse/metadata@npm:12.3.0":
  version: 12.3.0
  resolution: "@vueuse/metadata@npm:12.3.0"
  checksum: 10/116143706e3190a398b7caef54bf5eb96b2e2cceab2bd40f686c47629fe8d9a59e987f6fd26a31ce7433bb69610b8a4ec57cb3121ecc2cda7624a97ae6c796f3
  languageName: node
  linkType: hard

"@vueuse/shared@npm:10.11.1":
  version: 10.11.1
  resolution: "@vueuse/shared@npm:10.11.1"
  dependencies:
    vue-demi: "npm:>=0.14.8"
  checksum: 10/5d8c28ed441a66b200c76ab44b6dbc6af152a6cecc527a1f4f645d95547e1de65e4b99e16d25f77609cb146e9e4b4961b260fe723043b08ab198e88ed1d47f4e
  languageName: node
  linkType: hard

"@vueuse/shared@npm:12.3.0":
  version: 12.3.0
  resolution: "@vueuse/shared@npm:12.3.0"
  dependencies:
    vue: "npm:^3.5.13"
  checksum: 10/bc0ce6506d1d794da13682bdbbb17e879a9d106761c9a7f9359456fcc9660fdc87d1bcc200f9df6e41994f8b0e0135221dade0c2b4e1f9d300e4954e8a2cdaec
  languageName: node
  linkType: hard

"@whatwg-node/disposablestack@npm:^0.0.5":
  version: 0.0.5
  resolution: "@whatwg-node/disposablestack@npm:0.0.5"
  dependencies:
    tslib: "npm:^2.6.3"
  checksum: 10/4e47701c51e505f5d793af4ec9ec63f114c73b82dc55c55e16c9ce28a70b094f68f982ca0b054fc28fd195023551a8bd2eb4f9485a3f66230e10b38dc291b75d
  languageName: node
  linkType: hard

"@whatwg-node/fetch@npm:^0.10.0, @whatwg-node/fetch@npm:^0.10.1":
  version: 0.10.1
  resolution: "@whatwg-node/fetch@npm:0.10.1"
  dependencies:
    "@whatwg-node/node-fetch": "npm:^0.7.1"
    urlpattern-polyfill: "npm:^10.0.0"
  checksum: 10/dd1096a33c565ec8bdd1b3d5318f4a49bb4215c78024d166f081af6ef29f3c099a68dd9000576d10345ca23cc35a76500973795fa463617caff3ece407343e8f
  languageName: node
  linkType: hard

"@whatwg-node/fetch@npm:^0.9.20":
  version: 0.9.23
  resolution: "@whatwg-node/fetch@npm:0.9.23"
  dependencies:
    "@whatwg-node/node-fetch": "npm:^0.6.0"
    urlpattern-polyfill: "npm:^10.0.0"
  checksum: 10/6024a3fcc2175de6a20ea4833c009d0488cf68c01cd235541ec0dba0ce59bb0b0befcd4cd788db0e65b99a5a8755bc00d490dc9d7beeb0c2f35058ef46732fe0
  languageName: node
  linkType: hard

"@whatwg-node/node-fetch@npm:^0.6.0":
  version: 0.6.0
  resolution: "@whatwg-node/node-fetch@npm:0.6.0"
  dependencies:
    "@kamilkisiela/fast-url-parser": "npm:^1.1.4"
    busboy: "npm:^1.6.0"
    fast-querystring: "npm:^1.1.1"
    tslib: "npm:^2.6.3"
  checksum: 10/87ad7c4cc68b24499089166617d16cbe25d9107b4d9354c804232f8c53c4fc27d1e2166471d878390442620e09588aa1d8705a8e2ea5bcc2d728a558ad1156c3
  languageName: node
  linkType: hard

"@whatwg-node/node-fetch@npm:^0.7.1":
  version: 0.7.4
  resolution: "@whatwg-node/node-fetch@npm:0.7.4"
  dependencies:
    "@kamilkisiela/fast-url-parser": "npm:^1.1.4"
    "@whatwg-node/disposablestack": "npm:^0.0.5"
    busboy: "npm:^1.6.0"
    fast-querystring: "npm:^1.1.1"
    tslib: "npm:^2.6.3"
  checksum: 10/c17900238f6e5906ff61935251664cad0ad17f1ddc8b23408f93b05ceff9ec49c197d1187c4e07c138b638051c08ea5aec98b8d745436d0c812bdf063275472e
  languageName: node
  linkType: hard

"@whatwg-node/promise-helpers@npm:^1.0.0":
  version: 1.3.0
  resolution: "@whatwg-node/promise-helpers@npm:1.3.0"
  dependencies:
    tslib: "npm:^2.6.3"
  checksum: 10/4971319c340f4c16755dd3fe84772e28bcc550e1f723ae10f94e02910ded545f88e65da0a44499912e23c27ed5f7458e1303a99cdd5818792c5958205ee386b4
  languageName: node
  linkType: hard

"abbrev@npm:^2.0.0":
  version: 2.0.0
  resolution: "abbrev@npm:2.0.0"
  checksum: 10/ca0a54e35bea4ece0ecb68a47b312e1a9a6f772408d5bcb9051230aaa94b0460671c5b5c9cb3240eb5b7bc94c52476550eb221f65a0bbd0145bdc9f3113a6707
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: 10/d4371eaef7995530b5b5ca4183ff6f062ca17901a6d3f673c9ac011b01ede37e7a1f7f61f8f5cfe709e88054757bb8f3277dc4061087cdf4f2a1f90ccbcdb977
  languageName: node
  linkType: hard

"acorn@npm:^8.14.0, acorn@npm:^8.5.0, acorn@npm:^8.9.0":
  version: 8.14.0
  resolution: "acorn@npm:8.14.0"
  bin:
    acorn: bin/acorn
  checksum: 10/6df29c35556782ca9e632db461a7f97947772c6c1d5438a81f0c873a3da3a792487e83e404d1c6c25f70513e91aa18745f6eafb1fcc3a43ecd1920b21dd173d2
  languageName: node
  linkType: hard

"agent-base@npm:^7.0.2":
  version: 7.1.1
  resolution: "agent-base@npm:7.1.1"
  dependencies:
    debug: "npm:^4.3.4"
  checksum: 10/c478fec8f79953f118704d007a38f2a185458853f5c45579b9669372bd0e12602e88dc2ad0233077831504f7cd6fcc8251c383375bba5eaaf563b102938bda26
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 10/3db6d8d4651f2aa1a9e4af35b96ab11a7607af57a24f3bc721a387eaa3b5f674e901f0a648b0caefd48f3fd117c7761b79a3b55854e2aebaa96c3f32cf76af84
  languageName: node
  linkType: hard

"aggregate-error@npm:^3.0.0":
  version: 3.1.0
  resolution: "aggregate-error@npm:3.1.0"
  dependencies:
    clean-stack: "npm:^2.0.0"
    indent-string: "npm:^4.0.0"
  checksum: 10/1101a33f21baa27a2fa8e04b698271e64616b886795fd43c31068c07533c7b3facfcaf4e9e0cab3624bd88f729a592f1c901a1a229c9e490eafce411a8644b79
  languageName: node
  linkType: hard

"ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: "npm:^3.1.1"
    fast-json-stable-stringify: "npm:^2.0.0"
    json-schema-traverse: "npm:^0.4.1"
    uri-js: "npm:^4.2.2"
  checksum: 10/48d6ad21138d12eb4d16d878d630079a2bda25a04e745c07846a4ad768319533031e28872a9b3c5790fa1ec41aabdf2abed30a56e5a03ebc2cf92184b8ee306c
  languageName: node
  linkType: hard

"alien-signals@npm:^1.0.3":
  version: 1.0.8
  resolution: "alien-signals@npm:1.0.8"
  checksum: 10/c70547b6529dc537e5965044fb9cb5c3dbb1dacd56825460d1be18d21d305101739645872d77e3761fdd4c6a7e234115df44e300f61574036823e9dcc0f37a45
  languageName: node
  linkType: hard

"ansi-escapes@npm:^4.2.1, ansi-escapes@npm:^4.3.0":
  version: 4.3.2
  resolution: "ansi-escapes@npm:4.3.2"
  dependencies:
    type-fest: "npm:^0.21.3"
  checksum: 10/8661034456193ffeda0c15c8c564a9636b0c04094b7f78bd01517929c17c504090a60f7a75f949f5af91289c264d3e1001d91492c1bd58efc8e100500ce04de2
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10/2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 10/495834a53b0856c02acd40446f7130cb0f8284f4a39afdab20d5dc42b2e198b1196119fe887beed8f9055c4ff2055e3b2f6d4641d0be018cdfb64fedf6fc1aac
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10/b4494dfbfc7e4591b4711a396bd27e540f8153914123dccb4cdbbcb514015ada63a3809f362b9d8d4f6b17a706f1d7bea3c6f974b15fa5ae76b5b502070889ff
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0, ansi-styles@npm:^6.2.1":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10/70fdf883b704d17a5dfc9cde206e698c16bcd74e7f196ab821511651aee4f9f76c9514bdfa6ca3a27b5e49138b89cb222a28caf3afe4567570139577f991df32
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 10/18640244e641a417ec75a9bd38b0b2b6b95af5199aa241b131d4b2fb206f334d7ecc600bd194861610a5579084978bfcbb02baa399dbe442d56d0ae5e60dbaef
  languageName: node
  linkType: hard

"array-union@npm:^2.1.0":
  version: 2.1.0
  resolution: "array-union@npm:2.1.0"
  checksum: 10/5bee12395cba82da674931df6d0fea23c4aa4660cb3b338ced9f828782a65caa232573e6bf3968f23e0c5eb301764a382cef2f128b170a9dc59de0e36c39f98d
  languageName: node
  linkType: hard

"arrify@npm:^1.0.1":
  version: 1.0.1
  resolution: "arrify@npm:1.0.1"
  checksum: 10/745075dd4a4624ff0225c331dacb99be501a515d39bcb7c84d24660314a6ec28e68131b137e6f7e16318170842ce97538cd298fc4cd6b2cc798e0b957f2747e7
  languageName: node
  linkType: hard

"asap@npm:~2.0.3":
  version: 2.0.6
  resolution: "asap@npm:2.0.6"
  checksum: 10/b244c0458c571945e4b3be0b14eb001bea5596f9868cc50cc711dc03d58a7e953517d3f0dad81ccde3ff37d1f074701fa76a6f07d41aaa992d7204a37b915dda
  languageName: node
  linkType: hard

"astral-regex@npm:^2.0.0":
  version: 2.0.0
  resolution: "astral-regex@npm:2.0.0"
  checksum: 10/876231688c66400473ba505731df37ea436e574dd524520294cc3bbc54ea40334865e01fa0d074d74d036ee874ee7e62f486ea38bc421ee8e6a871c06f011766
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 10/3ce727cbc78f69d6a4722517a58ee926c8c21083633b1d3fdf66fd688f6c127a53a592141bd4866f9b63240a86e9d8e974b13919450bd17fa33c2d22c4558ad8
  languageName: node
  linkType: hard

"auto-bind@npm:~4.0.0":
  version: 4.0.0
  resolution: "auto-bind@npm:4.0.0"
  checksum: 10/00cad71cce5742faccb7dd65c1b55ebc4f45add4b0c9a1547b10b05bab22813230133b0c892c67ba3eb969a4524710c5e43cc45c72898ec84e56f3a596e7a04f
  languageName: node
  linkType: hard

"babel-plugin-syntax-trailing-function-commas@npm:^7.0.0-beta.0":
  version: 7.0.0-beta.0
  resolution: "babel-plugin-syntax-trailing-function-commas@npm:7.0.0-beta.0"
  checksum: 10/e37509156ca945dd9e4b82c66dd74f2d842ad917bd280cb5aa67960942300cd065eeac476d2514bdcdedec071277a358f6d517c31d9f9244d9bbc3619a8ecf8a
  languageName: node
  linkType: hard

"babel-preset-fbjs@npm:^3.4.0":
  version: 3.4.0
  resolution: "babel-preset-fbjs@npm:3.4.0"
  dependencies:
    "@babel/plugin-proposal-class-properties": "npm:^7.0.0"
    "@babel/plugin-proposal-object-rest-spread": "npm:^7.0.0"
    "@babel/plugin-syntax-class-properties": "npm:^7.0.0"
    "@babel/plugin-syntax-flow": "npm:^7.0.0"
    "@babel/plugin-syntax-jsx": "npm:^7.0.0"
    "@babel/plugin-syntax-object-rest-spread": "npm:^7.0.0"
    "@babel/plugin-transform-arrow-functions": "npm:^7.0.0"
    "@babel/plugin-transform-block-scoped-functions": "npm:^7.0.0"
    "@babel/plugin-transform-block-scoping": "npm:^7.0.0"
    "@babel/plugin-transform-classes": "npm:^7.0.0"
    "@babel/plugin-transform-computed-properties": "npm:^7.0.0"
    "@babel/plugin-transform-destructuring": "npm:^7.0.0"
    "@babel/plugin-transform-flow-strip-types": "npm:^7.0.0"
    "@babel/plugin-transform-for-of": "npm:^7.0.0"
    "@babel/plugin-transform-function-name": "npm:^7.0.0"
    "@babel/plugin-transform-literals": "npm:^7.0.0"
    "@babel/plugin-transform-member-expression-literals": "npm:^7.0.0"
    "@babel/plugin-transform-modules-commonjs": "npm:^7.0.0"
    "@babel/plugin-transform-object-super": "npm:^7.0.0"
    "@babel/plugin-transform-parameters": "npm:^7.0.0"
    "@babel/plugin-transform-property-literals": "npm:^7.0.0"
    "@babel/plugin-transform-react-display-name": "npm:^7.0.0"
    "@babel/plugin-transform-react-jsx": "npm:^7.0.0"
    "@babel/plugin-transform-shorthand-properties": "npm:^7.0.0"
    "@babel/plugin-transform-spread": "npm:^7.0.0"
    "@babel/plugin-transform-template-literals": "npm:^7.0.0"
    babel-plugin-syntax-trailing-function-commas: "npm:^7.0.0-beta.0"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/1e73ebaaeac805aad15793d06a40a63be096730f58708ec434f08578b5ccba890190cda8fdf1c626ab081a8e1cfd376c9db82eaf78a0fafdbcc2362eb2963804
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10/9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"base64-js@npm:^1.3.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 10/669632eb3745404c2f822a18fc3a0122d2f9a7a13f7fb8b5823ee19d1d2ff9ee5b52c53367176ea4ad093c332fd5ab4bd0ebae5a8e27917a4105a4cfc86b1005
  languageName: node
  linkType: hard

"bl@npm:^4.1.0":
  version: 4.1.0
  resolution: "bl@npm:4.1.0"
  dependencies:
    buffer: "npm:^5.5.0"
    inherits: "npm:^2.0.4"
    readable-stream: "npm:^3.4.0"
  checksum: 10/b7904e66ed0bdfc813c06ea6c3e35eafecb104369dbf5356d0f416af90c1546de3b74e5b63506f0629acf5e16a6f87c3798f16233dcff086e9129383aa02ab55
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: 10/3e25c80ef626c3a3487c73dbfc70ac322ec830666c9ad915d11b701142fab25ec1e63eff2c450c74347acfd2de854ccde865cd79ef4db1683f7c7b046ea43bb0
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: "npm:^1.0.0"
    concat-map: "npm:0.0.1"
  checksum: 10/faf34a7bb0c3fcf4b59c7808bc5d2a96a40988addf2e7e09dfbb67a2251800e0d14cd2bfc1aa79174f2f5095c54ff27f46fb1289fe2d77dac755b5eb3434cc07
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10/a61e7cd2e8a8505e9f0036b3b6108ba5e926b4b55089eeb5550cd04a471fe216c96d4fe7e4c7f995c728c554ae20ddfc4244cad10aef255e72b62930afd233d1
  languageName: node
  linkType: hard

"braces@npm:^3.0.3":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: "npm:^7.1.1"
  checksum: 10/fad11a0d4697a27162840b02b1fad249c1683cbc510cd5bf1a471f2f8085c046d41094308c577a50a03a579dd99d5a6b3724c4b5e8b14df2c4443844cfcda2c6
  languageName: node
  linkType: hard

"browserslist@npm:^4.24.0":
  version: 4.24.4
  resolution: "browserslist@npm:4.24.4"
  dependencies:
    caniuse-lite: "npm:^1.0.30001688"
    electron-to-chromium: "npm:^1.5.73"
    node-releases: "npm:^2.0.19"
    update-browserslist-db: "npm:^1.1.1"
  bin:
    browserslist: cli.js
  checksum: 10/11fda105e803d891311a21a1f962d83599319165faf471c2d70e045dff82a12128f5b50b1fcba665a2352ad66147aaa248a9d2355a80aadc3f53375eb3de2e48
  languageName: node
  linkType: hard

"bser@npm:2.1.1":
  version: 2.1.1
  resolution: "bser@npm:2.1.1"
  dependencies:
    node-int64: "npm:^0.4.0"
  checksum: 10/edba1b65bae682450be4117b695997972bd9a3c4dfee029cab5bcb72ae5393a79a8f909b8bc77957eb0deec1c7168670f18f4d5c556f46cdd3bca5f3b3a8d020
  languageName: node
  linkType: hard

"buffer@npm:^5.5.0":
  version: 5.7.1
  resolution: "buffer@npm:5.7.1"
  dependencies:
    base64-js: "npm:^1.3.1"
    ieee754: "npm:^1.1.13"
  checksum: 10/997434d3c6e3b39e0be479a80288875f71cd1c07d75a3855e6f08ef848a3c966023f79534e22e415ff3a5112708ce06127277ab20e527146d55c84566405c7c6
  languageName: node
  linkType: hard

"busboy@npm:^1.6.0":
  version: 1.6.0
  resolution: "busboy@npm:1.6.0"
  dependencies:
    streamsearch: "npm:^1.1.0"
  checksum: 10/bee10fa10ea58e7e3e7489ffe4bda6eacd540a17de9f9cd21cc37e297b2dd9fe52b2715a5841afaec82900750d810d01d7edb4b2d456427f449b92b417579763
  languageName: node
  linkType: hard

"cac@npm:^6.7.12":
  version: 6.7.14
  resolution: "cac@npm:6.7.14"
  checksum: 10/002769a0fbfc51c062acd2a59df465a2a947916b02ac50b56c69ec6c018ee99ac3e7f4dd7366334ea847f1ecacf4defaa61bcd2ac283db50156ce1f1d8c8ad42
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": "npm:^4.0.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^7.0.2"
    ssri: "npm:^12.0.0"
    tar: "npm:^7.4.3"
    unique-filename: "npm:^4.0.0"
  checksum: 10/ea026b27b13656330c2bbaa462a88181dcaa0435c1c2e705db89b31d9bdf7126049d6d0445ba746dca21454a0cfdf1d6f47fd39d34c8c8435296b30bc5738a13
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.1, call-bind-apply-helpers@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
  checksum: 10/00482c1f6aa7cfb30fb1dbeb13873edf81cfac7c29ed67a5957d60635a56b2a4a480f1016ddbdb3395cc37900d46037fb965043a51c5c789ffeab4fc535d18b5
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 10/072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"camel-case@npm:^4.1.2":
  version: 4.1.2
  resolution: "camel-case@npm:4.1.2"
  dependencies:
    pascal-case: "npm:^3.1.2"
    tslib: "npm:^2.0.3"
  checksum: 10/bcbd25cd253b3cbc69be3f535750137dbf2beb70f093bdc575f73f800acc8443d34fd52ab8f0a2413c34f1e8203139ffc88428d8863e4dfe530cfb257a379ad6
  languageName: node
  linkType: hard

"camelcase@npm:^5.0.0":
  version: 5.3.1
  resolution: "camelcase@npm:5.3.1"
  checksum: 10/e6effce26b9404e3c0f301498184f243811c30dfe6d0b9051863bd8e4034d09c8c2923794f280d6827e5aa055f6c434115ff97864a16a963366fb35fd673024b
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001688":
  version: 1.0.30001692
  resolution: "caniuse-lite@npm:1.0.30001692"
  checksum: 10/92449ec9e9ac6cd8ce7ecc18a8759ae34e4b3ef412acd998714ee9d70dc286bc8d0d6e4917fa454798da9b37667eb5b3b41386bc9d25e4274d0b9c7af8339b0e
  languageName: node
  linkType: hard

"capital-case@npm:^1.0.4":
  version: 1.0.4
  resolution: "capital-case@npm:1.0.4"
  dependencies:
    no-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
    upper-case-first: "npm:^2.0.2"
  checksum: 10/41fa8fa87f6d24d0835a2b4a9341a3eaecb64ac29cd7c5391f35d6175a0fa98ab044e7f2602e1ec3afc886231462ed71b5b80c590b8b41af903ec2c15e5c5931
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0, chalk@npm:^4.1.0, chalk@npm:^4.1.1, chalk@npm:^4.1.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10/cb3f3e594913d63b1814d7ca7c9bafbf895f75fbf93b92991980610dfd7b48500af4e3a5d4e3a8f337990a96b168d7eb84ee55efdce965e2ee8efc20f8c8f139
  languageName: node
  linkType: hard

"change-case-all@npm:1.0.15":
  version: 1.0.15
  resolution: "change-case-all@npm:1.0.15"
  dependencies:
    change-case: "npm:^4.1.2"
    is-lower-case: "npm:^2.0.2"
    is-upper-case: "npm:^2.0.2"
    lower-case: "npm:^2.0.2"
    lower-case-first: "npm:^2.0.2"
    sponge-case: "npm:^1.0.1"
    swap-case: "npm:^2.0.2"
    title-case: "npm:^3.0.3"
    upper-case: "npm:^2.0.2"
    upper-case-first: "npm:^2.0.2"
  checksum: 10/e1dabdcd8447a3690f3faf15f92979dfbc113109b50916976e1d5e518e6cfdebee4f05f54d0ca24fb79a4bf835185b59ae25e967bb3dc10bd236a775b19ecc52
  languageName: node
  linkType: hard

"change-case@npm:^4.1.2":
  version: 4.1.2
  resolution: "change-case@npm:4.1.2"
  dependencies:
    camel-case: "npm:^4.1.2"
    capital-case: "npm:^1.0.4"
    constant-case: "npm:^3.0.4"
    dot-case: "npm:^3.0.4"
    header-case: "npm:^2.0.4"
    no-case: "npm:^3.0.4"
    param-case: "npm:^3.0.4"
    pascal-case: "npm:^3.1.2"
    path-case: "npm:^3.0.4"
    sentence-case: "npm:^3.0.4"
    snake-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10/e4bc4a093a1f7cce8b33896665cf9e456e3bc3cc0def2ad7691b1994cfca99b3188d0a513b16855b01a6bd20692fcde12a7d4d87a5615c4c515bbbf0e651f116
  languageName: node
  linkType: hard

"chardet@npm:^0.7.0":
  version: 0.7.0
  resolution: "chardet@npm:0.7.0"
  checksum: 10/b0ec668fba5eeec575ed2559a0917ba41a6481f49063c8445400e476754e0957ee09e44dc032310f526182b8f1bf25e9d4ed371f74050af7be1383e06bc44952
  languageName: node
  linkType: hard

"chokidar@npm:^4.0.0":
  version: 4.0.3
  resolution: "chokidar@npm:4.0.3"
  dependencies:
    readdirp: "npm:^4.0.1"
  checksum: 10/bf2a575ea5596000e88f5db95461a9d59ad2047e939d5a4aac59dd472d126be8f1c1ff3c7654b477cf532d18f42a97279ef80ee847972fd2a25410bf00b80b59
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: 10/b63cb1f73d171d140a2ed8154ee6566c8ab775d3196b0e03a2a94b5f6a0ce7777ee5685ca56849403c8d17bd457a6540672f9a60696a6137c7a409097495b82c
  languageName: node
  linkType: hard

"clean-stack@npm:^2.0.0":
  version: 2.2.0
  resolution: "clean-stack@npm:2.2.0"
  checksum: 10/2ac8cd2b2f5ec986a3c743935ec85b07bc174d5421a5efc8017e1f146a1cf5f781ae962618f416352103b32c9cd7e203276e8c28241bbe946160cab16149fb68
  languageName: node
  linkType: hard

"cli-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "cli-cursor@npm:3.1.0"
  dependencies:
    restore-cursor: "npm:^3.1.0"
  checksum: 10/2692784c6cd2fd85cfdbd11f53aea73a463a6d64a77c3e098b2b4697a20443f430c220629e1ca3b195ea5ac4a97a74c2ee411f3807abf6df2b66211fec0c0a29
  languageName: node
  linkType: hard

"cli-spinners@npm:^2.5.0":
  version: 2.9.2
  resolution: "cli-spinners@npm:2.9.2"
  checksum: 10/a0a863f442df35ed7294424f5491fa1756bd8d2e4ff0c8736531d886cec0ece4d85e8663b77a5afaf1d296e3cbbebff92e2e99f52bbea89b667cbe789b994794
  languageName: node
  linkType: hard

"cli-truncate@npm:^2.1.0":
  version: 2.1.0
  resolution: "cli-truncate@npm:2.1.0"
  dependencies:
    slice-ansi: "npm:^3.0.0"
    string-width: "npm:^4.2.0"
  checksum: 10/976f1887de067a8cd6ec830a7a8508336aebe6cec79b521d98ed13f67ef073b637f7305675b6247dd22f9e9cf045ec55fe746c7bdb288fbe8db0dfdc9fd52e55
  languageName: node
  linkType: hard

"cli-width@npm:^3.0.0":
  version: 3.0.0
  resolution: "cli-width@npm:3.0.0"
  checksum: 10/8730848b04fb189666ab037a35888d191c8f05b630b1d770b0b0e4c920b47bb5cc14bddf6b8ffe5bfc66cee97c8211d4d18e756c1ffcc75d7dbe7e1186cd7826
  languageName: node
  linkType: hard

"cliui@npm:^6.0.0":
  version: 6.0.0
  resolution: "cliui@npm:6.0.0"
  dependencies:
    string-width: "npm:^4.2.0"
    strip-ansi: "npm:^6.0.0"
    wrap-ansi: "npm:^6.2.0"
  checksum: 10/44afbcc29df0899e87595590792a871cd8c4bc7d6ce92832d9ae268d141a77022adafca1aeaeccff618b62a613b8354e57fe22a275c199ec04baf00d381ef6ab
  languageName: node
  linkType: hard

"cliui@npm:^8.0.1":
  version: 8.0.1
  resolution: "cliui@npm:8.0.1"
  dependencies:
    string-width: "npm:^4.2.0"
    strip-ansi: "npm:^6.0.1"
    wrap-ansi: "npm:^7.0.0"
  checksum: 10/eaa5561aeb3135c2cddf7a3b3f562fc4238ff3b3fc666869ef2adf264be0f372136702f16add9299087fb1907c2e4ec5dbfe83bd24bce815c70a80c6c1a2e950
  languageName: node
  linkType: hard

"clone@npm:^1.0.2":
  version: 1.0.4
  resolution: "clone@npm:1.0.4"
  checksum: 10/d06418b7335897209e77bdd430d04f882189582e67bd1f75a04565f3f07f5b3f119a9d670c943b6697d0afb100f03b866b3b8a1f91d4d02d72c4ecf2bb64b5dd
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10/fa00c91b4332b294de06b443923246bccebe9fab1b253f7fe1772d37b06a2269b4039a85e309abe1fe11b267b11c08d1d0473fda3badd6167f57313af2887a64
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10/b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"colorette@npm:^2.0.16":
  version: 2.0.20
  resolution: "colorette@npm:2.0.20"
  checksum: 10/0b8de48bfa5d10afc160b8eaa2b9938f34a892530b2f7d7897e0458d9535a066e3998b49da9d21161c78225b272df19ae3a64d6df28b4c9734c0e55bbd02406f
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: "npm:~1.0.0"
  checksum: 10/2e969e637d05d09fa50b02d74c83a1186f6914aae89e6653b62595cc75a221464f884f55f231b8f4df7a49537fba60bdc0427acd2bf324c09a1dbb84837e36e4
  languageName: node
  linkType: hard

"commander@npm:^10.0.0":
  version: 10.0.1
  resolution: "commander@npm:10.0.1"
  checksum: 10/8799faa84a30da985802e661cc9856adfaee324d4b138413013ef7f087e8d7924b144c30a1f1405475f0909f467665cd9e1ce13270a2f41b141dab0b7a58f3fb
  languageName: node
  linkType: hard

"commander@npm:^6.1.0":
  version: 6.2.1
  resolution: "commander@npm:6.2.1"
  checksum: 10/25b88c2efd0380c84f7844b39cf18510da7bfc5013692d68cdc65f764a1c34e6c8a36ea6d72b6620e3710a930cf8fab2695bdec2bf7107a0f4fa30a3ef3b7d0e
  languageName: node
  linkType: hard

"common-tags@npm:1.8.2":
  version: 1.8.2
  resolution: "common-tags@npm:1.8.2"
  checksum: 10/c665d0f463ee79dda801471ad8da6cb33ff7332ba45609916a508ad3d77ba07ca9deeb452e83f81f24c2b081e2c1315347f23d239210e63d1c5e1a0c7c019fe2
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 10/9680699c8e2b3af0ae22592cb764acaf973f292a7b71b8a06720233011853a58e256c89216a10cbe889727532fd77f8bcd49a760cedfde271b8e006c20e079f2
  languageName: node
  linkType: hard

"concurrently@npm:9.1.2":
  version: 9.1.2
  resolution: "concurrently@npm:9.1.2"
  dependencies:
    chalk: "npm:^4.1.2"
    lodash: "npm:^4.17.21"
    rxjs: "npm:^7.8.1"
    shell-quote: "npm:^1.8.1"
    supports-color: "npm:^8.1.1"
    tree-kill: "npm:^1.2.2"
    yargs: "npm:^17.7.2"
  bin:
    conc: dist/bin/concurrently.js
    concurrently: dist/bin/concurrently.js
  checksum: 10/52f20653db53e25950e84026d153177af976d6d0e139b95fc5983c684221cc20ef5548ad5dc8885f146fff2c87bc7f7beb18f5fa54eee3bb62b5e795234d6cbc
  languageName: node
  linkType: hard

"config-chain@npm:^1.1.13":
  version: 1.1.13
  resolution: "config-chain@npm:1.1.13"
  dependencies:
    ini: "npm:^1.3.4"
    proto-list: "npm:~1.2.1"
  checksum: 10/83d22cabf709e7669f6870021c4d552e4fc02e9682702b726be94295f42ce76cfed00f70b2910ce3d6c9465d9758e191e28ad2e72ff4e3331768a90da6c1ef03
  languageName: node
  linkType: hard

"constant-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "constant-case@npm:3.0.4"
  dependencies:
    no-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
    upper-case: "npm:^2.0.2"
  checksum: 10/6c3346d51afc28d9fae922e966c68eb77a19d94858dba230dd92d7b918b37d36db50f0311e9ecf6847e43e934b1c01406a0936973376ab17ec2c471fbcfb2cf3
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 10/c987be3ec061348cdb3c2bfb924bec86dea1eacad10550a85ca23edb0fe3556c3a61c7399114f3331ccb3499d7fd0285ab24566e5745929412983494c3926e15
  languageName: node
  linkType: hard

"cosmiconfig@npm:^8.1.0, cosmiconfig@npm:^8.1.3":
  version: 8.3.6
  resolution: "cosmiconfig@npm:8.3.6"
  dependencies:
    import-fresh: "npm:^3.3.0"
    js-yaml: "npm:^4.1.0"
    parse-json: "npm:^5.2.0"
    path-type: "npm:^4.0.0"
  peerDependencies:
    typescript: ">=4.9.5"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10/91d082baca0f33b1c085bf010f9ded4af43cbedacba8821da0fb5667184d0a848addc52c31fadd080007f904a555319c238cf5f4c03e6d58ece2e4876b2e73d6
  languageName: node
  linkType: hard

"cross-fetch@npm:^3.1.5":
  version: 3.1.8
  resolution: "cross-fetch@npm:3.1.8"
  dependencies:
    node-fetch: "npm:^2.6.12"
  checksum: 10/ac8c4ca87d2ac0e17a19b6a293a67ee8934881aee5ec9a5a8323c30e9a9a60a0f5291d3c0d633ec2a2f970cbc60978d628804dfaf03add92d7e720b6d37f392c
  languageName: node
  linkType: hard

"cross-inspect@npm:1.0.1":
  version: 1.0.1
  resolution: "cross-inspect@npm:1.0.1"
  dependencies:
    tslib: "npm:^2.4.0"
  checksum: 10/7c1e02e0a9670b62416a3ea1df7ae880fdad3aa0a857de8932c4e5f8acd71298c7e3db9da8e9da603f5692cd1879938f5e72e34a9f5d1345987bef656d117fc1
  languageName: node
  linkType: hard

"cross-spawn@npm:7.0.5":
  version: 7.0.5
  resolution: "cross-spawn@npm:7.0.5"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10/c95062469d4bdbc1f099454d01c0e77177a3733012d41bf907a71eb8d22d2add43b5adf6a0a14ef4e7feaf804082714d6c262ef4557a1c480b86786c120d18e2
  languageName: node
  linkType: hard

"cssesc@npm:^3.0.0":
  version: 3.0.0
  resolution: "cssesc@npm:3.0.0"
  bin:
    cssesc: bin/cssesc
  checksum: 10/0e161912c1306861d8f46e1883be1cbc8b1b2879f0f509287c0db71796e4ddfb97ac96bdfca38f77f452e2c10554e1bb5678c99b07a5cf947a12778f73e47e12
  languageName: node
  linkType: hard

"cssstyle@npm:^4.2.1":
  version: 4.2.1
  resolution: "cssstyle@npm:4.2.1"
  dependencies:
    "@asamuzakjp/css-color": "npm:^2.8.2"
    rrweb-cssom: "npm:^0.8.0"
  checksum: 10/e287234f2fd4feb1d79217480f48356f398cc11b9d17d39e6624f7dc1bf4b51d1e2c49f12b1a324834b445c17cbbf83ae5d3ba22c89a6b229f86bcebeda746a8
  languageName: node
  linkType: hard

"csstype@npm:^3.1.3":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 10/f593cce41ff5ade23f44e77521e3a1bcc2c64107041e1bf6c3c32adc5187d0d60983292fda326154d20b01079e24931aa5b08e4467cc488b60bb1e7f6d478ade
  languageName: node
  linkType: hard

"data-urls@npm:^5.0.0":
  version: 5.0.0
  resolution: "data-urls@npm:5.0.0"
  dependencies:
    whatwg-mimetype: "npm:^4.0.0"
    whatwg-url: "npm:^14.0.0"
  checksum: 10/5c40568c31b02641a70204ff233bc4e42d33717485d074244a98661e5f2a1e80e38fe05a5755dfaf2ee549f2ab509d6a3af2a85f4b2ad2c984e5d176695eaf46
  languageName: node
  linkType: hard

"dataloader@npm:^2.2.2":
  version: 2.2.2
  resolution: "dataloader@npm:2.2.2"
  checksum: 10/9c7a1f02cfa6391ab8bc21ebd0ef60b03832bd3beafdfecf48b111fba14090f98d33965f8e268045ba3c289f801b6a9000a9e61a41188363bdee2344811f64f1
  languageName: node
  linkType: hard

"de-indent@npm:^1.0.2":
  version: 1.0.2
  resolution: "de-indent@npm:1.0.2"
  checksum: 10/30bf43744dca005f9252dbb34ed95dcb3c30dfe52bfed84973b89c29eccff04e27769f222a34c61a93354acf47457785e9032e6184be390ed1d324fb9ab3f427
  languageName: node
  linkType: hard

"debounce@npm:^1.2.0":
  version: 1.2.1
  resolution: "debounce@npm:1.2.1"
  checksum: 10/0b95b2a9d80ed69117d890f8dab8c0f2d6066f8d20edd1d810ae51f8f366a6d4c8b1d56e97dcb9304e93d57de4d5db440d34a03def7dad50403fc3f22bf16808
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.1.0, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.3, debug@npm:^4.3.4":
  version: 4.4.0
  resolution: "debug@npm:4.4.0"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10/1847944c2e3c2c732514b93d11886575625686056cd765336212dc15de2d2b29612b6cd80e1afba767bb8e1803b778caf9973e98169ef1a24a7a7009e1820367
  languageName: node
  linkType: hard

"decamelize@npm:^1.2.0":
  version: 1.2.0
  resolution: "decamelize@npm:1.2.0"
  checksum: 10/ad8c51a7e7e0720c70ec2eeb1163b66da03e7616d7b98c9ef43cce2416395e84c1e9548dd94f5f6ffecfee9f8b94251fc57121a8b021f2ff2469b2bae247b8aa
  languageName: node
  linkType: hard

"decimal.js@npm:^10.4.3":
  version: 10.4.3
  resolution: "decimal.js@npm:10.4.3"
  checksum: 10/de663a7bc4d368e3877db95fcd5c87b965569b58d16cdc4258c063d231ca7118748738df17cd638f7e9dd0be8e34cec08d7234b20f1f2a756a52fc5a38b188d0
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: 10/ec12d074aef5ae5e81fa470b9317c313142c9e8e2afe3f8efa124db309720db96d1d222b82b84c834e5f87e7a614b44a4684b6683583118b87c833b3be40d4d8
  languageName: node
  linkType: hard

"defaults@npm:^1.0.3":
  version: 1.0.4
  resolution: "defaults@npm:1.0.4"
  dependencies:
    clone: "npm:^1.0.2"
  checksum: 10/3a88b7a587fc076b84e60affad8b85245c01f60f38fc1d259e7ac1d89eb9ce6abb19e27215de46b98568dd5bc48471730b327637e6f20b0f1bc85cf00440c80a
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 10/46fe6e83e2cb1d85ba50bd52803c68be9bd953282fa7096f51fc29edd5d67ff84ff753c51966061e5ba7cb5e47ef6d36a91924eddb7f3f3483b1c560f77a0020
  languageName: node
  linkType: hard

"dependency-graph@npm:^0.11.0":
  version: 0.11.0
  resolution: "dependency-graph@npm:0.11.0"
  checksum: 10/6b5eb540303753037a613e781da4b81534d139cbabc92f342630ed622e3ef4c332fc40cf87823e1ec71a7aeb4b195f8d88d7e625931ce6007bf2bf09a8bfb01e
  languageName: node
  linkType: hard

"detect-indent@npm:^6.0.0":
  version: 6.1.0
  resolution: "detect-indent@npm:6.1.0"
  checksum: 10/ab953a73c72dbd4e8fc68e4ed4bfd92c97eb6c43734af3900add963fd3a9316f3bc0578b018b24198d4c31a358571eff5f0656e81a1f3b9ad5c547d58b2d093d
  languageName: node
  linkType: hard

"detect-libc@npm:^1.0.3":
  version: 1.0.3
  resolution: "detect-libc@npm:1.0.3"
  bin:
    detect-libc: ./bin/detect-libc.js
  checksum: 10/3849fe7720feb153e4ac9407086956e073f1ce1704488290ef0ca8aab9430a8d48c8a9f8351889e7cdc64e5b1128589501e4fef48f3a4a49ba92cd6d112d0757
  languageName: node
  linkType: hard

"dir-glob@npm:^3.0.1":
  version: 3.0.1
  resolution: "dir-glob@npm:3.0.1"
  dependencies:
    path-type: "npm:^4.0.0"
  checksum: 10/fa05e18324510d7283f55862f3161c6759a3f2f8dbce491a2fc14c8324c498286c54282c1f0e933cb930da8419b30679389499b919122952a4f8592362ef4615
  languageName: node
  linkType: hard

"dot-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "dot-case@npm:3.0.4"
  dependencies:
    no-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10/a65e3519414856df0228b9f645332f974f2bf5433370f544a681122eab59e66038fc3349b4be1cdc47152779dac71a5864f1ccda2f745e767c46e9c6543b1169
  languageName: node
  linkType: hard

"dot-object@npm:^2.1.4":
  version: 2.1.5
  resolution: "dot-object@npm:2.1.5"
  dependencies:
    commander: "npm:^6.1.0"
    glob: "npm:^7.1.6"
  bin:
    dot-object: bin/dot-object
  checksum: 10/db2414eff38269b6283ea7641f1455d3fdaa4675fe63a4f3595b736cd63e53f3ce4bddfa89e2c10173be5f7383097867e97ad8a8ae41e5e9bad74368ced089f4
  languageName: node
  linkType: hard

"dotenv@npm:16.4.7":
  version: 16.4.7
  resolution: "dotenv@npm:16.4.7"
  checksum: 10/f13bfe97db88f0df4ec505eeffb8925ec51f2d56a3d0b6d916964d8b4af494e6fb1633ba5d09089b552e77ab2a25de58d70259b2c5ed45ec148221835fc99a0c
  languageName: node
  linkType: hard

"dotenv@npm:^16.0.0":
  version: 16.4.5
  resolution: "dotenv@npm:16.4.5"
  checksum: 10/55a3134601115194ae0f924e54473459ed0d9fc340ae610b676e248cca45aa7c680d86365318ea964e6da4e2ea80c4514c1adab5adb43d6867fb57ff068f95c8
  languageName: node
  linkType: hard

"dset@npm:3.1.4":
  version: 3.1.4
  resolution: "dset@npm:3.1.4"
  checksum: 10/6268c9e2049c8effe6e5a1952f02826e8e32468b5ced781f15f8f3b1c290da37626246fec014fbdd1503413f981dff6abd8a4c718ec9952fd45fccb6ac9de43f
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.2.0"
  checksum: 10/5add88a3d68d42d6e6130a0cac450b7c2edbe73364bbd2fc334564418569bea97c6943a8fcd70e27130bf32afc236f30982fc4905039b703f23e9e0433c29934
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10/9b1d3e1baefeaf7d70799db8774149cef33b97183a6addceeba0cf6b85ba23ee2686f302f14482006df32df75d32b17c509c143a3689627929e4a8efaf483952
  languageName: node
  linkType: hard

"editorconfig@npm:^1.0.4":
  version: 1.0.4
  resolution: "editorconfig@npm:1.0.4"
  dependencies:
    "@one-ini/wasm": "npm:0.1.1"
    commander: "npm:^10.0.0"
    minimatch: "npm:9.0.1"
    semver: "npm:^7.5.3"
  bin:
    editorconfig: bin/editorconfig
  checksum: 10/bd0a7236f31a7f54801cb6f3222508d4f872a24e440bef30ee29f4ba667c0741724e52e0ad521abe3409b12cdafd8384bb751de9b2a2ee5f845c740edd2e742f
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.73":
  version: 1.5.80
  resolution: "electron-to-chromium@npm:1.5.80"
  checksum: 10/fa77236f05d1544d29ed71e3a787ad7d6da250fe0b5fc40aac0fcf46de9cb074d8a90a2b176f2245d2c4f2823b7ec2e95aafb0e29f612895b891d7ccfe43889a
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10/c72d67a6821be15ec11997877c437491c313d924306b8da5d87d2a2bcc2cec9903cb5b04ee1a088460501d8e5b44f10df82fdc93c444101a7610b80c8b6938e1
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10/915acf859cea7131dac1b2b5c9c8e35c4849e325a1d114c30adb8cd615970f6dca0e27f64f3a4949d7d6ed86ecd79a1c5c63f02e697513cddd7b5835c90948b8
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10/bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"entities@npm:^4.5.0":
  version: 4.5.0
  resolution: "entities@npm:4.5.0"
  checksum: 10/ede2a35c9bce1aeccd055a1b445d41c75a14a2bb1cd22e242f20cf04d236cdcd7f9c859eb83f76885327bfae0c25bf03303665ee1ce3d47c5927b98b0e3e3d48
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10/65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10/1d20d825cdcce8d811bfbe86340f4755c02655a7feb2f13f8c880566d9d72a3f6c92c192a6867632e490d6da67b678271f46e01044996a6443e870331100dfdd
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: "npm:^0.2.1"
  checksum: 10/d547740aa29c34e753fb6fed2c5de81802438529c12b3673bd37b6bb1fe49b9b7abdc3c11e6062fe625d8a296b3cf769a80f878865e25e685f787763eede3ffb
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 10/f8dc9e660d90919f11084db0a893128f3592b781ce967e4fccfb8f3106cb83e400a4032c559184ec52ee1dbd4b01e7776c7cd0b3327b1961b1a4a7008920fe78
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 10/96e65d640156f91b707517e8cdc454dd7d47c32833aa3e85d79f24f9eb7ea85f39b63e36216ef0114996581969b59fe609a94e30316b08f5f4df1d44134cf8d5
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0":
  version: 1.0.0
  resolution: "es-object-atoms@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
  checksum: 10/f8910cf477e53c0615f685c5c96210591841850871b81924fcf256bfbaa68c254457d994a4308c60d15b20805e7f61ce6abc669375e01a5349391a8c1767584f
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.1.1":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: "npm:^1.3.0"
  checksum: 10/54fe77de288451dae51c37bfbfe3ec86732dc3778f98f3eb3bdb4bf48063b2c0b8f9c93542656986149d08aa5be3204286e2276053d19582b76753f1a2728867
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10/86814bf8afbcd8966653f731415888019d4bc4aca6b6c354132a7a75bb87566751e320369654a101d23a91c87a85c79b178bcf40332839bd347aff437c4fb65f
  languageName: node
  linkType: hard

"esbuild@npm:^0.24.2":
  version: 0.24.2
  resolution: "esbuild@npm:0.24.2"
  dependencies:
    "@esbuild/aix-ppc64": "npm:0.24.2"
    "@esbuild/android-arm": "npm:0.24.2"
    "@esbuild/android-arm64": "npm:0.24.2"
    "@esbuild/android-x64": "npm:0.24.2"
    "@esbuild/darwin-arm64": "npm:0.24.2"
    "@esbuild/darwin-x64": "npm:0.24.2"
    "@esbuild/freebsd-arm64": "npm:0.24.2"
    "@esbuild/freebsd-x64": "npm:0.24.2"
    "@esbuild/linux-arm": "npm:0.24.2"
    "@esbuild/linux-arm64": "npm:0.24.2"
    "@esbuild/linux-ia32": "npm:0.24.2"
    "@esbuild/linux-loong64": "npm:0.24.2"
    "@esbuild/linux-mips64el": "npm:0.24.2"
    "@esbuild/linux-ppc64": "npm:0.24.2"
    "@esbuild/linux-riscv64": "npm:0.24.2"
    "@esbuild/linux-s390x": "npm:0.24.2"
    "@esbuild/linux-x64": "npm:0.24.2"
    "@esbuild/netbsd-arm64": "npm:0.24.2"
    "@esbuild/netbsd-x64": "npm:0.24.2"
    "@esbuild/openbsd-arm64": "npm:0.24.2"
    "@esbuild/openbsd-x64": "npm:0.24.2"
    "@esbuild/sunos-x64": "npm:0.24.2"
    "@esbuild/win32-arm64": "npm:0.24.2"
    "@esbuild/win32-ia32": "npm:0.24.2"
    "@esbuild/win32-x64": "npm:0.24.2"
  dependenciesMeta:
    "@esbuild/aix-ppc64":
      optional: true
    "@esbuild/android-arm":
      optional: true
    "@esbuild/android-arm64":
      optional: true
    "@esbuild/android-x64":
      optional: true
    "@esbuild/darwin-arm64":
      optional: true
    "@esbuild/darwin-x64":
      optional: true
    "@esbuild/freebsd-arm64":
      optional: true
    "@esbuild/freebsd-x64":
      optional: true
    "@esbuild/linux-arm":
      optional: true
    "@esbuild/linux-arm64":
      optional: true
    "@esbuild/linux-ia32":
      optional: true
    "@esbuild/linux-loong64":
      optional: true
    "@esbuild/linux-mips64el":
      optional: true
    "@esbuild/linux-ppc64":
      optional: true
    "@esbuild/linux-riscv64":
      optional: true
    "@esbuild/linux-s390x":
      optional: true
    "@esbuild/linux-x64":
      optional: true
    "@esbuild/netbsd-arm64":
      optional: true
    "@esbuild/netbsd-x64":
      optional: true
    "@esbuild/openbsd-arm64":
      optional: true
    "@esbuild/openbsd-x64":
      optional: true
    "@esbuild/sunos-x64":
      optional: true
    "@esbuild/win32-arm64":
      optional: true
    "@esbuild/win32-ia32":
      optional: true
    "@esbuild/win32-x64":
      optional: true
  bin:
    esbuild: bin/esbuild
  checksum: 10/95425071c9f24ff88bf61e0710b636ec0eb24ddf8bd1f7e1edef3044e1221104bbfa7bbb31c18018c8c36fa7902c5c0b843f829b981ebc89160cf5eebdaa58f4
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1, escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 10/9d7169e3965b2f9ae46971afa392f6e5a25545ea30f2e2dd99c9b0a95a3f52b5653681a84f5b2911a413ddad2d7a93d3514165072f349b5ffc59c75a899970d6
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 10/6092fda75c63b110c706b6a9bfde8a612ad595b628f0bd2147eea1d3406723020810e591effc7db1da91d80a71a737a313567c5abb3813e8d9c71f4aa595b410
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 10/98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"eslint-compat-utils@npm:^0.6.0":
  version: 0.6.4
  resolution: "eslint-compat-utils@npm:0.6.4"
  dependencies:
    semver: "npm:^7.5.4"
  peerDependencies:
    eslint: ">=6.0.0"
  checksum: 10/97f08f4aa8d9a1bc1087aaeceab46a5fa65a6d70703c1a2f2cd533562381208fdd0a293ce0f63ad607f1e697ddb348ef1076b02f5afa83c70f4a07ca0dcec90e
  languageName: node
  linkType: hard

"eslint-config-prettier@npm:^9.1.0":
  version: 9.1.0
  resolution: "eslint-config-prettier@npm:9.1.0"
  peerDependencies:
    eslint: ">=7.0.0"
  bin:
    eslint-config-prettier: bin/cli.js
  checksum: 10/411e3b3b1c7aa04e3e0f20d561271b3b909014956c4dba51c878bf1a23dbb8c800a3be235c46c4732c70827276e540b6eed4636d9b09b444fd0a8e07f0fcd830
  languageName: node
  linkType: hard

"eslint-formatter-gitlab@npm:5.1.0":
  version: 5.1.0
  resolution: "eslint-formatter-gitlab@npm:5.1.0"
  dependencies:
    chalk: "npm:^4.0.0"
    yaml: "npm:^2.0.0"
  peerDependencies:
    eslint: ">=5"
  checksum: 10/cf5f7bcb884addd263be2126c21484e2eb9139ea5d89d484d65d4f332d35937d8dce5fb3b263037dad0088dfa1c16de82b18ba9eb79891f71897f34b807d1817
  languageName: node
  linkType: hard

"eslint-plugin-playwright@npm:2.1.0":
  version: 2.1.0
  resolution: "eslint-plugin-playwright@npm:2.1.0"
  dependencies:
    globals: "npm:^13.23.0"
  peerDependencies:
    eslint: ">=8.40.0"
  checksum: 10/5c36202a56760203bf3738b03fbd1fddce520f09772b998d2cd7631636f5dec1c4dda724af94ccafb462ffadd113bb8a940ef6991661095cab4997cf19863000
  languageName: node
  linkType: hard

"eslint-plugin-prettier@npm:^5.2.1":
  version: 5.2.1
  resolution: "eslint-plugin-prettier@npm:5.2.1"
  dependencies:
    prettier-linter-helpers: "npm:^1.0.0"
    synckit: "npm:^0.9.1"
  peerDependencies:
    "@types/eslint": ">=8.0.0"
    eslint: ">=8.0.0"
    eslint-config-prettier: "*"
    prettier: ">=3.0.0"
  peerDependenciesMeta:
    "@types/eslint":
      optional: true
    eslint-config-prettier:
      optional: true
  checksum: 10/10ddf68215237e327af09a47adab4c63f3885fda4fb28c4c42d1fc5f47d8a0cc45df6484799360ff1417a0aa3c77c3aaac49d7e9dfd145557b17e2d7ecc2a27c
  languageName: node
  linkType: hard

"eslint-plugin-vue@npm:9.32.0":
  version: 9.32.0
  resolution: "eslint-plugin-vue@npm:9.32.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.4.0"
    globals: "npm:^13.24.0"
    natural-compare: "npm:^1.4.0"
    nth-check: "npm:^2.1.1"
    postcss-selector-parser: "npm:^6.0.15"
    semver: "npm:^7.6.3"
    vue-eslint-parser: "npm:^9.4.3"
    xml-name-validator: "npm:^4.0.0"
  peerDependencies:
    eslint: ^6.2.0 || ^7.0.0 || ^8.0.0 || ^9.0.0
  checksum: 10/2e1f22e82dfa5aa82b71a3b1ff1ba3de25b65018f62b9ae081bea6291832ce1bdbdbdcaeaab23c67863224a1dbd66bfe71ef6d40640468e93480318e60020816
  languageName: node
  linkType: hard

"eslint-scope@npm:^7.1.1":
  version: 7.2.2
  resolution: "eslint-scope@npm:7.2.2"
  dependencies:
    esrecurse: "npm:^4.3.0"
    estraverse: "npm:^5.2.0"
  checksum: 10/5c660fb905d5883ad018a6fea2b49f3cb5b1cbf2cd4bd08e98646e9864f9bc2c74c0839bed2d292e90a4a328833accc197c8f0baed89cbe8d605d6f918465491
  languageName: node
  linkType: hard

"eslint-scope@npm:^8.2.0":
  version: 8.2.0
  resolution: "eslint-scope@npm:8.2.0"
  dependencies:
    esrecurse: "npm:^4.3.0"
    estraverse: "npm:^5.2.0"
  checksum: 10/cd9ab60d5a68f3a0fcac04d1cff5a7383d0f331964d5f1c446259123caec5b3ccc542284d07846e4f4d1389da77750821cc9a6e1ce18558c674977351666f9a6
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.0.0, eslint-visitor-keys@npm:^3.3.0, eslint-visitor-keys@npm:^3.4.1, eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 10/3f357c554a9ea794b094a09bd4187e5eacd1bc0d0653c3adeb87962c548e6a1ab8f982b86963ae1337f5d976004146536dcee5d0e2806665b193fbfbf1a9231b
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^4.2.0":
  version: 4.2.0
  resolution: "eslint-visitor-keys@npm:4.2.0"
  checksum: 10/9651b3356b01760e586b4c631c5268c0e1a85236e3292bf754f0472f465bf9a856c0ddc261fceace155334118c0151778effafbab981413dbf9288349343fa25
  languageName: node
  linkType: hard

"eslint@npm:9.17.0":
  version: 9.17.0
  resolution: "eslint@npm:9.17.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.2.0"
    "@eslint-community/regexpp": "npm:^4.12.1"
    "@eslint/config-array": "npm:^0.19.0"
    "@eslint/core": "npm:^0.9.0"
    "@eslint/eslintrc": "npm:^3.2.0"
    "@eslint/js": "npm:9.17.0"
    "@eslint/plugin-kit": "npm:^0.2.3"
    "@humanfs/node": "npm:^0.16.6"
    "@humanwhocodes/module-importer": "npm:^1.0.1"
    "@humanwhocodes/retry": "npm:^0.4.1"
    "@types/estree": "npm:^1.0.6"
    "@types/json-schema": "npm:^7.0.15"
    ajv: "npm:^6.12.4"
    chalk: "npm:^4.0.0"
    cross-spawn: "npm:^7.0.6"
    debug: "npm:^4.3.2"
    escape-string-regexp: "npm:^4.0.0"
    eslint-scope: "npm:^8.2.0"
    eslint-visitor-keys: "npm:^4.2.0"
    espree: "npm:^10.3.0"
    esquery: "npm:^1.5.0"
    esutils: "npm:^2.0.2"
    fast-deep-equal: "npm:^3.1.3"
    file-entry-cache: "npm:^8.0.0"
    find-up: "npm:^5.0.0"
    glob-parent: "npm:^6.0.2"
    ignore: "npm:^5.2.0"
    imurmurhash: "npm:^0.1.4"
    is-glob: "npm:^4.0.0"
    json-stable-stringify-without-jsonify: "npm:^1.0.1"
    lodash.merge: "npm:^4.6.2"
    minimatch: "npm:^3.1.2"
    natural-compare: "npm:^1.4.0"
    optionator: "npm:^0.9.3"
  peerDependencies:
    jiti: "*"
  peerDependenciesMeta:
    jiti:
      optional: true
  bin:
    eslint: bin/eslint.js
  checksum: 10/a48ee67dd4e737974bbb49ca5d12d0ce35bcd874507807599e3655bb398320ab27c9deed1aad508a963967815e626c21208f52158c2fc0796d0cc8186528efeb
  languageName: node
  linkType: hard

"espree@npm:^10.0.1, espree@npm:^10.3.0":
  version: 10.3.0
  resolution: "espree@npm:10.3.0"
  dependencies:
    acorn: "npm:^8.14.0"
    acorn-jsx: "npm:^5.3.2"
    eslint-visitor-keys: "npm:^4.2.0"
  checksum: 10/3412d44d4204c9e29d6b5dd0277400cfa0cd68495dc09eae1b9ce79d0c8985c1c5cc09cb9ba32a1cd963f48a49b0c46bdb7736afe395a300aa6bb1c0d86837e8
  languageName: node
  linkType: hard

"espree@npm:^9.0.0, espree@npm:^9.3.1":
  version: 9.6.1
  resolution: "espree@npm:9.6.1"
  dependencies:
    acorn: "npm:^8.9.0"
    acorn-jsx: "npm:^5.3.2"
    eslint-visitor-keys: "npm:^3.4.1"
  checksum: 10/255ab260f0d711a54096bdeda93adff0eadf02a6f9b92f02b323e83a2b7fc258797919437ad331efec3930475feb0142c5ecaaf3cdab4befebd336d47d3f3134
  languageName: node
  linkType: hard

"esquery@npm:^1.4.0, esquery@npm:^1.5.0":
  version: 1.6.0
  resolution: "esquery@npm:1.6.0"
  dependencies:
    estraverse: "npm:^5.1.0"
  checksum: 10/c587fb8ec9ed83f2b1bc97cf2f6854cc30bf784a79d62ba08c6e358bf22280d69aee12827521cf38e69ae9761d23fb7fde593ce315610f85655c139d99b05e5a
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: "npm:^5.2.0"
  checksum: 10/44ffcd89e714ea6b30143e7f119b104fc4d75e77ee913f34d59076b40ef2d21967f84e019f84e1fd0465b42cdbf725db449f232b5e47f29df29ed76194db8e16
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 10/37cbe6e9a68014d34dbdc039f90d0baf72436809d02edffcc06ba3c2a12eb298048f877511353b130153e532aac8d68ba78430c0dd2f44806ebc7c014b01585e
  languageName: node
  linkType: hard

"estree-walker@npm:^2.0.2":
  version: 2.0.2
  resolution: "estree-walker@npm:2.0.2"
  checksum: 10/b02109c5d46bc2ed47de4990eef770f7457b1159a229f0999a09224d2b85ffeed2d7679cffcff90aeb4448e94b0168feb5265b209cdec29aad50a3d6e93d21e2
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 10/b23acd24791db11d8f65be5ea58fd9a6ce2df5120ae2da65c16cfc5331ff59d5ac4ef50af66cd4bde238881503ec839928a0135b99a036a9cdfa22d17fd56cdb
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.1
  resolution: "exponential-backoff@npm:3.1.1"
  checksum: 10/2d9bbb6473de7051f96790d5f9a678f32e60ed0aa70741dc7fdc96fec8d631124ec3374ac144387604f05afff9500f31a1d45bd9eee4cdc2e4f9ad2d9b9d5dbd
  languageName: node
  linkType: hard

"external-editor@npm:^3.0.3":
  version: 3.1.0
  resolution: "external-editor@npm:3.1.0"
  dependencies:
    chardet: "npm:^0.7.0"
    iconv-lite: "npm:^0.4.24"
    tmp: "npm:^0.0.33"
  checksum: 10/776dff1d64a1d28f77ff93e9e75421a81c062983fd1544279d0a32f563c0b18c52abbb211f31262e2827e48edef5c9dc8f960d06dd2d42d1654443b88568056b
  languageName: node
  linkType: hard

"extract-files@npm:^11.0.0":
  version: 11.0.0
  resolution: "extract-files@npm:11.0.0"
  checksum: 10/02bf0dde9617d67795e38a182d8bf58828a7c5d77762623ff05e72d461a0e980071a860e2503231db2cc8824d8da35cefb1750937dcbe018cb0e67e37f20a7be
  languageName: node
  linkType: hard

"fast-decode-uri-component@npm:^1.0.1":
  version: 1.0.1
  resolution: "fast-decode-uri-component@npm:1.0.1"
  checksum: 10/4b6ed26974414f688be4a15eab6afa997bad4a7c8605cb1deb928b28514817b4523a1af0fa06621c6cbfedb7e5615144c2c3e7512860e3a333a31a28d537dca7
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: 10/e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-diff@npm:^1.1.2":
  version: 1.3.0
  resolution: "fast-diff@npm:1.3.0"
  checksum: 10/9e57415bc69cd6efcc720b3b8fe9fdaf42dcfc06f86f0f45378b1fa512598a8aac48aa3928c8751d58e2f01bb4ba4f07e4f3d9bc0d57586d45f1bd1e872c6cde
  languageName: node
  linkType: hard

"fast-glob@npm:^3.2.12, fast-glob@npm:^3.3.2":
  version: 3.3.3
  resolution: "fast-glob@npm:3.3.3"
  dependencies:
    "@nodelib/fs.stat": "npm:^2.0.2"
    "@nodelib/fs.walk": "npm:^1.2.3"
    glob-parent: "npm:^5.1.2"
    merge2: "npm:^1.3.0"
    micromatch: "npm:^4.0.8"
  checksum: 10/dcc6432b269762dd47381d8b8358bf964d8f4f60286ac6aa41c01ade70bda459ff2001b516690b96d5365f68a49242966112b5d5cc9cd82395fa8f9d017c90ad
  languageName: node
  linkType: hard

"fast-glob@npm:^3.2.9":
  version: 3.3.2
  resolution: "fast-glob@npm:3.3.2"
  dependencies:
    "@nodelib/fs.stat": "npm:^2.0.2"
    "@nodelib/fs.walk": "npm:^1.2.3"
    glob-parent: "npm:^5.1.2"
    merge2: "npm:^1.3.0"
    micromatch: "npm:^4.0.4"
  checksum: 10/222512e9315a0efca1276af9adb2127f02105d7288fa746145bf45e2716383fb79eb983c89601a72a399a56b7c18d38ce70457c5466218c5f13fad957cee16df
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: 10/2c20055c1fa43c922428f16ca8bb29f2807de63e5c851f665f7ac9790176c01c3b40335257736b299764a8d383388dabc73c8083b8e1bc3d99f0a941444ec60e
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 10/eb7e220ecf2bab5159d157350b81d01f75726a4382f5a9266f42b9150c4523b9795f7f5d9fbbbeaeac09a441b2369f05ee02db48ea938584205530fe5693cfe1
  languageName: node
  linkType: hard

"fast-querystring@npm:^1.1.1":
  version: 1.1.2
  resolution: "fast-querystring@npm:1.1.2"
  dependencies:
    fast-decode-uri-component: "npm:^1.0.1"
  checksum: 10/981da9b914f2b639dc915bdfa4f34ab028b967d428f02fbd293d99258593fde69c48eea73dfa03ced088268e0a8045c642e8debcd9b4821ebd125e130a0430c7
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.18.0
  resolution: "fastq@npm:1.18.0"
  dependencies:
    reusify: "npm:^1.0.4"
  checksum: 10/c5b501333dc8f5d188d828ea162aad03ff5a81aed185b6d4a5078aaeae0a42babc34296d7af13ebce86401cccd93c9b7b3cbf61280821c5f20af233378b42fbb
  languageName: node
  linkType: hard

"fb-watchman@npm:^2.0.0":
  version: 2.0.2
  resolution: "fb-watchman@npm:2.0.2"
  dependencies:
    bser: "npm:2.1.1"
  checksum: 10/4f95d336fb805786759e383fd7fff342ceb7680f53efcc0ef82f502eb479ce35b98e8b207b6dfdfeea0eba845862107dc73813775fc6b56b3098c6e90a2dad77
  languageName: node
  linkType: hard

"fbjs-css-vars@npm:^1.0.0":
  version: 1.0.2
  resolution: "fbjs-css-vars@npm:1.0.2"
  checksum: 10/72baf6d22c45b75109118b4daecb6c8016d4c83c8c0f23f683f22e9d7c21f32fff6201d288df46eb561e3c7d4bb4489b8ad140b7f56444c453ba407e8bd28511
  languageName: node
  linkType: hard

"fbjs@npm:^3.0.0":
  version: 3.0.5
  resolution: "fbjs@npm:3.0.5"
  dependencies:
    cross-fetch: "npm:^3.1.5"
    fbjs-css-vars: "npm:^1.0.0"
    loose-envify: "npm:^1.0.0"
    object-assign: "npm:^4.1.0"
    promise: "npm:^7.1.1"
    setimmediate: "npm:^1.0.5"
    ua-parser-js: "npm:^1.0.35"
  checksum: 10/71252595b00b06fb0475a295c74d81ada1cc499b7e11f2cde51fef04618affa568f5b7f4927f61720c23254b9144be28f8acb2086a5001cf65df8eec87c6ca5c
  languageName: node
  linkType: hard

"figures@npm:^3.0.0":
  version: 3.2.0
  resolution: "figures@npm:3.2.0"
  dependencies:
    escape-string-regexp: "npm:^1.0.5"
  checksum: 10/a3bf94e001be51d3770500789157f067218d4bc681a65e1f69d482de15120bcac822dceb1a7b3803f32e4e3a61a46df44f7f2c8ba95d6375e7491502e0dd3d97
  languageName: node
  linkType: hard

"file-entry-cache@npm:^8.0.0":
  version: 8.0.0
  resolution: "file-entry-cache@npm:8.0.0"
  dependencies:
    flat-cache: "npm:^4.0.0"
  checksum: 10/********************************************************************************************************************************
  languageName: node
  linkType: hard

"file-saver@npm:2.0.5":
  version: 2.0.5
  resolution: "file-saver@npm:2.0.5"
  checksum: 10/fbba443d9b682fec0be6676c048a7ac688b9bd33b105c02e8e1a1cb3e354ed441198dc1f15dcbc137fa044bea73f8a7549f2617e3a952fa7d96390356d54a7c3
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: "npm:^5.0.1"
  checksum: 10/a7095cb39e5bc32fada2aa7c7249d3f6b01bd1ce461a61b0adabacccabd9198500c6fb1f68a7c851a657e273fce2233ba869638897f3d7ed2e87a2d89b4436ea
  languageName: node
  linkType: hard

"find-up@npm:^4.1.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: "npm:^5.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10/4c172680e8f8c1f78839486e14a43ef82e9decd0e74145f40707cc42e7420506d5ec92d9a11c22bd2c48fb0c384ea05dd30e10dd152fefeec6f2f75282a8b844
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: "npm:^6.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10/07955e357348f34660bde7920783204ff5a26ac2cafcaa28bace494027158a97b9f56faaf2d89a6106211a8174db650dd9f503f9c0d526b1202d5554a00b9095
  languageName: node
  linkType: hard

"flat-cache@npm:^4.0.0":
  version: 4.0.1
  resolution: "flat-cache@npm:4.0.1"
  dependencies:
    flatted: "npm:^3.2.9"
    keyv: "npm:^4.5.4"
  checksum: 10/58ce851d9045fffc7871ce2bd718bc485ad7e777bf748c054904b87c351ff1080c2c11da00788d78738bfb51b71e4d5ea12d13b98eb36e3358851ffe495b62dc
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9":
  version: 3.3.2
  resolution: "flatted@npm:3.3.2"
  checksum: 10/********************************************************************************************************************************
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.0
  resolution: "foreground-child@npm:3.3.0"
  dependencies:
    cross-spawn: "npm:^7.0.0"
    signal-exit: "npm:^4.0.1"
  checksum: 10/e3a60480f3a09b12273ce2c5fcb9514d98dd0e528f58656a1b04680225f918d60a2f81f6a368f2f3b937fcee9cfc0cbf16f1ad9a0bc6a3a6e103a84c9a90087e
  languageName: node
  linkType: hard

"form-data@npm:^4.0.1":
  version: 4.0.2
  resolution: "form-data@npm:4.0.2"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.8"
    es-set-tostringtag: "npm:^2.1.0"
    mime-types: "npm:^2.1.12"
  checksum: 10/82c65b426af4a40090e517a1bc9057f76970b4c6043e37aa49859c447d88553e77d4cc5626395079a53d2b0889ba5f2a49f3900db3ad3f3f1bf76613532572fb
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/af143246cf6884fe26fa281621d45cfe111d34b30535a475bfa38dafe343dadb466c047a924ffc7d6b7b18265df4110224ce3803806dbb07173bf2087b648d7f
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 10/e703107c28e362d8d7b910bbcbfd371e640a3bb45ae157a362b5952c0030c0b6d4981140ec319b347bce7adc025dd7813da1ff908a945ac214d64f5402a51b96
  languageName: node
  linkType: hard

"fsevents@npm:2.3.2":
  version: 2.3.2
  resolution: "fsevents@npm:2.3.2"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10/6b5b6f5692372446ff81cf9501c76e3e0459a4852b3b5f1fc72c103198c125a6b8c72f5f166bdd76ffb2fca261e7f6ee5565daf80dca6e571e55bcc589cc1256
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2, fsevents@npm:~2.3.3":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10/4c1ade961ded57cdbfbb5cac5106ec17bc8bccd62e16343c569a0ceeca83b9dfef87550b4dc5cbb89642da412b20c5071f304c8c464b80415446e8e155a038c0
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A2.3.2#optional!builtin<compat/fsevents>":
  version: 2.3.2
  resolution: "fsevents@patch:fsevents@npm%3A2.3.2#optional!builtin<compat/fsevents>::version=2.3.2&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A~2.3.2#optional!builtin<compat/fsevents>, fsevents@patch:fsevents@npm%3A~2.3.3#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 10/185e20d20f10c8d661d59aac0f3b63b31132d492e1b11fcc2a93cb2c47257ebaee7407c38513efd2b35cafdf972d9beb2ea4593c1e0f3bf8f2744836928d7454
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: 10/17d8333460204fbf1f9160d067e1e77f908a5447febb49424b8ab043026049835c9ef3974445c57dbd39161f4d2b04356d7de12b2eecaa27a7a7ea7d871cbedd
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.1, get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: 10/b9769a836d2a98c3ee734a88ba712e62703f1df31b94b784762c433c27a386dd6029ff55c2a920c392e33657d80191edbf18c61487e198844844516f843496b9
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.6":
  version: 1.3.0
  resolution: "get-intrinsic@npm:1.3.0"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    function-bind: "npm:^1.1.2"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10/6e9dd920ff054147b6f44cb98104330e87caafae051b6d37b13384a45ba15e71af33c3baeac7cb630a0aaa23142718dcf25b45cfdd86c184c5dcb4e56d953a10
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10/4fc96afdb58ced9a67558698b91433e6b037aaa6f1493af77498d7c85b141382cf223c0e5946f334fb328ee85dfe6edd06d218eaf09556f4bc4ec6005d7f5f7b
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: "npm:^4.0.1"
  checksum: 10/32cd106ce8c0d83731966d31517adb766d02c3812de49c30cfe0675c7c0ae6630c11214c54a5ae67aca882cf738d27fd7768f21aa19118b9245950554be07247
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: "npm:^4.0.3"
  checksum: 10/c13ee97978bef4f55106b71e66428eb1512e71a7466ba49025fc2aec59a5bfb0954d5abd58fc5ee6c9b076eef4e1f6d3375c2e964b88466ca390da4419a786a8
  languageName: node
  linkType: hard

"glob@npm:11.0.2":
  version: 11.0.2
  resolution: "glob@npm:11.0.2"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^4.0.1"
    minimatch: "npm:^10.0.0"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^2.0.0"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10/53501530240150fdceb9ace47ab856acd1e0d598f8101b0760b665fc11dae2160d366563b89232ae4f5df7ddba8f7c92294719268fe932bd3a32d16cc58c3d02
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10, glob@npm:^10.3.3, glob@npm:^10.3.7":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^3.1.2"
    minimatch: "npm:^9.0.4"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^1.11.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10/698dfe11828b7efd0514cd11e573eaed26b2dff611f0400907281ce3eab0c1e56143ef9b35adc7c77ecc71fba74717b510c7c223d34ca8a98ec81777b293d4ac
  languageName: node
  linkType: hard

"glob@npm:^7.1.1, glob@npm:^7.1.6":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^3.1.1"
    once: "npm:^1.3.0"
    path-is-absolute: "npm:^1.0.0"
  checksum: 10/59452a9202c81d4508a43b8af7082ca5c76452b9fcc4a9ab17655822e6ce9b21d4f8fbadabe4fe3faef448294cec249af305e2cd824b7e9aaf689240e5e96a7b
  languageName: node
  linkType: hard

"glob@npm:^8.0.1":
  version: 8.1.0
  resolution: "glob@npm:8.1.0"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^5.0.1"
    once: "npm:^1.3.0"
  checksum: 10/9aab1c75eb087c35dbc41d1f742e51d0507aa2b14c910d96fb8287107a10a22f4bbdce26fc0a3da4c69a20f7b26d62f1640b346a4f6e6becfff47f335bb1dc5e
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 10/9f054fa38ff8de8fa356502eb9d2dae0c928217b8b5c8de1f09f5c9b6c8a96d8b9bd3afc49acbcd384a98a81fea713c859e1b09e214c60509517bb8fc2bc13c2
  languageName: node
  linkType: hard

"globals@npm:^13.23.0, globals@npm:^13.24.0":
  version: 13.24.0
  resolution: "globals@npm:13.24.0"
  dependencies:
    type-fest: "npm:^0.20.2"
  checksum: 10/62c5b1997d06674fc7191d3e01e324d3eda4d65ac9cc4e78329fa3b5c4fd42a0e1c8722822497a6964eee075255ce21ccf1eec2d83f92ef3f06653af4d0ee28e
  languageName: node
  linkType: hard

"globals@npm:^14.0.0":
  version: 14.0.0
  resolution: "globals@npm:14.0.0"
  checksum: 10/03939c8af95c6df5014b137cac83aa909090c3a3985caef06ee9a5a669790877af8698ab38007e4c0186873adc14c0b13764acc754b16a754c216cc56aa5f021
  languageName: node
  linkType: hard

"globals@npm:^15.0.0":
  version: 15.14.0
  resolution: "globals@npm:15.14.0"
  checksum: 10/e35ffbdbc024d6381efca906f67211a7bbf935db2af8c14a65155785479e28b3e475950e5933bb6b296eed54b6dcd924e25b26dbc8579b1bde9d5d25916e1c5f
  languageName: node
  linkType: hard

"globby@npm:^11.0.3":
  version: 11.1.0
  resolution: "globby@npm:11.1.0"
  dependencies:
    array-union: "npm:^2.1.0"
    dir-glob: "npm:^3.0.1"
    fast-glob: "npm:^3.2.9"
    ignore: "npm:^5.2.0"
    merge2: "npm:^1.4.1"
    slash: "npm:^3.0.0"
  checksum: 10/288e95e310227bbe037076ea81b7c2598ccbc3122d87abc6dab39e1eec309aa14f0e366a98cdc45237ffcfcbad3db597778c0068217dcb1950fef6249104e1b1
  languageName: node
  linkType: hard

"gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: 10/94e296d69f92dc1c0768fcfeecfb3855582ab59a7c75e969d5f96ce50c3d201fd86d5a2857c22565764d5bb8a816c7b1e58f133ec318cd56274da36c5e3fb1a1
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10/bf152d0ed1dc159239db1ba1f74fdbc40cb02f626770dcd5815c427ce0688c2635a06ed69af364396da4636d0408fcf7d4afdf7881724c3307e46aff30ca49e2
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: 10/6dd60dba97007b21e3a829fab3f771803cc1292977fe610e240ea72afd67e5690ac9eeaafc4a99710e78962e5936ab5a460787c2a1180f1cb0ccfac37d29f897
  languageName: node
  linkType: hard

"graphql-config@npm:^5.1.1, graphql-config@npm:^5.1.3":
  version: 5.1.3
  resolution: "graphql-config@npm:5.1.3"
  dependencies:
    "@graphql-tools/graphql-file-loader": "npm:^8.0.0"
    "@graphql-tools/json-file-loader": "npm:^8.0.0"
    "@graphql-tools/load": "npm:^8.0.0"
    "@graphql-tools/merge": "npm:^9.0.0"
    "@graphql-tools/url-loader": "npm:^8.0.0"
    "@graphql-tools/utils": "npm:^10.0.0"
    cosmiconfig: "npm:^8.1.0"
    jiti: "npm:^2.0.0"
    minimatch: "npm:^9.0.5"
    string-env-interpolation: "npm:^1.0.1"
    tslib: "npm:^2.4.0"
  peerDependencies:
    cosmiconfig-toml-loader: ^1.0.0
    graphql: ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0
  peerDependenciesMeta:
    cosmiconfig-toml-loader:
      optional: true
  checksum: 10/9d37f5d424f302808102d118988878be5e4841ba1a06a865cdb9052b24e26eaa9923fb18163bf4f32102d87b3895c53e2ffcdebc1d651f04b56f93f5c38b83c3
  languageName: node
  linkType: hard

"graphql-depth-limit@npm:^1.1.0":
  version: 1.1.0
  resolution: "graphql-depth-limit@npm:1.1.0"
  dependencies:
    arrify: "npm:^1.0.1"
  peerDependencies:
    graphql: "*"
  checksum: 10/fc45ac82f002d8130f9997cbe3d650409fbb5c33f4f9a057218f5847a2cb1cfceb89d6b4271f22693370a1f1c82e3697a9da7667e1d8f55672700f97c4b973b0
  languageName: node
  linkType: hard

"graphql-request@npm:^6.0.0":
  version: 6.1.0
  resolution: "graphql-request@npm:6.1.0"
  dependencies:
    "@graphql-typed-document-node/core": "npm:^3.2.0"
    cross-fetch: "npm:^3.1.5"
  peerDependencies:
    graphql: 14 - 16
  checksum: 10/a9c6f2eeaad972cdecb91437c15c785a282263fd0ef36f6fc5648e0945da488cdc10ab4736891ee1fbb928c7bf6e0bc8e0284df514254adefe02cc406ba5fce5
  languageName: node
  linkType: hard

"graphql-request@npm:^7.1.2":
  version: 7.1.2
  resolution: "graphql-request@npm:7.1.2"
  dependencies:
    "@graphql-typed-document-node/core": "npm:^3.2.0"
  peerDependencies:
    graphql: 14 - 16
  checksum: 10/08e6612d88103ced678f210e4c1a50366ca882622c6383e974aa285963e33c24979fea62aaeb4380b2c1069abd640346d132509f1350f4d4decdf5122ed21e16
  languageName: node
  linkType: hard

"graphql-tag@npm:^2.11.0":
  version: 2.12.6
  resolution: "graphql-tag@npm:2.12.6"
  dependencies:
    tslib: "npm:^2.1.0"
  peerDependencies:
    graphql: ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0
  checksum: 10/23a2bc1d3fbeae86444204e0ac08522e09dc369559ba75768e47421a7321b59f352fb5b2c9a5c37d3cf6de890dca4e5ac47e740c7cc622e728572ecaa649089e
  languageName: node
  linkType: hard

"graphql-ws@npm:^5.14.0":
  version: 5.16.0
  resolution: "graphql-ws@npm:5.16.0"
  peerDependencies:
    graphql: ">=0.11 <=16"
  checksum: 10/e56d903920c78fa88966e31940d281f8b35ef8c9f4543255bfe349e47a0e972c6ca746bcb52040b1c6938d22e42560228994399972abc473cfa6bcd183aca709
  languageName: node
  linkType: hard

"graphql@npm:^16.9.0":
  version: 16.9.0
  resolution: "graphql@npm:16.9.0"
  checksum: 10/5833f82bb6c31bec120bbf9cd400eda873e1bb7ef5c17974fa262cd82dc68728fda5d4cb859dc8aaa4c4fe4f6fe1103a9c47efc01a12c02ae5cb581d8e4029e2
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 10/261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: 10/959385c98696ebbca51e7534e0dc723ada325efa3475350951363cce216d27373e0259b63edb599f72eb94d6cde8577b4b2375f080b303947e560f85692834fa
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: "npm:^1.0.3"
  checksum: 10/c74c5f5ceee3c8a5b8bc37719840dc3749f5b0306d818974141dda2471a1a2ca6c8e46b9d6ac222c5345df7a901c9b6f350b1e6d62763fec877e26609a401bfe
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 10/7898a9c1788b2862cf0f9c345a6bec77ba4a0c0983c7f19d610c382343d4f98fa260686b225dfb1f88393a66679d2ec58ee310c1d6868c081eda7918f32cc70a
  languageName: node
  linkType: hard

"he@npm:^1.2.0":
  version: 1.2.0
  resolution: "he@npm:1.2.0"
  bin:
    he: bin/he
  checksum: 10/d09b2243da4e23f53336e8de3093e5c43d2c39f8d0d18817abfa32ce3e9355391b2edb4bb5edc376aea5d4b0b59d6a0482aab4c52bc02ef95751e4b818e847f1
  languageName: node
  linkType: hard

"header-case@npm:^2.0.4":
  version: 2.0.4
  resolution: "header-case@npm:2.0.4"
  dependencies:
    capital-case: "npm:^1.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10/571c83eeb25e8130d172218712f807c0b96d62b020981400bccc1503a7cf14b09b8b10498a962d2739eccf231d950e3848ba7d420b58a6acd2f9283439546cd9
  languageName: node
  linkType: hard

"html-encoding-sniffer@npm:^4.0.0":
  version: 4.0.0
  resolution: "html-encoding-sniffer@npm:4.0.0"
  dependencies:
    whatwg-encoding: "npm:^3.1.1"
  checksum: 10/e86efd493293a5671b8239bd099d42128433bb3c7b0fdc7819282ef8e118a21f5dead0ad6f358e024a4e5c84f17ebb7a9b36075220fac0a6222b207248bede6f
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 10/362d5ed66b12ceb9c0a328fb31200b590ab1b02f4a254a697dc796850cc4385603e75f53ec59f768b2dad3bfa1464bd229f7de278d2899a0e3beffc634b6683f
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0, http-proxy-agent@npm:^7.0.2":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 10/d062acfa0cb82beeb558f1043c6ba770ea892b5fb7b28654dbc70ea2aeea55226dd34c02a294f6c1ca179a5aa483c4ea641846821b182edbd9cc5d89b54c6848
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.0":
  version: 7.0.5
  resolution: "https-proxy-agent@npm:7.0.5"
  dependencies:
    agent-base: "npm:^7.0.2"
    debug: "npm:4"
  checksum: 10/6679d46159ab3f9a5509ee80c3a3fc83fba3a920a5e18d32176c3327852c3c00ad640c0c4210a8fd70ea3c4a6d3a1b375bf01942516e7df80e2646bdc77658ab
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1, https-proxy-agent@npm:^7.0.6":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:4"
  checksum: 10/784b628cbd55b25542a9d85033bdfd03d4eda630fb8b3c9477959367f3be95dc476ed2ecbb9836c359c7c698027fc7b45723a302324433590f45d6c1706e8c13
  languageName: node
  linkType: hard

"iconv-lite@npm:0.6.3, iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10/24e3292dd3dadaa81d065c6f8c41b274a47098150d444b96e5f53b4638a9a71482921ea6a91a1f59bb71d9796de25e04afd05919fa64c360347ba65d3766f10f
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3"
  checksum: 10/6d3a2dac6e5d1fb126d25645c25c3a1209f70cceecc68b8ef51ae0da3cdc078c151fade7524a30b12a3094926336831fca09c666ef55b37e2c69638b5d6bd2e3
  languageName: node
  linkType: hard

"ieee754@npm:^1.1.13":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 10/d9f2557a59036f16c282aaeb107832dc957a93d73397d89bbad4eb1130560560eb695060145e8e6b3b498b15ab95510226649a0b8f52ae06583575419fe10fc4
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0, ignore@npm:^5.3.1":
  version: 5.3.2
  resolution: "ignore@npm:5.3.2"
  checksum: 10/cceb6a457000f8f6a50e1196429750d782afce5680dd878aa4221bd79972d68b3a55b4b1458fc682be978f4d3c6a249046aa0880637367216444ab7b014cfc98
  languageName: node
  linkType: hard

"ignore@npm:^6.0.0":
  version: 6.0.2
  resolution: "ignore@npm:6.0.2"
  checksum: 10/af39e49996cd989763920e445eff897d0ae1e36b5f27b0e09e14a4fd2df89b362f92e720ecf06ef729056842366527db8561d310e904718810b92ffbcd23056d
  languageName: node
  linkType: hard

"immutable@npm:^5.0.2":
  version: 5.0.3
  resolution: "immutable@npm:5.0.3"
  checksum: 10/9aca1c783951bb204d7036fbcefac6dd42e7c8ad77ff54b38c5fc0924e6e16ce2d123c95db47c1170ba63dd3f6fc7aa74a29be7adef984031936c4cd1e9e8554
  languageName: node
  linkType: hard

"immutable@npm:~3.7.6":
  version: 3.7.6
  resolution: "immutable@npm:3.7.6"
  checksum: 10/4f2cc2e0b6839befa2ea9d3ca478971a88ca78cb66c2b077416e5d5203f8e168bffb78284dd45fe1b427a4a8ac37194dfa3cd3e50b39529a00cca387bd6ac955
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1, import-fresh@npm:^3.3.0":
  version: 3.3.0
  resolution: "import-fresh@npm:3.3.0"
  dependencies:
    parent-module: "npm:^1.0.0"
    resolve-from: "npm:^4.0.0"
  checksum: 10/2cacfad06e652b1edc50be650f7ec3be08c5e5a6f6d12d035c440a42a8cc028e60a5b99ca08a77ab4d6b1346da7d971915828f33cdab730d3d42f08242d09baa
  languageName: node
  linkType: hard

"import-from@npm:4.0.0":
  version: 4.0.0
  resolution: "import-from@npm:4.0.0"
  checksum: 10/1fa29c05b048da18914e91d9a529e5d9b91774bebbfab10e53f59bcc1667917672b971cf102fee857f142e5e433ce69fa1f0a596e1c7d82f9947a5ec352694b9
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10/2d30b157a91fe1c1d7c6f653cbf263f039be6c5bfa959245a16d4ee191fc0f2af86c08545b6e6beeb041c56b574d2d5b9f95343d378ab49c0f37394d541e7fc8
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 10/cd3f5cbc9ca2d624c6a1f53f12e6b341659aba0e2d3254ae2b4464aaea8b4294cdb09616abbc59458f980531f2429784ed6a420d48d245bcad0811980c9efae9
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: "npm:^1.3.0"
    wrappy: "npm:1"
  checksum: 10/d2ebd65441a38c8336c223d1b80b921b9fa737e37ea466fd7e253cb000c64ae1f17fa59e68130ef5bda92cfd8d36b83d37dab0eb0a4558bcfec8e8cdfd2dcb67
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:^2.0.3, inherits@npm:^2.0.4":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 10/cd45e923bee15186c07fa4c89db0aace24824c482fb887b528304694b2aa6ff8a898da8657046a5dcf3e46cd6db6c61629551f9215f208d7c3f157cf9b290521
  languageName: node
  linkType: hard

"ini@npm:^1.3.4":
  version: 1.3.8
  resolution: "ini@npm:1.3.8"
  checksum: 10/314ae176e8d4deb3def56106da8002b462221c174ddb7ce0c49ee72c8cd1f9044f7b10cc555a7d8850982c3b9ca96fc212122749f5234bc2b6fb05fb942ed566
  languageName: node
  linkType: hard

"inquirer@npm:^8.0.0":
  version: 8.2.6
  resolution: "inquirer@npm:8.2.6"
  dependencies:
    ansi-escapes: "npm:^4.2.1"
    chalk: "npm:^4.1.1"
    cli-cursor: "npm:^3.1.0"
    cli-width: "npm:^3.0.0"
    external-editor: "npm:^3.0.3"
    figures: "npm:^3.0.0"
    lodash: "npm:^4.17.21"
    mute-stream: "npm:0.0.8"
    ora: "npm:^5.4.1"
    run-async: "npm:^2.4.0"
    rxjs: "npm:^7.5.5"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
    through: "npm:^2.3.6"
    wrap-ansi: "npm:^6.0.1"
  checksum: 10/f642b9e5a94faaba54f277bdda2af0e0a6b592bd7f88c60e1614b5795b19336c7025e0c2923915d5f494f600a02fe8517413779a794415bb79a9563b061d68ab
  languageName: node
  linkType: hard

"invariant@npm:^2.2.4":
  version: 2.2.4
  resolution: "invariant@npm:2.2.4"
  dependencies:
    loose-envify: "npm:^1.0.0"
  checksum: 10/cc3182d793aad82a8d1f0af697b462939cb46066ec48bbf1707c150ad5fad6406137e91a262022c269702e01621f35ef60269f6c0d7fd178487959809acdfb14
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: "npm:1.1.0"
    sprintf-js: "npm:^1.1.3"
  checksum: 10/1ed81e06721af012306329b31f532b5e24e00cb537be18ddc905a84f19fe8f83a09a1699862bf3a1ec4b9dea93c55a3fa5faf8b5ea380431469df540f38b092c
  languageName: node
  linkType: hard

"is-absolute@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-absolute@npm:1.0.0"
  dependencies:
    is-relative: "npm:^1.0.0"
    is-windows: "npm:^1.0.1"
  checksum: 10/9d16b2605eda3f3ce755410f1d423e327ad3a898bcb86c9354cf63970ed3f91ba85e9828aa56f5d6a952b9fae43d0477770f78d37409ae8ecc31e59ebc279b27
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: 10/73ced84fa35e59e2c57da2d01e12cd01479f381d7f122ce41dcbb713f09dbfc651315832cd2bf8accba7681a69e4d6f1e03941d94dd10040d415086360e7005e
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: 10/df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10/44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-glob@npm:4.0.3, is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 10/3ed74f2b0cdf4f401f38edb0442ddfde3092d79d7d35c9919c86641efdbcbb32e45aa3c0f70ce5eecc946896cd5a0f26e4188b9f2b881876f7cb6c505b82da11
  languageName: node
  linkType: hard

"is-interactive@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-interactive@npm:1.0.0"
  checksum: 10/824808776e2d468b2916cdd6c16acacebce060d844c35ca6d82267da692e92c3a16fdba624c50b54a63f38bdc4016055b6f443ce57d7147240de4f8cdabaf6f9
  languageName: node
  linkType: hard

"is-language-code@npm:^3.1.0":
  version: 3.1.0
  resolution: "is-language-code@npm:3.1.0"
  dependencies:
    "@babel/runtime": "npm:^7.14.0"
  checksum: 10/21d8a8be0e364e30de9a4ad4da525e094943aeff7ec849bc4b05ef9ad73986363e8bd431124ce2861c20ca22a3d9665c8a551b82e809f3f47bc38d986e1ece60
  languageName: node
  linkType: hard

"is-lower-case@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-lower-case@npm:2.0.2"
  dependencies:
    tslib: "npm:^2.0.3"
  checksum: 10/ba57dd1201e15fd9b590654736afccf1b3b68e919f40c23ef13b00ebcc639b1d9c2f81fe86415bff3e8eccffec459786c9ac9dc8f3a19cfa4484206c411c1d7d
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 10/6a6c3383f68afa1e05b286af866017c78f1226d43ac8cb064e115ff9ed85eb33f5c4f7216c96a71e4dfea289ef52c5da3aef5bbfade8ffe47a0465d70c0c8e86
  languageName: node
  linkType: hard

"is-potential-custom-element-name@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-potential-custom-element-name@npm:1.0.1"
  checksum: 10/ced7bbbb6433a5b684af581872afe0e1767e2d1146b2207ca0068a648fb5cab9d898495d1ac0583524faaf24ca98176a7d9876363097c2d14fee6dd324f3a1ab
  languageName: node
  linkType: hard

"is-relative@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-relative@npm:1.0.0"
  dependencies:
    is-unc-path: "npm:^1.0.0"
  checksum: 10/3271a0df109302ef5e14a29dcd5d23d9788e15ade91a40b942b035827ffbb59f7ce9ff82d036ea798541a52913cbf9d2d0b66456340887b51f3542d57b5a4c05
  languageName: node
  linkType: hard

"is-unc-path@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-unc-path@npm:1.0.0"
  dependencies:
    unc-path-regex: "npm:^0.1.2"
  checksum: 10/e8abfde203f7409f5b03a5f1f8636e3a41e78b983702ef49d9343eb608cdfe691429398e8815157519b987b739bcfbc73ae7cf4c8582b0ab66add5171088eab6
  languageName: node
  linkType: hard

"is-unicode-supported@npm:^0.1.0":
  version: 0.1.0
  resolution: "is-unicode-supported@npm:0.1.0"
  checksum: 10/a2aab86ee7712f5c2f999180daaba5f361bdad1efadc9610ff5b8ab5495b86e4f627839d085c6530363c6d6d4ecbde340fb8e54bdb83da4ba8e0865ed5513c52
  languageName: node
  linkType: hard

"is-upper-case@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-upper-case@npm:2.0.2"
  dependencies:
    tslib: "npm:^2.0.3"
  checksum: 10/cf4fd43c00c2e72cd5cff911923070b89f0933b464941bd782e2315385f80b5a5acd772db3b796542e5e3cfed735f4dffd88c54d62db1ebfc5c3daa7b1af2bc6
  languageName: node
  linkType: hard

"is-valid-glob@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-valid-glob@npm:1.0.0"
  checksum: 10/0155951e89291d405cbb2ff4e25a38ee7a88bc70b05f246c25d31a1d09f13d4207377e5860f67443bbda8e3e353da37047b60e586bd9c97a39c9301c30b67acb
  languageName: node
  linkType: hard

"is-windows@npm:^1.0.1":
  version: 1.0.2
  resolution: "is-windows@npm:1.0.2"
  checksum: 10/438b7e52656fe3b9b293b180defb4e448088e7023a523ec21a91a80b9ff8cdb3377ddb5b6e60f7c7de4fa8b63ab56e121b6705fe081b3cf1b828b0a380009ad7
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10/7c9f715c03aff08f35e98b1fadae1b9267b38f0615d501824f9743f3aab99ef10e303ce7db3f186763a0b70a19de5791ebfc854ff884d5a8c4d92211f642ec92
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 10/7fe1931ee4e88eb5aa524cd3ceb8c882537bc3a81b02e438b240e47012eef49c86904d0f0e593ea7c3a9996d18d0f1f3be8d3eaa92333977b0c3a9d353d5563e
  languageName: node
  linkType: hard

"isomorphic-ws@npm:^5.0.0":
  version: 5.0.0
  resolution: "isomorphic-ws@npm:5.0.0"
  peerDependencies:
    ws: "*"
  checksum: 10/e20eb2aee09ba96247465fda40c6d22c1153394c0144fa34fe6609f341af4c8c564f60ea3ba762335a7a9c306809349f9b863c8beedf2beea09b299834ad5398
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10/96f8786eaab98e4bf5b2a5d6d9588ea46c4d06bbc4f2eb861fdd7b6b182b16f71d8a70e79820f335d52653b16d4843b29dd9cdcf38ae80406756db9199497cf3
  languageName: node
  linkType: hard

"jackspeak@npm:^4.0.1":
  version: 4.1.0
  resolution: "jackspeak@npm:4.1.0"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
  checksum: 10/d3ad964e87a3d66ec86b6d466ff150cf3472bbda738a9c4f882ece96c7fb59f0013be1f6cad17cbedd36260741db6cf8912b8e037cd7c7eb72b3532246e54f77
  languageName: node
  linkType: hard

"jiti@npm:^1.17.1":
  version: 1.21.6
  resolution: "jiti@npm:1.21.6"
  bin:
    jiti: bin/jiti.js
  checksum: 10/289b124cea411c130a14ffe88e3d38376ab44b6695616dfa0a1f32176a8f20ec90cdd6d2b9d81450fc6467cfa4d865f04f49b98452bff0f812bc400fd0ae78d6
  languageName: node
  linkType: hard

"jiti@npm:^2.0.0":
  version: 2.4.1
  resolution: "jiti@npm:2.4.1"
  bin:
    jiti: lib/jiti-cli.mjs
  checksum: 10/c05d3645ff4a88f5c52e33757dbae18737f6b51aa46631ed18cbf7741f2d997eb91ffd4249f61b47779d8ac1931d6539ec48dfdab8e1ca761cc160aa240d09f2
  languageName: node
  linkType: hard

"jose@npm:^5.0.0":
  version: 5.9.6
  resolution: "jose@npm:5.9.6"
  checksum: 10/3ebbda9f6a96d493944f2720bf4436347884666cd87b7087a61cff12a3b540fe6fd743b5eb8defe7bc2a45aa58992ae6687da78797d91fc4e3e5e8588aa98c7d
  languageName: node
  linkType: hard

"js-beautify@npm:^1.14.9":
  version: 1.15.1
  resolution: "js-beautify@npm:1.15.1"
  dependencies:
    config-chain: "npm:^1.1.13"
    editorconfig: "npm:^1.0.4"
    glob: "npm:^10.3.3"
    js-cookie: "npm:^3.0.5"
    nopt: "npm:^7.2.0"
  bin:
    css-beautify: js/bin/css-beautify.js
    html-beautify: js/bin/html-beautify.js
    js-beautify: js/bin/js-beautify.js
  checksum: 10/9fc7111fc30035e8674e778ed2c271b2762084039482252d0a53fd94341e2009daceda99f4d9d0272672f2e136367ec8ce1008a04eb00140bd42c9668c3ed9af
  languageName: node
  linkType: hard

"js-cookie@npm:^3.0.5":
  version: 3.0.5
  resolution: "js-cookie@npm:3.0.5"
  checksum: 10/366494b1630b9fb8abaef3659748db5dfd52c58c6fc3459b9f0a03b492593bc1b01c6dfcc066b46f6413c28edb3a00cc68fb61ea8cdf6991bedf1f100f8a389d
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0, js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 10/af37d0d913fb56aec6dc0074c163cc71cd23c0b8aad5c2350747b6721d37ba118af35abdd8b33c47ec2800de07dedb16a527ca9c530ee004093e04958bd0cbf2
  languageName: node
  linkType: hard

"js-yaml@npm:^4.0.0, js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: "npm:^2.0.1"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10/c138a34a3fd0d08ebaf71273ad4465569a483b8a639e0b118ff65698d257c2791d3199e3f303631f2cb98213fa7b5f5d6a4621fd0fff819421b990d30d967140
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 10/bebe7ae829bbd586ce8cbe83501dd8cb8c282c8902a8aeeed0a073a89dc37e8103b1244f3c6acd60278bcbfe12d93a3f83c9ac396868a3b3bbc3c5e5e3b648ef
  languageName: node
  linkType: hard

"jsdom@npm:26.0.0":
  version: 26.0.0
  resolution: "jsdom@npm:26.0.0"
  dependencies:
    cssstyle: "npm:^4.2.1"
    data-urls: "npm:^5.0.0"
    decimal.js: "npm:^10.4.3"
    form-data: "npm:^4.0.1"
    html-encoding-sniffer: "npm:^4.0.0"
    http-proxy-agent: "npm:^7.0.2"
    https-proxy-agent: "npm:^7.0.6"
    is-potential-custom-element-name: "npm:^1.0.1"
    nwsapi: "npm:^2.2.16"
    parse5: "npm:^7.2.1"
    rrweb-cssom: "npm:^0.8.0"
    saxes: "npm:^6.0.0"
    symbol-tree: "npm:^3.2.4"
    tough-cookie: "npm:^5.0.0"
    w3c-xmlserializer: "npm:^5.0.0"
    webidl-conversions: "npm:^7.0.0"
    whatwg-encoding: "npm:^3.1.1"
    whatwg-mimetype: "npm:^4.0.0"
    whatwg-url: "npm:^14.1.0"
    ws: "npm:^8.18.0"
    xml-name-validator: "npm:^5.0.0"
  peerDependencies:
    canvas: ^3.0.0
  peerDependenciesMeta:
    canvas:
      optional: true
  checksum: 10/8c230ee4657240bbbca6b4ebb484be53fc6a777a22a3357c80c5537222813666e3e1f54740bc13e769c461d9598ba7dac402c245949c6cef7ef7014ce6f36f01
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2":
  version: 3.1.0
  resolution: "jsesc@npm:3.1.0"
  bin:
    jsesc: bin/jsesc
  checksum: 10/20bd37a142eca5d1794f354db8f1c9aeb54d85e1f5c247b371de05d23a9751ecd7bd3a9c4fc5298ea6fa09a100dafb4190fa5c98c6610b75952c3487f3ce7967
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 10/82876154521b7b68ba71c4f969b91572d1beabadd87bd3a6b236f85fbc7dc4695089191ed60bb59f9340993c51b33d479f45b6ba9f3548beb519705281c32c3c
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 10/5f3a99009ed5f2a5a67d06e2f298cc97bc86d462034173308156f15b43a6e850be8511dc204b9b94566305da2947f7d90289657237d210351a39059ff9d666cf
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^4.0.0":
  version: 4.0.0
  resolution: "json-parse-even-better-errors@npm:4.0.0"
  checksum: 10/da1ae7ef0cc9db02972a06a71322f26bdcda5d7f648c23b28ce7f158ba35707461bcbd91945d8aace10d8d79c383b896725c65ffa410242352692328aa9b5edf
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 10/7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: 10/12786c2e2f22c27439e6db0532ba321f1d0617c27ad8cb1c352a0e9249a50182fd1ba8b52a18899291604b0c32eafa8afd09e51203f19109a0537f68db2b652d
  languageName: node
  linkType: hard

"json-to-pretty-yaml@npm:^1.2.2":
  version: 1.2.2
  resolution: "json-to-pretty-yaml@npm:1.2.2"
  dependencies:
    remedial: "npm:^1.0.7"
    remove-trailing-spaces: "npm:^1.0.6"
  checksum: 10/3ccd527c9a9cf41e123d75445605801dd0eebcddf53e00af05febc212a3657fceb03063399693d79cb2b7a8530dd062420caf35fa02cc0a4ae182fb74843d920
  languageName: node
  linkType: hard

"json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 10/1db67b853ff0de3534085d630691d3247de53a2ed1390ba0ddff681ea43e9b3e30ecbdb65c5e9aab49435e44059c23dbd6fee8ee619419ba37465bb0dd7135da
  languageName: node
  linkType: hard

"jsonc-eslint-parser@npm:^2.3.0":
  version: 2.4.0
  resolution: "jsonc-eslint-parser@npm:2.4.0"
  dependencies:
    acorn: "npm:^8.5.0"
    eslint-visitor-keys: "npm:^3.0.0"
    espree: "npm:^9.0.0"
    semver: "npm:^7.3.5"
  checksum: 10/bd1d41c852c3488414605a1754617aa7c240ed6730a25a7fd7fb76473e92efdc5ba1728ad3f08f8069de3a19abf1fd275c2b145eb51e2f7f6ca293c8105e1ffe
  languageName: node
  linkType: hard

"keycloak-js@npm:26.0.7":
  version: 26.0.7
  resolution: "keycloak-js@npm:26.0.7"
  checksum: 10/794528358cdb03a28bff99e8cd7019eac5a2c10eb0ab900d1e31baf64f655a538e878350b44cdeadddec210341348d856aa310aee2fd89eed48b3b8a22e87692
  languageName: node
  linkType: hard

"keyv@npm:^4.5.4":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: "npm:3.0.1"
  checksum: 10/167eb6ef64cc84b6fa0780ee50c9de456b422a1e18802209234f7c2cf7eae648c7741f32e50d7e24ccb22b24c13154070b01563d642755b156c357431a191e75
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:~0.4.0"
  checksum: 10/2e4720ff79f21ae08d42374b0a5c2f664c5be8b6c8f565bb4e1315c96ed3a8acaa9de788ffed82d7f2378cf36958573de07ef92336cb5255ed74d08b8318c9ee
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 10/0c37f9f7fa212b38912b7145e1cd16a5f3cd34d782441c3e6ca653485d326f58b3caccda66efce1c5812bde4961bbde3374fae4b0d11bf1226152337f3894aa5
  languageName: node
  linkType: hard

"listr2@npm:^4.0.5":
  version: 4.0.5
  resolution: "listr2@npm:4.0.5"
  dependencies:
    cli-truncate: "npm:^2.1.0"
    colorette: "npm:^2.0.16"
    log-update: "npm:^4.0.0"
    p-map: "npm:^4.0.0"
    rfdc: "npm:^1.3.0"
    rxjs: "npm:^7.5.5"
    through: "npm:^2.3.8"
    wrap-ansi: "npm:^7.0.0"
  peerDependencies:
    enquirer: ">= 2.3.0 < 3"
  peerDependenciesMeta:
    enquirer:
      optional: true
  checksum: 10/9c591fdd4fd6b7e8b4feca60380be01d74c65a98857f6caff2418c609fb9f0016c2e1b65c0ef5b1f4ff015967be87e8642e7ac3ad7ce0aa3c1a0329b60128b3b
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: "npm:^4.1.0"
  checksum: 10/83e51725e67517287d73e1ded92b28602e3ae5580b301fe54bfb76c0c723e3f285b19252e375712316774cf52006cb236aed5704692c32db0d5d089b69696e30
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: "npm:^5.0.0"
  checksum: 10/72eb661788a0368c099a184c59d2fee760b3831c9c1c33955e8a19ae4a21b4116e53fa736dc086cdeb9fce9f7cc508f2f92d2d3aae516f133e16a2bb59a39f5a
  languageName: node
  linkType: hard

"lodash-es@npm:4.17.21":
  version: 4.17.21
  resolution: "lodash-es@npm:4.17.21"
  checksum: 10/03f39878ea1e42b3199bd3f478150ab723f93cc8730ad86fec1f2804f4a07c6e30deaac73cad53a88e9c3db33348bb8ceeb274552390e7a75d7849021c02df43
  languageName: node
  linkType: hard

"lodash.lowercase@npm:^4.3.0":
  version: 4.3.0
  resolution: "lodash.lowercase@npm:4.3.0"
  checksum: 10/9c809375a3e6f5a49e9a4c639d20763cab40ecdf33256627a3607b5e0fb13a065113a9f093ab256b6495f857c2d29e8f1a2416da56f000bab192a7ced51ceb7e
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: 10/d0ea2dd0097e6201be083865d50c3fb54fbfbdb247d9cc5950e086c991f448b7ab0cdab0d57eacccb43473d3f2acd21e134db39f22dac2d6c9ba6bf26978e3d6
  languageName: node
  linkType: hard

"lodash.sortby@npm:^4.7.0":
  version: 4.7.0
  resolution: "lodash.sortby@npm:4.7.0"
  checksum: 10/38df19ae28608af2c50ac342fc1f414508309d53e1d58ed9adfb2c3cd17c3af290058c0a0478028d932c5404df3d53349d19fa364ef6bed6145a6bc21320399e
  languageName: node
  linkType: hard

"lodash@npm:^4.17.20, lodash@npm:^4.17.21, lodash@npm:~4.17.0":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: 10/c08619c038846ea6ac754abd6dd29d2568aa705feb69339e836dfa8d8b09abbb2f859371e86863eda41848221f9af43714491467b5b0299122431e202bb0c532
  languageName: node
  linkType: hard

"log-symbols@npm:^4.0.0, log-symbols@npm:^4.1.0":
  version: 4.1.0
  resolution: "log-symbols@npm:4.1.0"
  dependencies:
    chalk: "npm:^4.1.0"
    is-unicode-supported: "npm:^0.1.0"
  checksum: 10/fce1497b3135a0198803f9f07464165e9eb83ed02ceb2273930a6f8a508951178d8cf4f0378e9d28300a2ed2bc49050995d2bd5f53ab716bb15ac84d58c6ef74
  languageName: node
  linkType: hard

"log-update@npm:^4.0.0":
  version: 4.0.0
  resolution: "log-update@npm:4.0.0"
  dependencies:
    ansi-escapes: "npm:^4.3.0"
    cli-cursor: "npm:^3.1.0"
    slice-ansi: "npm:^4.0.0"
    wrap-ansi: "npm:^6.2.0"
  checksum: 10/ae2f85bbabc1906034154fb7d4c4477c79b3e703d22d78adee8b3862fa913942772e7fa11713e3d96fb46de4e3cabefbf5d0a544344f03b58d3c4bff52aa9eb2
  languageName: node
  linkType: hard

"loose-envify@npm:^1.0.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: "npm:^3.0.0 || ^4.0.0"
  bin:
    loose-envify: cli.js
  checksum: 10/6517e24e0cad87ec9888f500c5b5947032cdfe6ef65e1c1936a0c48a524b81e65542c9c3edc91c97d5bddc806ee2a985dbc79be89215d613b1de5db6d1cfe6f4
  languageName: node
  linkType: hard

"lower-case-first@npm:^2.0.2":
  version: 2.0.2
  resolution: "lower-case-first@npm:2.0.2"
  dependencies:
    tslib: "npm:^2.0.3"
  checksum: 10/33e3da1098ddda219ce125d4ab7a78a944972c0ee8872e95b6ccc35df8ad405284ab233b0ba4d72315ad1a06fe2f0d418ee4cba9ec1ef1c386dea78899fc8958
  languageName: node
  linkType: hard

"lower-case@npm:^2.0.2":
  version: 2.0.2
  resolution: "lower-case@npm:2.0.2"
  dependencies:
    tslib: "npm:^2.0.3"
  checksum: 10/83a0a5f159ad7614bee8bf976b96275f3954335a84fad2696927f609ddae902802c4f3312d86668722e668bef41400254807e1d3a7f2e8c3eede79691aa1f010
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 10/e6e90267360476720fa8e83cc168aa2bf0311f3f2eea20a6ba78b90a885ae72071d9db132f40fda4129c803e7dcec3a6b6a6fbb44ca90b081630b810b5d6a41a
  languageName: node
  linkType: hard

"lru-cache@npm:^11.0.0":
  version: 11.1.0
  resolution: "lru-cache@npm:11.1.0"
  checksum: 10/5011011675ca98428902de774d0963b68c3a193cd959347cb63b781dad4228924124afab82159fd7b8b4db18285d9aff462b877b8f6efd2b41604f806c1d9db4
  languageName: node
  linkType: hard

"lru-cache@npm:^11.0.2":
  version: 11.0.2
  resolution: "lru-cache@npm:11.0.2"
  checksum: 10/25fcb66e9d91eaf17227c6abfe526a7bed5903de74f93bfde380eb8a13410c5e8d3f14fe447293f3f322a7493adf6f9f015c6f1df7a235ff24ec30f366e1c058
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: "npm:^3.0.2"
  checksum: 10/951d2673dcc64a7fb888bf3d13bc2fdf923faca97d89cdb405ba3dfff77e2b26e5798d405e78fcd7094c9e7b8b4dab2ddc5a4f8a11928af24a207b7c738ca3f8
  languageName: node
  linkType: hard

"magic-string@npm:^0.30.11":
  version: 0.30.17
  resolution: "magic-string@npm:0.30.17"
  dependencies:
    "@jridgewell/sourcemap-codec": "npm:^1.5.0"
  checksum: 10/2f71af2b0afd78c2e9012a29b066d2c8ba45a9cd0c8070f7fd72de982fb1c403b4e3afdb1dae00691d56885ede66b772ef6bedf765e02e3a7066208fe2fec4aa
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": "npm:^3.0.0"
    cacache: "npm:^19.0.1"
    http-cache-semantics: "npm:^4.1.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^4.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^1.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^12.0.0"
  checksum: 10/fce0385840b6d86b735053dfe941edc2dd6468fda80fe74da1eeff10cbd82a75760f406194f2bc2fa85b99545b2bc1f84c08ddf994b21830775ba2d1a87e8bdf
  languageName: node
  linkType: hard

"map-cache@npm:^0.2.0":
  version: 0.2.2
  resolution: "map-cache@npm:0.2.2"
  checksum: 10/3067cea54285c43848bb4539f978a15dedc63c03022abeec6ef05c8cb6829f920f13b94bcaf04142fc6a088318e564c4785704072910d120d55dbc2e0c421969
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 10/11df2eda46d092a6035479632e1ec865b8134bdfc4bd9e571a656f4191525404f13a283a515938c3a8de934dbfd9c09674d9da9fa831e6eb7e22b50b197d2edd
  languageName: node
  linkType: hard

"memorystream@npm:^0.3.1":
  version: 0.3.1
  resolution: "memorystream@npm:0.3.1"
  checksum: 10/2e34a1e35e6eb2e342f788f75f96c16f115b81ff6dd39e6c2f48c78b464dbf5b1a4c6ebfae4c573bd0f8dbe8c57d72bb357c60523be184655260d25855c03902
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0, merge2@npm:^1.4.1":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 10/7268db63ed5169466540b6fb947aec313200bcf6d40c5ab722c22e242f651994619bcd85601602972d3c85bd2cc45a358a4c61937e9f11a061919a1da569b0c2
  languageName: node
  linkType: hard

"meros@npm:^1.2.1":
  version: 1.3.0
  resolution: "meros@npm:1.3.0"
  peerDependencies:
    "@types/node": ">=13"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10/1893d226866058a32161ab069294a1a16975c765416a2b05165dfafba07cd958ca12503e35c621ffe736c62d935ccb1ce60cb723e2a9e0b85e02bb3236722ef6
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.4, micromatch@npm:^4.0.5, micromatch@npm:^4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: "npm:^3.0.3"
    picomatch: "npm:^2.3.1"
  checksum: 10/6bf2a01672e7965eb9941d1f02044fad2bd12486b5553dc1116ff24c09a8723157601dc992e74c911d896175918448762df3b3fd0a6b61037dd1a9766ddfbf58
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 10/54bb60bf39e6f8689f6622784e668a3d7f8bed6b0d886f5c3c446cb3284be28b30bf707ed05d0fe44a036f8469976b2629bbea182684977b084de9da274694d7
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 10/89aa9651b67644035de2784a6e665fc685d79aba61857e02b9c8758da874a754aed4a9aced9265f5ed1171fd934331e5516b84a7f0218031b6fa0270eca1e51a
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: 10/d2421a3444848ce7f84bd49115ddacff29c15745db73f54041edc906c14b131a38d05298dae3081667627a59b2eb1ca4b436ff2e1b80f69679522410418b478a
  languageName: node
  linkType: hard

"minimatch@npm:9.0.1":
  version: 9.0.1
  resolution: "minimatch@npm:9.0.1"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10/b4e98f4dc740dcf33999a99af23ae6e5e1c47632f296dc95cb649a282150f92378d41434bf64af4ea2e5975255a757d031c3bf014bad9214544ac57d97f3ba63
  languageName: node
  linkType: hard

"minimatch@npm:^10.0.0":
  version: 10.0.1
  resolution: "minimatch@npm:10.0.1"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10/082e7ccbc090d5f8c4e4e029255d5a1d1e3af37bda837da2b8b0085b1503a1210c91ac90d9ebfe741d8a5f286ece820a1abb4f61dc1f82ce602a055d461d93f3
  languageName: node
  linkType: hard

"minimatch@npm:^3.1.1, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: 10/e0b25b04cd4ec6732830344e5739b13f8690f8a012d73445a4a19fbc623f5dd481ef7a5827fde25954cd6026fede7574cc54dc4643c99d6c6b653d6203f94634
  languageName: node
  linkType: hard

"minimatch@npm:^5.0.1":
  version: 5.1.6
  resolution: "minimatch@npm:5.1.6"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10/126b36485b821daf96d33b5c821dac600cc1ab36c87e7a532594f9b1652b1fa89a1eebcaad4dff17c764dce1a7ac1531327f190fed5f97d8f6e5f889c116c429
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.0, minimatch@npm:^9.0.3, minimatch@npm:^9.0.4, minimatch@npm:^9.0.5":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10/dd6a8927b063aca6d910b119e1f2df6d2ce7d36eab91de83167dd136bb85e1ebff97b0d3de1cb08bd1f7e018ca170b4962479fefab5b2a69e2ae12cb2edc8348
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/b251bceea62090f67a6cced7a446a36f4cd61ee2d5cea9aee7fff79ba8030e416327a1c5aa2908dc22629d06214b46d88fdab8c51ac76bacbf5703851b5ad342
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.0
  resolution: "minipass-fetch@npm:4.0.0"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^3.0.1"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10/4b0772dbee77727b469dc5bfc371541d9aba1e243fbb46ddc1b9ff7efa4de4a4cf5ff3a359d6a3b3a460ca26df9ae67a9c93be26ab6417c225e49d63b52b2801
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/40982d8d836a52b0f37049a0a7e5d0f089637298e6d9b45df9c115d4f0520682a78258905e5c8b180fb41b593b0a82cc1361d2c74b45f7ada66334f84d1ecfdd
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10/a5c6ef069f70d9a524d3428af39f2b117ff8cd84172e19b754e7264a33df460873e6eb3d6e55758531580970de50ae950c496256bb4ad3691a2974cddff189f0
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 10/c25f0ee8196d8e6036661104bacd743785b2599a21de5c516b32b3fa2b83113ac89a2358465bc04956baab37ffb956ae43be679b2262bf7be15fce467ccd7950
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.1
  resolution: "minizlib@npm:3.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
    rimraf: "npm:^5.0.5"
  checksum: 10/622cb85f51e5c206a080a62d20db0d7b4066f308cb6ce82a9644da112367c3416ae7062017e631eb7ac8588191cfa4a9a279b8651c399265202b298e98c4acef
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 10/16fd79c28645759505914561e249b9a1f5fe3362279ad95487a4501e4467abeb714fd35b95307326b8fd03f3c7719065ef11a6f97b7285d7888306d1bd2232ba
  languageName: node
  linkType: hard

"ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10/aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"muggle-string@npm:^0.4.1":
  version: 0.4.1
  resolution: "muggle-string@npm:0.4.1"
  checksum: 10/8fa2ea08f497c04069718bd3fd1909b382114dacbad832d10967ca72690de43f5f8492d8ccfbf827d6be63868ed5fc10395e7b7c082aa95997eea498586c6620
  languageName: node
  linkType: hard

"mute-stream@npm:0.0.8":
  version: 0.0.8
  resolution: "mute-stream@npm:0.0.8"
  checksum: 10/a2d2e79dde87e3424ffc8c334472c7f3d17b072137734ca46e6f221131f1b014201cc593b69a38062e974fb2394d3d1cb4349f80f012bbf8b8ac1b28033e515f
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.7":
  version: 3.3.8
  resolution: "nanoid@npm:3.3.8"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 10/2d1766606cf0d6f47b6f0fdab91761bb81609b2e3d367027aff45e6ee7006f660fb7e7781f4a34799fe6734f1268eeed2e37a5fdee809ade0c2d4eb11b0f9c40
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.8":
  version: 3.3.11
  resolution: "nanoid@npm:3.3.11"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 10/73b5afe5975a307aaa3c95dfe3334c52cdf9ae71518176895229b8d65ab0d1c0417dd081426134eb7571c055720428ea5d57c645138161e7d10df80815527c48
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 10/23ad088b08f898fc9b53011d7bb78ec48e79de7627e01ab5518e806033861bef68d5b0cd0e2205c2f36690ac9571ff6bcb05eb777ced2eeda8d4ac5b44592c3d
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 10/b5734e87295324fabf868e36fb97c84b7d7f3156ec5f4ee5bf6e488079c11054f818290fc33804cef7b1ee21f55eeb14caea83e7dafae6492a409b3e573153e5
  languageName: node
  linkType: hard

"neo-async@npm:^2.6.2":
  version: 2.6.2
  resolution: "neo-async@npm:2.6.2"
  checksum: 10/1a7948fea86f2b33ec766bc899c88796a51ba76a4afc9026764aedc6e7cde692a09067031e4a1bf6db4f978ccd99e7f5b6c03fe47ad9865c3d4f99050d67e002
  languageName: node
  linkType: hard

"no-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "no-case@npm:3.0.4"
  dependencies:
    lower-case: "npm:^2.0.2"
    tslib: "npm:^2.0.3"
  checksum: 10/0b2ebc113dfcf737d48dde49cfebf3ad2d82a8c3188e7100c6f375e30eafbef9e9124aadc3becef237b042fd5eb0aad2fd78669c20972d045bbe7fea8ba0be5c
  languageName: node
  linkType: hard

"node-addon-api@npm:^7.0.0":
  version: 7.1.1
  resolution: "node-addon-api@npm:7.1.1"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10/ee1e1ed6284a2f8cd1d59ac6175ecbabf8978dcf570345e9a8095a9d0a2b9ced591074ae77f9009287b00c402352b38aa9322a34f2199cdc9f567b842a636b94
  languageName: node
  linkType: hard

"node-fetch@npm:^2.6.1, node-fetch@npm:^2.6.12":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: "npm:^5.0.0"
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: 10/b24f8a3dc937f388192e59bcf9d0857d7b6940a2496f328381641cb616efccc9866e89ec43f2ec956bbd6c3d3ee05524ce77fe7b29ccd34692b3a16f237d6676
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.0.0
  resolution: "node-gyp@npm:11.0.0"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    glob: "npm:^10.3.10"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^14.0.3"
    nopt: "npm:^8.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^7.4.3"
    which: "npm:^5.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10/5d07430e887a906f85c7c6ed87e8facb7ecd4ce42d948a2438c471df2e24ae6af70f4def114ec1a03127988d164648dda8d75fe666f3c4b431e53856379fdf13
  languageName: node
  linkType: hard

"node-int64@npm:^0.4.0":
  version: 0.4.0
  resolution: "node-int64@npm:0.4.0"
  checksum: 10/b7afc2b65e56f7035b1a2eec57ae0fbdee7d742b1cdcd0f4387562b6527a011ab1cbe9f64cc8b3cca61e3297c9637c8bf61cec2e6b8d3a711d4b5267dfafbe02
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.19":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 10/c2b33b4f0c40445aee56141f13ca692fa6805db88510e5bbb3baadb2da13e1293b738e638e15e4a8eb668bb9e97debb08e7a35409b477b5cc18f171d35a83045
  languageName: node
  linkType: hard

"nopt@npm:^7.2.0":
  version: 7.2.1
  resolution: "nopt@npm:7.2.1"
  dependencies:
    abbrev: "npm:^2.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10/95a1f6dec8a81cd18cdc2fed93e6f0b4e02cf6bdb4501c848752c6e34f9883d9942f036a5e3b21a699047d8a448562d891e67492df68ec9c373e6198133337ae
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.0.0
  resolution: "nopt@npm:8.0.0"
  dependencies:
    abbrev: "npm:^2.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10/2d137f64b6f9331ec97047dd1cbbe4dcd9a61ceef4fd0f2252c0bbac1d69ba15671e6fd83a441328824b3ca78afe6ebe1694f12ebcd162b73a221582a06179ff
  languageName: node
  linkType: hard

"normalize-path@npm:^2.1.1":
  version: 2.1.1
  resolution: "normalize-path@npm:2.1.1"
  dependencies:
    remove-trailing-separator: "npm:^1.0.1"
  checksum: 10/7e9cbdcf7f5b8da7aa191fbfe33daf290cdcd8c038f422faf1b8a83c972bf7a6d94c5be34c4326cb00fb63bc0fd97d9fbcfaf2e5d6142332c2cd36d2e1b86cea
  languageName: node
  linkType: hard

"npm-normalize-package-bin@npm:^4.0.0":
  version: 4.0.0
  resolution: "npm-normalize-package-bin@npm:4.0.0"
  checksum: 10/e1a0971e5640bc116c5197f9707d86dc404b6d8e13da2c7ea82baa5583b8da279a3c8607234aa1d733c2baac3b3eba87b156f021f20ae183dc4806530e61675d
  languageName: node
  linkType: hard

"npm-run-all2@npm:7.0.2":
  version: 7.0.2
  resolution: "npm-run-all2@npm:7.0.2"
  dependencies:
    ansi-styles: "npm:^6.2.1"
    cross-spawn: "npm:^7.0.6"
    memorystream: "npm:^0.3.1"
    minimatch: "npm:^9.0.0"
    pidtree: "npm:^0.6.0"
    read-package-json-fast: "npm:^4.0.0"
    shell-quote: "npm:^1.7.3"
    which: "npm:^5.0.0"
  bin:
    npm-run-all: bin/npm-run-all/index.js
    npm-run-all2: bin/npm-run-all/index.js
    run-p: bin/run-p/index.js
    run-s: bin/run-s/index.js
  checksum: 10/46d3f7a9117d6af2463285a02909ecc92bde31fc5cd9d8a7c4cc66157e639481eed13d5553dda62b9def6df36b8a5c748393ea65233aea97ee0e615033595266
  languageName: node
  linkType: hard

"nth-check@npm:^2.1.1":
  version: 2.1.1
  resolution: "nth-check@npm:2.1.1"
  dependencies:
    boolbase: "npm:^1.0.0"
  checksum: 10/5afc3dafcd1573b08877ca8e6148c52abd565f1d06b1eb08caf982e3fa289a82f2cae697ffb55b5021e146d60443f1590a5d6b944844e944714a5b549675bcd3
  languageName: node
  linkType: hard

"nullthrows@npm:^1.1.1":
  version: 1.1.1
  resolution: "nullthrows@npm:1.1.1"
  checksum: 10/c7cf377a095535dc301d81cf7959d3784d090a609a2a4faa40b6121a0c1d7f70d3a3aa534a34ab852e8553b66848ec503c28f2c19efd617ed564dc07dfbb6d33
  languageName: node
  linkType: hard

"nwsapi@npm:^2.2.16":
  version: 2.2.16
  resolution: "nwsapi@npm:2.2.16"
  checksum: 10/1e5e086cdd4ca4a45f414d37f49bf0ca81d84ed31c6871ac68f531917d2910845db61f77c6d844430dc90fda202d43fce9603024e74038675de95229eb834dba
  languageName: node
  linkType: hard

"object-assign@npm:^4.1.0":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: 10/fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"once@npm:^1.3.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: "npm:1"
  checksum: 10/cd0a88501333edd640d95f0d2700fbde6bff20b3d4d9bdc521bdd31af0656b5706570d6c6afe532045a20bb8dc0849f8332d6f2a416e0ba6d3d3b98806c7db68
  languageName: node
  linkType: hard

"onetime@npm:^5.1.0":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: "npm:^2.1.0"
  checksum: 10/e9fd0695a01cf226652f0385bf16b7a24153dbbb2039f764c8ba6d2306a8506b0e4ce570de6ad99c7a6eb49520743afdb66edd95ee979c1a342554ed49a9aadd
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.4
  resolution: "optionator@npm:0.9.4"
  dependencies:
    deep-is: "npm:^0.1.3"
    fast-levenshtein: "npm:^2.0.6"
    levn: "npm:^0.4.1"
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:^0.4.0"
    word-wrap: "npm:^1.2.5"
  checksum: 10/a8398559c60aef88d7f353a4f98dcdff6090a4e70f874c827302bf1213d9106a1c4d5fcb68dacb1feb3c30a04c4102f41047aa55d4c576b863d6fc876e001af6
  languageName: node
  linkType: hard

"ora@npm:^5.4.1":
  version: 5.4.1
  resolution: "ora@npm:5.4.1"
  dependencies:
    bl: "npm:^4.1.0"
    chalk: "npm:^4.1.0"
    cli-cursor: "npm:^3.1.0"
    cli-spinners: "npm:^2.5.0"
    is-interactive: "npm:^1.0.0"
    is-unicode-supported: "npm:^0.1.0"
    log-symbols: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
    wcwidth: "npm:^1.0.1"
  checksum: 10/8d071828f40090a8e1c6e8f350c6eb065808e9ab2b3e57fa37e0d5ae78cb46dac00117c8f12c3c8b8da2923454afbd8265e08c10b69881170c5b269f451e7fef
  languageName: node
  linkType: hard

"os-tmpdir@npm:~1.0.2":
  version: 1.0.2
  resolution: "os-tmpdir@npm:1.0.2"
  checksum: 10/5666560f7b9f10182548bf7013883265be33620b1c1b4a4d405c25be2636f970c5488ff3e6c48de75b55d02bde037249fe5dbfbb4c0fb7714953d56aed062e6d
  languageName: node
  linkType: hard

"p-limit@npm:3.1.0, p-limit@npm:^3.0.2":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: "npm:^0.1.0"
  checksum: 10/7c3690c4dbf62ef625671e20b7bdf1cbc9534e83352a2780f165b0d3ceba21907e77ad63401708145ca4e25bfc51636588d89a8c0aeb715e6c37d1c066430360
  languageName: node
  linkType: hard

"p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: "npm:^2.0.0"
  checksum: 10/84ff17f1a38126c3314e91ecfe56aecbf36430940e2873dadaa773ffe072dc23b7af8e46d4b6485d302a11673fe94c6b67ca2cfbb60c989848b02100d0594ac1
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: "npm:^2.2.0"
  checksum: 10/513bd14a455f5da4ebfcb819ef706c54adb09097703de6aeaa5d26fe5ea16df92b48d1ac45e01e3944ce1e6aa2a66f7f8894742b8c9d6e276e16cd2049a2b870
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: "npm:^3.0.2"
  checksum: 10/1623088f36cf1cbca58e9b61c4e62bf0c60a07af5ae1ca99a720837356b5b6c5ba3eb1b2127e47a06865fee59dd0453cad7cc844cda9d5a62ac1a5a51b7c86d3
  languageName: node
  linkType: hard

"p-map@npm:^4.0.0":
  version: 4.0.0
  resolution: "p-map@npm:4.0.0"
  dependencies:
    aggregate-error: "npm:^3.0.0"
  checksum: 10/7ba4a2b1e24c05e1fc14bbaea0fc6d85cf005ae7e9c9425d4575550f37e2e584b1af97bcde78eacd7559208f20995988d52881334db16cf77bc1bcf68e48ed7c
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 10/2ef48ccfc6dd387253d71bf502604f7893ed62090b2c9d73387f10006c342606b05233da0e4f29388227b61eb5aeface6197e166520c465c234552eeab2fe633
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: 10/f8a8e9a7693659383f06aec604ad5ead237c7a261c18048a6e1b5b85a5f8a067e469aa24f5bc009b991ea3b058a87f5065ef4176793a200d4917349881216cae
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 10/58ee9538f2f762988433da00e26acc788036914d57c71c246bf0be1b60cdbd77dd60b6a3e1a30465f0b248aeb80079e0b34cb6050b1dfa18c06953bb1cbc7602
  languageName: node
  linkType: hard

"param-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "param-case@npm:3.0.4"
  dependencies:
    dot-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10/b34227fd0f794e078776eb3aa6247442056cb47761e9cd2c4c881c86d84c64205f6a56ef0d70b41ee7d77da02c3f4ed2f88e3896a8fefe08bdfb4deca037c687
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: "npm:^3.0.0"
  checksum: 10/6ba8b255145cae9470cf5551eb74be2d22281587af787a2626683a6c20fbb464978784661478dd2a3f1dad74d1e802d403e1b03c1a31fab310259eec8ac560ff
  languageName: node
  linkType: hard

"parse-filepath@npm:^1.0.2":
  version: 1.0.2
  resolution: "parse-filepath@npm:1.0.2"
  dependencies:
    is-absolute: "npm:^1.0.0"
    map-cache: "npm:^0.2.0"
    path-root: "npm:^0.1.1"
  checksum: 10/6794c3f38d3921f0f7cc63fb1fb0c4d04cd463356ad389c8ce6726d3c50793b9005971f4138975a6d7025526058d5e65e9bfe634d0765e84c4e2571152665a69
  languageName: node
  linkType: hard

"parse-json@npm:^5.2.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": "npm:^7.0.0"
    error-ex: "npm:^1.3.1"
    json-parse-even-better-errors: "npm:^2.3.0"
    lines-and-columns: "npm:^1.1.6"
  checksum: 10/62085b17d64da57f40f6afc2ac1f4d95def18c4323577e1eced571db75d9ab59b297d1d10582920f84b15985cbfc6b6d450ccbf317644cfa176f3ed982ad87e2
  languageName: node
  linkType: hard

"parse5@npm:^7.0.0, parse5@npm:^7.1.2, parse5@npm:^7.2.1":
  version: 7.2.1
  resolution: "parse5@npm:7.2.1"
  dependencies:
    entities: "npm:^4.5.0"
  checksum: 10/fd1a8ad1540d871e1ad6ca9bf5b67e30280886f1ce4a28052c0cb885723aa984d8cb1ec3da998349a6146960c8a84aa87b1a42600eb3b94495c7303476f2f88e
  languageName: node
  linkType: hard

"pascal-case@npm:^3.1.2":
  version: 3.1.2
  resolution: "pascal-case@npm:3.1.2"
  dependencies:
    no-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10/ba98bfd595fc91ef3d30f4243b1aee2f6ec41c53b4546bfa3039487c367abaa182471dcfc830a1f9e1a0df00c14a370514fa2b3a1aacc68b15a460c31116873e
  languageName: node
  linkType: hard

"path-browserify@npm:^1.0.1":
  version: 1.0.1
  resolution: "path-browserify@npm:1.0.1"
  checksum: 10/7e7368a5207e7c6b9051ef045711d0dc3c2b6203e96057e408e6e74d09f383061010d2be95cb8593fe6258a767c3e9fc6b2bfc7ce8d48ae8c3d9f6994cca9ad8
  languageName: node
  linkType: hard

"path-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "path-case@npm:3.0.4"
  dependencies:
    dot-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10/61de0526222629f65038a66f63330dd22d5b54014ded6636283e1d15364da38b3cf29e4433aa3f9d8b0dba407ae2b059c23b0104a34ee789944b1bc1c5c7e06d
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 10/505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 10/060840f92cf8effa293bcc1bea81281bd7d363731d214cbe5c227df207c34cd727430f70c6037b5159c8a870b9157cba65e775446b0ab06fd5ecc7e54615a3b8
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10/55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-root-regex@npm:^0.1.0":
  version: 0.1.2
  resolution: "path-root-regex@npm:0.1.2"
  checksum: 10/dcd75d1f8e93faabe35a58e875b0f636839b3658ff2ad8c289463c40bc1a844debe0dab73c3398ef9dc8f6ec6c319720aff390cf4633763ddcf3cf4b1bbf7e8b
  languageName: node
  linkType: hard

"path-root@npm:^0.1.1":
  version: 0.1.1
  resolution: "path-root@npm:0.1.1"
  dependencies:
    path-root-regex: "npm:^0.1.0"
  checksum: 10/ff88aebfc1c59ace510cc06703d67692a11530989920427625e52b66a303ca9b3d4059b0b7d0b2a73248d1ad29bcb342b8b786ec00592f3101d38a45fd3b2e08
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10/5e8845c159261adda6f09814d7725683257fcc85a18f329880ab4d7cc1d12830967eae5d5894e453f341710d5484b8fdbbd4d75181b4d6e1eb2f4dc7aeadc434
  languageName: node
  linkType: hard

"path-scurry@npm:^2.0.0":
  version: 2.0.0
  resolution: "path-scurry@npm:2.0.0"
  dependencies:
    lru-cache: "npm:^11.0.0"
    minipass: "npm:^7.1.2"
  checksum: 10/285ae0c2d6c34ae91dc1d5378ede21981c9a2f6de1ea9ca5a88b5a270ce9763b83dbadc7a324d512211d8d36b0c540427d3d0817030849d97a60fa840a2c59ec
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 10/5b1e2daa247062061325b8fdbfd1fb56dde0a448fb1455453276ea18c60685bdad23a445dc148cf87bc216be1573357509b7d4060494a6fd768c7efad833ee45
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0, picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: 10/e1cf46bf84886c79055fdfa9dcb3e4711ad259949e3565154b004b260cd356c5d54b31a1437ce9782624bf766272fe6b0154f5f0c744fb7af5d454d2b60db045
  languageName: node
  linkType: hard

"picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 10/60c2595003b05e4535394d1da94850f5372c9427ca4413b71210f437f7b2ca091dbd611c45e8b37d10036fa8eade25c1b8951654f9d3973bfa66a2ff4d3b08bc
  languageName: node
  linkType: hard

"pidtree@npm:^0.6.0":
  version: 0.6.0
  resolution: "pidtree@npm:0.6.0"
  bin:
    pidtree: bin/pidtree.js
  checksum: 10/ea67fb3159e170fd069020e0108ba7712df9f0fd13c8db9b2286762856ddce414fb33932e08df4bfe36e91fe860b51852aee49a6f56eb4714b69634343add5df
  languageName: node
  linkType: hard

"pinia@npm:2.3.0":
  version: 2.3.0
  resolution: "pinia@npm:2.3.0"
  dependencies:
    "@vue/devtools-api": "npm:^6.6.3"
    vue-demi: "npm:^0.14.10"
  peerDependencies:
    typescript: ">=4.4.4"
    vue: ^2.7.0 || ^3.5.11
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10/16c219c6249330065e0b32adaccfccfbd94a9ec794630c6747c3bff662e03f6ebb4fbb6c01a28a7e79ecb20a7c4b8d9d3ebf04aa7ddd80a6b6db6ad223a44aba
  languageName: node
  linkType: hard

"playwright-core@npm:1.52.0":
  version: 1.52.0
  resolution: "playwright-core@npm:1.52.0"
  bin:
    playwright-core: cli.js
  checksum: 10/42e13f5f98dc25ebc95525fb338a215b9097b2ba39d41e99972a190bf75d79979f163f5bc07b1ca06847ee07acb2c9b487d070fab67e9cd55e33310fc05aca3c
  languageName: node
  linkType: hard

"playwright@npm:1.52.0":
  version: 1.52.0
  resolution: "playwright@npm:1.52.0"
  dependencies:
    fsevents: "npm:2.3.2"
    playwright-core: "npm:1.52.0"
  dependenciesMeta:
    fsevents:
      optional: true
  bin:
    playwright: cli.js
  checksum: 10/214175446089000c2ac997b925063b95f7d86d129c5d7c74caa5ddcb05bcad598dfd569d2133a10dc82d288bf67e7858877dcd099274b0b928b9c63db7d6ecec
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^6.0.15":
  version: 6.1.2
  resolution: "postcss-selector-parser@npm:6.1.2"
  dependencies:
    cssesc: "npm:^3.0.0"
    util-deprecate: "npm:^1.0.2"
  checksum: 10/190034c94d809c115cd2f32ee6aade84e933450a43ec3899c3e78e7d7b33efd3a2a975bb45d7700b6c5b196c06a7d9acf3f1ba6f1d87032d9675a29d8bca1dd3
  languageName: node
  linkType: hard

"postcss@npm:^8.4.48":
  version: 8.4.49
  resolution: "postcss@npm:8.4.49"
  dependencies:
    nanoid: "npm:^3.3.7"
    picocolors: "npm:^1.1.1"
    source-map-js: "npm:^1.2.1"
  checksum: 10/28fe1005b1339870e0a5006375ba5ac1213fd69800f79e7db09c398e074421ba6e162898e94f64942fed554037fd292db3811d87835d25ab5ef7f3c9daacb6ca
  languageName: node
  linkType: hard

"postcss@npm:^8.5.2":
  version: 8.5.3
  resolution: "postcss@npm:8.5.3"
  dependencies:
    nanoid: "npm:^3.3.8"
    picocolors: "npm:^1.1.1"
    source-map-js: "npm:^1.2.1"
  checksum: 10/6d7e21a772e8b05bf102636918654dac097bac013f0dc8346b72ac3604fc16829646f94ea862acccd8f82e910b00e2c11c1f0ea276543565d278c7ca35516a7c
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: 10/0b9d2c76801ca652a7f64892dd37b7e3fab149a37d2424920099bf894acccc62abb4424af2155ab36dea8744843060a2d8ddc983518d0b1e22265a22324b72ed
  languageName: node
  linkType: hard

"prettier-linter-helpers@npm:^1.0.0":
  version: 1.0.0
  resolution: "prettier-linter-helpers@npm:1.0.0"
  dependencies:
    fast-diff: "npm:^1.1.2"
  checksum: 10/00ce8011cf6430158d27f9c92cfea0a7699405633f7f1d4a45f07e21bf78e99895911cbcdc3853db3a824201a7c745bd49bfea8abd5fb9883e765a90f74f8392
  languageName: node
  linkType: hard

"prettier@npm:3.4.2":
  version: 3.4.2
  resolution: "prettier@npm:3.4.2"
  bin:
    prettier: bin/prettier.cjs
  checksum: 10/a3e806fb0b635818964d472d35d27e21a4e17150c679047f5501e1f23bd4aa806adf660f0c0d35214a210d5d440da6896c2e86156da55f221a57938278dc326e
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: 10/35610bdb0177d3ab5d35f8827a429fb1dc2518d9e639f2151ac9007f01a061c30e0c635a970c9b00c39102216160f6ec54b62377c92fac3b7bfc2ad4b98d195c
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10/96e1a82453c6c96eef53a37a1d6134c9f2482f94068f98a59145d0986ca4e497bf110a410adf73857e588165eab3899f0ebcf7b3890c1b3ce802abc0d65967d4
  languageName: node
  linkType: hard

"promise@npm:^7.1.1":
  version: 7.3.1
  resolution: "promise@npm:7.3.1"
  dependencies:
    asap: "npm:~2.0.3"
  checksum: 10/37dbe58ca7b0716cc881f0618128f1fd6ff9c46cdc529a269fd70004e567126a449a94e9428e2d19b53d06182d11b45d0c399828f103e06b2bb87643319bd2e7
  languageName: node
  linkType: hard

"proto-list@npm:~1.2.1":
  version: 1.2.4
  resolution: "proto-list@npm:1.2.4"
  checksum: 10/9cc3b46d613fa0d637033b225db1bc98e914c3c05864f7adc9bee728192e353125ef2e49f71129a413f6333951756000b0e54f299d921f02d3e9e370cc994100
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0, punycode@npm:^2.3.1":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: 10/febdc4362bead22f9e2608ff0171713230b57aff9dddc1c273aa2a651fbd366f94b7d6a71d78342a7c0819906750351ca7f2edd26ea41b626d87d6a13d1bd059
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: 10/72900df0616e473e824202113c3df6abae59150dfb73ed13273503127235320e9c8ca4aaaaccfd58cf417c6ca92a6e68ee9a5c3182886ae949a768639b388a7b
  languageName: node
  linkType: hard

"read-package-json-fast@npm:^4.0.0":
  version: 4.0.0
  resolution: "read-package-json-fast@npm:4.0.0"
  dependencies:
    json-parse-even-better-errors: "npm:^4.0.0"
    npm-normalize-package-bin: "npm:^4.0.0"
  checksum: 10/bf0becd7d0b652dcc5874b466d1dbd98313180e89505c072f35ff48a1ad6bdaf2427143301e1924d64e4af5064cda8be5df16f14de882f03130e29051bbaab87
  languageName: node
  linkType: hard

"readable-stream@npm:^3.4.0":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: "npm:^2.0.3"
    string_decoder: "npm:^1.1.1"
    util-deprecate: "npm:^1.0.1"
  checksum: 10/d9e3e53193adcdb79d8f10f2a1f6989bd4389f5936c6f8b870e77570853561c362bee69feca2bbb7b32368ce96a85504aa4cedf7cf80f36e6a9de30d64244048
  languageName: node
  linkType: hard

"readdirp@npm:^4.0.1":
  version: 4.0.2
  resolution: "readdirp@npm:4.0.2"
  checksum: 10/4ef93103307c7d5e42e78ecf201db58c984c4d66882a27c956250478b49c2444b1ff6aea8ce0f5e4157b2c07ce2fe870ad16c92ebd7c6ff30391ded6e42b9873
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.14.0":
  version: 0.14.1
  resolution: "regenerator-runtime@npm:0.14.1"
  checksum: 10/5db3161abb311eef8c45bcf6565f4f378f785900ed3945acf740a9888c792f75b98ecb77f0775f3bf95502ff423529d23e94f41d80c8256e8fa05ed4b07cf471
  languageName: node
  linkType: hard

"relay-runtime@npm:12.0.0":
  version: 12.0.0
  resolution: "relay-runtime@npm:12.0.0"
  dependencies:
    "@babel/runtime": "npm:^7.0.0"
    fbjs: "npm:^3.0.0"
    invariant: "npm:^2.2.4"
  checksum: 10/d6211e8206ea7273f88dccd5ea72abe6836c6f0bfe95a48ddf80c54e47a08edaf312bedecba98a0a0ba6abcd360cbacd6a2ddb4cef65f00170fb0f36cc324f5e
  languageName: node
  linkType: hard

"remedial@npm:^1.0.7":
  version: 1.0.8
  resolution: "remedial@npm:1.0.8"
  checksum: 10/41e23a7d656fd696678e4f648e57ece5c9e13c097094e8ac6e173990a0665a24d8e50cbb39d458af3b0d58cfbd7811fc0840c4646d10ce3285fe5819b1c82375
  languageName: node
  linkType: hard

"remove-accents@npm:0.5.0":
  version: 0.5.0
  resolution: "remove-accents@npm:0.5.0"
  checksum: 10/4aa1a9d0c18468515a33c6760b0f8e28dfbceddcb846fac90b2189445445b27b11cc1df9fbceb97b4449438bc13250d77b27d4ab325b2d69933acc156d6c5b50
  languageName: node
  linkType: hard

"remove-trailing-separator@npm:^1.0.1":
  version: 1.1.0
  resolution: "remove-trailing-separator@npm:1.1.0"
  checksum: 10/d3c20b5a2d987db13e1cca9385d56ecfa1641bae143b620835ac02a6b70ab88f68f117a0021838db826c57b31373d609d52e4f31aca75fc490c862732d595419
  languageName: node
  linkType: hard

"remove-trailing-spaces@npm:^1.0.6":
  version: 1.0.8
  resolution: "remove-trailing-spaces@npm:1.0.8"
  checksum: 10/81f615c5cd8dd6a5e3017dcc9af598965575d176d42ef99cfd7b894529991f464e629fd68aba089f5c6bebf5bb8070a5eee56f3b621aba55e8ef524d6a4d4f69
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: 10/a72468e2589270d91f06c7d36ec97a88db53ae5d6fe3787fadc943f0b0276b10347f89b363b2a82285f650bdcc135ad4a257c61bdd4d00d6df1fa24875b0ddaf
  languageName: node
  linkType: hard

"require-main-filename@npm:^2.0.0":
  version: 2.0.0
  resolution: "require-main-filename@npm:2.0.0"
  checksum: 10/8604a570c06a69c9d939275becc33a65676529e1c3e5a9f42d58471674df79357872b96d70bb93a0380a62d60dc9031c98b1a9dad98c946ffdd61b7ac0c8cedd
  languageName: node
  linkType: hard

"resolve-from@npm:5.0.0":
  version: 5.0.0
  resolution: "resolve-from@npm:5.0.0"
  checksum: 10/be18a5e4d76dd711778664829841cde690971d02b6cbae277735a09c1c28f407b99ef6ef3cd585a1e6546d4097b28df40ed32c4a287b9699dcf6d7f208495e23
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: 10/91eb76ce83621eea7bbdd9b55121a5c1c4a39e54a9ce04a9ad4517f102f8b5131c2cf07622c738a6683991bf54f2ce178f5a42803ecbd527ddc5105f362cc9e3
  languageName: node
  linkType: hard

"restore-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "restore-cursor@npm:3.1.0"
  dependencies:
    onetime: "npm:^5.1.0"
    signal-exit: "npm:^3.0.2"
  checksum: 10/f877dd8741796b909f2a82454ec111afb84eb45890eb49ac947d87991379406b3b83ff9673a46012fca0d7844bb989f45cc5b788254cf1a39b6b5a9659de0630
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10/1f914879f97e7ee931ad05fe3afa629bd55270fc6cf1c1e589b6a99fab96d15daad0fa1a52a00c729ec0078045fe3e399bd4fd0c93bcc906957bdc17f89cb8e6
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.0.4
  resolution: "reusify@npm:1.0.4"
  checksum: 10/14222c9e1d3f9ae01480c50d96057228a8524706db79cdeb5a2ce5bb7070dd9f409a6f84a02cbef8cdc80d39aef86f2dd03d155188a1300c599b05437dcd2ffb
  languageName: node
  linkType: hard

"rfdc@npm:^1.3.0":
  version: 1.4.1
  resolution: "rfdc@npm:1.4.1"
  checksum: 10/2f3d11d3d8929b4bfeefc9acb03aae90f971401de0add5ae6c5e38fec14f0405e6a4aad8fdb76344bfdd20c5193110e3750cbbd28ba86d73729d222b6cf4a729
  languageName: node
  linkType: hard

"rimraf@npm:^5.0.5":
  version: 5.0.10
  resolution: "rimraf@npm:5.0.10"
  dependencies:
    glob: "npm:^10.3.7"
  bin:
    rimraf: dist/esm/bin.mjs
  checksum: 10/f3b8ce81eecbde4628b07bdf9e2fa8b684e0caea4999acb1e3b0402c695cd41f28cd075609a808e61ce2672f528ca079f675ab1d8e8d5f86d56643a03e0b8d2e
  languageName: node
  linkType: hard

"rollup@npm:^4.30.1":
  version: 4.40.2
  resolution: "rollup@npm:4.40.2"
  dependencies:
    "@rollup/rollup-android-arm-eabi": "npm:4.40.2"
    "@rollup/rollup-android-arm64": "npm:4.40.2"
    "@rollup/rollup-darwin-arm64": "npm:4.40.2"
    "@rollup/rollup-darwin-x64": "npm:4.40.2"
    "@rollup/rollup-freebsd-arm64": "npm:4.40.2"
    "@rollup/rollup-freebsd-x64": "npm:4.40.2"
    "@rollup/rollup-linux-arm-gnueabihf": "npm:4.40.2"
    "@rollup/rollup-linux-arm-musleabihf": "npm:4.40.2"
    "@rollup/rollup-linux-arm64-gnu": "npm:4.40.2"
    "@rollup/rollup-linux-arm64-musl": "npm:4.40.2"
    "@rollup/rollup-linux-loongarch64-gnu": "npm:4.40.2"
    "@rollup/rollup-linux-powerpc64le-gnu": "npm:4.40.2"
    "@rollup/rollup-linux-riscv64-gnu": "npm:4.40.2"
    "@rollup/rollup-linux-riscv64-musl": "npm:4.40.2"
    "@rollup/rollup-linux-s390x-gnu": "npm:4.40.2"
    "@rollup/rollup-linux-x64-gnu": "npm:4.40.2"
    "@rollup/rollup-linux-x64-musl": "npm:4.40.2"
    "@rollup/rollup-win32-arm64-msvc": "npm:4.40.2"
    "@rollup/rollup-win32-ia32-msvc": "npm:4.40.2"
    "@rollup/rollup-win32-x64-msvc": "npm:4.40.2"
    "@types/estree": "npm:1.0.7"
    fsevents: "npm:~2.3.2"
  dependenciesMeta:
    "@rollup/rollup-android-arm-eabi":
      optional: true
    "@rollup/rollup-android-arm64":
      optional: true
    "@rollup/rollup-darwin-arm64":
      optional: true
    "@rollup/rollup-darwin-x64":
      optional: true
    "@rollup/rollup-freebsd-arm64":
      optional: true
    "@rollup/rollup-freebsd-x64":
      optional: true
    "@rollup/rollup-linux-arm-gnueabihf":
      optional: true
    "@rollup/rollup-linux-arm-musleabihf":
      optional: true
    "@rollup/rollup-linux-arm64-gnu":
      optional: true
    "@rollup/rollup-linux-arm64-musl":
      optional: true
    "@rollup/rollup-linux-loongarch64-gnu":
      optional: true
    "@rollup/rollup-linux-powerpc64le-gnu":
      optional: true
    "@rollup/rollup-linux-riscv64-gnu":
      optional: true
    "@rollup/rollup-linux-riscv64-musl":
      optional: true
    "@rollup/rollup-linux-s390x-gnu":
      optional: true
    "@rollup/rollup-linux-x64-gnu":
      optional: true
    "@rollup/rollup-linux-x64-musl":
      optional: true
    "@rollup/rollup-win32-arm64-msvc":
      optional: true
    "@rollup/rollup-win32-ia32-msvc":
      optional: true
    "@rollup/rollup-win32-x64-msvc":
      optional: true
    fsevents:
      optional: true
  bin:
    rollup: dist/bin/rollup
  checksum: 10/ab767c56e37410257864e051fccbdaf448ac7774129bf39295de716af816c49e0247e72749959969efbd892fc64e096880fa269764adf765579100e81abf5e7c
  languageName: node
  linkType: hard

"root-workspace-0b6124@workspace:.":
  version: 0.0.0-use.local
  resolution: "root-workspace-0b6124@workspace:."
  dependencies:
    concurrently: "npm:9.1.2"
  languageName: unknown
  linkType: soft

"rrweb-cssom@npm:^0.8.0":
  version: 0.8.0
  resolution: "rrweb-cssom@npm:0.8.0"
  checksum: 10/07521ee36fb6569c17906afad1ac7ff8f099d49ade9249e190693ac36cdf27f88d9acf0cc66978935d5d0a23fca105643d7e9125b9a9d91ed9db9e02d31d7d80
  languageName: node
  linkType: hard

"run-async@npm:^2.4.0":
  version: 2.4.1
  resolution: "run-async@npm:2.4.1"
  checksum: 10/c79551224dafa26ecc281cb1efad3510c82c79116aaf681f8a931ce70fdf4ca880d58f97d3b930a38992c7aad7955a08e065b32ec194e1dd49d7790c874ece50
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: "npm:^1.2.2"
  checksum: 10/cb4f97ad25a75ebc11a8ef4e33bb962f8af8516bb2001082ceabd8902e15b98f4b84b4f8a9b222e5d57fc3bd1379c483886ed4619367a7680dad65316993021d
  languageName: node
  linkType: hard

"rxjs@npm:^7.5.5, rxjs@npm:^7.8.1":
  version: 7.8.1
  resolution: "rxjs@npm:7.8.1"
  dependencies:
    tslib: "npm:^2.1.0"
  checksum: 10/b10cac1a5258f885e9dd1b70d23c34daeb21b61222ee735d2ec40a8685bdca40429000703a44f0e638c27a684ac139e1c37e835d2a0dc16f6fc061a138ae3abb
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 10/32872cd0ff68a3ddade7a7617b8f4c2ae8764d8b7d884c651b74457967a9e0e886267d3ecc781220629c44a865167b61c375d2da6c720c840ecd73f45d5d9451
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10/7eaf7a0cf37cc27b42fb3ef6a9b1df6e93a1c6d98c6c6702b02fe262d5fcbd89db63320793b99b21cb5348097d0a53de81bd5f4e8b86e20cc9412e3f1cfb4e83
  languageName: node
  linkType: hard

"sass-loader@npm:16.0.4":
  version: 16.0.4
  resolution: "sass-loader@npm:16.0.4"
  dependencies:
    neo-async: "npm:^2.6.2"
  peerDependencies:
    "@rspack/core": 0.x || 1.x
    node-sass: ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0 || ^9.0.0
    sass: ^1.3.0
    sass-embedded: "*"
    webpack: ^5.0.0
  peerDependenciesMeta:
    "@rspack/core":
      optional: true
    node-sass:
      optional: true
    sass:
      optional: true
    sass-embedded:
      optional: true
    webpack:
      optional: true
  checksum: 10/16e3552e04301864d59d99f1c90952e0c97cfa793a38a551b6dc19e49a9c0779ec8b3dd8ab39bc79cc4401972f13e8d5baaf577c96ea39911db5866a0b4bdcdd
  languageName: node
  linkType: hard

"sass@npm:1.83.1":
  version: 1.83.1
  resolution: "sass@npm:1.83.1"
  dependencies:
    "@parcel/watcher": "npm:^2.4.1"
    chokidar: "npm:^4.0.0"
    immutable: "npm:^5.0.2"
    source-map-js: "npm:>=0.6.2 <2.0.0"
  dependenciesMeta:
    "@parcel/watcher":
      optional: true
  bin:
    sass: sass.js
  checksum: 10/ee4ea5573faddeed375479eda6ac87a6f34dc6fc5391c6cc2743993f24827f4709f950fba6ef1ad0694215a1df40d43f4ed9006327daa3743c8e3ad1979d3c32
  languageName: node
  linkType: hard

"saxes@npm:^6.0.0":
  version: 6.0.0
  resolution: "saxes@npm:6.0.0"
  dependencies:
    xmlchars: "npm:^2.2.0"
  checksum: 10/97b50daf6ca3a153e89842efa18a862e446248296622b7473c169c84c823ee8a16e4a43bac2f73f11fc8cb9168c73fbb0d73340f26552bac17970e9052367aa9
  languageName: node
  linkType: hard

"scuid@npm:^1.1.0":
  version: 1.1.0
  resolution: "scuid@npm:1.1.0"
  checksum: 10/cd094ac3718b0070a222f9a499b280c698fdea10268cc163fa244421099544c1766dd893fdee0e2a8eba5d53ab9d0bcb11067bedff166665030fa6fda25a096b
  languageName: node
  linkType: hard

"semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: 10/1ef3a85bd02a760c6ef76a45b8c1ce18226de40831e02a00bad78485390b98b6ccaa31046245fc63bba4a47a6a592b6c7eedc65cc47126e60489f9cc1ce3ed7e
  languageName: node
  linkType: hard

"semver@npm:^7.3.5, semver@npm:^7.3.6, semver@npm:^7.5.3, semver@npm:^7.5.4, semver@npm:^7.6.0, semver@npm:^7.6.3":
  version: 7.6.3
  resolution: "semver@npm:7.6.3"
  bin:
    semver: bin/semver.js
  checksum: 10/36b1fbe1a2b6f873559cd57b238f1094a053dbfd997ceeb8757d79d1d2089c56d1321b9f1069ce263dc64cfa922fa1d2ad566b39426fe1ac6c723c1487589e10
  languageName: node
  linkType: hard

"sentence-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "sentence-case@npm:3.0.4"
  dependencies:
    no-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
    upper-case-first: "npm:^2.0.2"
  checksum: 10/3cfe6c0143e649132365695706702d7f729f484fa7b25f43435876efe7af2478243eefb052bacbcce10babf9319fd6b5b6bc59b94c80a1c819bcbb40651465d5
  languageName: node
  linkType: hard

"set-blocking@npm:^2.0.0":
  version: 2.0.0
  resolution: "set-blocking@npm:2.0.0"
  checksum: 10/8980ebf7ae9eb945bb036b6e283c547ee783a1ad557a82babf758a065e2fb6ea337fd82cac30dd565c1e606e423f30024a19fff7afbf4977d784720c4026a8ef
  languageName: node
  linkType: hard

"setimmediate@npm:^1.0.5":
  version: 1.0.5
  resolution: "setimmediate@npm:1.0.5"
  checksum: 10/76e3f5d7f4b581b6100ff819761f04a984fa3f3990e72a6554b57188ded53efce2d3d6c0932c10f810b7c59414f85e2ab3c11521877d1dea1ce0b56dc906f485
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10/6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10/1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"shell-quote@npm:^1.7.3":
  version: 1.8.2
  resolution: "shell-quote@npm:1.8.2"
  checksum: 10/3ae4804fd80a12ba07650d0262804ae3b479a62a6b6971a6dc5fa12995507aa63d3de3e6a8b7a8d18f4ce6eb118b7d75db7fcb2c0acbf016f210f746b10cfe02
  languageName: node
  linkType: hard

"shell-quote@npm:^1.8.1":
  version: 1.8.1
  resolution: "shell-quote@npm:1.8.1"
  checksum: 10/af19ab5a1ec30cb4b2f91fd6df49a7442d5c4825a2e269b3712eded10eedd7f9efeaab96d57829880733fc55bcdd8e9b1d8589b4befb06667c731d08145e274d
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.2":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: 10/a2f098f247adc367dffc27845853e9959b9e88b01cb301658cfe4194352d8d2bb32e18467c786a7fe15f1d44b233ea35633d076d5e737870b7139949d1ab6318
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10/c9fa63bbbd7431066174a48ba2dd9986dfd930c3a8b59de9c29d7b6854ec1c12a80d15310869ea5166d413b99f041bfa3dd80a7947bcd44ea8e6eb3ffeabfa1f
  languageName: node
  linkType: hard

"signedsource@npm:^1.0.0":
  version: 1.0.0
  resolution: "signedsource@npm:1.0.0"
  checksum: 10/64b2c8d7a48de9009cfd3aff62bb7c88abf3b8e0421f17ebb1d7f5ca9cc9c3ad10f5a1e3ae6cd804e4e6121c87b668202ae9057065f058ddfbf34ea65f63945d
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 10/94a93fff615f25a999ad4b83c9d5e257a7280c90a32a7cb8b4a87996e4babf322e469c42b7f649fd5796edd8687652f3fb452a86dc97a816f01113183393f11c
  languageName: node
  linkType: hard

"slice-ansi@npm:^3.0.0":
  version: 3.0.0
  resolution: "slice-ansi@npm:3.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    astral-regex: "npm:^2.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
  checksum: 10/5ec6d022d12e016347e9e3e98a7eb2a592213a43a65f1b61b74d2c78288da0aded781f665807a9f3876b9daa9ad94f64f77d7633a0458876c3a4fdc4eb223f24
  languageName: node
  linkType: hard

"slice-ansi@npm:^4.0.0":
  version: 4.0.0
  resolution: "slice-ansi@npm:4.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    astral-regex: "npm:^2.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
  checksum: 10/4a82d7f085b0e1b070e004941ada3c40d3818563ac44766cca4ceadd2080427d337554f9f99a13aaeb3b4a94d9964d9466c807b3d7b7541d1ec37ee32d308756
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10/927484aa0b1640fd9473cee3e0a0bcad6fce93fd7bbc18bac9ad0c33686f5d2e2c422fba24b5899c184524af01e11dd2bd051c2bf2b07e47aff8ca72cbfc60d2
  languageName: node
  linkType: hard

"snake-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "snake-case@npm:3.0.4"
  dependencies:
    dot-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10/0a7a79900bbb36f8aaa922cf111702a3647ac6165736d5dc96d3ef367efc50465cac70c53cd172c382b022dac72ec91710608e5393de71f76d7142e6fd80e8a3
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.8.3"
  checksum: 10/ee99e1dacab0985b52cbe5a75640be6e604135e9489ebdc3048635d186012fbaecc20fbbe04b177dee434c319ba20f09b3e7dfefb7d932466c0d707744eac05c
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.3
  resolution: "socks@npm:2.8.3"
  dependencies:
    ip-address: "npm:^9.0.5"
    smart-buffer: "npm:^4.2.0"
  checksum: 10/ffcb622c22481dfcd7589aae71fbfd71ca34334064d181df64bf8b7feaeee19706aba4cffd1de35cc7bbaeeaa0af96be2d7f40fcbc7bc0ab69533a7ae9ffc4fb
  languageName: node
  linkType: hard

"source-map-js@npm:>=0.6.2 <2.0.0, source-map-js@npm:^1.0.2, source-map-js@npm:^1.2.0, source-map-js@npm:^1.2.1":
  version: 1.2.1
  resolution: "source-map-js@npm:1.2.1"
  checksum: 10/ff9d8c8bf096d534a5b7707e0382ef827b4dd360a577d3f34d2b9f48e12c9d230b5747974ee7c607f0df65113732711bb701fe9ece3c7edbd43cb2294d707df3
  languageName: node
  linkType: hard

"sponge-case@npm:^1.0.1":
  version: 1.0.1
  resolution: "sponge-case@npm:1.0.1"
  dependencies:
    tslib: "npm:^2.0.3"
  checksum: 10/64f53d930f63c5a9e59d4cae487c1ffa87d25eab682833b01d572cc885e7e3fdbad4f03409a41f03ecb27f1f8959432253eb48332c7007c3388efddb24ba2792
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: 10/e7587128c423f7e43cc625fe2f87e6affdf5ca51c1cc468e910d8aaca46bb44a7fbcfa552f787b1d3987f7043aeb4527d1b99559e6621e01b42b3f45e5a24cbb
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/7024c1a6e39b3f18aa8f1c8290e884fe91b0f9ca5a6c6d410544daad54de0ba664db879afe16412e187c6c292fd60b937f047ee44292e5c2af2dcc6d8e1a9b48
  languageName: node
  linkType: hard

"streamsearch@npm:^1.1.0":
  version: 1.1.0
  resolution: "streamsearch@npm:1.1.0"
  checksum: 10/612c2b2a7dbcc859f74597112f80a42cbe4d448d03da790d5b7b39673c1197dd3789e91cd67210353e58857395d32c1e955a9041c4e6d5bae723436b3ed9ed14
  languageName: node
  linkType: hard

"string-env-interpolation@npm:^1.0.1":
  version: 1.0.1
  resolution: "string-env-interpolation@npm:1.0.1"
  checksum: 10/d126329587f635bee65300e4451e7352b9b67e03daeb62f006ca84244cac12a1f6e45176b018653ba0c3ec3b5d980f9ca59d2eeed99cf799501cdaa7f871dc6f
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10/e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10/7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: "npm:~5.2.0"
  checksum: 10/54d23f4a6acae0e93f999a585e673be9e561b65cd4cca37714af1e893ab8cd8dfa52a9e4f58f48f87b4a44918d3a9254326cb80ed194bf2e4c226e2b21767e56
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10/ae3b5436d34fadeb6096367626ce987057713c566e1e7768818797e00ac5d62023d0f198c4e681eae9e20701721980b26a64a8f5b91238869592a9c6800719a2
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10/475f53e9c44375d6e72807284024ac5d668ee1d06010740dec0b9744f2ddf47de8d7151f80e5f6190fc8f384e802fdf9504b76a7e9020c9faee7103623338be2
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 10/492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10/c8bb7afd564e3b26b50ca6ee47572c217526a1389fe018d00345856d4a9b08ffbd61fadaf283a87368d94c3dcdb8f5ffe2650a5a65863e21ad2730ca0f05210a
  languageName: node
  linkType: hard

"supports-color@npm:^8.1.1":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10/157b534df88e39c5518c5e78c35580c1eca848d7dbaf31bbe06cdfc048e22c7ff1a9d046ae17b25691128f631a51d9ec373c1b740c12ae4f0de6e292037e4282
  languageName: node
  linkType: hard

"swap-case@npm:^2.0.2":
  version: 2.0.2
  resolution: "swap-case@npm:2.0.2"
  dependencies:
    tslib: "npm:^2.0.3"
  checksum: 10/6e21c9e1b3cd5735eb2af679a99ec3efc78a14e3d4d5e3fd594e254b91cfd37185b3d1c6e41b22f53a2cdf5d1b963ce30c0fe8b78337e3fd43d0137084670a5f
  languageName: node
  linkType: hard

"symbol-tree@npm:^3.2.4":
  version: 3.2.4
  resolution: "symbol-tree@npm:3.2.4"
  checksum: 10/c09a00aadf279d47d0c5c46ca3b6b2fbaeb45f0a184976d599637d412d3a70bbdc043ff33effe1206dea0e36e0ad226cb957112e7ce9a4bf2daedf7fa4f85c53
  languageName: node
  linkType: hard

"synckit@npm:^0.9.0, synckit@npm:^0.9.1":
  version: 0.9.2
  resolution: "synckit@npm:0.9.2"
  dependencies:
    "@pkgr/core": "npm:^0.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10/d45c4288be9c0232343650643892a7edafb79152c0c08d7ae5d33ca2c296b67a0e15f8cb5c9153969612c4ea5cd5686297542384aab977db23cfa6653fe02027
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": "npm:^4.0.0"
    chownr: "npm:^3.0.0"
    minipass: "npm:^7.1.2"
    minizlib: "npm:^3.0.1"
    mkdirp: "npm:^3.0.1"
    yallist: "npm:^5.0.0"
  checksum: 10/12a2a4fc6dee23e07cc47f1aeb3a14a1afd3f16397e1350036a8f4cdfee8dcac7ef5978337a4e7b2ac2c27a9a6d46388fc2088ea7c80cb6878c814b1425f8ecf
  languageName: node
  linkType: hard

"through@npm:^2.3.6, through@npm:^2.3.8":
  version: 2.3.8
  resolution: "through@npm:2.3.8"
  checksum: 10/5da78346f70139a7d213b65a0106f3c398d6bc5301f9248b5275f420abc2c4b1e77c2abc72d218dedc28c41efb2e7c312cb76a7730d04f9c2d37d247da3f4198
  languageName: node
  linkType: hard

"title-case@npm:^3.0.3":
  version: 3.0.3
  resolution: "title-case@npm:3.0.3"
  dependencies:
    tslib: "npm:^2.0.3"
  checksum: 10/369fe90f650a66205c34ebef63a69c6d1fd411ae3aad23db0aae165ddb881af50e67c6ea6800d605bc2b9e0ab5f22dada58fe97a1a7e7f3131ee0ef176cc65ec
  languageName: node
  linkType: hard

"tldts-core@npm:^6.1.71":
  version: 6.1.71
  resolution: "tldts-core@npm:6.1.71"
  checksum: 10/9bed9e06791c9044d5f6cfe75c7b685c650c1d5cd3fc6f8c5ee3b24478925a1c356a621fe592b889f11b340e5db96a8876ec810379abad11c71700c9d4fe86b7
  languageName: node
  linkType: hard

"tldts@npm:^6.1.32":
  version: 6.1.71
  resolution: "tldts@npm:6.1.71"
  dependencies:
    tldts-core: "npm:^6.1.71"
  bin:
    tldts: bin/cli.js
  checksum: 10/6a3e9ebf140c45ce5e179b3d9011b6b828c14bacfb2834f3ca61e8a5557b29b2a4e0b62da2c0382156d09e265e0f16255b5acdfef1cbccb25d7cd051ef11e9eb
  languageName: node
  linkType: hard

"tmp@npm:^0.0.33":
  version: 0.0.33
  resolution: "tmp@npm:0.0.33"
  dependencies:
    os-tmpdir: "npm:~1.0.2"
  checksum: 10/09c0abfd165cff29b32be42bc35e80b8c64727d97dedde6550022e88fa9fd39a084660415ed8e3ebaa2aca1ee142f86df8b31d4196d4f81c774a3a20fd4b6abf
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: "npm:^7.0.0"
  checksum: 10/10dda13571e1f5ad37546827e9b6d4252d2e0bc176c24a101252153ef435d83696e2557fe128c4678e4e78f5f01e83711c703eef9814eb12dab028580d45980a
  languageName: node
  linkType: hard

"tough-cookie@npm:^5.0.0":
  version: 5.1.0
  resolution: "tough-cookie@npm:5.1.0"
  dependencies:
    tldts: "npm:^6.1.32"
  checksum: 10/01908de89d5268e424eb07c17230ef69110fed598f8036db366d2c992d5e8e52ccd3af600c87b7fb43479046eb4289f21baa4467a3032a2230a8d3878d3cb76d
  languageName: node
  linkType: hard

"tr46@npm:^5.0.0":
  version: 5.0.0
  resolution: "tr46@npm:5.0.0"
  dependencies:
    punycode: "npm:^2.3.1"
  checksum: 10/29155adb167d048d3c95d181f7cb5ac71948b4e8f3070ec455986e1f34634acae50ae02a3c8d448121c3afe35b76951cd46ed4c128fd80264280ca9502237a3e
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 10/8f1f5aa6cb232f9e1bdc86f485f916b7aa38caee8a778b378ffec0b70d9307873f253f5cbadbe2955ece2ac5c83d0dc14a77513166ccd0a0c7fe197e21396695
  languageName: node
  linkType: hard

"tree-kill@npm:^1.2.2":
  version: 1.2.2
  resolution: "tree-kill@npm:1.2.2"
  bin:
    tree-kill: cli.js
  checksum: 10/49117f5f410d19c84b0464d29afb9642c863bc5ba40fcb9a245d474c6d5cc64d1b177a6e6713129eb346b40aebb9d4631d967517f9fbe8251c35b21b13cd96c7
  languageName: node
  linkType: hard

"ts-api-utils@npm:^2.0.0":
  version: 2.0.0
  resolution: "ts-api-utils@npm:2.0.0"
  peerDependencies:
    typescript: ">=4.8.4"
  checksum: 10/485bdf8bbba98d58712243d958f4fd44742bbe49e559cd77882fb426d866eec6dd05c67ef91935dc4f8a3c776f235859735e1f05be399e4dc9e7ffd580120974
  languageName: node
  linkType: hard

"ts-log@npm:^2.2.3":
  version: 2.2.7
  resolution: "ts-log@npm:2.2.7"
  checksum: 10/e6d52866608373d1dc429f74158e28fe3f842b8ab2b12f113e786c581f011664efbfa6cea0089f7165d3a1ac3e019747919bbd214f6c7d723193c98353628198
  languageName: node
  linkType: hard

"tslib@npm:^2.0.3, tslib@npm:^2.1.0, tslib@npm:^2.4.0, tslib@npm:^2.5.0, tslib@npm:^2.6.2, tslib@npm:^2.6.3":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: 10/3e2e043d5c2316461cb54e5c7fe02c30ef6dccb3384717ca22ae5c6b5bc95232a6241df19c622d9c73b809bea33b187f6dbc73030963e29950c2141bc32a79f7
  languageName: node
  linkType: hard

"tslib@npm:~2.6.0":
  version: 2.6.3
  resolution: "tslib@npm:2.6.3"
  checksum: 10/52109bb681f8133a2e58142f11a50e05476de4f075ca906d13b596ae5f7f12d30c482feb0bff167ae01cfc84c5803e575a307d47938999246f5a49d174fc558c
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: "npm:^1.2.1"
  checksum: 10/14687776479d048e3c1dbfe58a2409e00367810d6960c0f619b33793271ff2a27f81b52461f14a162f1f89a9b1d8da1b237fc7c99b0e1fdcec28ec63a86b1fec
  languageName: node
  linkType: hard

"type-fest@npm:^0.20.2":
  version: 0.20.2
  resolution: "type-fest@npm:0.20.2"
  checksum: 10/8907e16284b2d6cfa4f4817e93520121941baba36b39219ea36acfe64c86b9dbc10c9941af450bd60832c8f43464974d51c0957f9858bc66b952b66b6914cbb9
  languageName: node
  linkType: hard

"type-fest@npm:^0.21.3":
  version: 0.21.3
  resolution: "type-fest@npm:0.21.3"
  checksum: 10/f4254070d9c3d83a6e573bcb95173008d73474ceadbbf620dd32d273940ca18734dff39c2b2480282df9afe5d1675ebed5499a00d791758748ea81f61a38961f
  languageName: node
  linkType: hard

"typescript-eslint@npm:^8.18.1":
  version: 8.19.1
  resolution: "typescript-eslint@npm:8.19.1"
  dependencies:
    "@typescript-eslint/eslint-plugin": "npm:8.19.1"
    "@typescript-eslint/parser": "npm:8.19.1"
    "@typescript-eslint/utils": "npm:8.19.1"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.8.0"
  checksum: 10/3e7861bcd47c0bea962662d5f18a9f9214270057c082f2e3839ee2f681a42018395755216005d2408447de5b96892b6a18cc794daf8663bba1753def48e6756c
  languageName: node
  linkType: hard

"typescript@npm:5.7.3":
  version: 5.7.3
  resolution: "typescript@npm:5.7.3"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10/6a7e556de91db3d34dc51cd2600e8e91f4c312acd8e52792f243c7818dfadb27bae677175fad6947f9c81efb6c57eb6b2d0c736f196a6ee2f1f7d57b74fc92fa
  languageName: node
  linkType: hard

"typescript@patch:typescript@npm%3A5.7.3#optional!builtin<compat/typescript>":
  version: 5.7.3
  resolution: "typescript@patch:typescript@npm%3A5.7.3#optional!builtin<compat/typescript>::version=5.7.3&hash=5786d5"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10/dc58d777eb4c01973f7fbf1fd808aad49a0efdf545528dab9b07d94fdcb65b8751742804c3057e9619a4627f2d9cc85547fdd49d9f4326992ad0181b49e61d81
  languageName: node
  linkType: hard

"ua-parser-js@npm:^1.0.35":
  version: 1.0.39
  resolution: "ua-parser-js@npm:1.0.39"
  bin:
    ua-parser-js: script/cli.js
  checksum: 10/dd4026b6ece8a34a0d39b6de5542154c4506077d8def8647a300a29e1b3ffa0e23f5c8eeeb8101df6162b7b3eb3597d0b4adb031ae6104cbdb730d6ebc07f3c0
  languageName: node
  linkType: hard

"ui@workspace:ui":
  version: 0.0.0-use.local
  resolution: "ui@workspace:ui"
  dependencies:
    "@bd/cd-system3": "npm:^11.3.2"
    "@eslint/compat": "npm:1.2.4"
    "@graphql-codegen/cli": "npm:5.0.3"
    "@graphql-codegen/client-preset": "npm:4.5.1"
    "@graphql-eslint/eslint-plugin": "npm:^4.4.0"
    "@intlify/eslint-plugin-vue-i18n": "npm:3.2.0"
    "@playwright/test": "npm:1.52.0"
    "@tanstack/vue-query": "npm:4.37.1"
    "@tsconfig/node22": "npm:22.0.0"
    "@types/file-saver": "npm:2.0.7"
    "@types/jsdom": "npm:21.1.7"
    "@types/lodash-es": "npm:4.17.12"
    "@types/node": "npm:22.10.5"
    "@vitejs/plugin-vue": "npm:5.2.1"
    "@vitest/eslint-plugin": "npm:1.1.24"
    "@vue/eslint-config-prettier": "npm:10.1.0"
    "@vue/eslint-config-typescript": "npm:14.2.0"
    "@vue/test-utils": "npm:2.4.6"
    "@vue/tsconfig": "npm:0.7.0"
    "@vueuse/core": "npm:12.3.0"
    dotenv: "npm:16.4.7"
    eslint: "npm:9.17.0"
    eslint-formatter-gitlab: "npm:5.1.0"
    eslint-plugin-playwright: "npm:2.1.0"
    eslint-plugin-vue: "npm:9.32.0"
    file-saver: "npm:2.0.5"
    glob: "npm:11.0.2"
    graphql: "npm:^16.9.0"
    graphql-request: "npm:^7.1.2"
    jsdom: "npm:26.0.0"
    keycloak-js: "npm:26.0.7"
    lodash-es: "npm:4.17.21"
    npm-run-all2: "npm:7.0.2"
    pinia: "npm:2.3.0"
    prettier: "npm:3.4.2"
    sass: "npm:1.83.1"
    sass-loader: "npm:16.0.4"
    typescript: "npm:5.7.3"
    vite: "npm:6.1.6"
    vite-plugin-graphql-codegen: "npm:^3.5.0"
    vite-plugin-vuetify: "npm:2.0.4"
    vue: "npm:3.5.13"
    vue-i18n: "npm:11.1.2"
    vue-i18n-extract: "npm:2.0.7"
    vue-router: "npm:4.5.0"
    vue-tsc: "npm:2.2.8"
    vuetify: "npm:3.7.6"
  languageName: unknown
  linkType: soft

"unc-path-regex@npm:^0.1.2":
  version: 0.1.2
  resolution: "unc-path-regex@npm:0.1.2"
  checksum: 10/a05fa2006bf4606051c10fc7968f08ce7b28fa646befafa282813aeb1ac1a56f65cb1b577ca7851af2726198d59475bb49b11776036257b843eaacee2860a4ec
  languageName: node
  linkType: hard

"undici-types@npm:~6.19.8":
  version: 6.19.8
  resolution: "undici-types@npm:6.19.8"
  checksum: 10/cf0b48ed4fc99baf56584afa91aaffa5010c268b8842f62e02f752df209e3dea138b372a60a963b3b2576ed932f32329ce7ddb9cb5f27a6c83040d8cd74b7a70
  languageName: node
  linkType: hard

"undici-types@npm:~6.20.0":
  version: 6.20.0
  resolution: "undici-types@npm:6.20.0"
  checksum: 10/583ac7bbf4ff69931d3985f4762cde2690bb607844c16a5e2fbb92ed312fe4fa1b365e953032d469fa28ba8b224e88a595f0b10a449332f83fa77c695e567dbe
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: "npm:^5.0.0"
  checksum: 10/6a62094fcac286b9ec39edbd1f8f64ff92383baa430af303dfed1ffda5e47a08a6b316408554abfddd9730c78b6106bef4ca4d02c1231a735ddd56ced77573df
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10/beafdf3d6f44990e0a5ce560f8f881b4ee811be70b6ba0db25298c31c8cf525ed963572b48cd03be1c1349084f9e339be4241666d7cf1ebdad20598d3c652b27
  languageName: node
  linkType: hard

"unixify@npm:^1.0.0":
  version: 1.0.0
  resolution: "unixify@npm:1.0.0"
  dependencies:
    normalize-path: "npm:^2.1.1"
  checksum: 10/3be30e48579fc6c7390bd59b4ab9e745fede0c164dfb7351cf710bd1dbef8484b1441186205af6bcb13b731c0c88caf9b33459f7bf8c89e79c046e656ae433f0
  languageName: node
  linkType: hard

"upath@npm:^2.0.1":
  version: 2.0.1
  resolution: "upath@npm:2.0.1"
  checksum: 10/7b98a83559a295d59f87f7a8d615c7549d19e4aec4dd9d52be2bf1ba93e1d6ee7d8f2188cdecbf303a22cea3768abff4268b960350152a0264125f577d9ed79e
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.1":
  version: 1.1.2
  resolution: "update-browserslist-db@npm:1.1.2"
  dependencies:
    escalade: "npm:^3.2.0"
    picocolors: "npm:^1.1.1"
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 10/e7bf8221dfb21eba4a770cd803df94625bb04f65a706aa94c567de9600fe4eb6133fda016ec471dad43b9e7959c1bffb6580b5e20a87808d2e8a13e3892699a9
  languageName: node
  linkType: hard

"upper-case-first@npm:^2.0.2":
  version: 2.0.2
  resolution: "upper-case-first@npm:2.0.2"
  dependencies:
    tslib: "npm:^2.0.3"
  checksum: 10/4487db4701effe3b54ced4b3e4aa4d9ab06c548f97244d04aafb642eedf96a76d5a03cf5f38f10f415531d5792d1ac6e1b50f2a76984dc6964ad530f12876409
  languageName: node
  linkType: hard

"upper-case@npm:^2.0.2":
  version: 2.0.2
  resolution: "upper-case@npm:2.0.2"
  dependencies:
    tslib: "npm:^2.0.3"
  checksum: 10/508723a2b03ab90cf1d6b7e0397513980fab821cbe79c87341d0e96cedefadf0d85f9d71eac24ab23f526a041d585a575cfca120a9f920e44eb4f8a7cf89121c
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: "npm:^2.1.0"
  checksum: 10/b271ca7e3d46b7160222e3afa3e531505161c9a4e097febae9664e4b59912f4cbe94861361a4175edac3a03fee99d91e44b6a58c17a634bc5a664b19fc76fbcb
  languageName: node
  linkType: hard

"urlpattern-polyfill@npm:^10.0.0":
  version: 10.0.0
  resolution: "urlpattern-polyfill@npm:10.0.0"
  checksum: 10/346819dbe718e929988298d02a988b8ddfa601d08daaa7e69b1148eab699c86c0f0f933d68d8c8cf913166fe64156ed28904e673200d18ef7e9ed6b58cea3fc7
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:^1.0.2":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 10/474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"value-or-promise@npm:^1.0.11, value-or-promise@npm:^1.0.12":
  version: 1.0.12
  resolution: "value-or-promise@npm:1.0.12"
  checksum: 10/a4cc31fc9c3826b8a216ef2037b676904324c00c4acd903aaec2fe0c08516a189345261dd3cc822ec108532b2ea36b7c99bbdee1c3ddcb7f4b3d57d7e61b2064
  languageName: node
  linkType: hard

"vite-plugin-graphql-codegen@npm:^3.5.0":
  version: 3.5.0
  resolution: "vite-plugin-graphql-codegen@npm:3.5.0"
  dependencies:
    "@graphql-codegen/plugin-helpers": "npm:^5.1.0"
  peerDependencies:
    "@graphql-codegen/cli": ">=1.0.0 <6.0.0"
    graphql: ">=14.0.0 <17.0.0"
    vite: ">=2.7.0 <7.0.0"
  checksum: 10/d0bd2301b818be175dcac9f4f511e02dc7c9f07906a10b949c0e3878f58e50c5028696ebd6602cf64967afae7d21c863f0e6d2b26ab813cf2b852799269ee70d
  languageName: node
  linkType: hard

"vite-plugin-vuetify@npm:2.0.4":
  version: 2.0.4
  resolution: "vite-plugin-vuetify@npm:2.0.4"
  dependencies:
    "@vuetify/loader-shared": "npm:^2.0.3"
    debug: "npm:^4.3.3"
    upath: "npm:^2.0.1"
  peerDependencies:
    vite: ">=5"
    vue: ^3.0.0
    vuetify: ^3.0.0
  checksum: 10/a7ae42d5df64f07906a36c4b94ab0647fa0879cdc75a7ced1ae818bef10550781eca96b12af9fd732d74772201e11c3010322cacd32bd8354a8577de24141997
  languageName: node
  linkType: hard

"vite@npm:6.1.6":
  version: 6.1.6
  resolution: "vite@npm:6.1.6"
  dependencies:
    esbuild: "npm:^0.24.2"
    fsevents: "npm:~2.3.3"
    postcss: "npm:^8.5.2"
    rollup: "npm:^4.30.1"
  peerDependencies:
    "@types/node": ^18.0.0 || ^20.0.0 || >=22.0.0
    jiti: ">=1.21.0"
    less: "*"
    lightningcss: ^1.21.0
    sass: "*"
    sass-embedded: "*"
    stylus: "*"
    sugarss: "*"
    terser: ^5.16.0
    tsx: ^4.8.1
    yaml: ^2.4.2
  dependenciesMeta:
    fsevents:
      optional: true
  peerDependenciesMeta:
    "@types/node":
      optional: true
    jiti:
      optional: true
    less:
      optional: true
    lightningcss:
      optional: true
    sass:
      optional: true
    sass-embedded:
      optional: true
    stylus:
      optional: true
    sugarss:
      optional: true
    terser:
      optional: true
    tsx:
      optional: true
    yaml:
      optional: true
  bin:
    vite: bin/vite.js
  checksum: 10/bd209fd55e8291b2d9ec7f3deb7cfd978c713a508b855c4e4191f70ac142a1f36b192c38f5be6cef071db495558ab2a3509d698f9e19f9595d703ac1945c79fc
  languageName: node
  linkType: hard

"vscode-uri@npm:^3.0.8":
  version: 3.0.8
  resolution: "vscode-uri@npm:3.0.8"
  checksum: 10/e882d6b679e0d053cbc042893c0951a135d899a192b62cd07f0a8924f11ae722067a8d6b1b5b147034becf57faf9fff9fb543b17b749fd0f17db1f54f783f07c
  languageName: node
  linkType: hard

"vue-component-type-helpers@npm:^2.0.0, vue-component-type-helpers@npm:^2.2.0":
  version: 2.2.0
  resolution: "vue-component-type-helpers@npm:2.2.0"
  checksum: 10/91ec570dcd01f2f4192118d0562faa28f7d06867e548bee265b7ca7a7dc5fd112b1251b5bca74f3b21558a4336b87466f6423b83bf42e41e068c1c7e122904f4
  languageName: node
  linkType: hard

"vue-demi@npm:>=0.14.8, vue-demi@npm:^0.14.10":
  version: 0.14.10
  resolution: "vue-demi@npm:0.14.10"
  peerDependencies:
    "@vue/composition-api": ^1.0.0-rc.1
    vue: ^3.0.0-0 || ^2.6.0
  peerDependenciesMeta:
    "@vue/composition-api":
      optional: true
  bin:
    vue-demi-fix: bin/vue-demi-fix.js
    vue-demi-switch: bin/vue-demi-switch.js
  checksum: 10/3028239c0c25a84361a13ab936fcc9a199f54e0320c2ec1d2f4fdf7da8bae663382b7c1e5b1f5a1a43f6aebc73b955892cd4b6c7b3eaf2b766e18cc2a6f7ebea
  languageName: node
  linkType: hard

"vue-demi@npm:^0.13.11":
  version: 0.13.11
  resolution: "vue-demi@npm:0.13.11"
  peerDependencies:
    "@vue/composition-api": ^1.0.0-rc.1
    vue: ^3.0.0-0 || ^2.6.0
  peerDependenciesMeta:
    "@vue/composition-api":
      optional: true
  bin:
    vue-demi-fix: bin/vue-demi-fix.js
    vue-demi-switch: bin/vue-demi-switch.js
  checksum: 10/6c02d1248cfc405478f21f2d3eca3bfba537ad0ff5116f5efc4e0bf296b35cd5d3e2beff4807365018894da18901110c4d5cb8c205273872c4c902fe7522de4f
  languageName: node
  linkType: hard

"vue-eslint-parser@npm:^9.3.1, vue-eslint-parser@npm:^9.4.3":
  version: 9.4.3
  resolution: "vue-eslint-parser@npm:9.4.3"
  dependencies:
    debug: "npm:^4.3.4"
    eslint-scope: "npm:^7.1.1"
    eslint-visitor-keys: "npm:^3.3.0"
    espree: "npm:^9.3.1"
    esquery: "npm:^1.4.0"
    lodash: "npm:^4.17.21"
    semver: "npm:^7.3.6"
  peerDependencies:
    eslint: ">=6.0.0"
  checksum: 10/228e43f0067e5f1fa87a4192f355ebbb4a224f0c7e170b1fbd4205fdf42fe7b3c6820a7e467496a8174e51ba351bc9caed00389d05519206cfa1615cac44516c
  languageName: node
  linkType: hard

"vue-i18n-extract@npm:2.0.7":
  version: 2.0.7
  resolution: "vue-i18n-extract@npm:2.0.7"
  dependencies:
    cac: "npm:^6.7.12"
    dot-object: "npm:^2.1.4"
    glob: "npm:^8.0.1"
    is-valid-glob: "npm:^1.0.0"
    js-yaml: "npm:^4.1.0"
  bin:
    vue-i18n-extract: bin/vue-i18n-extract.js
  checksum: 10/b40b10b804ec28da3bb6d644d996133045745655df15796d73915cfd32f423f90fcb076aa88019985bd5f14db555c86a65fdfcd0492cedfbf7da43ce4574878f
  languageName: node
  linkType: hard

"vue-i18n@npm:11.1.2":
  version: 11.1.2
  resolution: "vue-i18n@npm:11.1.2"
  dependencies:
    "@intlify/core-base": "npm:11.1.2"
    "@intlify/shared": "npm:11.1.2"
    "@vue/devtools-api": "npm:^6.5.0"
  peerDependencies:
    vue: ^3.0.0
  checksum: 10/a9fbb84001169defadff7d90e2d9eff29a8231b5aa142ab6952992e931d595c74e33a76d3dbdef8d0e078f7a98df09794606b659b308a49f1a8eec3186c8cb02
  languageName: node
  linkType: hard

"vue-i18n@npm:^10.0.5":
  version: 10.0.5
  resolution: "vue-i18n@npm:10.0.5"
  dependencies:
    "@intlify/core-base": "npm:10.0.5"
    "@intlify/shared": "npm:10.0.5"
    "@vue/devtools-api": "npm:^6.5.0"
  peerDependencies:
    vue: ^3.0.0
  checksum: 10/557c00fbce207a1eaa836894d4a5140be7c9df546168dac1e14dec7b04f2ecc351161ac11e9cc31eb34da408f863f7371477ab3947ae89f9480ad531c24db1f2
  languageName: node
  linkType: hard

"vue-router@npm:4.5.0":
  version: 4.5.0
  resolution: "vue-router@npm:4.5.0"
  dependencies:
    "@vue/devtools-api": "npm:^6.6.4"
  peerDependencies:
    vue: ^3.2.0
  checksum: 10/0dc76635924c84960614592c3dbf9919961cea765802c86e401d7d0747674ebbe4d7562ddbe1f349112e97e9ccb985b56037b12c7036dd7562c8da78b9cce9b5
  languageName: node
  linkType: hard

"vue-tsc@npm:2.2.8":
  version: 2.2.8
  resolution: "vue-tsc@npm:2.2.8"
  dependencies:
    "@volar/typescript": "npm:~2.4.11"
    "@vue/language-core": "npm:2.2.8"
  peerDependencies:
    typescript: ">=5.0.0"
  bin:
    vue-tsc: ./bin/vue-tsc.js
  checksum: 10/e3df1bfd2f6d3c9b2007e7b4f72b490b1ac5779ac997790d1529fdd00a7a9c637f6fbd621bd0cf240bfc55385ec3cdaa88b3d7eda065fcce951ae47923059084
  languageName: node
  linkType: hard

"vue@npm:3.5.13, vue@npm:^3.5.13":
  version: 3.5.13
  resolution: "vue@npm:3.5.13"
  dependencies:
    "@vue/compiler-dom": "npm:3.5.13"
    "@vue/compiler-sfc": "npm:3.5.13"
    "@vue/runtime-dom": "npm:3.5.13"
    "@vue/server-renderer": "npm:3.5.13"
    "@vue/shared": "npm:3.5.13"
  peerDependencies:
    typescript: "*"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10/bcab4ca74c4a37a2bd3f892d6783f8b01748281cd5aedc2f5dd16521b97a3ba0303cf56df5e217eaa712d38ed2a14b75a92d875cf2596973ac985ca806f44e79
  languageName: node
  linkType: hard

"vuetify@npm:3.7.6":
  version: 3.7.6
  resolution: "vuetify@npm:3.7.6"
  peerDependencies:
    typescript: ">=4.7"
    vite-plugin-vuetify: ">=1.0.0"
    vue: ^3.3.0
    webpack-plugin-vuetify: ">=2.0.0"
  peerDependenciesMeta:
    typescript:
      optional: true
    vite-plugin-vuetify:
      optional: true
    webpack-plugin-vuetify:
      optional: true
  checksum: 10/235d6ee4fb5a8b3e56c677cef0378387fe13c30721ac7765fc25b9132f9549f79207678199a1479c7f190ced4f87e1e074c5cbd9c95601b741aa5a4e8e2df167
  languageName: node
  linkType: hard

"w3c-xmlserializer@npm:^5.0.0":
  version: 5.0.0
  resolution: "w3c-xmlserializer@npm:5.0.0"
  dependencies:
    xml-name-validator: "npm:^5.0.0"
  checksum: 10/d78f59e6b4f924aa53b6dfc56949959229cae7fe05ea9374eb38d11edcec01398b7f5d7a12576bd5acc57ff446abb5c9115cd83b9d882555015437cf858d42f0
  languageName: node
  linkType: hard

"wcwidth@npm:^1.0.1":
  version: 1.0.1
  resolution: "wcwidth@npm:1.0.1"
  dependencies:
    defaults: "npm:^1.0.3"
  checksum: 10/182ebac8ca0b96845fae6ef44afd4619df6987fe5cf552fdee8396d3daa1fb9b8ec5c6c69855acb7b3c1231571393bd1f0a4cdc4028d421575348f64bb0a8817
  languageName: node
  linkType: hard

"webfontloader@npm:^1.6.28":
  version: 1.6.28
  resolution: "webfontloader@npm:1.6.28"
  checksum: 10/fba6a6a41222d893401aa43199ba3937679a6d71a7e1aeed1fc57d2f034c96fefa267b57cf9aff6af5877fed78c11a5327d7f3c8d3d6f2bfb7543fce8640e5f0
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: 10/b65b9f8d6854572a84a5c69615152b63371395f0c5dcd6729c45789052296df54314db2bc3e977df41705eacb8bc79c247cee139a63fa695192f95816ed528ad
  languageName: node
  linkType: hard

"webidl-conversions@npm:^7.0.0":
  version: 7.0.0
  resolution: "webidl-conversions@npm:7.0.0"
  checksum: 10/4c4f65472c010eddbe648c11b977d048dd96956a625f7f8b9d64e1b30c3c1f23ea1acfd654648426ce5c743c2108a5a757c0592f02902cf7367adb7d14e67721
  languageName: node
  linkType: hard

"whatwg-encoding@npm:^3.1.1":
  version: 3.1.1
  resolution: "whatwg-encoding@npm:3.1.1"
  dependencies:
    iconv-lite: "npm:0.6.3"
  checksum: 10/bbef815eb67f91487c7f2ef96329743f5fd8357d7d62b1119237d25d41c7e452dff8197235b2d3c031365a17f61d3bb73ca49d0ed1582475aa4a670815e79534
  languageName: node
  linkType: hard

"whatwg-mimetype@npm:^4.0.0":
  version: 4.0.0
  resolution: "whatwg-mimetype@npm:4.0.0"
  checksum: 10/894a618e2d90bf444b6f309f3ceb6e58cf21b2beaa00c8b333696958c4076f0c7b30b9d33413c9ffff7c5832a0a0c8569e5bb347ef44beded72aeefd0acd62e8
  languageName: node
  linkType: hard

"whatwg-url@npm:^14.0.0":
  version: 14.1.0
  resolution: "whatwg-url@npm:14.1.0"
  dependencies:
    tr46: "npm:^5.0.0"
    webidl-conversions: "npm:^7.0.0"
  checksum: 10/3afd325de6cf3a367820ce7c3566a1f78eb1409c4f27b1867c74c76dab096d26acedf49a8b9b71db53df7d806ec2e9ae9ed96990b2f7d1abe6ecf1fe753af6eb
  languageName: node
  linkType: hard

"whatwg-url@npm:^14.1.0":
  version: 14.1.1
  resolution: "whatwg-url@npm:14.1.1"
  dependencies:
    tr46: "npm:^5.0.0"
    webidl-conversions: "npm:^7.0.0"
  checksum: 10/803bede3ec6c8f14de0d84ac6032479646b5a2b08f5a7289366c3461caed9d7888d171e2846b59798869191037562c965235c2eed6ff2e266c05a2b4a6ce0160
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: "npm:~0.0.3"
    webidl-conversions: "npm:^3.0.0"
  checksum: 10/f95adbc1e80820828b45cc671d97da7cd5e4ef9deb426c31bcd5ab00dc7103042291613b3ef3caec0a2335ed09e0d5ed026c940755dbb6d404e2b27f940fdf07
  languageName: node
  linkType: hard

"which-module@npm:^2.0.0":
  version: 2.0.1
  resolution: "which-module@npm:2.0.1"
  checksum: 10/1967b7ce17a2485544a4fdd9063599f0f773959cca24176dbe8f405e55472d748b7c549cd7920ff6abb8f1ab7db0b0f1b36de1a21c57a8ff741f4f1e792c52be
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10/4782f8a1d6b8fc12c65e968fea49f59752bf6302dc43036c3bf87da718a80710f61a062516e9764c70008b487929a73546125570acea95c5b5dcc8ac3052c70f
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: 10/6ec99e89ba32c7e748b8a3144e64bfc74aa63e2b2eacbb61a0060ad0b961eb1a632b08fb1de067ed59b002cec3e21de18299216ebf2325ef0f78e0f121e14e90
  languageName: node
  linkType: hard

"word-wrap@npm:^1.2.5":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: 10/1ec6f6089f205f83037be10d0c4b34c9183b0b63fca0834a5b3cee55dd321429d73d40bb44c8fc8471b5203d6e8f8275717f49a8ff4b2b0ab41d7e1b563e0854
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10/cebdaeca3a6880da410f75209e68cd05428580de5ad24535f22696d7d9cab134d1f8498599f344c3cf0fb37c1715807a183778d8c648d6cc0cb5ff2bb4236540
  languageName: node
  linkType: hard

"wrap-ansi@npm:^6.0.1, wrap-ansi@npm:^6.2.0":
  version: 6.2.0
  resolution: "wrap-ansi@npm:6.2.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10/0d64f2d438e0b555e693b95aee7b2689a12c3be5ac458192a1ce28f542a6e9e59ddfecc37520910c2c88eb1f82a5411260566dba5064e8f9895e76e169e76187
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10/7b1e4b35e9bb2312d2ee9ee7dc95b8cb5f8b4b5a89f7dde5543fe66c1e3715663094defa50d75454ac900bd210f702d575f15f3f17fa9ec0291806d2578d1ddf
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 10/159da4805f7e84a3d003d8841557196034155008f817172d4e986bd591f74aa82aa7db55929a54222309e01079a65a92a9e6414da5a6aa4b01ee44a511ac3ee5
  languageName: node
  linkType: hard

"ws@npm:^8.17.1, ws@npm:^8.18.0":
  version: 8.18.0
  resolution: "ws@npm:8.18.0"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10/70dfe53f23ff4368d46e4c0b1d4ca734db2c4149c6f68bc62cb16fc21f753c47b35fcc6e582f3bdfba0eaeb1c488cddab3c2255755a5c3eecb251431e42b3ff6
  languageName: node
  linkType: hard

"xml-name-validator@npm:^4.0.0":
  version: 4.0.0
  resolution: "xml-name-validator@npm:4.0.0"
  checksum: 10/f9582a3f281f790344a471c207516e29e293c6041b2c20d84dd6e58832cd7c19796c47e108fd4fd4b164a5e72ad94f2268f8ace8231cde4a2c6428d6aa220f92
  languageName: node
  linkType: hard

"xml-name-validator@npm:^5.0.0":
  version: 5.0.0
  resolution: "xml-name-validator@npm:5.0.0"
  checksum: 10/43f30f3f6786e406dd665acf08cd742d5f8a46486bd72517edb04b27d1bcd1599664c2a4a99fc3f1e56a3194bff588b12f178b7972bc45c8047bdc4c3ac8d4a1
  languageName: node
  linkType: hard

"xmlchars@npm:^2.2.0":
  version: 2.2.0
  resolution: "xmlchars@npm:2.2.0"
  checksum: 10/4ad5924974efd004a47cce6acf5c0269aee0e62f9a805a426db3337af7bcbd331099df174b024ace4fb18971b8a56de386d2e73a1c4b020e3abd63a4a9b917f1
  languageName: node
  linkType: hard

"y18n@npm:^4.0.0":
  version: 4.0.3
  resolution: "y18n@npm:4.0.3"
  checksum: 10/392870b2a100bbc643bc035fe3a89cef5591b719c7bdc8721bcdb3d27ab39fa4870acdca67b0ee096e146d769f311d68eda6b8195a6d970f227795061923013f
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 10/5f1b5f95e3775de4514edbb142398a2c37849ccfaf04a015be5d75521e9629d3be29bd4432d23c57f37e5b61ade592fb0197022e9993f81a06a5afbdcda9346d
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 10/9af0a4329c3c6b779ac4736c69fae4190ac03029fa27c1aef4e6bcc92119b73dea6fe5db5fe881fb0ce2a0e9539a42cdf60c7c21eda04d1a0b8c082e38509efb
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10/4cb02b42b8a93b5cf50caf5d8e9beb409400a8a4d85e83bb0685c1457e9ac0b7a00819e9f5991ac25ffabb56a78e2f017c1acc010b3a1babfe6de690ba531abd
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: 10/1884d272d485845ad04759a255c71775db0fac56308764b4c77ea56a20d56679fad340213054c8c9c9c26fcfd4c4b2a90df993b7e0aaf3cdb73c618d1d1a802a
  languageName: node
  linkType: hard

"yaml-ast-parser@npm:^0.0.43":
  version: 0.0.43
  resolution: "yaml-ast-parser@npm:0.0.43"
  checksum: 10/a54d00c8e0716a392c6e76eee965b3b4bba434494196490946e416fc47f20a1d89820461afacd9431edbb8209e28fce33bcff1fb42dd83f90e51fc31e80251c9
  languageName: node
  linkType: hard

"yaml-eslint-parser@npm:^1.2.2":
  version: 1.2.3
  resolution: "yaml-eslint-parser@npm:1.2.3"
  dependencies:
    eslint-visitor-keys: "npm:^3.0.0"
    lodash: "npm:^4.17.21"
    yaml: "npm:^2.0.0"
  checksum: 10/b936470882a38457333591cbecae5ace8e3cdb92cf0754a51429230e5a1fc56f90a8433341474ed8961125fd81047ee57fa3eb053dcd94cde76c8ae35cb58add
  languageName: node
  linkType: hard

"yaml@npm:^2.0.0, yaml@npm:^2.3.1":
  version: 2.6.1
  resolution: "yaml@npm:2.6.1"
  bin:
    yaml: bin.mjs
  checksum: 10/cf412f03a33886db0a3aac70bb4165588f4c5b3c6f8fc91520b71491e5537800b6c2c73ed52015617f6e191eb4644c73c92973960a1999779c62a200ee4c231d
  languageName: node
  linkType: hard

"yargs-parser@npm:^18.1.2":
  version: 18.1.3
  resolution: "yargs-parser@npm:18.1.3"
  dependencies:
    camelcase: "npm:^5.0.0"
    decamelize: "npm:^1.2.0"
  checksum: 10/235bcbad5b7ca13e5abc54df61d42f230857c6f83223a38e4ed7b824681875b7f8b6ed52139d88a3ad007050f28dc0324b3c805deac7db22ae3b4815dae0e1bf
  languageName: node
  linkType: hard

"yargs-parser@npm:^21.1.1":
  version: 21.1.1
  resolution: "yargs-parser@npm:21.1.1"
  checksum: 10/9dc2c217ea3bf8d858041252d43e074f7166b53f3d010a8c711275e09cd3d62a002969a39858b92bbda2a6a63a585c7127014534a560b9c69ed2d923d113406e
  languageName: node
  linkType: hard

"yargs@npm:^15.3.1":
  version: 15.4.1
  resolution: "yargs@npm:15.4.1"
  dependencies:
    cliui: "npm:^6.0.0"
    decamelize: "npm:^1.2.0"
    find-up: "npm:^4.1.0"
    get-caller-file: "npm:^2.0.1"
    require-directory: "npm:^2.1.1"
    require-main-filename: "npm:^2.0.0"
    set-blocking: "npm:^2.0.0"
    string-width: "npm:^4.2.0"
    which-module: "npm:^2.0.0"
    y18n: "npm:^4.0.0"
    yargs-parser: "npm:^18.1.2"
  checksum: 10/bbcc82222996c0982905b668644ca363eebe6ffd6a572fbb52f0c0e8146661d8ce5af2a7df546968779bb03d1e4186f3ad3d55dfaadd1c4f0d5187c0e3a5ba16
  languageName: node
  linkType: hard

"yargs@npm:^17.0.0, yargs@npm:^17.7.2":
  version: 17.7.2
  resolution: "yargs@npm:17.7.2"
  dependencies:
    cliui: "npm:^8.0.1"
    escalade: "npm:^3.1.1"
    get-caller-file: "npm:^2.0.5"
    require-directory: "npm:^2.1.1"
    string-width: "npm:^4.2.3"
    y18n: "npm:^5.0.5"
    yargs-parser: "npm:^21.1.1"
  checksum: 10/abb3e37678d6e38ea85485ed86ebe0d1e3464c640d7d9069805ea0da12f69d5a32df8e5625e370f9c96dd1c2dc088ab2d0a4dd32af18222ef3c4224a19471576
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: 10/f77b3d8d00310def622123df93d4ee654fc6a0096182af8bd60679ddcdfb3474c56c6c7190817c84a2785648cdee9d721c0154eb45698c62176c322fb46fc700
  languageName: node
  linkType: hard
