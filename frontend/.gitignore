.vscode
.idea

# dependencies
node_modules
/.pnp
.pnp.js

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Yarn (we can talk about Yarn Zero-Installs in the future: https://yarnpkg.com/features/caching#zero-installs)
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# Files
*.tsbuildinfo
.eslintcache
*.logs
#examples/**/*lock*
/wunderctl
scripts/mc

# Logs
logs
*.log
lerna-debug.log*
.pnpm-debug.log*
