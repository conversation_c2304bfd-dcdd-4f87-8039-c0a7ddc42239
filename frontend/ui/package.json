{"name": "ui", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --mode dev", "local": "vite --mode localbackend", "build": "run-p \"build-only {@}\" --", "build-watch": "vite build --watch", "preview": "vite preview --mode dev", "test:unit": "vitest", "test:ui": "playwright test", "build-only": "vite build", "type-check": "vue-tsc --build", "check-translations": "node i18n-check.js", "lint": "eslint . --fix", "lint-ci": "eslint . --no-fix --format gitlab --max-warnings 0", "format": "prettier --write src/", "codegen": "graphql-codegen --config codegen.config.ts"}, "dependencies": {"@bd/cd-system3": "^11.3.2", "@tanstack/vue-query": "4.37.1", "@vueuse/core": "12.3.0", "file-saver": "2.0.5", "graphql": "^16.9.0", "graphql-request": "^7.1.2", "keycloak-js": "26.0.7", "pinia": "2.3.0", "vue": "3.5.13", "vue-i18n": "11.1.2", "vue-router": "4.5.0", "vuetify": "3.7.6"}, "devDependencies": {"@eslint/compat": "1.2.4", "@graphql-codegen/cli": "5.0.3", "@graphql-codegen/client-preset": "4.5.1", "@graphql-eslint/eslint-plugin": "^4.4.0", "@intlify/eslint-plugin-vue-i18n": "3.2.0", "@playwright/test": "1.52.0", "@tsconfig/node22": "22.0.0", "@types/file-saver": "2.0.7", "@types/jsdom": "21.1.7", "@types/lodash-es": "4.17.12", "@types/node": "22.10.5", "@vitejs/plugin-vue": "5.2.1", "@vitest/eslint-plugin": "1.1.24", "@vue/eslint-config-prettier": "10.1.0", "@vue/eslint-config-typescript": "14.2.0", "@vue/test-utils": "2.4.6", "@vue/tsconfig": "0.7.0", "dotenv": "16.4.7", "eslint": "9.17.0", "eslint-formatter-gitlab": "5.1.0", "eslint-plugin-playwright": "2.1.0", "eslint-plugin-vue": "9.32.0", "glob": "11.0.2", "jsdom": "26.0.0", "lodash-es": "4.17.21", "npm-run-all2": "7.0.2", "prettier": "3.4.2", "sass": "1.83.1", "sass-loader": "16.0.4", "typescript": "5.7.3", "vite": "6.1.6", "vite-plugin-graphql-codegen": "^3.5.0", "vite-plugin-vuetify": "2.0.4", "vue-i18n-extract": "2.0.7", "vue-tsc": "2.2.8"}}