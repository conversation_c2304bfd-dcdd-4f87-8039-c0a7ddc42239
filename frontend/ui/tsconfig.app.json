{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "include": ["env.d.ts", "src/**/*", "src/**/*.vue"],
  "exclude": ["src/**/__tests__/*"],
  "compilerOptions": {
    "composite": true,
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",

    "paths": {
      "@/*": ["./src/*"]
    },

    // Overriding the expected target runtime given in `@vue/tsconfig/tsconfig.dom.json` to be in sync with `build.target` in the Vite
    // config. If we want to use more modern features and keep compatability we should ensure to bundle the required polyfills.
    "lib": [
      "ES2022",
      "DOM",
      "DOM.Iterable"
    ]
  }
}
