/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_LOCAL_KEYCLOAK_URL: string
  readonly VITE_LOCAL_KEYCLOAK_CLIENTID: string
  readonly VITE_LOCAL_KEYCLOAK_REALM: string
  readonly VITE_LOCAL_PROXY_URL: string
  readonly VITE_LOCAL_API_URL: string
  readonly VITE_LOCAL_API_GRAPH_URL: string
  readonly VITE_VUETIFY_VIMG_EAGER: string
  readonly VITE_DOCK_DISABLE: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// Workaround until https://github.com/intlify/vue-i18n/issues/1403 is resolved to fix vue-i18n augmentations like `$t` not being found in
// component templates. Source and rationale: https://nuxt.com/blog/v3-13#vue-typescript-changes
import type {
  ComponentCustomOptions as _ComponentCustomOptions,
  ComponentCustomProperties as _ComponentCustomProperties,
} from 'vue';

declare module '@vue/runtime-core' {
  /* eslint-disable */
  interface ComponentCustomProperties extends _ComponentCustomProperties {}
  interface ComponentCustomOptions extends _ComponentCustomOptions {}
  /* eslint-enable */
}
