const noContracts = {
  contracts: {
    count: 0,
    results: []
  }
}

const contractData1 = {
  contracts: {
    count: 3,
    results: [
      {
        companyId: '52967218-7c7e-4471-8391-4962e7bbe537',
        contractId: 'BC_1000800001',
        contractPeriod: 'P3M',
        contractType: 'SUBSCRIPTION',
        noticePeriod: 'P1M',
        orderNumber: '928374',
        productId: 'DDCIH_hydraulic_hub_quarterly',
        startDate: '2024-07-16T12:07:12.247Z',
        contractState: 'ACTIVE'
      },
      {
        companyId: '52967218-7c7e-4471-8391-4962e7bbe537',
        contractId: 'BC_1000800002',
        contractPeriod: 'P3M',
        contractType: 'SUBSCRIPTION',
        endDate: '2024-07-20T12:07:12.247Z',
        noticePeriod: 'P1M',
        orderNumber: '928374',
        productId: 'DDCIH_hydraulic_hub_quarterly',
        startDate: '2024-07-16T12:07:12.247Z',
        contractState: 'CANCELLED',
        licenses: [
          {
            contractId: 'BC_1000800002',
            email: '<EMAIL>',
            firstname: '<PERSON> Joseph',
            lastname: 'Testermann',
            licenseId: '6cb7389e-e556-4f3b-93dd-dc81d894ccf4',
            licenseModel: 'DCKEYCLOAK'
          }
        ]
      },
      {
        companyId: '52967218-7c7e-4471-8391-4962e7bbe537',
        contractId: 'BC_1000800003',
        contractPeriod: 'P1M',
        contractType: 'CONSUMPTION',
        noticePeriod: 'P28D',
        orderNumber: '928375',
        productId: 'DRX_PR_BODAS_EXTENDED',
        startDate: '2025-01-02T12:34:56.789Z',
        endDate: '2025-02-02T12:34:56.789Z',
        contractState: 'CANCELLATION_PENDING'
      }
    ]
  }
}

const contractData2 = {
  contracts: {
    count: 1,
    results: [
      {
        companyId: '52967218-7c7e-4471-8391-4962e7bbe537',
        contractId: 'BC_1000800001',
        contractPeriod: 'P3M',
        contractType: 'SUBSCRIPTION',
        noticePeriod: 'P1M',
        orderNumber: '928374',
        productId: 'DRX_PC_15756M_DE',
        startDate: '2024-07-16T12:07:12.247Z',
        contractState: 'ACTIVE',
        projectedEndDate: '2026-07-16T12:07:12.247Z',
        licenses: [
          {
            contractId: 'BC_1000800001',
            email: '<EMAIL>',
            firstname: 'Franz Joseph',
            lastname: 'Testermann',
            licenseId: '6cb7389e-e556-4f3b-93dd-dc81d894ccf4',
            licenseModel: 'BCCENTRAL'
          }
        ]
      }
    ]
  }
}

export const contracts = {
  'No Contracts': noContracts,
  'Contract Data 1': contractData1,
  'Contract Data 2': contractData2
}
