import { test as base, type Page } from '@playwright/test'

type TestFixtures = {
  checkoutSuccessPage: Page
}

export type TestOptions = {
  paymentId?: string
}

export const test = base.extend<TestFixtures & TestOptions>({
  paymentId: [undefined, { option: true }],

  checkoutSuccessPage: async ({ page, paymentId }, use) => {
    await page.mouse.move(0, 0)
    await page.goto(`/checkout-success?id=${paymentId}`)
    await use(page)
  }
})

export { expect } from '@playwright/test'
