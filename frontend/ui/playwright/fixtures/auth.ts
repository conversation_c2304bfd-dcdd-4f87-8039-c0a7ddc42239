import process from 'node:process'
import { test as base, expect as baseExpect, Page } from '@playwright/test'
import { users, type UserCredentials } from '../users'

type TestOptions = {
  credentials?: UserCredentials
}

export type Authentication = {
  credentials: UserCredentials,
  logout: () => Promise<void>
}

type TestFixtures = {
  authentication: Authentication
}

export const test = base.extend<TestFixtures & TestOptions>({
  credentials: [undefined, { option: true }],

  authentication: async ({ page, credentials }, use) => {
    expect(credentials).toBeTruthy()

    await page.goto('/')
    await page.getByTestId('login-button').click()
    await page.locator('input#username').fill(credentials!.username)
    await page.locator('input#password').fill(credentials!.password)
    await page.locator('button#kc-login').click()
    await page.waitForLoadState()

    const authentication: Authentication = {
      credentials: credentials!,
      logout: async () => {
        const keycloakUrl = process.env.VITE_LOCAL_KEYCLOAK_URL
        expect(keycloakUrl).toBeTruthy()
        const keycloakRealm = process.env.VITE_LOCAL_KEYCLOAK_REALM
        expect(keycloakRealm).toBeTruthy()

        // Wait for things to settle, otherwise the next request might interfere with whatever has been done during the test execution.
        await page.waitForLoadState('networkidle')

        await page.goto(`${keycloakUrl}/realms/${keycloakRealm}/protocol/openid-connect/logout`)
        await page.locator('input#kc-logout').click()
        await page.waitForLoadState()
      }
    }

    await use(authentication)
    await authentication.logout()
  }
})

export const expect = baseExpect.extend({
  async toBeLogin (page: Page, options?: { timeout?: number, visible?: boolean }) {
    try {
      await baseExpect(page.locator('button#kc-login')).toBeVisible(options)
      return {
        message: () => 'Login button is visible on page',
        pass: true
      }
    } catch {
      return {
        message: () => 'Login button is not visible on page',
        pass: false
      }
    }
  }
})

export { users }
