import { mergeTests, mergeExpects, type Expect, type Page } from '@playwright/test'
import { test as authTest, expect as authExpect } from './auth'
import { test as cartPageTest, expect as cartPageExpect } from './cart-page'
import { test as checkoutPageTest, expect as checkoutPageExpect } from './checkout-page'
import { test as checkoutSuccessPageTest, expect as checkoutSuccessPageExpect } from './checkout-success-page'
import { test as configurationMockTest, expect as configurationMockExpect } from './configuration-mock'
import { test as graphQlTest, expect as graphQlExpect } from './graphql-mock'
import { test as meMockTest, expect as meMockExpect } from './me-mock'
import { test as productSelectionPageTest, expect as productSelectionPageExpect } from './product-selection-page'
import { test as productsOverviewPageTest, expect as productsOverviewPageExpect } from './products-overview-page'
import { test as subscriptionsPageTest, expect as subscriptionsPageExpect } from './subscriptions-page'

export const test = mergeTests(authTest,
  cartPageTest,
  checkoutPageTest,
  checkoutSuccessPageTest,
  configurationMockTest,
  graphQlTest,
  meMockTest,
  productSelectionPageTest,
  productsOverviewPageTest,
  subscriptionsPageTest,
)

export const expect = mergeExpects(authExpect,
  cartPageExpect,
  checkoutPageExpect,
  checkoutSuccessPageExpect,
  configurationMockExpect,
  graphQlExpect,
  meMockExpect,
  productSelectionPageExpect,
  productsOverviewPageExpect,
  subscriptionsPageExpect,
) as Expect<{
  // Any extensions to `expect` must be added here manually for proper typing. See: https://github.com/microsoft/playwright/issues/27870
  // e.g.
  // ```
  // toBeBar(foo: number): Promise<void>
  // toBeFoo(page: Page, value: string): Promise<void>
  // ```
  toBeLogin(page: Page, options?: { timeout?: number, visible?: boolean }): Promise<void>
}>
