import { test as base, expect, type Page } from '@playwright/test'

export type TestFixtures = {
  productSelectionPage: Page
}

export type TestOptions = {
  productCode?: string
}

export const test = base.extend<TestFixtures & TestOptions>({
  productCode: [undefined, { option: true }],

  productSelectionPage: async ({ page, productCode }, use) => {
    expect(productCode).toBeTruthy()

    await page.mouse.move(0, 0)
    await page.goto(`/products/${productCode}`)
    await use(page)
  }
})

export { expect } from '@playwright/test'
