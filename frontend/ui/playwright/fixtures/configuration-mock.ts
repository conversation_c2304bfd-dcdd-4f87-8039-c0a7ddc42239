import { test as base, expect } from '@playwright/test'
import { configurations } from '../configurations'

type TestOptions = {
  // TODO fix project setup so tests and fixtures are aware of types in `src/types`
  configuration?: unknown
}

type TestFixtures = {
  configurationMock: void
}

export const test = base.extend<TestFixtures & TestOptions>({
  configuration: [undefined, { option: true }],

  configurationMock: async ({ page, configuration }, use) => {
    expect(configuration).toBeTruthy()

    await page.route('*/**/rest/configuration', async route => {
      await route.fulfill({ json: configuration })
    })

    await use()
  }
})

export { expect } from '@playwright/test'

export { configurations }
