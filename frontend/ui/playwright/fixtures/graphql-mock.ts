import path from 'path'
import { fileURLToPath } from 'url'
import type { Page } from '@playwright/test'
import { test as base } from 'playwright/test'

export class GraphQlMock {

  constructor(private readonly page: Page) {}

  async fulfill(operationName: string, data: Record<string, unknown>, errors?: Record<string, unknown>) {
    await this.page.route('**/graphql', async route => {
      const request = route.request().postDataJSON()

      if (request.operationName !== operationName) {
        return route.fallback()
      }
      return route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ data, errors }),
      });
    });
  }
}

type TestOptions = {
  // TODO fix project setup so tests and fixtures are aware of types in `src/types`
  cart?: Record<string, unknown>,
  categories?: Record<string, unknown>
  checkout?: Record<string, unknown>
  contracts?: Record<string, unknown>
  products?: Record<string, unknown>
  invoices?: Record<string, unknown>
  payment?: Record<string, unknown>
}

type TestFixtures = {
  graphQlMock: GraphQlMock
}

export const test = base.extend<TestFixtures & TestOptions>({
  cart: [undefined, { option: true }],
  categories: [undefined, { option: true }],
  checkout: [undefined, { option: true }],
  contracts: [undefined, { option: true }],
  products: [undefined, { option: true }],
  invoices: [undefined, { option: true }],
  payment: [undefined, { option: true }],

  graphQlMock: async ({ page, cart, categories,checkout, contracts, products, invoices, payment }, use) => {
    // FIXME create separate mock for static assets
    await page.route('*/**/rexroth_product.png', async route => {
      await route.fulfill({ path: path.join(path.dirname(fileURLToPath(import.meta.url)), 'static', 'rexroth_product.png') })
    })
    await page.route('*/**/rexroth_icon.png', async route => {
      await route.fulfill({ path: path.join(path.dirname(fileURLToPath(import.meta.url)), 'static', 'rexroth_icon.png') })
    })

    const mock = new GraphQlMock(page)

    if (cart) {
      await mock.fulfill('CurrentCart', cart)
    }
    if (categories) {
      await mock.fulfill('AllCategories', categories)
    }
    if (contracts) {
      await mock.fulfill('AllContracts', contracts)
    }
    if (checkout) {
      await mock.fulfill('CheckoutData', checkout)
    }
    if (products) {
      await mock.fulfill('AllProducts', products)
    }
    if (invoices) {
      await mock.fulfill('AllInvoices', invoices)
    }
    if (payment) {
      await mock.fulfill('Payment', payment)
    }

    await use(mock)
  }
})

export { carts } from '../carts'
export { categories } from '../categories'
export { checkout } from '../checkoutData'
export { contracts } from '../contractData'
export { invoices } from '../invoiceData'
export { products } from '../products'
export { payments } from '../paymentData'

export { expect } from '@playwright/test'
