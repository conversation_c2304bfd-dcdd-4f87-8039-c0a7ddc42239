import { test as base, expect } from '@playwright/test'
import { companies } from '../me'

type TestOptions = {
  // TODO fix project setup so tests and fixtures are aware of types in `src/types`
  company?: unknown
}

type TestFixtures = {
  meMock: void
}

export const test = base.extend<TestFixtures & TestOptions>({
  company: [undefined, { option: true }],

  meMock: async ({ page, company }, use) => {
    expect(company).toBeTruthy()

    await page.route('*/**/rest/me/company', async (route) => {
      await route.fulfill({ json: company })
    })

    await use()
  },
})

export { expect } from '@playwright/test'

export { companies }
