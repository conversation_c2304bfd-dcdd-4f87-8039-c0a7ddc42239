const checkoutData1 = {
  cart: {
    lineItems: [
      {
        name: 'Hydraulic Hub',
        productId: '6d8ab4be-e24b-473b-a6a5-f1228380b86c',
        quantity: 1,
        variant: {
          name: 'Quarterly Team',
          runtime: 'P3M',
          licenseType: 'SUBSCRIPTION',
          agreements: [
            {
              name: 'License agreements',
              linkType: 'SOFTWARE_LICENSE',
              url: 'http://azena.com/someotherdocument_en.pdf',
            },
            {
              name: 'Terms and conditions',
              linkType: 'PRIVACY_POLICY',
              url: 'http://azena.com/somedocument_en.pdf',
            },
          ],
        },
        itemPrice: {
          currencyCode: 'EUR',
          value: 350,
        },
        totalPrice: {
          currencyCode: 'EUR',
          value: 350,
        },
        product: {
          images: ['https://localhost/rexroth_product.png', 'https://localhost/rexroth_icon.png'],
        },
      },
      {
        name: 'Hydraulic Hub',
        productId: '6d8ab4be-e24b-473b-a6a5-f1228380b86c',
        quantity: 2,
        variant: {
          name: 'Quarterly ',
          runtime: 'P3M',
          licenseType: 'SUBSCRIPTION',
          agreements: [
            {
              name: 'License agreements',
              linkType: 'SOFTWARE_LICENSE',
              url: 'http://azena.com/someotherdocument_en.pdf',
            },
            {
              name: 'Terms and conditions',
              linkType: 'SOFTWARE_LICENSE',
              url: 'http://azena.com/somedocument_en.pdf',
            },
          ],
        },
        itemPrice: {
          currencyCode: 'EUR',
          value: 150,
        },
        totalPrice: {
          currencyCode: 'EUR',
          value: 300,
        },
        product: {
          images: ['https://localhost/rexroth_product.png', 'https://localhost/rexroth_icon.png'],
        },
      },
    ],
    totalPrice: {
      currencyCode: 'EUR',
      value: 650,
    },
    sellerCompany: {
      name: 'Bosch Rexroth',
    },
    billingAddress: {
      city: 'Wörgl',
      postalCode: '6300',
      streetName: 'Innsbrucker Str. Store test',
      streetNumber: '56',
      country: 'AT',
      email: '<EMAIL>',
    },
  },
  paymentConfig: {
    paymentMethods: [
      {
        paymentMethodId: 'BOSCH_TRANSFER/SEPA_CREDIT',
        accountHolder: 'Bosch GmbH',
        bankName: 'Deutsche Bank',
        bic: 'BELADEBEXXX',
        iban: '**********************',
      },
      {
        paymentMethodId: 'BOSCH_TRANSFER/ACH_CREDIT',
        accountHolder: 'Kiroshi Optics',
        accountNumber: '9832714',
        bankName: 'Arasaka Bank',
        bic: 'AJKHSDK',
        routingNumber: '********',
      },
      {
        paymentMethodId: 'PGW/SEPA_DIRECTDEBIT',
      },
    ],
  },
}

export const checkout = {
  'Checkout Data 1': checkoutData1,
}
