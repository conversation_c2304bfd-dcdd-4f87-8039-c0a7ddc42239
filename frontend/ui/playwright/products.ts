const testProducts1 = {
  productSearch: {
    count: 2,
    results: [
      {
        id: '6d8ab4be-e24b-473b-a6a5-f1228380b86c',
        product: {
          id: '6d8ab4be-e24b-473b-a6a5-f1228380b86c',
          name: 'Hydraulic Hub',
          description: 'Hydraulic Hub description',
          productType: 'a46f1d35-7553-4813-8a20-993f1e84b9f4',
          images: ['https://localhost/rexroth_product.png', 'https://localhost/rexroth_icon.png'],
          variants: [
            {
              sku: 'DDCIH_hydraulic_hub_trial',
              price: {
                value: 0.0,
                currencyCode: 'EUR',
              },
              bundleAmount: 1,
              licenseType: 'TRIAL',
              noticePeriod: 'P0D',
              runtime: 'P1D',
              name: 'Free trial',
              description: 'this is the free trial',
              features: 'feature 1\nfeature 2',
              agreements: [],
              entitlements: ['DCKEYCLOAK:FREEMIUM'],
            },
            {
              sku: 'DDCIH_hydraulic_hub_yearly',
              price: {
                value: 200.0,
                currencyCode: 'EUR',
              },
              bundleAmount: 1,
              licenseType: 'SUBSCRIPTION',
              runtime: 'P1Y',
              noticePeriod: 'P1M',
              name: 'Yearly',
              description: 'this renews yearly',
              features: 'feature 1\nfeature 2',
              agreements: [],
              entitlements: ['DCKEYCLOAK:PREMIUM', 'LITMOS'],
            },
            {
              sku: 'DDCIH_hydraulic_hub_yearly_team',
              price: {
                value: 300.0,
                currencyCode: 'EUR',
              },
              bundleAmount: 5,
              licenseType: 'SUBSCRIPTION',
              runtime: 'P1Y',
              noticePeriod: 'P1M',
              name: 'Yearly Team',
              description: 'this is the description for the team size yearly subscription',
              features: 'feature 2\nfeature 3\nfeature 4',
              agreements: [],
              entitlements: ['DCKEYCLOAK:PREMIUM', 'LITMOS'],
            },
            {
              sku: 'DDCIH_hydraulic_hub_quarterly',
              price: {
                value: 150.0,
                currencyCode: 'EUR',
              },
              bundleAmount: 1,
              licenseType: 'SUBSCRIPTION',
              runtime: 'P3M',
              noticePeriod: 'P1M',
              name: 'Quarterly ',
              description: 'Quarterly payment',
              features: 'renews quarterly\n1 seat\nfeature 3',
              agreements: [],
              entitlements: ['DCKEYCLOAK:PREMIUM', 'LITMOS'],
            },
            {
              sku: 'DDCIH_hydraulic_hub_quarterly_team',
              price: {
                value: 350.0,
                currencyCode: 'EUR',
              },
              bundleAmount: 5,
              licenseType: 'SUBSCRIPTION',
              runtime: 'P3M',
              noticePeriod: 'P30D',
              name: 'Quarterly Team',
              description: 'valid for a team of 5',
              features:
                'renews quarterly\nvalid for 5 seats\nfeature 3\nfeature 4\nfeature 5\nfeature 6',
              agreements: [],
              entitlements: ['DCKEYCLOAK:PREMIUM', 'LITMOS'],
            },
          ],
          externalDocuments: [
            {
              name: 'Whitepaper',
              url: 'http://azena.com/someotherdocument_en.pdf',
            },
            {
              name: 'Marketing Brochure',
              url: 'http://azena.com/somedocument_en.pdf',
            },
          ],
          sellerCompany: {
            name: 'Bosch Rexroth AG',
          },
        },
      },
      {
        id: '5abd3673-2110-4aa0-bbf9-c97cd1e24c89',
        product: {
          id: '5abd3673-2110-4aa0-bbf9-c97cd1e24c89',
          name: 'RCU Series 10 & 20',
          description:
            'BODAS Connect for RCU Series 10 & 20 (standard & high-performance). The Telematics solution for machine OEMs to gain internal R&D and customer support efficiency & establish external data driven business models. Includes integrated Device Management, Cellular Connectivity and Data Management and offers Over-the-Air functionality for updating ECUs. ',
          productType: 'a46f1d35-7553-4813-8a20-993f1e84b9f4',
          images: ['https://localhost/rexroth_product.jpeg', 'https://localhost/rexroth_icon.png'],
          variants: [
            {
              addons: [
                {
                  addonVariants: [
                    {
                      name: '50 MB',
                      sku: 'DRX_PR_BODAS_CONNECTION_50MB',
                    },
                    {
                      description: '⭐️ Recommended',
                      name: '100 MB',
                      sku: 'DRX_PR_BODAS_CONNECTION_100MB',
                    },
                  ],
                  description: 'Cellular Services for RCU Series 10 & 20',
                  name: 'Cellular Connection',
                },
              ],
              agreements: [],
              bundleAmount: 1,
              description:
                'The Telematics solution for machine OEMs to gain internal R&D and customer support efficiency & establish external data driven business models. Includes integrated Device Management, Cellular Connectivity and Data Management and offers Over-the-Air functionality for updating ECUs.',
              entitlements: ['BCCENTRAL'],
              features: 'SOTA\nCustom Snaps\nCybersecurity',
              licenseType: 'CONSUMPTION',
              name: 'Device Management Compact',
              noticePeriod: 'P28D',
              price: {
                currencyCode: 'EUR',
                value: 249,
              },
              runtime: 'P1M',
              sku: 'DRX_PR_BODAS_COMPACT',
            },
            {
              addons: [
                {
                  addonVariants: [
                    {
                      description: '600MB of data',
                      name: '600MB',
                      sku: 'DRX_PR_BODAS_DATA_600MB',
                    },
                    {
                      description: '3GB',
                      name: '3GB',
                      sku: 'DRX_PR_BODAS_DATA_3GB',
                    },
                  ],
                  description: 'Data Management',
                  name: 'Data Management',
                },
                {
                  addonVariants: [
                    {
                      name: '50 MB',
                      sku: 'DRX_PR_BODAS_CONNECTION_50MB',
                    },
                    {
                      description: '⭐️ Recommended',
                      name: '100 MB',
                      sku: 'DRX_PR_BODAS_CONNECTION_100MB',
                    },
                  ],
                  description: 'Cellular Services for RCU Series 10 & 20',
                  name: 'Cellular Connection',
                },
              ],
              agreements: [],
              bundleAmount: 1,
              description: 'Compact + OTA Services + Universal Flasher ',
              entitlements: ['BCCENTRAL'],
              features: 'Compact\nOTA Services\nUniversal Flasher',
              licenseType: 'CONSUMPTION',
              name: 'Device Management Extended',
              noticePeriod: 'P28D',
              price: {
                currencyCode: 'EUR',
                value: 499,
              },
              runtime: 'P1M',
              sku: 'DRX_PR_BODAS_EXTENDED',
            },
          ],
          externalDocuments: [
            {
              name: 'Fact Sheet Device Connectivity',
              url: 'https://www.boschrexroth.com/de/de/media-details/29d12ccd-61cb-455c-9378-8a58440cf4ae',
            },
          ],
          sellerCompany: {
            name: 'Bosch Rexroth AG',
          },
        },
      },
    ],
  },
}

const testProducts2 = {
  productSearch: {
    count: 1,
    results: [
      {
        id: 'b21d8b73-debd-4f44-9377-8ba93208d7ae',
        product: {
          id: 'b21d8b73-debd-4f44-9377-8ba93208d7ae',
          name: 'BODAS Connect Lite',
          description:
            'Interested in quick connectivity with RCU Series 5? Once your telematics hardware (RCU Series 5) is ordered and underway, book your Basic Device Management package including cellular connectivity now.  For new customers without an existing cloud-based Data Management subscription, we recommend adding it to your order here.',
          productType: 'a46f1d35-7553-4813-8a20-993f1e84b9f4',
          images: ['https://localhost/rexroth_product.jpeg', 'https://localhost/rexroth_icon.png'],
          variants: [
            {
              addons: [
                {
                  addonVariants: [
                    {
                      name: '600MB pro RCU',
                      sku: 'DRX_PC_15757A_DE',
                    },
                    {
                      description: '⭐️ Recommended',
                      name: '3GB per RCU',
                      sku: 'DRX_PC_15758A_DE',
                    },
                    {
                      name: '6 GB per RCU',
                      sku: 'DRX_PC_15759A_DE',
                    },
                  ],
                  description:
                    'Zugang zu einer dedizierten Cloud-Instanz für das Datenmanagement, um die Daten Ihrer Flotte effizient zu verwalten, einschließlich Geolokalisierung, Zustandsüberwachung und Analyse der CAN-Signale. Richten Sie Geofence- oder Schwellwert Benachrichtigungen ein und passen Sie Dashboards für sich oder weitere Flotten an, oder erweitern Sie das Angebot mit Ihren eigene Regeln, ERP-Daten oder Domain-Namen weiter. Wählen Sie aus drei Cloud-Speichervolumen, wobei die Grundgebühr für die ersten 20 verbundenen Geräte alle Funktionen abdeckt (1 GB custom processing pipeline, 2 UI- und 1 API-Read-Ticket). Zusätzliche Speichervolumina werden automatisch gemäß der Preisliste nach Ihrem tatsächlichen Verbrauch abgerechnet.',
                  name: 'Data Management',
                },
              ],
              agreements: [],
              bundleAmount: 1,
              description:
                'Zugang zu Ihrer dedizierten Cloud-Instanz für Gerätemanagement, RCU Updates und Mobilfunkverbindung. Steuergeräte-Updates nicht möglich. Der Mobilfunkdatenverbrauch ist über die CAN-Signalkonfiguration begrenzt. ',
              entitlements: ['BCCENTRAL'],
              features:
                'Die ersten 20 registrierten RCUs (Series 5) sind in der Grundgebühr enthalten.',
              licenseType: 'CONSUMPTION',
              name: 'Basic Device Management (RCU Series 5)',
              noticePeriod: 'P7D',
              price: {
                currencyCode: 'EUR',
                value: 123,
              },
              runtime: 'P1M',
              sku: 'DRX_PC_15756M_DE',
            },
          ],
          externalDocuments: [
            {
              name: 'Fact Sheet RCU Lite',
              url: 'https://www.boschrexroth.com/en/gb/media-details/7c160919-92d6-4251-9b8f-f193011d115a',
            },
          ],
          sellerCompany: {
            name: 'Bosch Rexroth AG',
          },
        },
      },
    ],
  },
}

export const products = {
  'Test Products 1': testProducts1,
  'Test Products 2': testProducts2,
}
