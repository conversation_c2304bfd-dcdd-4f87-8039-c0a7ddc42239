const emptyCart = {
  currentCart: {
    id: 'de58c781-c0ba-4af6-83b1-6078580bb86c',
    lineItems: [],
    totalPrice: {
      value: 0.0,
      currencyCode: 'EUR',
    },
  },
}

const testCart1 = {
  currentCart: {
    id: 'de58c781-c0ba-4af6-83b1-6078580bb86c',
    lineItems: [
      {
        lineItemId: '0c6617d0-ec62-4b26-b4f1-924926ed42a9',
        name: 'Hydraulic Hub',
        productId: '6d8ab4be-e24b-473b-a6a5-f1228380b86c',
        variantId: 0,
        sku: 'DDCIH_hydraulic_hub_trial',
        quantity: 1,
        totalPrice: {
          value: 0.0,
          currencyCode: 'EUR',
        },
        itemPrice: {
          value: 0.0,
          currencyCode: 'EUR',
        },
      },
      {
        lineItemId: '48f64e8e-510d-4fa4-9dbb-9623bffe485a',
        name: 'Hydraulic Hub',
        productId: '6d8ab4be-e24b-473b-a6a5-f1228380b86c',
        variantId: 0,
        sku: 'DDCIH_hydraulic_hub_yearly',
        quantity: 2,
        totalPrice: {
          value: 200.0,
          currencyCode: 'EUR',
        },
        itemPrice: {
          value: 100.0,
          currencyCode: 'EUR',
        },
      },
    ],
    totalPrice: {
      value: 200.0,
      currencyCode: 'EUR',
    },
    sellerCompany: {
      name: 'Bosch Rexroth',
    },
  },
}

const testCart2 = {
  currentCart: {
    id: 'd3076575-2bdf-4b77-b581-580af48bdd58',
    lineItems: [
      {
        lineItemId: '0c6617d0-ec62-4b26-b4f1-924926ed42a9',
        name: 'RCU Series 10 & 20',
        productId: '5abd3673-2110-4aa0-bbf9-c97cd1e24c89',
        variantId: 0,
        sku: 'DRX_PR_BODAS_COMPACT',
        quantity: 1,
        totalPrice: {
          currencyCode: 'EUR',
          value: 249,
        },
        itemPrice: {
          currencyCode: 'EUR',
          value: 249,
        },
        addons: [
          {
            addonLineItemId: '02abefed-4a2b-468d-8ad8-f0087c9ae633',
            addonVariant: {
              name: 'Cellular Connection',
              sku: 'DRX_PR_BODAS_CONNECTION_100MB',
            },
            itemPrice: {
              currencyCode: 'EUR',
              value: 0,
            },
            name: 'Cellular Connection',
            parentLineItemId: '0c6617d0-ec62-4b26-b4f1-924926ed42a9',
            quantity: 1,
            totalPrice: {
              currencyCode: 'EUR',
              value: 0,
            },
          },
        ],
      },
      {
        lineItemId: 'c621c31c-5d6c-4c19-b70e-25e3b21b2ed8',
        name: 'Hydraulic Hub',
        productId: '6d8ab4be-e24b-473b-a6a5-f1228380b86c',
        variantId: 0,
        sku: 'DDCIH_hydraulic_hub_trial',
        quantity: 1,
        totalPrice: {
          value: 0.0,
          currencyCode: 'EUR',
        },
        itemPrice: {
          value: 0.0,
          currencyCode: 'EUR',
        },
      },
      {
        lineItemId: '48f64e8e-510d-4fa4-9dbb-9623bffe485a',
        name: 'Hydraulic Hub',
        productId: '6d8ab4be-e24b-473b-a6a5-f1228380b86c',
        variantId: 0,
        sku: 'DDCIH_hydraulic_hub_yearly',
        quantity: 2,
        totalPrice: {
          value: 200.0,
          currencyCode: 'EUR',
        },
        itemPrice: {
          value: 100.0,
          currencyCode: 'EUR',
        },
      },
    ],
    totalPrice: {
      currencyCode: 'EUR',
      value: 449,
    },
    sellerCompany: {
      name: 'Bosch Rexroth AG',
    },
  },
}

export const carts = {
  'Empty Cart': emptyCart,
  'Test Cart 1': testCart1,
  'Cart with addons': testCart2,
}
