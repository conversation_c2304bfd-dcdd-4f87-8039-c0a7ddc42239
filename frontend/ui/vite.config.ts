import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vuetify from 'vite-plugin-vuetify'
import codegen from 'vite-plugin-graphql-codegen'

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  return {
    plugins: [
      codegen({ configFilePathOverride: 'codegen.config.ts' }),
      vue({
        template: {
          compilerOptions: {
            isCustomElement: (tag) => tag === 'dock-privacy-settings'
          }
        }
      }),
      vuetify({
        autoImport: true,
        styles: {
          configFile: '../node_modules/@bd/cd-system3/dist/styles/settings.scss'
        }
      })
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    build: {
      target: 'ES2022'
    },
    css: {
      preprocessorOptions: {
        // Until Vite uses modern API by default we have to enforce it. The default legacy API makes Sass emit deprecation warnings. The
        // Option must be applied for sass and scss to make both build and dev mode happy.
        // Details:
        // - https://sass-lang.com/documentation/breaking-changes/legacy-js-api/#bundlers
        // - https://vitejs.dev/config/shared-options.html#css-preprocessoroptions
        sass: { api: 'modern-compiler' },
        scss: { api: 'modern-compiler' }
      }
    },
    server: {
      proxy: {
        '/rest': env.VITE_LOCAL_PROXY_URL ?? ''
      }
    }
  }
})
