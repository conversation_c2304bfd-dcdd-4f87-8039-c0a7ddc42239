import type { CodegenConfig } from '@graphql-codegen/cli'

const config: CodegenConfig = {
  overwrite: true,
  schema: '../../api-graph/src/main/resources/graphql/schema.graphqls',
  documents: ['src/**/*.vue', 'src/**/*.ts', 'src/**/*.graphql'],
  ignoreNoDocuments: true,
  generates: {
    'src/graphql/codegen/': {
      preset: 'client',
      plugins: [],
      config: {
        useTypeImports: true,
        enumsAsTypes: true
      }
    }
  }
}

export default config
