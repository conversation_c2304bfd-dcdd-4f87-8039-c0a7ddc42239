/* eslint-env node */
import VueI18NExtract from 'vue-i18n-extract'
import { resolve } from 'path'
import { glob } from 'glob'
import fs from 'fs'
import crypto from 'crypto'
import { has } from 'lodash-es'

/**
 * Unused translation keys that are retained for at least one deployment on all stages, so that a
 * translation distribution can be released while a frontend deployment is still outstanding or in
 * progress.
 */
const unusedKeysWhitelist = [
  // moved in BOSS-1690, to be removed
  'subscriptions.notificationLearnMore',
  'subscriptions.notificationAssign',
  // unused since BOSS-1669, to be removed
  'error.operations.fetchLicenses'
]

const log = console
const currentDir = process.cwd()
const vueFiles = resolve(currentDir, './src/**/*.?(js|ts|vue)')
const languageFiles = resolve(currentDir, './src/locales/**/*.json')

log.log(`srcFiles: ${vueFiles}`)
log.log(`localeFiles: ${languageFiles}`)

// disable hardcoded reporting of vue-i18n-extract which is confusing
const oldConsoleLog = console.log
const oldConsoleInfo = console.info
const oldConsoleWarn = console.warn

console.log = function () {}
console.info = function () {}
console.warn = function () {}

const report = await VueI18NExtract.createI18NReport({
  vueFiles,
  languageFiles
})

console.log = oldConsoleLog
console.info = oldConsoleInfo
console.warn = oldConsoleWarn

// dynamic keys are found in the src files but not in the translation file and end with a dot e.g 'some.key.' + variable
// if they don't end with a dot they are actually missing in the translation file
const missingKeys = report.missingKeys.filter(key => {
  if (key.path.endsWith('.')) {
    return false;
  }
  // vue-i18n-extract doesn't implement all vue-i18n methods (e.g $tm)
  // see https://github.com/Spittal/vue-i18n-extract?tab=readme-ov-file#supported-vue-i18n-formats
  // vue-i18n-extract thinks those strings are missing, so we manually exclude them from missingKeys below
  if (glob.sync(languageFiles)
      .map(file => {
        const keys = JSON.parse(fs.readFileSync(file, 'utf8'))
        return has(keys, key.path)
      })
      .every(hasKey => hasKey)) {
    return false;
  }
  return key;
})
const dynamicKeys = report.missingKeys.filter(k => k.path.endsWith('.'))

// dynamic keys are unused in the translation file but start with one of the keys found above
// if they don't start with one of the missing keys, they are actually unused
const unusedKeys1 = report.unusedKeys.filter(k => !dynamicKeys.map(k => k.path).some(p => k.path.startsWith(p)))
// loading all javascript code and checking if the key appears somewhere
const files = glob.sync(vueFiles)
const vueFileContent = files.map(filename => fs.readFileSync(filename, 'utf8')).join()
const unusedKeys = unusedKeys1.filter(key => !vueFileContent.includes(key.path)).filter(key => !unusedKeysWhitelist.includes(key.path))

log.log(`found ${missingKeys.length} missing and ${unusedKeys.length} unused keys`)

const codeclimatereport = []

if (missingKeys.length) {
  log.error('\x1b[31m%s\x1b[0m', 'missing keys from translation file (add them to en.json):')
  log.error(missingKeys.map(k => `${k.language} ${k.file}:${k.line} ${k.path}`))
  missingKeys.forEach(k => {
    codeclimatereport.push({
      description: `translation key ${k.path} is used but not defined in the translation file`,
      check_name: 'missing-keys',
      fingerprint: crypto.createHash('sha256').update(k.language + k.file + k.line + k.path).digest('hex'),
      severity: 'major',
      location: {
        path: 'frontend/ui/' + k.file,
        lines: {
          begin: k.line
        }
      }
    })
  })
}

if (unusedKeys.length) {
  log.error('\x1b[31m%s\x1b[0m', 'unused keys found in translation file (remove them from en.json):')
  log.error(unusedKeys.map(k => `${k.file} ${k.path}`))
  unusedKeys.forEach(k => {
    codeclimatereport.push({
      description: `translation key ${k.path} is defined in the translation file but not used in any code file`,
      check_name: 'unused-keys',
      fingerprint: crypto.createHash('sha256').update(k.file + k.path).digest('hex'),
      severity: 'minor',
      location: {
        path: 'frontend/ui/' + k.file,
        lines: {
          begin: 0
        }
      }
    })
  })
}

fs.writeFileSync('i18n-report-codeclimate.json', JSON.stringify(codeclimatereport))

if (missingKeys.length || unusedKeys.length) {
  process.exitCode = 1
}
