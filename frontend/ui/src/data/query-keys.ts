/**
 * Query keys
 *
 * @see https://tkdodo.eu/blog/effective-react-query-keys#use-query-key-factories
 */

/** Query keys of the _me_ resources */
export const MeKeys = {
  all: ['me'] as const,
  company: () => [...MeKeys.all, 'company'] as const
}

export const ContractKeys = {
  all: ['contracts'] as const
}

export const CategoryKeys = {
  all: ['category'] as const
}

export const InvoiceKeys = {
  all: ['invoices'] as const
}

export const DefaultsKeys = {
  all: ['defaults'] as const,
  contracts: (contractIds: unknown) => [...DefaultsKeys.all, contractIds] as const
}
