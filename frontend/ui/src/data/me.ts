import { useQuery, useQueryClient } from '@tanstack/vue-query'
import fetchAuthenticated from '@/helpers/fetch-authenticated.ts'
import { MeKeys } from '@/data/query-keys.ts'
import appConfig from '@/helpers/app-config.ts'

async function fetchMeCompany(signal?: AbortSignal) {
  const response = await fetchAuthenticated(`${appConfig.apiUrl}/rest/me/company`, {
    signal,
    headers: { 'X-Tenant': 'rexroth' },
  })
  if (!response.ok) {
    throw new Error('Network request failed', { cause: response })
  }
  const json = await response.json()
  return json as MeCompany
}

export function useMeCompanyData() {
  const { data, error, isLoading } = useQuery({
    queryFn: ({ signal }) => fetchMeCompany(signal),
    queryKey: MeKeys.company(),
  })
  return { company: data, error, isLoading }
}

export async function fetchMeCompanyData() {
  const queryClient = useQueryClient()
  return await queryClient.fetchQuery({
    queryFn: ({ signal }) => fetchMeCompany(signal),
    queryKey: MeKeys.company(),
  })
}
