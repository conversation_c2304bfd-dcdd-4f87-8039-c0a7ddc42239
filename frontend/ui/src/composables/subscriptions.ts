import { useQuery, useMutation, useQueryClient } from '@tanstack/vue-query'
import { client, graphql } from '@/graphql'
import { ContractKeys } from '@/data/query-keys.ts'
import type { MaybeRefOrGetter } from 'vue'

const subscriptionsQuery = graphql(`
  query AllContracts {
    contracts {
      count
      results {
        companyId
        contractId
        contractPeriod
        contractState
        contractType
        projectedEndDate
        endDate
        noticePeriod
        orderNumber
        productId
        startDate
        licenses {
          licenseId
          licenseModel
          contractId
          ... on BcCentralLicense {
            name
            emails
          }
          ... on DcKeycloakLicense {
            email
            firstname
            lastname
          }
          ... on LitmosLicense {
            email
            firstname
            lastname
          }
        }
      }
    }
  }
`)

const cancelSubscriptionsMutation = graphql(`
  mutation CancelContracts($contractIds: [ID!]!) {
    cancelContracts(contractIds: $contractIds)
  }
`)

export function useSubscriptionData(enabled?: MaybeRefOrGetter<boolean>) {
  const { data, error, isLoading } = useQuery({
    queryFn: async () => client.request(subscriptionsQuery),
    queryKey: ContractKeys.all,
    select: (data) => data.contracts,
    refetchOnWindowFocus: true,
    meta: {
      errorKey: 'error.operations.fetchContracts'
    },
    enabled,
  })

  return { data, error, isLoading }
}

export function useCancelSubscription() {
  const queryClient = useQueryClient()

  const { mutateAsync } = useMutation({
    mutationFn: async (variables: { contractIds: string[] }) => client.request(cancelSubscriptionsMutation, variables)
  })

  async function cancel(contractIds: string[]) {
    await mutateAsync({ contractIds }, {
      onSuccess() {
        queryClient.invalidateQueries({ queryKey: ContractKeys.all })
      }
    })
  }

  return { cancel }
}
