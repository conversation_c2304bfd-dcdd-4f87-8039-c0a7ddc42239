import { computed, type MaybeRefOrGetter, toValue } from 'vue'
import { useDisplay } from 'vuetify'

/** Allowed size of a dialog as defined by design in Figma */
export type DialogSize = 'small' | 'medium' | 'fullscreen'

/**
 * Determines `fullscreen` and `width` for a dialog according to the rules defined by design in Figma.
 *
 * @param size intended size of the dialog
 */
export function useDialogDimensions(size: MaybeRefOrGetter<DialogSize>) {
  const display = useDisplay()

  const fullscreen = computed(() => {
    const dialogSize = toValue(size)
    return dialogSize === 'fullscreen' || (dialogSize === 'medium' && display.mdAndDown.value)
  })

  const width = computed(() => {
    const dialogSize = toValue(size)
    return fullscreen.value ? undefined : (dialogSize === 'medium' ? 640 : 400)
  })

  return { fullscreen, width }
}
