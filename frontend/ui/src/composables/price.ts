import { computed, type MaybeRefOrGetter, type Ref, toValue } from 'vue'
import { useI18n } from 'vue-i18n'

type Input = { price: Money, runtime?: string }
type InputOrUndefined = Input | undefined
type FormattedPrice = { price: string, runtime: string | undefined }
type FormattedPriceOrUndefined = { price: string, runtime: string | undefined } | undefined
type Output<T extends InputOrUndefined> =
    T extends undefined
    ? { formattedPrice: Ref<FormattedPriceOrUndefined> }
    : { formattedPrice: Ref<FormattedPrice> }

export function useFormattedPrice<T extends InputOrUndefined>(input: MaybeRefOrGetter<T>): Output<T> {
  const { t, n } = useI18n()

  const formattedPrice = computed(() => {
    const value = toValue(input)
    if (!value) return undefined
    const price = n(value.price.value, { key: 'currency', currency: value.price.currencyCode })
    const runtime = value.price.value && value.runtime ? t('price.runtime.' + value.runtime) : undefined
    return { price, runtime }
  })

  // Sadly this type assertion seems to be necessary as we have to promise that the implementation returns the proper `Output<T>` depending
  // on what input was provided. At least from the current implementation the compiler can't deduce by himself. Can this be improved?
  return { formattedPrice } as Output<T>
}
