import { toValue } from 'vue'
import { useQuery, useQueryClient, useMutation } from '@tanstack/vue-query'
import { client, graphql } from '@/graphql'
import { ContractKeys, DefaultsKeys } from '@/data/query-keys.ts'
import type { AssignLicense } from '@/graphql/codegen/graphql'
import type { MaybeRefOrGetter } from 'vue'

const assignLicensesMutation = graphql(`
  mutation AssignLicenses($input: [AssignLicense!]!) {
    assignLicenses(assignments: $input) {
      contractId
    }
  }`)

const unassignLicenseMutation = graphql(`
  mutation UnassignLicense($licenseId: ID!) {
    unassignLicense(licenseId: $licenseId)
  }`)

const defaultsQuery = graphql(`
  query Defaults($contractIds: [ID!]!) {
    defaults(contractIds: $contractIds) {
      __typename
      licenseModel
      contractId

      ... on BcCentralAssignDefaults {
        name
        emails
      }

      ... on DcKeycloakAssignDefaults {
        firstname
        lastname
        email
      }

      ... on LitmosAssignDefaults {
        firstname
        lastname
        email
      }
    }
  }
`)

export function useAssignLitmosLicenses() {
  const queryClient = useQueryClient()

  const { mutateAsync, isLoading } = useMutation({
    mutationFn: async (input: AssignLicense[]) => client.request(assignLicensesMutation, { input })
  })

  type Assignment = {
    contractId: string
    firstname: string
    lastname: string
    email: string
  }

  async function assign(assignments: Assignment[]) {
    await mutateAsync(assignments.map(assignment => ({ litmos: assignment })))
    await queryClient.invalidateQueries({ queryKey: ContractKeys.all })
  }

  return { assign, isLoading }
}

export function useAssignBcCentralLicenses() {
  const queryClient = useQueryClient()

  const { mutateAsync, isLoading } = useMutation({
    mutationFn: async (input: AssignLicense[]) => client.request(assignLicensesMutation, { input })
  })

  type Assignment = {
    contractId: string
    name: string
    emails: string[]
  }

  async function assign(assignments: Assignment[]) {
    await mutateAsync(assignments.map(assignment => ({ bcCentral: assignment })))
    await queryClient.invalidateQueries({ queryKey: ContractKeys.all })
  }

  return { assign, isLoading }
}

export function useUnassignLicense() {
  const queryClient = useQueryClient()

  const { mutateAsync, isLoading } = useMutation({
    mutationFn: async (licenseId: string) => client.request(unassignLicenseMutation, { licenseId })
  })

  async function unassign(licenseIds: string[]) {
    await Promise.all(licenseIds.map(licenseId => mutateAsync(licenseId)))
    await queryClient.invalidateQueries({ queryKey: ContractKeys.all })
  }

  return { unassign, isLoading }
}

export function useDefaultsData(contractIds: MaybeRefOrGetter<string[]>, enabled?: MaybeRefOrGetter<boolean>) {
  const { data, error } = useQuery({
    queryFn: async () => client.request(defaultsQuery, { contractIds: toValue(contractIds) }),
    queryKey: DefaultsKeys.contracts(contractIds),
    meta: {
      errorKey: 'error.operations.fetchLicenseDefaults'
    },
    enabled,
    refetchOnWindowFocus: false
  })

  return { data, error }
}
