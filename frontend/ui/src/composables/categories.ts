import { useQuery } from '@tanstack/vue-query'
import { client, graphql } from '@/graphql'
import { CategoryKeys } from '@/data/query-keys.ts'

const categoriesQuery = graphql(`
  query AllCategories {
    categories {
      count
      results {
        categoryId
        description
        name
        order
        parentCategories
        parentCategoryId
      }
    }
  }
`)

export function useCategoryData() {
  const { data, error, isLoading, refetch } = useQuery({
    queryFn: async () => client.request(categoriesQuery),
    queryKey: CategoryKeys.all,
    select: (data) => ({ ...data.categories, results: data.categories.results.map(c => ({ ...c, order: 0 })) }),
    refetchOnWindowFocus: true,
    meta: {
      errorKey: 'error.operations.fetchCategoryData'
    }
  })

  return { data, error, isLoading, refetch }
}
