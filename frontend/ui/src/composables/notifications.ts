import { type Maybe<PERSON><PERSON><PERSON>rGetter, onUnmounted, ref, toValue, watch } from 'vue'
import { type InfoNotification, useNotificationStore } from '@/stores/notification-store'

export interface Options {
  /**
   * Will automatically open the notification when `useInfoNotification` is used.
   *
   * @default true
   */
  immediate?: boolean
  /**
   * Will keep the notification open when the calling component is unmounted.
   *
   * @default false
   */
  persist?: boolean
}

export function useInfoNotification(input: MaybeRefOrGetter<InfoNotification>, options?: Options) {
  const {
    immediate = true,
    persist = false
  } = options || {}

  const store = useNotificationStore()
  const notification = ref<UserNotification>()

  function open() {
    if (!notification.value) {
      notification.value = store.infoNotification(toValue(input))
    }
  }

  function close() {
    store.closeNotification(notification.value)
    notification.value = undefined
  }

  // FIXME for now remove and add the notification to the store again if the input changes. This could result in notifications getting
  //  reordered when the current locale changes. It would be better if the notification store itself would accept notifications as
  //  reactive values.
  watch(input, () => {
    close()
    open()
  })

  if (immediate) {
    open()
  }

  if (!persist) {
    onUnmounted(() => {
      close()
    })
  }

  return { open, close }
}
