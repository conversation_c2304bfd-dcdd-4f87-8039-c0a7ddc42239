import { useQuery } from '@tanstack/vue-query'
import fetchAuthenticated from '@/helpers/fetch-authenticated.ts'
import appConfig from '@/helpers/app-config.ts'
import { client, graphql } from '@/graphql'
import { InvoiceKeys } from '@/data/query-keys.ts'

const invoicesQuery = graphql(`
  query AllInvoices {
    invoices {
      count
      results {
        invoiceDate
        invoiceNumber
        orderIds
        status
        totalAmount {
          currencyCode
          value
        }
      }
    }
  }
`)

export function useInvoiceData() {
  const { data, error, isLoading } = useQuery({
    queryFn: async () => client.request(invoicesQuery),
    queryKey: InvoiceKeys.all,
    select: (data) => data.invoices,
    refetchOnWindowFocus: true,
    meta: {
      errorKey: 'error.operations.fetchInvoices',
    },
  })

  return { data, error, isLoading }
}

export async function fetchInvoiceDownload(documentNumber: string) {
  const response = await fetchAuthenticated(
    new URL(`/rest/invoices/${documentNumber}`, appConfig.apiUrl),
    {
      headers: { 'X-Tenant': 'rexroth' },
    },
  )
  if (!response.ok) {
    throw new Error('Network request failed', { cause: response })
  }
  return await response.blob()
}
