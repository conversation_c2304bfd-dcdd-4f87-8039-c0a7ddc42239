import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

export function useRouteMeta() {
  const route = useRoute()

  const hideFooter = computed(() => <PERSON>olean(route.meta.hideFooter))
  const hideNeedHelp = computed(() => <PERSON><PERSON><PERSON>(route.meta.hideNeedHelp))
  const minimalHeader = computed(() => Boolean(route.meta.minimalHeader))

  return {
    hideFooter,
    hideNeedHelp,
    minimalHeader
  }
}

export function useInitialNavigation() {
  const router = useRouter()
  const isReady = ref(false)

  router.isReady().then(() => isReady.value = true)

  return { isReady }
}
