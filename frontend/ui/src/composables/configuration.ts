import { useQuery, useQueryClient } from '@tanstack/vue-query'
import fetchAuthenticated from '@/helpers/fetch-authenticated.ts'
import appConfig from '@/helpers/app-config.ts'

async function fetchConfiguration(signal?: AbortSignal) {
  const response = await fetchAuthenticated(`${appConfig.apiUrl}/rest/configuration`, { signal, headers: { 'X-Tenant': 'rexroth' } })
  if (!response.ok) {
    throw new Error('Network request failed', { cause: response })
  }
  return await response.json()
}

export function useConfigurationData() {
  const { data, error, isLoading } = useQuery({
    queryFn: ({ signal }) => fetchConfiguration(signal),
    queryKey: ['configuration'],
    select: (data) => data as Configuration,
    initialData: {
      tenant: '',
      countries: [] as Country[],
      defaultCountry: ''
    } satisfies Configuration,
  })

  return { configuration: data, error, isLoading }
}

export async function fetchConfigurationData() {
  const queryClient = useQueryClient()
  const data = await queryClient.fetchQuery({
    queryFn: ({ signal }) => fetchConfiguration(signal),
    queryKey: ['configuration'],
  })

  return data as Configuration
}
