import { computed, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'

const fragment = ref<string | null | undefined>(null)

export function useBreadcrumbs () {
  const route = useRoute()
  const { t } = useI18n()

  const item = (disabled: boolean, title?: string, href?: string) => ({ title, disabled, href })
  const root = (disabled: boolean) => item(disabled, t('navigation.breadcrumbs.root'), '/')

  const breadcrumbs = computed(() => {
    const routeTitle = () => route.meta.lazyBreadcrumb ? undefined : t('navigation.breadcrumbs.' + String(route.name))
    const title = () => fragment.value === null ? routeTitle() : fragment.value
    const fragments = route.path !== '/' ? [item(true, title(), route.fullPath)] : []

    return [root(fragments.length === 0), ...fragments]
  })

  return { breadcrumbs, fragment }
}
