import { useQuery } from '@tanstack/vue-query'
import { useCountryLanguageStore } from '@/stores/country-language-store'
import { client, graphql } from '@/graphql'
import { toValue } from 'vue'
import { storeToRefs } from 'pinia'
import type { ResultOf } from '@graphql-typed-document-node/core'

const checkoutQuery = graphql(`
  query CheckoutData($locale: Locale) {
    cart: currentCart(locale: $locale) {
      id
      lineItems {
        lineItemId
        name
        quantity
        productId
        variant {
          name
          runtime
          agreements {
            linkType
            name
            url
          }
          addons {
            id
          }
          priceList
        }
        itemPrice {
          currencyCode
          value
        }
        totalPrice {
          currencyCode
          value
        }
        addons {
          name
          addonVariant {
            name
          }
          totalPrice {
            currencyCode
            value
          }
        }
      }
      totalPrice {
        currencyCode
        value
      }
      sellerCompany {
        name
      }
      billingAddress {
        city
        postalCode
        streetName
        streetNumber
        state
        region
        country
        email
      }
    }
    paymentConfig {
      paymentMethods {
        paymentMethodId
        ... on BoschAchCreditInformation {
          accountHolder
          accountNumber
          routingNumber
          bic
          bankName
        }
        ... on BoschSepaCreditInformation {
          accountHolder
          bankName
          iban
          bic
        }
      }
    }
  }
`)

export type CheckoutCartLineItem = ResultOf<typeof checkoutQuery>['cart']['lineItems'][number]

export function useCheckoutData() {
  const countryLanguageStore = useCountryLanguageStore()

  const { activeLanguage } = storeToRefs(countryLanguageStore)

  const { data, error, isLoading } = useQuery({
    queryFn: async () => client.request(checkoutQuery, { locale: toValue(activeLanguage) }),
    queryKey: ['checkout', activeLanguage],
    refetchOnWindowFocus: false,
    meta: {
      errorKey: 'error.operations.fetchCheckoutData'
    }
  })

  return { data, error, isLoading }
}
