/* eslint-disable @typescript-eslint/no-explicit-any --
 * This file is anyway getting removed soon
**/
import BaseApi from '@/api/base-api'

class PaymentApi extends BaseApi {
  constructor () {
    super('/operations/ordermanagement', 'cannotFetchPayments')
  }

  public getPayment = (paymentId: string): Promise<Payment> => this.get('/GetPayment', { paymentId }).then((res: any) => res.data.ordermanagement_getPayment)
  public getPaymentConfig = (): Promise<PaymentConfig> => this.get('/GetPaymentConfig', { }).then((res: any) => res.data.ordermanagement_getPaymentConfig)
}

export default new PaymentApi()
