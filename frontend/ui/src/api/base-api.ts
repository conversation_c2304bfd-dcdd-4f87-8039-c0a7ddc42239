/// <reference lib="dom" />

import { useNotificationStore } from '@/stores/notification-store'
import i18n from '@/plugins/i18n'
import fetchAuthenticated from '@/helpers/fetch-authenticated'

export default class BaseApi {
  baseUrl = ''
  errorMessage = ''

  public constructor (baseUrl: string, errorMessage: string) {
    this.baseUrl = baseUrl
    this.errorMessage = errorMessage
  }

  async get<T> (url: string, params?: Record<string, string | undefined>): Promise<T> {
    try {
      const uri = this.baseUrl + url + (params === undefined ? '' : '?' + new URLSearchParams(JSON.parse(JSON.stringify(params))))
      const response = await fetchAuthenticated(uri, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json'
        }
      })
      if (!response.ok) {
        this.handleError(response)
      }
      const string = await response.text()
      const json = string === '' ? {} : JSON.parse(string)
      return (await json) as T
    } catch (e) {
      this.handleError(e)
      return Promise.reject(e)
    }
  }

  async post<T> (url: string, body: unknown, params?: Record<string, string | undefined>): Promise<T> {
    try {
      const uri = this.baseUrl + url + (params === undefined ? '' : '?' + new URLSearchParams(JSON.parse(JSON.stringify(params))))
      const response = await fetchAuthenticated(uri, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      })
      if (!response.ok) {
        this.handleError(response)
      }
      const string = await response.text()
      const json = string === '' ? {} : JSON.parse(string)
      if (json.errors) {
        this.handleError(json.errors)
      }
      return (await json) as T
    } catch (e) {
      this.handleError(e)
      return Promise.reject(e)
    }
  }

  async put<T> (url: string, body: unknown, params?: Record<string, string | undefined>): Promise<T> {
    try {
      const uri = this.baseUrl + url + (params === undefined ? '' : '?' + new URLSearchParams(JSON.parse(JSON.stringify(params))))
      const response = await fetchAuthenticated(uri, {
        method: 'PUT',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      })
      if (!response.ok) {
        this.handleError(response)
      }
      const string = await response.text()
      const json = string === '' ? {} : JSON.parse(string)
      return (await json) as T
    } catch (e) {
      this.handleError(e)
      return Promise.reject(e)
    }
  }

  async delete<T> (url: string, params?: Record<string, string | undefined>): Promise<T> {
    try {
      const uri = this.baseUrl + url + (params === undefined ? '' : '?' + new URLSearchParams(JSON.parse(JSON.stringify(params))))
      const response = await fetchAuthenticated(uri, {
        method: 'DELETE'
      })
      if (!response.ok) {
        this.handleError(response)
      }
      const string = await response.text()
      const json = string === '' ? {} : JSON.parse(string)
      return (await json) as T
    } catch (e) {
      this.handleError(e)
      return Promise.reject(e)
    }
  }

  private handleError (arg: unknown) {
    console.error(`${this.errorMessage} ${JSON.stringify(arg)}`)
    const { errorNotification } = useNotificationStore()

    if (arg instanceof Response) {
      if (arg?.status === 403) {
        errorNotification({ message: i18n.global.t('errorMessages.authError') })
      }
      if (arg?.status === 400) {
        errorNotification({ message: i18n.global.t('errorMessages.cartCommunicationError'), toast: true })
      }
      // errorMessages.cartCommunicationError
      // errorMessages.cannotFetchOrders
      // errorMessages.cannotFetchPayments
      // errorMessages.cannotFetchProducts
      // errorMessages.cartCommunicationError
      // errorMessages.cannotFetchOrders
      // errorMessages.cannotFetchPayments
      // errorMessages.cannotFetchProducts
    }

    errorNotification({ message: i18n.global.t('errorMessages.' + this.errorMessage) })

    throw new Error(arg?.toString())
  }
}
