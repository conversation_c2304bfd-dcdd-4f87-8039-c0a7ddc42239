import { computed, watch, ref, onMounted } from 'vue'
import { defineStore, storeToRefs } from 'pinia'
import { client, graphql } from '@/graphql'
import { useAuthStore } from '@/stores/auth-store'

const cartQuery = graphql(`
  query CurrentCart {
    currentCart {
      billingAddress {
        city
        country
        email
        postalCode
        region
        state
        streetName
        streetNumber
      }
      id
      lineItems {
        itemPrice {
          currencyCode
          value
        }
        lineItemId
        name
        productId
        quantity
        sku
        totalPrice {
          currencyCode
          value
        }
        variant {
          agreements {
            name
            url
          }
          licenseType
          name
          runtime
          sku
          priceList
        }
        addons {
          addonLineItemId
          addonVariant {
            name
            sku
          }
          itemPrice {
            currencyCode
            value
          }
          name
          parentLineItemId
          quantity
          totalPrice {
            currencyCode
            value
          }
        }
      }
      sellerCompany {
        name
      }
      totalPrice {
        currencyCode
        value
      }
    }
  }`)

const checkoutMutation = graphql(`
  mutation CheckoutCurrentCart($input: CartCheckout!) {
    authorization: checkoutCurrentCart(input: $input) {
      paymentId
      redirectUrl
    }
  }`)

const updateMutation = graphql(`
  mutation UpdateCurrentCart($input: CartUpdate!) {
    updateCurrentCart(input: $input) {
      id
    }
  }`)

const addMutation = graphql(`
  mutation AddToCurrentCart($input: CartAdd!) {
    addToCurrentCart(input: $input) {
      id
    }
  }`)

const useCartStoreInternal = defineStore('CartStore', () => {
  const authStore = useAuthStore()

  const loading = ref(false)
  const updating = ref(false)
  const cart = ref<Cart>()
  const cartCurrency = computed(() => cart.value ? cart.value.totalPrice.currencyCode : 'EUR')
  const cartQuantity = computed(() => cart.value ? cart.value.lineItems.reduce((acc, item) => acc + item.quantity, 0) : 0)

  onMounted(() => {
    fetchCart()
  })

  function fetchCart () {
    if (!authStore.loggedIn) return
    loading.value = true
    client.request(cartQuery).then((cartData) => {
      cart.value = cartData.currentCart
    }).finally(() => {
      loading.value = false
    })
  }

  async function updateCart (lineItemId: string, quantity: number) {
    try {
      updating.value = true
      // TODO refactor to use the mutation response as new cart state
      await client.request(updateMutation, { input: { lineItemId, quantity } })
    } catch (e) {
      console.warn(`Update cart failed: ${e}`)
    } finally {
      fetchCart()
      updating.value = false
    }
  }

  async function addProductToCart (sku: string, quantity: number, addons?: { sku: string }[]) {
    try {
      // TODO refactor to use the mutation response as new cart state
      await client.request(addMutation, { input: { sku, quantity, addons } })
    } catch (e) {
      console.warn(`Update cart failed: ${e}`)
    } finally {
      fetchCart()
    }
  }

  async function addToCart (products: CartQuantity[]) {
    // TODO refactor to batch multiple adds into a single request
    for (const product of products) {
      if (cartQuantity.value + product.quantity <= 100) {
        const addons = product.addons ?? []
        await addProductToCart(product.sku, product.quantity, addons.map(sku => ({ sku })))
      }
    }
  }

  async function checkoutCart(paymentMethodId: string, notes?: string[]): Promise<AuthorizationInformationDto> {
    const result = await client.request(checkoutMutation, { input: { paymentMethodId, notes } })
    return result.authorization
  }

  // refresh cart when user logs in
  watch(() => authStore.loggedIn, () => fetchCart())

  return { cart, loading, updating, checkoutCart, updateCart, fetchCart, cartCurrency, addToCart }
})

export const useCartStore = () => {
  const store = useCartStoreInternal()
  const refs = storeToRefs(store)
  return {
    ...store,
    ...refs
  }
}
