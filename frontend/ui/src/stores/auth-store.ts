import { defineStore } from 'pinia'
import { computed } from 'vue'
import keycloak from '@/helpers/keycloak'

export const useAuthStore = defineStore('auth-store', () => {
  const loggedIn = computed(() => keycloak.client.value.authenticated)
  const userInfo = computed(() => {
    const token = keycloak.client.value.tokenParsed
    return token
      ? {
        userId: token.preferred_username,
        firstname: token.given_name,
        lastname: token.family_name,
        name: token.name,
        email: token.email,
        companyName: token.company_name,
        companyId: token.company_id,
        communication_language: token.communication_language,
        loginIdp: token.login_idp,
        loginIdpAccountUrl: token.login_idp_account_url
      }
      : undefined
  })

  async function login(options?: { redirectUri?: string, loginHint?: string }): Promise<void> {
    await keycloak.client.value.login(options)
  }

  async function logout(): Promise<void> {
    await keycloak.client.value.logout({ redirectUri: window.location.origin + '/' })
  }

  async function updateToken() : Promise<string> {
    return keycloak.updateToken()
  }

  return {
    // refs
    keycloak,
    loggedIn,
    // computed
    userInfo,
    // functions
    login,
    logout,
    updateToken
  }
})
