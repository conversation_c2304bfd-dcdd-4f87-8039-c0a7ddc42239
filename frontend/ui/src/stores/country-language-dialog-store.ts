import { ref } from 'vue'
import { acceptHMRUpdate, defineStore } from 'pinia'

export const useCountryLanguageDialogStore = defineStore('country-language-dialog', () => {
  const isActive = ref(false)

  function open() {
    isActive.value = true
  }

  function close() {
    isActive.value = false
  }

  return { isActive, open, close }
})

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useCountryLanguageDialogStore, import.meta.hot))
}
