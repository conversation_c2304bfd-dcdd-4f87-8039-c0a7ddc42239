import { watch, ref, type Ref, onMounted } from 'vue'
import { defineStore, storeToRefs } from 'pinia'
import { useAuthStore } from '@/stores/auth-store'
import { useCountryLanguageStore } from '@/stores/country-language-store'
import { client, graphql } from '@/graphql'

const productsQuery = graphql(`
  query AllProducts($locale: Locale, $country: String) {
    # eslint-disable-next-line @graphql-eslint/no-deprecated -- we can remove the expand argument once we only search for ids
    productSearch(locale: $locale, country: $country, expand: true) {
      count
      results {
        id
        product {
          id
          name
          description
          productType
          images
          variants {
            sku
            price {
              value
              currencyCode
            }
            bundleAmount
            licenseType
            runtime
            name
            description
            features
            noticePeriod
            agreements {
              name
              url
              linkType
            }
            priceList
            addons {
              id
              name
              description
              addonVariants {
                sku
                price {
                  value
                  currencyCode
                }
                name
                description
              }
            }
            entitlements
          }
          externalDocuments {
            name
            url
          }
          sellerCompany {
            name
          }
          categories
        }
      }
    }
  }
`)

const useProductStoreInternal = defineStore('ProductStore', () => {
  const authStore = useAuthStore()
  const countryLanguageStore = useCountryLanguageStore()
  const products: Ref<{state: Product[]| null, loading: boolean}> = ref({ state: null, loading: false })

  onMounted(() => {
    fetchProducts()
  })

  function fetchProducts (language?: string, country?: string) {
    products.value.loading = true
    const lang = language ?? countryLanguageStore.activeLanguage
    const coun = country ?? countryLanguageStore.activeCountry
    client.request(productsQuery, { locale: lang, country: coun }).then((productsData) => {
      products.value.state = productsData.productSearch.results.map(result => result.product as Product).filter(product => !!product)
    }).finally(() => {
      products.value.loading = false
    })
  }

  function getProduct (productId: string) {
    return products.value.state?.find(p => p.id === productId)
  }

  function getProductBySku (sku: string) {
    return products.value.state?.find(product => product.variants.some(variant => variant.sku === sku))
  }

  function getVariantBySku (sku: string) {
    const foundProduct = getProductBySku(sku)
    return foundProduct?.variants.find(variant => variant.sku === sku)
  }

  // refresh products when user logs in, prices might change depending on company
  watch(() => authStore.loggedIn, () => fetchProducts())
  // refresh products when country and/or language changes
  watch([() => countryLanguageStore.activeCountry, () => countryLanguageStore.activeLanguage], () => fetchProducts())

  return { products, fetchProducts, getProduct, getProductBySku, getVariantBySku }
})

export const useProductStore = () => {
  const store = useProductStoreInternal()
  const refs = storeToRefs(store)
  return {
    ...store,
    ...refs
  }
}
