import { acceptHMRUpdate, defineStore } from 'pinia'
import { computed, readonly, ref } from 'vue'
import { refreshLocaleAsync } from '@/plugins/i18n'
import { useConfigurationData } from '@/composables/configuration'

export const FallbackCountry = 'DE'
export const FallbackLanguage = 'en'

export const useCountryLanguageStore = defineStore('country-language', () => {
  const { configuration } = useConfigurationData()

  const activeCountry = ref(FallbackCountry)
  const activeLanguage = ref(FallbackLanguage)

  const countries = computed(() => {
    const countryName = new Intl.DisplayNames(activeLanguage.value, { type: 'region' })

    return configuration.value.countries.map(country => ({
      name: countryName.of(country.code) ?? country.code,
      code: country.code,
      languages: country.languages.map(language => ({
        name: new Intl.DisplayNames(language, { type: 'language' }).of(language) ?? language,
        code: language,
      })),
      defaultLanguage: country.defaultLanguage
    }))
  })

  const findCountry = computed(() => {
    const countryMap = new Map(countries.value.map(country => [country.code, country]))
    return (country: string) => countryMap.get(country)
  })

  async function set(countryCode: string, languageCode?: string) {
    const country = findCountry.value(countryCode)
    if (!country) {
      console.warn(`country ${countryCode} not found in configuration`)
      return
    }

    if (!languageCode) {
      languageCode = country.defaultLanguage
    } else if (!country.languages.map(l => l.code).includes(languageCode)) {
      console.warn(`language ${languageCode} not found in configuration for country ${countryCode}`)
      languageCode = country.defaultLanguage
    }

    activeCountry.value = countryCode
    activeLanguage.value = languageCode
    await refreshLocaleAsync(languageCode)
  }

  return {
    activeCountry: readonly(activeCountry),
    activeLanguage: readonly(activeLanguage),
    countries,
    findCountry,
    set
  }
})

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useCountryLanguageStore, import.meta.hot))
}
