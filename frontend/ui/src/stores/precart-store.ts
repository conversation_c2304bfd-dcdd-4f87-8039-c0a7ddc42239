import { type Ref, computed, ref } from 'vue'
import { defineStore, storeToRefs } from 'pinia'
import { useAuthStore } from '@/stores/auth-store'
import { useProductStore } from '@/stores/product-store'
import { calculateTotalPrice } from '@/helpers/money'

export const usePrecartStoreInternal = defineStore('PrecartStore', () => {
  const authStore = useAuthStore()
  const productStore = useProductStore()
  const precart: Ref<CartQuantity[]> = ref([])
  const precartPrices = computed(() => precart.value.map(item => item.calculatedPrice ?? 0))
  const precartPricesTotal = computed(() => precartPrices.value?.reduce((a: number, b: number) => a + b, 0))
  const precartQuantities = computed(() => precart.value.map(item => item.quantity))
  const precartQuantitiesTotal = computed(() => precartQuantities.value?.reduce((a: number, b: number) => a + b, 0))
  const isPrecartEmpty = computed(() => precart.value.length === 0)

  const clearPrecart = () => {
    precart.value = []
  }
  const removeItem = (index: number) => precart.value.splice(index, 1)

  const precartSummary = computed(() => precart.value.map(item => {
    const product = productStore.getProductBySku(item.sku)!
    const variant = product.variants.find(variant => variant.sku === item.sku)!
    return { item, product, variant }
  }))

  // replace with vueuse / pick???
  function getPrecartData<T extends keyof CartQuantity>(sku: string, query: T): CartQuantity[T] | undefined {
    const match = precart.value.find((item: CartQuantity) => item.sku === sku)
    if (match) {
      return match[query]
    }
  }

  function setPrecartData<T extends keyof CartQuantity>(sku: string, query: T, value: CartQuantity[T]) {
    const match = precart.value.find((item: CartQuantity) => item.sku === sku)
    if (match) {
      match[query] = value
    }
  }

  async function addToPrecart (payload: CartQuantity) {
    if (!authStore.loggedIn) {
      await authStore.login()
      return
    }

    // remove items with different runtime and licensetype from precart unless it's a trial
    precart.value = precart.value.filter((item: CartQuantity) =>
      (item.runtime === payload.runtime && item.licenseType === payload.licenseType) ||
    item.licenseType === 'TRIAL' ||
    payload.licenseType === 'TRIAL')

    const itemPrice = calculateTotalPrice(
      payload.price,
      payload.addons
        ?.map(addon => productStore.getVariantBySku(addon)?.price)
        .filter(price => !!price))
    payload.calculatedPrice = itemPrice.value * payload.quantity

    // merge items with the same sku
    const index = precart.value.findIndex((precartItem: CartQuantity) => precartItem.sku === payload.sku)
    if (index === -1) {
      precart.value.push(payload)
    } else if (payload.quantity === 0) {
      removeItem(index)
    } else {
      precart.value[index].quantity = payload.quantity
      precart.value[index].calculatedPrice = payload.calculatedPrice
    }
  }

  return {
    precart,
    addToPrecart,
    isPrecartEmpty,
    precartPricesTotal,
    precartQuantitiesTotal,
    clearPrecart,
    precartSummary,
    getPrecartData,
    setPrecartData
  }
})

export const usePrecartStore = () => {
  const store = usePrecartStoreInternal()
  const refs = storeToRefs(store)
  return {
    ...store,
    ...refs
  }
}
