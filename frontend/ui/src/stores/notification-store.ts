import { defineStore, storeToRefs } from 'pinia'
import { ref, type Ref } from 'vue'
import {useRouter} from "vue-router";

export type ErrorNotification = {
  message: string
  action?: () => void
  actionMessage?: string
  toast?: boolean
}

export type InfoNotification = {
  message: string
  action?: () => void
  actionMessage?: string
  helpAction?: () => void
  helpActionMessage?: string
  toast?: boolean
}

export type SuccessNotification = {
  message: string
  action?: () => void
  actionMessage?: string
  toast?: boolean
}

const useNotificationStoreInteral = defineStore('NotificationStore', () => {
  const router = useRouter()
  const notifications: Ref<UserNotification[]> = ref([])

  router.beforeEach(() => {
    notifications.value = notifications.value.filter(n => n.severity !== 'error')
  })

  function errorNotification ({ message, action, actionMessage, toast }: ErrorNotification): UserNotification {
    return appendNotification({
      message,
      severity: 'error',
      action,
      actionMessage,
      validFor: 120 * 1000,
      toast
    })
  }

  function infoNotification ({ message, action, actionMessage, helpAction, helpActionMessage, toast }: InfoNotification): UserNotification {
    return appendNotification({
      message,
      severity: 'info',
      action,
      actionMessage,
      helpAction,
      helpActionMessage,
      toast
    })
  }

  function successNotification ({ message, action, actionMessage, toast }: SuccessNotification): UserNotification {
    return appendNotification({
      message,
      severity: 'success',
      action,
      actionMessage,
      validFor: 60 * 1000,
      toast
    })
  }

  function appendNotification (notification: UserNotification): UserNotification {
    const isDuplicated = notifications.value.map(n => n.message).some(msg => msg === notification.message)
    if (!isDuplicated) {
      notifications.value = [...notifications.value, notification]
      if (notification.toast) {
        setTimeout(closeNotification, 4000, notification)
      }
      if (notification.validFor) {
        setTimeout(closeNotification, notification.validFor, notification)
      }
    }
    return notification
  }

  function closeNotification (notification?: UserNotification): void {
    if (!notification) {
      return
    }
    notifications.value = notifications.value.filter(n => n.message !== notification.message)
  }

  return { errorNotification, infoNotification, successNotification, closeNotification, notifications }
})

export const useNotificationStore = () => {
  const store = useNotificationStoreInteral()
  const refs = storeToRefs(store)
  return {
    ...store,
    ...refs
  }
}
