import { createPinia } from 'pinia'
import { createApp } from 'vue'
import i18n from '@/plugins/i18n'
import router from '@/routes/router'
import vuetify from '@/plugins/vuetify'
import cookies from '@/plugins/cookies'
import vueQuery from '@/plugins/vue-query'
import '@bd/cd-system3/dist/cd-system.css'
// // bosch fonts added via assets folder. Font files should be imported automatically to your project
import '@bd/cd-system3/brands/bosch/fonts/fonts.css'

import {
  CDNavbar,
  CDFooter,
  CDNeedHelpButton,
  CDChip,
  CDFlyoutMenu,
  CDFlyoutMenuItem,
  CDNotification,
  CDToast,
  CDCookieBanner,
  CDInput,
  CDQuantity,
  CDTable,
  CDDataTable,
  CDTabs,
  CDLanguageSwitcher,
  CDUserFlyoutMenu,
  CDEnvironmentSwitcher,
  CDNavbarItem,
  CDBreadcrumbs,
  CDSubnavigation,
  CDSegmentedControl,
  CDTextArea
} from '@bd/cd-system3'

import App from '@/App.vue'

createApp(App)
  .use(createPinia())
  .use(vuetify)
  .use(i18n)
  .use(router)
  .use(vueQuery)
  .use(cookies)
  .component('CDInput', CDInput)
  .component('CDNavbar', CDNavbar)
  .component('CDFooter', CDFooter)
  .component('CDNeedHelpButton', CDNeedHelpButton)
  .component('CDChip', CDChip)
  .component('CDFlyoutMenu', CDFlyoutMenu)
  .component('CDFlyoutMenuItem', CDFlyoutMenuItem)
  .component('CDNotification', CDNotification)
  .component('CDToast', CDToast)
  .component('CDCookieBanner', CDCookieBanner)
  .component('CDQuantity', CDQuantity)
  .component('CDTable', CDTable)
  .component('CDTabs', CDTabs)
  .component('CDDataTable', CDDataTable)
  .component('CDLanguageSwitcher', CDLanguageSwitcher)
  .component('CDUserFlyoutMenu', CDUserFlyoutMenu)
  .component('CDEnvironmentSwitcher', CDEnvironmentSwitcher)
  .component('CDNavbarItem', CDNavbarItem)
  .component('CDBreadcrumbs', CDBreadcrumbs)
  .component('CDSubnavigation', CDSubnavigation)
  .component('CDSegmentedControl', CDSegmentedControl)
  .component('CDTextArea', CDTextArea)
  .mount('#app')
