import type { Plugin } from 'vue'

export default {
  install: () => {
    if (!import.meta.env.VITE_DOCK_DISABLE) {
      const dockScript = document.createElement('script')
      dockScript.setAttribute('src', 'https://dock.ui.bosch.tech/releases/4-latest/build/dock-privacy-settings.esm.js')
      dockScript.setAttribute('type', 'module')
      document.head.appendChild(dockScript)
    }
  }
} satisfies Plugin
