<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bosch Dock Privacy Settings Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
        }

        .description {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #0070f3;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .dock-container {
            margin-top: 20px;
            padding: 20px;
            border: 1px dashed #ddd;
            border-radius: 4px;
            min-height: 100px;
        }

        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }

        .info-box h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }

        .status {
            margin: 15px 0;
            padding: 10px;
            border-radius: 4px;
        }

        .status.success {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
        }

        .status.error {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>Bosch Dock Privacy Settings</h1>
    <p class="description">
        This page demonstrates loading the Bosch Dock Privacy Settings component.
        The script will be loaded dynamically and should render privacy-related content below.
    </p>

    <div class="info-box">
        <h3>About the Component</h3>
        <p>The Bosch Dock Privacy Settings is a modular component that provides privacy configuration options. It's loaded as an ES module from Bosch's UI technology stack.</p>
    </div>

    <div id="status" class="status">
        <div class="spinner"></div>
        Loading Dock Privacy Settings...
    </div>

    <div class="dock-container" id="dock-output">
        <div class="loading">
            <div class="spinner"></div>
            Waiting for dock script to load and render...
        </div>
    </div>
</div>

<script>
    // Function to load the dock script (adapted from the Vue plugin code)
    function loadDockScript() {
        console.log('Loading Dock Privacy Settings script...');

        const dockScript = document.createElement('script');
        dockScript.setAttribute('src', 'https://dock.ui.bosch.tech/releases/4-latest/build/dock-privacy-settings.esm.js');
        dockScript.setAttribute('type', 'module');
        dockScript.setAttribute('visible', 'true')

        // Add event listeners to track loading
        dockScript.onload = function() {
            console.log('Dock script loaded successfully');
            updateStatus('Script loaded successfully!', 'success');

            // Wait a bit for the component to initialize and render
            setTimeout(() => {
                checkForDockContent();
            }, 2000);
        };

        dockScript.onerror = function() {
            console.error('Failed to load dock script');
            updateStatus('Failed to load dock script. This might be due to CORS restrictions or network issues.', 'error');
            document.getElementById('dock-output').innerHTML =
                '<p style="color: #666; text-align: center;">Unable to load the Bosch Dock component. This may be due to network restrictions or the component requiring specific initialization.</p>';
        };

        document.head.appendChild(dockScript);
    }

    function updateStatus(message, type) {
        const statusEl = document.getElementById('status');
        statusEl.className = `status ${type}`;
        statusEl.innerHTML = message;
    }

    function checkForDockContent() {
        // First, let's see what the script actually added to the page
        const bodySnapshot = document.body.innerHTML;
        console.log('Current body content length:', bodySnapshot.length);

        // Look for elements that were likely added by the dock script
        // Exclude our own container to avoid false positives
        const ourContainer = document.querySelector('.container');
        const possibleSelectors = [
            'dock-privacy-settings',
            '[class*="bosch"]',
            '[class*="dock"]:not(#dock-output):not(.dock-container)',
            '[id*="dock"]:not(#dock-output)',
            '[class*="privacy"]:not(.description)',
            '[id*="privacy"]',
            'script[src*="dock"]'
        ];

        let foundElements = [];
        possibleSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                // Make sure it's not part of our demo page structure
                if (!ourContainer.contains(el) || el.tagName === 'SCRIPT') {
                    foundElements.push(el);
                }
            });
        });

        // Also check if any new custom elements were defined
        const customElements = [];
        try {
            // Check for common custom element patterns
            const potentialCustomElements = ['dock-privacy-settings', 'bosch-dock', 'privacy-settings'];
            potentialCustomElements.forEach(tagName => {
                const elements = document.getElementsByTagName(tagName);
                if (elements.length > 0) {
                    customElements.push(...elements);
                }
            });
        } catch (e) {
            console.log('Error checking custom elements:', e);
        }

        // Check for any new elements in the document that might be the component
        const allElements = document.querySelectorAll('*');
        const newElements = [];
        allElements.forEach(el => {
            // Look for elements with Bosch/dock related attributes or content
            const hasBoschContent = el.textContent && (
                el.textContent.toLowerCase().includes('bosch') ||
                el.textContent.toLowerCase().includes('privacy') &&
                el.textContent.toLowerCase().includes('settings') &&
                !ourContainer.contains(el)
            );

            const hasBoschAttributes = el.className && (
                el.className.includes('bosch') ||
                el.className.includes('dock')
            ) && !ourContainer.contains(el);

            if ((hasBoschContent || hasBoschAttributes) && !ourContainer.contains(el)) {
                newElements.push(el);
            }
        });

        const allFoundElements = [...foundElements, ...customElements, ...newElements];

        if (allFoundElements.length > 0) {
            console.log('Found potential dock elements:', allFoundElements);
            updateStatus('Searching for dock component content...', 'success');

            const dockContainer = document.getElementById('dock-output');
            dockContainer.innerHTML = '<h3>Detected Elements:</h3>';

            if (allFoundElements.length === 0) {
                dockContainer.innerHTML += '<p>No dock component elements found.</p>';
            } else {
                allFoundElements.forEach((el, index) => {
                    const wrapper = document.createElement('div');
                    wrapper.style.cssText = 'margin: 10px 0; padding: 15px; background: #f0f8ff; border: 1px solid #0070f3; border-radius: 4px;';

                    let elementInfo = `<strong>Element ${index + 1}:</strong> &lt;${el.tagName.toLowerCase()}&gt;`;

                    if (el.id) elementInfo += ` <em>id="${el.id}"</em>`;
                    if (el.className) elementInfo += ` <em>class="${el.className}"</em>`;

                    if (el.textContent && el.textContent.trim() && el.textContent.length < 500) {
                        elementInfo += `<br><strong>Content:</strong> ${el.textContent.trim()}`;
                    } else if (el.innerHTML && el.innerHTML.length < 1000) {
                        elementInfo += `<br><strong>HTML:</strong> <code>${el.innerHTML.substring(0, 200)}...</code>`;
                    }

                    wrapper.innerHTML = elementInfo;
                    dockContainer.appendChild(wrapper);
                });
            }
        } else {
            // No elements found - the component might not have rendered or requires initialization
            updateStatus('Script loaded but no visible component detected', 'error');
            document.getElementById('dock-output').innerHTML = `
                    <div style="text-align: center; color: #666;">
                        <h3>Component Analysis</h3>
                        <p>The Dock Privacy Settings script loaded successfully, but no visible component was rendered.</p>
                        <div style="text-align: left; background: #f9f9f9; padding: 15px; border-radius: 4px; margin: 15px 0;">
                            <strong>Possible reasons:</strong>
                            <ul>
                                <li>The component requires manual initialization (e.g., calling a function)</li>
                                <li>It needs specific DOM elements or containers to render into</li>
                                <li>Authentication or API keys may be required</li>
                                <li>The component might be designed for specific Bosch application contexts</li>
                                <li>It could be waiting for additional configuration parameters</li>
                            </ul>
                        </div>
                        <p><strong>Next steps:</strong> Check the browser console for any error messages or initialization instructions.</p>
                    </div>
                `;
        }

        // Log some diagnostic information
        console.log('Document title:', document.title);
        console.log('Scripts in head:', document.head.querySelectorAll('script').length);
        console.log('Total elements in document:', document.querySelectorAll('*').length);
    }

    // Start loading when the page is ready
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Page loaded, initializing dock script...');
        loadDockScript();
    });

    // Also monitor for any dynamic content changes
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length > 0) {
                console.log('DOM changes detected:', mutation.addedNodes);
            }
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
</script>
</body>
</html>