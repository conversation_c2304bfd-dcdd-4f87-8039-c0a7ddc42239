import '@/styles/main.scss'
import { createVuetify } from 'vuetify'
import { rexrothOptions } from '@bd/cd-system3'
import { createVueI18nAdapter } from 'vuetify/locale/adapters/vue-i18n'
import { type I18n, useI18n } from 'vue-i18n'
import i18n from '@/plugins/i18n'

export default createVuetify({
  ...rexrothOptions,
  locale: {
    // FIXME Forcing the type of the I18n instance is necessary for now:
    //  - https://github.com/vuetifyjs/vuetify/issues/19865
    //  - https://github.com/vuetifyjs/vuetify/issues/16848
    adapter: createVueI18nAdapter({ i18n: (i18n as unknown as I18n<never, never, never, string, false>), useI18n })
  },
  defaults: {
    ...rexrothOptions.defaults,
    VImg: {
      eager: import.meta.env.VITE_VUETIFY_VIMG_EAGER === 'true'
    }
  }
})
