import { createI18n } from 'vue-i18n'
import en from '@/locales/en.json'
import { cdTranslationsEN, CD_SYSTEM_TRANSLATION_PROJECT_ID } from '@bd/cd-system3'
import appConfig from '@/helpers/app-config'

const loadedLanguages = ['en']

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  fallbackLocale: 'en',
  silentTranslationWarn: true,
  allowComposition: true,
  messages: {
    en: { ...en, $vuetify: cdTranslationsEN.en }
  },
  // see also https://vue-i18n.intlify.dev/guide/essentials/number.html
  // and https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/NumberFormat/NumberFormat
  numberFormats: {
    de: {
      currency: {
        style: 'currency',
        currencyDisplay: 'symbol'
      }
    },
    en: {
      currency: {
        style: 'currency',
        currencyDisplay: 'symbol'
      }
    }
  },
  datetimeFormats: {
    de: {
      long: {
        year: 'numeric',
        month: 'short',
        day: '2-digit'
      }
    },
    en: {
      long: {
        year: 'numeric',
        month: 'short',
        day: '2-digit'
      }
    }
  }
})

function setI18nLanguage (lang: string) {
  if (i18n.mode === 'composition') {
    i18n.global.locale.value = lang as typeof i18n.global.locale.value
  } else {
    throw new Error('composition mode needs to be configured')
  }
  return lang
}

export async function refreshLocaleAsync (lang: string): Promise<void> {
  // If the same language
  if (i18n.global.locale.value === lang) return

  // If the language was already loaded
  if (loadedLanguages.includes(lang)) {
    setI18nLanguage(lang)
    return
  }

  // when one of the translations fails, the succeeding ones are still added
  const [translations1, translations2] = await Promise.all([
    fetch(`${appConfig.translationUrl}/project/${appConfig.crowdinDistributionId}/locale/${lang}`).then(data => data.json()),
    fetch(`${appConfig.translationUrl}/project/${CD_SYSTEM_TRANSLATION_PROJECT_ID}/locale/${lang}`).then(data => data.json())
  ])

  const translations = { ...translations1, $vuetify: { ...translations2 } }

  i18n.global.setLocaleMessage(lang, translations)
  loadedLanguages.push(lang)
  setI18nLanguage(lang)
}

export default i18n
