import type { App } from 'vue'
import { MutationCache, QueryCache, QueryClient, VueQueryPlugin } from '@tanstack/vue-query'
import { useNotificationStore } from '@/stores/notification-store'
import i18n from '@/plugins/i18n'

export default (app: App) => {
  app.use(VueQueryPlugin, {
    queryClient: new QueryClient({
      queryCache: new QueryCache({
        onError: (error, query) => onError(error, query.meta)
      }),
      mutationCache: new MutationCache({
        onError: (error, variables, context, mutation) => onError(error, mutation.meta)
      })
    })
  })
}

function onError(error: unknown, meta: { [index: string]: unknown } | undefined) {
  const { errorNotification } = useNotificationStore()

  const fromResponseStatus = () =>
    error instanceof Error && error.cause instanceof Response && [400, 401, 404].includes(error.cause.status)
      ? i18n.global.t('error.status.' + String(error.cause.status))
      : undefined

  const fromMeta = () =>
    meta?.errorKey
      ? i18n.global.t(String(meta.errorKey))
      : undefined

  const message = fromResponseStatus() ?? fromMeta() ?? i18n.global.t('error.generic')

  errorNotification({ message })
}
