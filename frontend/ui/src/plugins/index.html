<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Dock Script Test</title>
</head>
<body>
<h1>Testing Dock Privacy Settings Script</h1>
<div id="dock-output"></div>

<script type="module">
    // Mimic your TypeScript plugin behavior
    const dockScript = document.createElement('script');
    dockScript.setAttribute('src', 'https://dock.ui.bosch.tech/releases/4-latest/build/dock-privacy-settings.esm.js');
    dockScript.setAttribute('type', 'module');

    // Optionally set locale if needed:
    // dockScript.setAttribute('locale', 'en');

    document.head.appendChild(dockScript);

    // Optional: observe the DOM to detect dynamic changes (if the script renders asynchronously)
    const observer = new MutationObserver((mutations) => {
        console.log('DOM changed:', mutations);
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
</script>
</body>
</html>