// eslint-disable-next-line @typescript-eslint/no-unused-vars
import Vue from 'vue'

declare module '*.vue' {
    import Vue from 'vue'
    export default Vue
}

declare module 'vue-router' {
  interface RouteMeta {
    /** If `true` unauthenticated users will be redirected to login. */
    requiresLogin?: boolean
    /** If `true` breadcrumb title will be initialized with `undefined`. */
    lazyBreadcrumb?: boolean
    showFooter?: boolean
    showNeedHelp?: boolean
    /** If `true` the header will be rendered without buttons, breadcrumbs and subbrand identifier. */
    minimalHeader?: boolean
  }
}
