/* eslint no-use-before-define: 0 */

type Product = {
    id: string;
    name: string;
    description: string;
    productType: string;
    images: string[];
    variants: Variant[];
    externalDocuments: LocalizedLink[];
    sellerCompany?: {
        name: string;
    };
    categories: string[];
};

type Category = {
    categoryId: string;
    name: string;
    description?: string | null;
    products: Product[];
};

type Entitlement = 'LITMOS' | 'DCKEYCLOAK:FREEMIUM' | 'DCKEYCLOAK:PREMIUM' | 'BCCENTRAL'

type Variant = {
    sku: string;
    price: Money;
    bundleAmount: number;
    licenseType: 'TRIAL' | 'SUBSCRIPTION';
    runtime?: string;
    name: string;
    description: string;
    features: string;
    noticePeriod: string;
    agreements: LocalizedLink[];
    priceList?: string;
    addons: Addon[];
    entitlements: Entitlement[];
};

type Addon = {
    name: string;
    description: string;
    addonVariants: AddonVariant[];
}

type AddonVariant = {
    sku: string;
    price: Money;
    name: string;
    description: string;
}

type LocalizedLink = {
    name?: string | null;
    url?: string | null;
    linkType?: 'SOFTWARE_LICENSE' | 'PRIVACY_POLICY' | null;
}

type Cart = {
    id: string;
    lineItems: CartItem[];
    totalPrice: Money;
    sellerCompany?: {
        name: string;
    } | null;
};

type CartItem = {
    lineItemId: string;
    name: string;
    productId: string;
    quantity: number;
    itemPrice: Money;
    totalPrice: Money;
    sku?: string | null;
    addons?: AddonCartItem[] | null;
};

type AddonCartItem = {
  addonLineItemId: string;
  addonVariant?: {
    name?: string | null;
    sku?: string | null;
  } | null;
  itemPrice: Money;
  name?: string | null;
  parentLineItemId: string;
  quantity: number;
  totalPrice: Money;
};

type CartQuantity = {
    sku: string;
    quantity: number;
    runtime?: string;
    price: Money;
    licenseType: 'TRIAL' | 'SUBSCRIPTION';
    calculatedPrice: number;
    addons?: string[];
}

type CartWithProductVariant = CartItem & { product?: Product; variant?: Variant; addons?: AddonCartItem[] };
type CartWithProductVariantAddons = CartItemProductVariant & { addons?: AddonCartItem[] };

type Money = {
    value: number;
    currencyCode: string;
};

type Order = {
    orderId: string;
    lineItems: CartItem[];
    totalPrice: Money;
    createdAt: string;
};

type Payment = {
    paymentId: string;
    paymentStatus: 'INITIAL' | 'PENDING' | 'SUCCESS' | 'FAILURE';
    orderId: string;
};

type UIComponent = {
    name: string
    folder?: string
    props: Record<string, object | string>
}

type KeycloakUser = {
    userId: string;
    firstname: string;
    lastname: string;
    email: string;
    companyName: string;
    companyId: string;
    communication_language?: string;
}

type BoschSepaCreditInformation = {
    paymentMethodId: 'BOSCH_TRANSFER/SEPA_CREDIT';
    accountHolder: string;
    bankName: string;
    iban: string;
    bic: string;
}

type BoschAchCreditInformation = {
    paymentMethodId: 'BOSCH_TRANSFER/ACH_CREDIT';
    accountHolder: string;
    accountNumber: string;
    bankName: string;
    bic: string;
    routingNumber: string;
}

type GenericPaymentInformation = {
    paymentMethodId: string;
}

type PaymentMethod = GenericPaymentInformation | BoschSepaCreditInformation | BoschAchCreditInformation

type PaymentConfig = {
    paymentMethods: PaymentMethod[];
}

type NotificationSeverity = 'error' | 'info' | 'success' | 'warning'

type UserNotification = {
  message: string;
  prependMessage?: string;
  severity: NotificationSeverity;
  action?: () => void;
  actionMessage?: string;
  helpAction?: () => void;
  helpActionMessage?: string;
  validFor?: number;
  toast?: boolean;
}

type AuthorizationInformationDto = {
    paymentId: string;
    redirectUrl?: string | null;
}

type Subscription = {
    companyId: string;
    contractId: string;
    contractPeriod?: string | null;
    contractType: 'FIXED_TERM' | 'ONE_TIME' | 'SUBSCRIPTION' | 'CONSUMPTION';
    contractState: 'ACTIVE' | 'CANCELLATION_PENDING' | 'CANCELLED' | 'EXPIRED';
    endDate?: string | null;
    noticePeriod?: string | null;
    orderNumber: string;
    productId: string;
    startDate: string;
    projectedEndDate?: string | null;
    licenses?: License[] | null;
}

type LicenseModel = 'DCKEYCLOAK' | 'LITMOS' | 'BCCENTRAL'

type BcCentralLicense = {
    licenseId: string
    licenseModel: 'BCCENTRAL'
    contractId: string
    name?: string | null
    emails?: string[] | null
}

type DcKeycloakLicense = {
    licenseId: string
    licenseModel: 'DCKEYCLOAK'
    contractId: string
    firstname?: string | null
    lastname?: string | null
    email?: string | null
}

type LitmosLicense = {
    licenseId: string
    licenseModel: 'LITMOS'
    contractId: string
    firstname?: string | null
    lastname?: string | null
    email?: string | null
}

type License = BcCentralLicense | DcKeycloakLicense | LitmosLicense

type Country = {
  code: string
  languages: string[]
  defaultLanguage: string
}

type Configuration = {
  tenant: string
  countries: Country[]
  defaultCountry: string
}

type MeCompany = {
  name: string
  country: string
}

type BillingAddress = {
  city?: string | null;
  postalCode?: string | null;
  streetName?: string | null;
  streetNumber?: string | null;
  state?: string | null;
  region?: string | null;
  country?: string | null;
}
