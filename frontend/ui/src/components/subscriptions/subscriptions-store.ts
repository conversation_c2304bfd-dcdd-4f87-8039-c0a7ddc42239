import { defineStore, storeToRefs } from 'pinia'
import { ref } from 'vue'

const useSubscriptionDialogsInternal = defineStore('SubscriptionsDialogs', () => {

    // dialogs
    const helpDialog = ref(false)
    const cancelDialog = ref(false)
    const assignDialog = ref(false)
    const unassignDialog = ref(false)
    const bodasConnectSetupDialog = ref(false)

    const cancelPeriod = ref<string>()
    const endDate = ref<string>()
    const selectedSubscription = ref<Subscription>()
    const isUnassigning = ref(false)

    return {
        helpDialog,
        cancelDialog,
        assignDialog,
        unassignDialog,
        cancelPeriod,
        endDate,
        selectedSubscription,
        isUnassigning,
        bodasConnectSetupDialog
    }
})

export const useSubscriptionDialogs = () => {
    const store = useSubscriptionDialogsInternal()
    const refs = storeToRefs(store)
    return {
        ...store,
        ...refs
    }
}
