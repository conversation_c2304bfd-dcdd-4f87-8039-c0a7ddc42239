<template>
  <div>
    <v-data-table
      v-if="subscriptions && subscriptions?.length"
      :items="subscriptions"
      :items-per-page-options="[5, 10, 20]"
      :headers="headers"
      :disable-sort="true"
      item-value="contractId"
    >
      <template
        v-for="header in headers"
        :key="header.key"
        #[`header.${header.key}`]="{ column }"
      >
        <h5 class="text-h5">
          {{ column.title }}
        </h5>
      </template>

      <template #item="{ item: subscription }">
        <SubscriptionsTableRow
          :key="subscription.contractId"
          :subscription="subscription"
        />
      </template>
    </v-data-table>

    <template v-else-if="subscriptions && !subscriptions.length">
      <div class="d-flex flex-column align-center justify-center empty-content pa-12">
        <h2 class="text-h2 ma-4">
          {{ $t('subscriptions.emptyTitle') }}
        </h2>
        <div>{{ $t('subscriptions.emptyDescription') }}</div>
        <CDButton
          color="primary"
          class="mt-10"
          :to="{ path: ShopRoute.HOME }"
        >
          {{ $t('subscriptions.emptyButton') }}
        </CDButton>
      </div>
    </template>

    <v-list v-if="isLoading">
      <v-skeleton-loader
        type="list-item-three-line"
        width="400px"
      />
    </v-list>
  </div>
</template>

<script setup lang="ts">
import SubscriptionsTableRow from '@/components/subscriptions/SubscriptionsTableRow.vue'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { ShopRoute } from '@/constants'

const { subscriptions, isLoading } = defineProps<{
  subscriptions?: Subscription[]
  isLoading: boolean
}>()

const { t } = useI18n()

const headers = computed(() => ([
  { title: t('subscriptions.headsubscription'), key: 'contractId' },
  { title: t('subscriptions.headvalidity'), key: 'contractState', width: 320 },
  { title: t('subscriptions.headorder'), key: 'orderNumber', width: 240 },
  { title: '', width: 60 }
]))
</script>
