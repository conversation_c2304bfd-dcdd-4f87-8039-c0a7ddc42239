<template>
  <CDTable>
    <template #default>
      <thead>
        <tr>
          <th class="text-h5">{{ $t('invoices.table.headings.invoiceId')}}</th>
          <th class="text-h5">{{ $t('invoices.table.headings.orderId')}}</th>
          <th class="text-h5">{{ $t('invoices.table.headings.invoiceDate')}}</th>
          <th class="text-h5">{{ $t('invoices.table.headings.status')}}</th>
          <th class="text-h5">{{ $t('invoices.table.headings.totalAmount')}}</th>
          <th class="text-h5">{{ $t('invoices.table.headings.download')}}</th>
        </tr>
      </thead>
      <tbody>
        <tr
          v-for="invoice in invoices"
          :key="invoice.invoiceNumber"
          data-testId="invoice-row"
        >
          <td>{{ invoice.invoiceNumber }}</td>
          <td>
            <ul>
              <template
                v-for="id in invoice.orderIds"
                :key="id"
              >
                <li>{{ id }}</li>
              </template>
            </ul>
          </td>
          <td>{{ $d(new Date(invoice.invoiceDate), 'long') }}</td>
          <td>{{ $t('invoices.status.' + invoice.status) }}</td>
          <td>
            {{ $n(invoice.totalAmount.value, 'currency', { currency: invoice.totalAmount.currencyCode }) }}
          </td>
          <td class="text-right">
            <CDIcon
              icon="download"
              color="cd-text-hyperlink"
              @click="getInvoice(invoice.invoiceNumber)"
            />
          </td>
        </tr>
      </tbody>
    </template>
  </CDTable>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { saveAs } from 'file-saver'
import { fetchInvoiceDownload, useInvoiceData } from '@/composables/invoices'

const { data } = useInvoiceData()

const invoices = computed(() => data.value?.results)

async function getInvoice (documentNumber: string) {
  const blob = await fetchInvoiceDownload(documentNumber)
  saveAs(blob, `${documentNumber}.pdf`)
}
</script>

<style lang="scss" scoped>
ul {
  list-style-type: none;
}
</style>
