<template>
  <div v-if="props.subscription.contractState === 'ACTIVE'" class="d-inline-flex align-md-center align-sm-start flex-sm-column flex-md-row">
    <v-img
      width="16"
      height="16"
      :src="renewIcon"
      class="float-left mr-2"
    />
    <span class="text-wrap">{{ $t('subscriptions.validityActive') }}</span>
  </div>
  <div v-if="props.subscription.contractState === 'CANCELLATION_PENDING'" class="d-inline-flex align-md-center align-sm-start flex-sm-column flex-md-row">
    <v-img
      width="16"
      height="16"
      :src="endIcon"
      class="float-left mr-2"
    />
    <span class="text-wrap">{{ $t('subscriptions.cancellationPending') }}</span>
  </div>
  <div v-else-if="props.subscription.endDate && isExpired" class="d-inline-flex align-md-center align-sm-start flex-sm-column flex-md-row">
    <v-img
      width="16"
      height="16"
      :src="errorIcon"
      class="float-left mr-2"
    />
    <span class="text-wrap">{{ $t('subscriptions.validityExpired', {date: $d(new Date(props.subscription.endDate), 'long')}) }}</span>
  </div>
  <div v-else-if="props.subscription.endDate" class="d-inline-flex align-md-center align-sm-start flex-sm-column flex-md-row">
    <v-img
      width="16"
      height="16"
      :src="endIcon"
      class="float-left mr-2"
    />
    <span class="text-wrap">{{ $t('subscriptions.validityEnding', {date:$d(new Date(props.subscription.endDate), 'long')}) }}</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import renewIcon from '@/assets/Renew.svg'
import endIcon from '@/assets/End.svg'
import errorIcon from '@/assets/Error.svg'

const isExpired = computed(() => {
  return props.subscription.endDate && new Date(props.subscription.endDate) < new Date()
})

const props = defineProps<{
  subscription: Subscription,
}>()

</script>
