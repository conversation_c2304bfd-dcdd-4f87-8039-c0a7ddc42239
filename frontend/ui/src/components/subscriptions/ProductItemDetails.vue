<template>
  <div class="product-item-details">
    <template v-if="license">
      <ProductItemDetailsBcCentral
        v-if="license.licenseModel === 'BCCENTRAL'"
        :name="license.name"
        :emails="license.emails"
      />
      <ProductItemDetailsDcKeycloakLitmos
        v-else-if="['DCKEYCLOAK', 'LITMOS'].includes(license.licenseModel)"
        :firstname="license.firstname"
        :lastname="license.lastname"
        :email="license.email"
      />
    </template>

    <template v-else-if="licenseModel">
      <ProductItemDetailsBcCentral v-if="licenseModel === 'BCCENTRAL'" />
      <ProductItemDetailsDcKeycloakLitmos v-else-if="['DCKEYCLOAK', 'LITMOS'].includes(licenseModel)" />
    </template>
  </div>
</template>

<script setup lang="ts">
import ProductItemDetailsBcCentral from '@/components/subscriptions/ProductItemDetailsBcCentral.vue'
import ProductItemDetailsDcKeycloakLitmos from '@/components/subscriptions/ProductItemDetailsDcKeycloakLitmos.vue'

defineProps<{
  licenseModel?: LicenseModel
  license?: License
}>()
</script>

<style scoped lang="scss">
.product-item-details {
  display: grid;
  gap: 16px;
}
</style>
