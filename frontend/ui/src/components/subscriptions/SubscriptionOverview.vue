<template>
  <PageLayout :title="$t('subscriptions.title')">
    <template #content>
      <div class="tabs-wrapper mb-4">
        <v-tabs
          v-model="tabs"
          slider-color="cd-text-hyperlink"
        >
          <v-tab value="subscriptions">
            <p class="text-body-1 text-capitalize">
              {{ $t('subscriptions.title') }}
            </p>
          </v-tab>
          <v-tab value="invoices">
            <p class="text-body-1 text-capitalize">
              {{ $t('invoices.title') }}
            </p>
          </v-tab>
        </v-tabs>
      </div>

      <v-tabs-window v-model="tabs">
        <v-tabs-window-item
          value="subscriptions"
          :transition="false"
          :reverse-transition="false"
        >
          <SubscriptionsTable
            v-if="subscriptions"
            :subscriptions="subscriptions"
            :is-loading="isLoading"
          />
        </v-tabs-window-item>
        <v-tabs-window-item
          value="invoices"
          :transition="false"
          :reverse-transition="false"
        >
          <InvoicesTable />
        </v-tabs-window-item>
      </v-tabs-window>

      <HelpDialog />
      <CancelDialog />
      <UnassignDialog />
      <AssignSubscriptionDialog />
      <BodasConnectSetupDialog />
    </template>
  </PageLayout>
</template>

<script setup lang="ts">
import { computed, ref, watchEffect } from 'vue'
import PageLayout from '@/components/generic-partials/PageLayout.vue'
import SubscriptionsTable from './SubscriptionsTable.vue'
import InvoicesTable from './InvoicesTable.vue'
import HelpDialog from './Dialogs/HelpDialog.vue'
import CancelDialog from './Dialogs/CancelDialog.vue'
import UnassignDialog from './Dialogs/UnassignDialog.vue'
import AssignSubscriptionDialog from './Dialogs/AssignSubscriptionDialog.vue'
import BodasConnectSetupDialog from '@/components/subscriptions/Dialogs/BodasConnectSetupDialog.vue'
import { useSubscriptionDialogs } from './subscriptions-store'
import { useInfoNotification } from '@/composables/notifications'
import { useI18n } from 'vue-i18n'
import { useSubscriptionData } from '@/composables/subscriptions'
import { useProductStore } from '@/stores/product-store'

const { helpDialog, assignDialog, bodasConnectSetupDialog } = useSubscriptionDialogs()
const tabs = ref(null)
const { t } = useI18n()

const { data, isLoading } = useSubscriptionData()
const productStore = useProductStore()

const hydraulicHubNotification = useInfoNotification(
  () => ({
    message: t('subscriptions.notifications.hydraulicHub.text'),
    action: () => (assignDialog.value = true),
    actionMessage: t('subscriptions.notifications.hydraulicHub.action'),
    helpAction: () => {
      helpDialog.value = true
    },
    helpActionMessage: t('subscriptions.notifications.hydraulicHub.learnMore'),
  }),
  { immediate: false },
)

const bodasConnectNotification = useInfoNotification(
  () => ({
    message: t('subscriptions.notifications.bodasConnect.text'),
    action: () => (bodasConnectSetupDialog.value = true),
    actionMessage: t('subscriptions.notifications.bodasConnect.action'),
  }),
  { immediate: false },
)

const subscriptions = computed(() => data.value?.results)

watchEffect(() => {
  const variantBySku = (sku: string) =>
    productStore.getProductBySku(sku)?.variants.find((variant) => variant.sku === sku)
  const entitlements = (sku: string) => variantBySku(sku)?.entitlements ?? []
  const unassignedEntitlementExists = (...required: string[]) =>
    subscriptions.value
      ?.filter((subscription) => subscription.contractState !== 'EXPIRED')
      ?.find((subscription) =>
        entitlements(subscription.productId).some((entitlement) => required.includes(entitlement))
        && !subscription.licenses?.length)

  if (unassignedEntitlementExists('DCKEYCLOAK:FREEMIUM', 'DCKEYCLOAK:PREMIUM')) {
    hydraulicHubNotification.open()
  } else {
    hydraulicHubNotification.close()
  }

  if (unassignedEntitlementExists('BCCENTRAL')) {
    bodasConnectNotification.open()
  } else {
    bodasConnectNotification.close()
  }
})
</script>

<style lang="scss" scoped>
.tabs-wrapper {
  border-bottom: 1px solid rgb(var(--v-theme-cd-stroke-separator));
}

:deep(.v-tab__slider.text-cd-text-hyperlink) {
  height: 4px;
}
</style>
