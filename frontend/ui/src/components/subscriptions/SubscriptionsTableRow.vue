<template>
  <tr data-testId="subscription-row">
    <td>
      <ProductItem :product-id="subscription.productId!">
        <ProductItemDetails
          :license-model="subscription.licenses?.[0]?.licenseModel ?? getLicenseModel(subscription)"
          :license="subscription.licenses?.[0]"
        />
      </ProductItem>
    </td>
    <td>
      <ValidityDetails :subscription="subscription" />
    </td>
    <td>
      {{ subscription.orderNumber }}
    </td>
    <td class="text-right">
      <CDFlyoutMenu
        v-if="canBeCanceled || canBeUnassigned"
        menu-icon="more"
        location="bottom end"
      >
        <CDFlyoutMenuItem
          v-if="canBeCanceled"
          :title="$t('subscriptions.cancel')"
          icon="subscriptionscancel"
          @click="showCancelDialog(subscription)"
        />
        <CDFlyoutMenuItem
          v-if="canBeUnassigned"
          :title="$t('subscriptions.unassign')"
          icon="trash"
          @click="showUnassignDialog(subscription)"
        />
      </CDFlyoutMenu>
    </td>
  </tr>
</template>
<script setup lang="ts">
import ValidityDetails from '@/components/subscriptions/ValidityDetails.vue'
import ProductItem from '@/components/subscriptions/ProductItem.vue'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useSubscriptionDialogs } from './subscriptions-store'
import ProductItemDetails from '@/components/subscriptions/ProductItemDetails.vue'
import { useProductStore } from '@/stores/product-store.ts'

const props = defineProps<{
  subscription: Subscription
}>()

const productStore = useProductStore()
const { t, locale } = useI18n()
const { unassignDialog, cancelDialog, selectedSubscription, cancelPeriod, endDate } = useSubscriptionDialogs()

const hasBcCentral = computed(() => getLicenseModel(props.subscription) === 'BCCENTRAL')
const canBeCanceled = computed(() =>
  (props.subscription.contractState === 'ACTIVE' &&
    (props.subscription.contractType === 'SUBSCRIPTION' ||
     props.subscription.contractType === 'CONSUMPTION'))
)
const canBeUnassigned = computed(() => !hasBcCentral.value && !!props.subscription.licenses?.length)

function showCancelDialog (subscription: Subscription) {
  cancelPeriod.value = t('subscriptions.canceldialog.noticePeriod.' + subscription.noticePeriod)
  endDate.value = formatEffectiveDate(subscription.projectedEndDate)
  cancelDialog.value = true
  selectedSubscription.value = subscription
}

function showUnassignDialog (subscription: Subscription) {
  unassignDialog.value = true
  selectedSubscription.value = subscription
}

function getLicenseModel(subscription: Subscription) {
  const variantBySku = (sku: string) => productStore.getProductBySku(sku)?.variants.find(variant => variant.sku === sku)
  const entitlements = (sku: string) => variantBySku(sku)?.entitlements ?? []
  return entitlements(subscription.productId)[0]?.split(':')[0] as LicenseModel | undefined
}

function formatEffectiveDate(date?: string | null) {
  if (!date) return '-'
  return new Date(date).toLocaleDateString(locale.value)
}


</script>
