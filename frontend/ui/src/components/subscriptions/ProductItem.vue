<template>
  <div class="grid">
    <v-img
      v-if="item.product"
      :src="item.product.images[1]"
      aspect-ratio="1"
    />
    <v-skeleton-loader
      v-else
      type="avatar"
    />
    <div>
      <div>
        <h5 class="mb-1 d-flex align-center">
          <span class="mr-2 text-body-1">{{ item.product?.name }}</span>
          <CDChip
            v-if="item.variant"
            outlined
            size="small"
          >
            {{ $t('shop.productSelection.variantSelection.runtime.name.' + productRuntime) }}
          </CDChip>
        </h5>
      </div>
      <div class="mb-4">
        <p class="text-body-2">
          <span
            v-if="item.variant"
            v-text="item.variant.name"
          />
          <v-skeleton-loader
            v-else
            type="text@2"
            width="150px"
          />
        </p>
      </div>
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useProductStore } from '@/stores/product-store'

const { getProductBySku } = useProductStore()

const props = defineProps<{
  productId: string,
}>()

const item = computed(() => {
  const product = getProductBySku(props.productId)
  const variant = product?.variants.find((variant: Variant) => variant.sku === props.productId)
  return {
    product,
    variant
  }
})

const productRuntime = computed(() => {
  if (!item.value.variant) return ''
  return item.value.variant.licenseType + '|' + item.value.variant.runtime
})
</script>
<style scoped lang="scss">
.grid {
  display: grid;
  grid-template-columns: 32px auto;
  align-items: start;
  gap: 0 15px;
}
</style>
