<template>
  <div style="grid-column: 1;">
    <span class="text-h5 mr-2">{{ $t('subscriptions.assignment.bodasConnect.name') }}</span>{{ name ?? '-' }}
  </div>
  <div
    v-for="email in emails?.filter(e => !!e) ?? ['-']"
    :key="email"
    style="grid-column: 2;"
  >
    <span class="text-h5 mr-2">{{ $t('subscriptions.assignment.bodasConnect.email') }}</span>{{ email }}
  </div>
</template>

<script setup lang="ts">
defineProps<{
  name?: string | null
  emails?: string[] | null
}>()
</script>
