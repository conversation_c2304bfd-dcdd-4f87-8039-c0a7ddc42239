<template>
  <GenericDialog
    v-model="helpDialog"
    :dialog-title="$t('subscriptions.helpdialog.title')"
    :confirm-label="$t('subscriptions.helpdialog.confirm')"
    :cancel-label="$t('subscriptions.helpdialog.cancel')"
    scrollable
    @confirm="confirm"
  >
    <p>{{ $t('subscriptions.helpdialog.text') }}</p>
    <h4 class="text-h4 mt-6 mb-3">
      {{ $t('subscriptions.helpdialog.subtitle1') }}
    </h4>
    <p>{{ $t('subscriptions.helpdialog.text1') }}</p>
    <CDNotification
      color="info"
      class="mt-4"
    >
      <p>{{ $t('subscriptions.helpdialog.info') }}</p>
    </CDNotification>
    <h4 class="text-h4 mt-6 mb-3">
      {{ $t('subscriptions.helpdialog.subtitle2') }}
    </h4>
    <p>{{ $t('subscriptions.helpdialog.text2') }}</p>
    <h4 class="text-h4 mt-6 mb-3">
      {{ $t('subscriptions.helpdialog.subtitle3') }}
    </h4>
    <p>{{ $t('subscriptions.helpdialog.text3') }}</p>
  </GenericDialog>
</template>

<script setup lang="ts">
import { useSubscriptionDialogs } from '../subscriptions-store'
import GenericDialog from '@/components/generic-partials/GenericDialog.vue'

const { helpDialog, assignDialog } = useSubscriptionDialogs()

function confirm() {
  helpDialog.value = false
  assignDialog.value = true
}
</script>
