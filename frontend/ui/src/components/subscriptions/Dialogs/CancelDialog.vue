<template>
  <GenericDialog
    v-model="cancelDialog"
    :dialog-title="$t('subscriptions.canceldialog.title')"
    :cancel-label="$t('subscriptions.canceldialog.cancel')"
    :confirm-label="$t('subscriptions.canceldialog.confirm')"
    @confirm="confirm"
  >
    <p class="text-pre-line">
      {{ $t('subscriptions.canceldialog.text', { noticePeriod: cancelPeriod, endDate }) }}
    </p>
  </GenericDialog>
</template>

<script setup lang="ts">
import { useCancelSubscription } from '@/composables/subscriptions'
import { useSubscriptionDialogs } from '../subscriptions-store'
import GenericDialog from '@/components/generic-partials/GenericDialog.vue'
import { useNotificationStore } from '@/stores/notification-store'
import { useI18n } from 'vue-i18n'

const { cancel } = useCancelSubscription()
const { cancelDialog, cancelPeriod, endDate, selectedSubscription } = useSubscriptionDialogs()
const { successNotification } = useNotificationStore()
const { t } = useI18n()

async function confirm() {
  if (!selectedSubscription.value) return
  try {
    await cancel([selectedSubscription.value.contractId])
    successNotification({
      message: t('subscriptions.canceldialog.requested'),
      toast: true
    })
  } finally {
    cancelDialog.value = false
  }
}
</script>
