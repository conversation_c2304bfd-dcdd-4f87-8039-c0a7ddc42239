<template>
  <GenericDialog
    v-model="unassignDialog"
    :dialog-title="$t('subscriptions.unassignDialog.title')"
    :cancel-label="$t('subscriptions.unassignDialog.cancel')"
    :confirm-label="$t('subscriptions.unassignDialog.confirm')"
    :confirm-loading="isUnassigning"
    @confirm="confirm"
  >
    <p>{{ $t('subscriptions.unassignDialog.text') }}</p>
  </GenericDialog>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import GenericDialog from '@/components/generic-partials/GenericDialog.vue'
import { useNotificationStore } from '@/stores/notification-store'
import { useSubscriptionDialogs } from '../subscriptions-store'
import { useUnassignLicense } from '@/composables/licenses'

const { t } = useI18n()
const { selectedSubscription, isUnassigning, unassignDialog } = useSubscriptionDialogs()
const { unassign } = useUnassignLicense()
const { successNotification } = useNotificationStore()

async function confirm() {
  const licenseIds = selectedSubscription.value?.licenses?.map(license => license.licenseId)
  if (!licenseIds) return
  try {
    isUnassigning.value = true
    await unassign(licenseIds)
    successNotification({
      message: t('subscriptions.unassignDialog.success'),
      toast: true
    })
  } finally {
    isUnassigning.value = false
    unassignDialog.value = false
  }
}
</script>
