<template>
  <GenericDialog
    v-model="bodasConnectSetupDialog"
    :dialog-title="$t('subscriptions.assignment.bodasConnect.title')"
    size="fullscreen"
    scrollable
  >
    <v-form
      v-if="isReady"
      ref="form"
    >
      <v-stepper
        ref="stepper"
        v-model="step"
        :items="[$t('subscriptions.assignment.stepperAssign'), $t('subscriptions.assignment.stepperReview')]"
        hide-actions
        flat
      >
        <template #[`item.1`]>
          {{ $t('subscriptions.assignment.bodasConnect.description') }}
          <v-divider />
          <CDTable
            v-if="subscriptions?.length"
            :hover="false"
          >
            <template #default>
              <thead>
              <tr>
                <th class="text-h5">
                  {{ $t('subscriptions.headsubscription') }}
                </th>
                <th class="text-h5">
                  {{ $t('subscriptions.headvalidity') }}
                </th>
                <th class="text-h5">
                  {{ $t('subscriptions.headorder') }}
                </th>
                <th />
              </tr>
              </thead>
              <tbody>
              <template
                v-for="subscription in subscriptions"
                :key="subscription.contractId"
              >
                <tr>
                  <td class="border-0">
                    <ProductItem :product-id="subscription.productId!" />
                  </td>
                  <td class="border-0">
                    <ValidityDetails :subscription="subscription" />
                  </td>
                  <td class="border-0">
                    {{ subscription.orderNumber }}
                  </td>
                </tr>
                <tr class="input-row">
                  <td colspan="3">
                    <CDInput
                      v-model="assignments[subscription.contractId].name"
                      :label="$t('subscriptions.assignment.bodasConnect.name')"
                      class="mr-8"
                      density="compact"
                      width="520"
                      :rules="[
                          (v: string) => !!v || $t('subscriptions.assignment.bodasConnect.errorNameRequired'),
                          (v: string) => !/[_#$*@]/.test(v) || $t('subscriptions.assignment.bodasConnect.errorNameInvalid'),
                          (v: string) => /^[a-zA-Z0-9\- ]+$/.test(v) || $t('subscriptions.assignment.bodasConnect.errorNameAlphabetical')
                        ]"
                    >
                      <template #append-inner>
                        <v-tooltip
                          location="bottom"
                          max-width="400"
                        >
                          <template #activator="{ props }">
                            <CDIcon
                              v-bind="props"
                              icon="info"
                            />
                          </template>

                          {{ $t('subscriptions.assignment.bodasConnect.nameTooltip') }}
                        </v-tooltip>
                      </template>
                    </CDInput>
                    <CDInput
                      v-for="({}, i) in assignments[subscription.contractId].emails"
                      :key="i"
                      v-model="assignments[subscription.contractId].emails[i]"
                      :label="$t('subscriptions.assignment.bodasConnect.email')"
                      class="mr-8"
                      density="compact"
                      width="520"
                      type="email"
                      :rules="[
                          (v: string) => i > 0 || !!v || $t('subscriptions.assignment.bodasConnect.errorEmailRequired'),
                          (v: string) => /^\S+@\S+\.\S+$/.test(v) || !v || $t('subscriptions.assignment.bodasConnect.errorEmailInvalid')
                        ]"
                    />
                    <CDButton
                      variant="text"
                      prepend-icon="plussquare"
                      text="Add another admin email"
                      @click="addEmail(subscription.contractId)"
                    />
                  </td>
                </tr>
              </template>
              </tbody>
            </template>
          </CDTable>
          <div
            v-else
            class="text-h4 ma-10"
          >
            {{ $t('subscriptions.assignment.empty') }}
          </div>
        </template>

        <template #[`item.2`]>
          <p class="mb-8">
            {{ $t('subscriptions.assignment.bodasConnect.description') }}
          </p>
          <v-divider />
          <CDTable v-if="subscriptionsToBeAssigned?.length">
            <template #default>
              <thead>
              <tr>
                <th class="text-h5">
                  {{ $t('subscriptions.headsubscription') }}
                </th>
                <th class="text-h5">
                  {{ $t('subscriptions.headvalidity') }}
                </th>
                <th class="text-h5">
                  {{ $t('subscriptions.headorder') }}
                </th>
                <th />
              </tr>
              </thead>
              <tbody>
              <tr
                v-for="subscription in subscriptionsToBeAssigned"
                :key="subscription.contractId"
              >
                <td class="pb-6">
                  <ProductItem
                    :product-id="subscription.productId!"
                    class="mb-4"
                  />
                  <div class="product-item-details">
                    <div style="grid-column: 1;">
                      <span class="text-h4 mr-2">{{ $t('subscriptions.assignment.bodasConnect.name') }}</span>{{ assignments[subscription.contractId].name }}
                    </div>
                    <div
                      v-for="email in assignments[subscription.contractId].emails.filter(e => !!e)"
                      :key="email"
                      style="grid-column: 2;"
                    >
                      <span class="text-h4 mr-2">{{ $t('subscriptions.assignment.bodasConnect.email') }}</span>{{ email }}
                    </div>
                  </div>
                </td>
                <td><ValidityDetails :subscription="subscription" /></td>
                <td>{{ subscription.orderNumber }}</td>
              </tr>
              </tbody>
            </template>
          </CDTable>
          <div
            v-else
            class="text-h4 ma-10"
          >
            {{ $t('subscriptions.assignment.empty') }}
          </div>
        </template>
      </v-stepper>
    </v-form>

    <div
      v-else
      class="h-100 d-flex align-center justify-center"
    >
      <v-progress-circular
        color="primary"
        indeterminate
      />
    </div>

    <template #actions>
      <CDButton
        color="secondary"
        class="mr-2"
        @click="bodasConnectSetupDialog = false"
      >
        {{ $t('subscriptions.assignment.cancelbutton') }}
      </CDButton>

      <v-spacer />

      <CDButton
        v-if="step === lastStep"
        color="secondary"
        class="mr-2"
        @click="stepper.prev()"
      >
        {{ $t('subscriptions.assignment.backbutton') }}
      </CDButton>

      <CDButton
        color="primary"
        :loading="isLoading"
        :disabled="!isReady || (step === lastStep && !subscriptionsToBeAssigned?.length)"
        @click="nextStep"
      >
        {{ step === lastStep ? $t('subscriptions.assignment.confirmbutton') : $t('subscriptions.assignment.continuebutton') }}
      </CDButton>
    </template>
  </GenericDialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useSubscriptionData } from '@/composables/subscriptions'
import { useAssignBcCentralLicenses, useDefaultsData } from '@/composables/licenses'
import GenericDialog from '@/components/generic-partials/GenericDialog.vue'
import ValidityDetails from '@/components/subscriptions/ValidityDetails.vue'
import ProductItem from '@/components/subscriptions/ProductItem.vue'
import { useNotificationStore } from '@/stores/notification-store'
import { useI18n } from 'vue-i18n'
import { useSubscriptionDialogs } from '../subscriptions-store'
import { useProductStore } from '@/stores/product-store.ts'

const { bodasConnectSetupDialog } = useSubscriptionDialogs()
const { assign, isLoading } = useAssignBcCentralLicenses()
const productStore = useProductStore()
const { successNotification } = useNotificationStore()
const { t } = useI18n()
const stepper = ref()
const step = ref(1)
const lastStep = 2
const assignments = ref<{ [key: string]: { name: string, emails: string[] } }>({})
const form = ref()

const shouldFetchData = computed(() => bodasConnectSetupDialog.value)
const { data } = useSubscriptionData(() => shouldFetchData.value)

const subscriptions = computed(() => {
  const variantBySku = (sku: string) => productStore.getProductBySku(sku)?.variants.find(variant => variant.sku === sku)
  const entitlements = (sku: string) => variantBySku(sku)?.entitlements ?? []
  const hasEntitlement = (sku: string, ...required: string[]) => entitlements(sku).some(entitlement => required.includes(entitlement))

  return data.value?.results
    ?.filter(subscription => subscription.contractState !== 'EXPIRED' && hasEntitlement(subscription.productId, 'BCCENTRAL'))
    .filter(subscription => !subscription.licenses?.length)
})

// Separate watcher to handle assignments initialization
watch(subscriptions, (filtered) => {
  if (filtered) {
    for (const sub of filtered) {
      if (!assignments.value[sub.contractId]) {
        assignments.value[sub.contractId] = { name: '', emails: [''] }
      }
    }
  }
}, { immediate: true })

const { data: defaults } = useDefaultsData(
  () => subscriptions.value?.map(s => s.contractId) ?? [],
  () => bodasConnectSetupDialog.value && subscriptions.value !== undefined
)

const subscriptionsToBeAssigned = computed(() => {
  return subscriptions.value?.filter((subscription) => assignments.value[subscription.contractId].name !== '')
})

const isReady = computed(() => subscriptions.value && defaults.value)

watch(subscriptions, () => {
  subscriptions.value?.forEach(element => {
    if (!assignments.value[element.contractId]) {
      assignments.value[element.contractId] = { name: '', emails: [''] }
    }
  })
})

watch(defaults, applyDefaults)

watch(bodasConnectSetupDialog, () => {
  // reset stepper state
  if (!bodasConnectSetupDialog.value) {
    step.value = 1
    applyDefaults()
  }
})

function applyDefaults() {
  defaults.value?.defaults?.forEach(element => {
    if (element.contractId && assignments.value[element.contractId] && element.__typename === 'BcCentralAssignDefaults') {
      const name = element.name ?? ''
      const emails = element.emails ?? []
      assignments.value[element.contractId] = { name, emails: emails.length > 0 ? emails : [''] }
    }
  })
}

function addEmail(contractId: string) {
  assignments.value[contractId].emails.push('')
}

async function nextStep() {
  if (step.value === lastStep) {
    await assign(Object.entries(assignments.value)
      .map(([contractId, { name, emails }]) => ({ contractId, name, emails })))
    bodasConnectSetupDialog.value = false
    successNotification({
      message: t('subscriptions.assignment.bodasConnect.dialog.success'),
      toast: true
    })
  } else {
    const { valid } = await form.value.validate()
    if (valid) {
      stepper.value.next()
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.v-stepper-header) {
  box-shadow: none;
  max-width: 500px;
}
.input-row td {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
.product-item-details {
  display: grid;
  gap: 16px;
}
</style>
