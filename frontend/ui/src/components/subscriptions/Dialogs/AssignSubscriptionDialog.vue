<template>
  <GenericDialog
    v-model="assignDialog"
    :dialog-title="$t('subscriptions.assignment.title')"
    size="fullscreen"
    scrollable
  >
    <v-form ref="form">
      <v-stepper
        ref="stepper"
        v-model="step"
        :items="[$t('subscriptions.assignment.stepperAssign'), $t('subscriptions.assignment.stepperReview')]"
        hide-actions
        flat
      >
        <template #[`item.1`]>
          {{ $t('subscriptions.assignment.description') }}
          <v-divider />
          <CDTable
            v-if="subscriptions?.length"
            :hover="false"
          >
            <template #default>
              <thead>
                <tr>
                  <th class="text-h5">
                    {{ $t('subscriptions.headsubscription') }}
                  </th>
                  <th class="text-h5">
                    {{ $t('subscriptions.headvalidity') }}
                  </th>
                  <th class="text-h5">
                    {{ $t('subscriptions.headorder') }}
                  </th>
                  <th />
                </tr>
              </thead>
              <tbody>
                <template
                  v-for="subscription in subscriptions"
                  :key="subscription.contractId"
                >
                  <tr>
                    <td class="border-0">
                      <ProductItem :product-id="subscription.productId!" />
                    </td>
                    <td class="border-0">
                      <ValidityDetails :subscription="subscription" />
                    </td>
                    <td class="border-0">
                      {{ subscription.orderNumber }}
                    </td>
                  </tr>
                  <tr class="input-row">
                    <td colspan="3">
                      <CDInput
                        v-model="assignments[subscription.contractId].firstname"
                        :label="$t('subscriptions.assignment.firstname')"
                        class="mr-8 float-left"
                        density="compact"
                        width="300"
                      />
                      <CDInput
                        v-model="assignments[subscription.contractId].lastname"
                        :label="$t('subscriptions.assignment.lastname')"
                        class="mr-8 float-left"
                        density="compact"
                        width="300"
                      />
                      <CDInput
                        v-model="assignments[subscription.contractId].email"
                        :label="$t('subscriptions.assignment.email')"
                        class="mr-8 float-left"
                        density="compact"
                        width="400"
                        :rules="[
                          (v: string) => !((!!assignments[subscription.contractId].firstname || !!assignments[subscription.contractId].lastname) && !v) || (!assignments[subscription.contractId].firstname && !assignments[subscription.contractId].lastname && !v) || $t('subscriptions.assignment.errorEmailRequired'),
                          (v: string) => /.+@.+\..+/.test(v) || !v || $t('subscriptions.assignment.errorEmailInvalid'),
                          (v: string) => isEmailUnique() || $t('subscriptions.assignment.errorEmailDuplicate')
                        ]"
                      />
                    </td>
                  </tr>
                </template>
              </tbody>
            </template>
          </CDTable>
          <div
            v-else
            class="text-h4 ma-10"
          >
            {{ $t('subscriptions.assignment.empty') }}
          </div>
        </template>
        <template #[`item.2`]>
          <p class="mb-8">
            {{ $t('subscriptions.assignment.description') }}
          </p>
          <v-divider />
          <CDTable v-if="subscriptionsToBeAssigned?.length">
            <template #default>
              <thead>
                <tr>
                  <th class="text-h5">
                    {{ $t('subscriptions.headsubscription') }}
                  </th>
                  <th class="text-h5">
                    {{ $t('subscriptions.headvalidity') }}
                  </th>
                  <th class="text-h5">
                    {{ $t('subscriptions.headorder') }}
                  </th>
                  <th />
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="subscription in subscriptionsToBeAssigned"
                  :key="subscription.contractId"
                >
                  <td class="pb-6">
                    <ProductItem
                      :product-id="subscription.productId!"
                      class="mb-4"
                    />
                    <span class="text-h4">{{ $t('subscriptions.assignment.firstname') }}:</span> {{ assignments[subscription.contractId].firstname }}
                    <span class="text-h4 ml-8">{{ $t('subscriptions.assignment.lastname') }}:</span> {{ assignments[subscription.contractId].lastname }}
                    <span class="text-h4 ml-8">{{ $t('subscriptions.assignment.email') }}:</span> {{ assignments[subscription.contractId].email }}
                  </td>
                  <td><ValidityDetails :subscription="subscription" /></td>
                  <td>{{ subscription.orderNumber }}</td>
                </tr>
              </tbody>
            </template>
          </CDTable>
          <div
            v-else
            class="text-h4 ma-10"
          >
            {{ $t('subscriptions.assignment.empty') }}
          </div>
        </template>
      </v-stepper>
    </v-form>

    <template #actions>
      <CDButton
        color="secondary"
        class="mr-2"
        @click="assignDialog = false"
      >
        {{ $t('subscriptions.assignment.cancelbutton') }}
      </CDButton>

      <v-spacer />

      <CDButton
        v-if="step === lastStep"
        color="secondary"
        class="mr-2"
        @click="stepper.prev()"
      >
        {{ $t('subscriptions.assignment.backbutton') }}
      </CDButton>

      <CDButton
        color="primary"
        :loading="isLoading"
        :disabled="step === lastStep && !subscriptionsToBeAssigned?.length"
        @click="nextStep"
      >
        {{ step === lastStep ? $t('subscriptions.assignment.confirmbutton') : $t('subscriptions.assignment.continuebutton') }}
      </CDButton>
    </template>
  </GenericDialog>
</template>

<script setup lang="ts">
import { computed, watchEffect, ref, watch } from 'vue'
import { useSubscriptionData } from '@/composables/subscriptions'
import { useAssignLitmosLicenses } from '@/composables/licenses'
import GenericDialog from '@/components/generic-partials/GenericDialog.vue'
import ValidityDetails from '@/components/subscriptions/ValidityDetails.vue'
import ProductItem from '@/components/subscriptions/ProductItem.vue'
import { useNotificationStore } from '@/stores/notification-store'
import { useI18n } from 'vue-i18n'
import { useSubscriptionDialogs } from '../subscriptions-store'
import { useProductStore } from '@/stores/product-store.ts'

const { assignDialog } = useSubscriptionDialogs()
const { data } = useSubscriptionData(() => assignDialog.value)
const productStore = useProductStore()
const { assign, isLoading } = useAssignLitmosLicenses()
const { successNotification } = useNotificationStore()
const { t } = useI18n()
const stepper = ref()
const step = ref(1)
const lastStep = 2
const assignments = ref<{ [key: string]: { firstname: string, lastname: string, email: string } }>({})
const form = ref()

const subscriptions = computed(() => {
  const variantBySku = (sku: string) => productStore.getProductBySku(sku)?.variants.find(variant => variant.sku === sku)
  const entitlements = (sku: string) => variantBySku(sku)?.entitlements ?? []
  const hasEntitlement = (sku: string, ...required: string[]) => entitlements(sku).some(entitlement => required.includes(entitlement))

  return data.value?.results
    ?.filter((subscription) => subscription.contractState !== 'EXPIRED' && hasEntitlement(subscription.productId, 'DCKEYCLOAK:FREEMIUM', 'DCKEYCLOAK:PREMIUM'))
    .filter((subscription) => !subscription.licenses?.length)
})

const subscriptionsToBeAssigned = computed(() => {
  return subscriptions.value?.filter((subscription) => assignments.value[subscription.contractId].email !== '')
})

const isEmailUnique = () => {
  return Object.entries(assignments.value)
    .every(([contractId1, { email: email1 }]) => Object.entries(assignments.value)
      .every(([contractId2, { email: email2 }]) => contractId1 === contractId2 || email1 !== email2 || email1 === '' || email2 === ''))
}

watchEffect(() => {
  subscriptions.value?.forEach(element => {
    if (!assignments.value[element.contractId]) {
      assignments.value[element.contractId] = { firstname: '', lastname: '', email: '' }
    }
  })
})

watch(assignDialog, () => {
  // reset stepper state
  if (!assignDialog.value) {
    step.value = 1
    assignments.value = {}
  }
})

async function nextStep() {
  if (step.value === lastStep) {
    await assign(Object.entries(assignments.value)
      .filter(([, { email }]) => !!email)
      .map(([contractId, { firstname, lastname, email }]) => ({ contractId, firstname, lastname, email })))
    assignDialog.value = false
    successNotification({
      message: t('subscriptions.assignment.dialog.success'),
      toast: true
    })
  } else {
    const { valid } = await form.value.validate()
    if (valid) {
      stepper.value.next()
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.v-stepper-header) {
  box-shadow: none;
  max-width: 500px;
}
.input-row td {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
</style>
