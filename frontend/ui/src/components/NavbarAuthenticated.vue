<template>
  <template v-if="isDesktop">
    <CDNavbarItem
      v-for="(item, i) in navigationItems"
      :key="i"
      :title="item.text"
      :href="item.href"
      :icon="item.icon"
      :active="item.active"
      :data-testid="`navbar-item-${item.icon}`"
      @click="() => item.route && router.push(item.route)"
    />
  </template>

  <ShoppingCart />

  <CDEnvironmentSwitcher
    v-if="isMobile"
    :items="navigationItems"
  />

  <CDUserFlyoutMenu
    :avatar-initials="avatarInitials"
    :idp="idp"
    :header="{
      text: authStore.userInfo?.name,
      sub: authStore.userInfo?.companyName
    }"
  >
    <CDFlyoutMenuItem>
      <CDButton
        block
        color="grey-lighten3"
        data-testid="logout-button"
        @click="authStore.logout"
      >
        <CDIcon
          icon="logout"
          class="mr-2"
        />
        {{ $t('navigation.items.storeSignOut') }}
      </CDButton>
    </CDFlyoutMenuItem>
  </CDUserFlyoutMenu>
</template>

<script setup lang="ts">
import { useAuthStore } from '@/stores/auth-store'
import { computed } from 'vue'
import ShoppingCart from '@/components/cart/ShoppingCart.vue'
import { useRoute, useRouter } from 'vue-router'
import { CDEnvironmentSwitcher, useVuetifyDisplay } from '@bd/cd-system3'
import { useI18n } from 'vue-i18n'
import { ShopRoute } from '@/constants'
import appConfig from '@/helpers/app-config'

const { t } = useI18n()
const { isMobile, isDesktop } = useVuetifyDisplay()
const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const avatarInitials = computed(() => authStore.userInfo?.name?.substr(0, 1))

const idp = computed(() =>
  authStore.userInfo?.loginIdp && authStore.userInfo?.loginIdpAccountUrl
    ? {
        login_idp: authStore.userInfo.loginIdp,
        login_idp_account_url: authStore.userInfo.loginIdpAccountUrl
      }
    : undefined
)

const navigationItems = computed(() => ([
  {
    text: t('navigation.items.marketplaceComponent'),
    route: '/',
    icon: 'store',
    active: route.path !== ShopRoute.SUBSCRIPTIONS
  },
  {
    text: t('navigation.items.deviceManagement'),
    route: '/subscriptions',
    icon: 'apps',
    active: route.path === ShopRoute.SUBSCRIPTIONS
  },
  {
    text: t('navigation.items.accountComponent'),
    href: appConfig.userManagementUrl,
    icon: 'user',
    active: false
  }
]))
</script>
