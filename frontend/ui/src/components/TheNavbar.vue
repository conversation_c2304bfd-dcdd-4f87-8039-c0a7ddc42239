<template>
  <CDNavbar
    home-link="/"
    :hide-subbar="minimalHeader"
  >
    <template #subbar-title>
      <p class="subbar-title">
        {{ $t('navigation.tagline') }}
      </p>
    </template>

    <template #logo>
      <img
        :src="logo"
        :height="mobile ? 32 : 40"
        data-testid="navbar-logo"
        alt="Rexroth"
      >
    </template>

    <template
      v-if="!minimalHeader"
      #content-end
    >
      <template v-if="!isReady">
        <v-skeleton-loader type="text" width="150" />
        <v-skeleton-loader type="text" width="150" />
        <v-skeleton-loader type="button" width="90" />
      </template>
      <template v-else-if="isUserAuthenticated">
        <NavbarAuthenticated />
      </template>
      <template v-else>
        <NavbarPublic />
      </template>
    </template>

    <template
      v-if="!minimalHeader"
      #content-end-mobile
    >
      <template v-if="!isReady">
        <v-skeleton-loader type="button" width="60" />
        <v-skeleton-loader type="button" width="60" />
        <v-skeleton-loader type="button" width="90" />
      </template>
      <template v-else-if="isUserAuthenticated">
        <NavbarAuthenticated />
      </template>
      <template v-else>
        <NavbarPublic />
      </template>
    </template>

    <template
      v-if="!minimalHeader"
      #content-subbar
    >
      <CDBreadcrumbs
        v-if="!mobile"
        :items="isReady ? breadcrumbs : [{ title: undefined }]"
      />
    </template>
  </CDNavbar>
</template>

<script setup lang="ts">
import { useAuthStore } from '@/stores/auth-store'
import { useBreadcrumbs } from '@/composables/breadcrumbs'
import { useInitialNavigation, useRouteMeta } from '@/composables/routing'
import { useDisplay } from 'vuetify'
import { storeToRefs } from 'pinia'
import logo from '@/assets/rexroth.gif'
import NavbarAuthenticated from '@/components/NavbarAuthenticated.vue'
import NavbarPublic from '@/components/NavbarPublic.vue'

const authStore = useAuthStore()

const { mobile } = useDisplay()
const { loggedIn: isUserAuthenticated } = storeToRefs(authStore)
const { breadcrumbs } = useBreadcrumbs()
const { minimalHeader } = useRouteMeta()
const { isReady } = useInitialNavigation()
</script>

<style scoped lang="scss">
.subbar-title {
  font-size: 11px;
  color: rgb(var(--v-theme-cd-text-secondary));
}
</style>
