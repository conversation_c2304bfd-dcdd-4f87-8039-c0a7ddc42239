<template>
  <GenericDialog
    v-model="model"
    :dialog-title="t('needHelp.dialog.title')"
    scrollable
  >
    <v-form>
      <v-container class="pa-0">
        <v-row dense>
          <v-col>
            <CDInput
              v-model="name"
              :label="t('needHelp.dialog.name')"
              type="text"
              hide-details
            />
          </v-col>
          <v-col>
            <CDInput
              v-model="company"
              :label="t('needHelp.dialog.company')"
              type="text"
              hide-details
            />
          </v-col>
        </v-row>
        <v-row dense>
          <v-col>
            <CDInput
              v-model="customer"
              :label="t('needHelp.dialog.customer')"
              type="text"
              hide-details
            />
          </v-col>
        </v-row>
        <v-row dense>
          <v-col cols="12">
            <CDInput
              v-model="address"
              :label="t('needHelp.dialog.address')"
              type="text"
              hide-details
            />
          </v-col>
        </v-row>
        <v-row dense>
          <v-col>
            <CDTextArea
              v-model="request"
              :label="t('needHelp.dialog.request')"
            />
          </v-col>
        </v-row>
      </v-container>
    </v-form>

    <template #actions>
      <CDButton
        :text="t('needHelp.dialog.cancel')"
        color="secondary"
        class="mr-2"
        @click="cancel"
      />
      <CDButton
        :text="t('needHelp.dialog.create')"
        color="primary"
        @click="create"
      />
    </template>
  </GenericDialog>
</template>

<script setup lang="ts">
import GenericDialog from '@/components/generic-partials/GenericDialog.vue'
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import appConfig from '@/helpers/app-config'

const { t } = useI18n()

const model = defineModel<boolean>({ default: false })

const name = ref('')
const company = ref('')
const customer = ref('')
const address = ref('')
const request = ref('')

const subject = computed(() => encodeURI(t('needHelp.email.subject')))
const body = computed(() => encodeURI(t('needHelp.email.body', {
  name: name.value,
  company: company.value,
  customer: customer.value,
  address: address.value,
  request: request.value
})))
const href = computed(() => (`${appConfig.contactUrl}?subject=${subject.value}&body=${body.value}`))

function cancel() {
  model.value = false
}

function create() {
  model.value = false
  window.location.href = href.value
}
</script>
