<template>
  <v-container class="mb-8">
    <v-carousel show-arrows="hover">
      <v-carousel-item
        v-for="carousel in carousels"
        :key="carousel.url"
        :src="carousel.url"
        cover
      />
    </v-carousel>
  </v-container>
</template>

<script setup lang="ts">
defineProps<{
  carousels: {url: string }[]
}>()
</script>

<style scoped lang="scss">
  h1 {
    font-size: 68px;
  }
</style>
