<template>
  <v-container>
    <h2 class="mb-5">
      {{ header }}
    </h2>
    <v-expansion-panels
      flat
      class="mb-8"
    >
      <v-expansion-panel
        v-for="panel in panels"
        :key="panel.title"
        :title="panel.title"
        :text="panel.text"
      />
    </v-expansion-panels>
  </v-container>
</template>

<script setup lang="ts">
defineProps<{
  header?: string,
  panels: {title: string, text: string}[]
}>()
</script>
