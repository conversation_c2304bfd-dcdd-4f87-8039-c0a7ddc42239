<template>
  <v-container class="mb-8">
    <div>
      <h1 class="mb-5">
        {{ header }}
      </h1>
      <div v-if="buttons">
        <CDButton
          v-for="button in buttons"
          :key="button.text"
          :color="button.color"
          :class="button.class"
          :to="button.to"
        >
          {{ button.text }}
        </CDButton>
      </div>
    </div>
  </v-container>
</template>

<script setup lang="ts">
defineProps<{
  header: string,
  buttons?: {text: string, color: string, class?: string, to: string}[],
}>()
</script>

<style scoped lang="scss">
    h1 {
        font-size: 50px;
        font-weight: 700;
    }
    .text-large {
        font-size: 20px;
    }
</style>
