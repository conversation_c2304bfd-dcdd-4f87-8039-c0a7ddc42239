<template>
  <v-container class="mb-8">
    <div
      class="d-flex align-center justify-space-between mb-5"
    >
      <h1>
        {{ header }}
      </h1>
      <div v-if="buttons">
        <CDButton
          v-for="button in buttons"
          :key="button.text"
          :color="button.color"
          :class="button.class"
          :to="button.to"
        >
          {{ button.text }}
        </CDButton>
      </div>
    </div>
    <p class="text-large mb-5">
      {{ subHeader }}
    </p>
    <p> {{ landingText }} </p>
  </v-container>
</template>

<script setup lang="ts">
defineProps<{
  header: string,
  buttons?: {text: string, color: string, class?: string, to: string}[],
  subHeader?: string,
  landingText?: string
}>()
</script>

<style scoped lang="scss">
    h1 {
        font-size: 68px;
        font-weight: 700;
    }
    .text-large {
        font-size: 20px;
    }
</style>
