<template>
  <v-dialog
    v-bind="$attrs"
    v-model="isActive"
    :fullscreen="fullscreen"
    :max-width="width"
  >
    <v-card>
      <GenericDialogTitle
        :text="dialogTitle"
        :fullscreen="fullscreen"
        :closable="!$attrs.persistent"
        @click:close="isActive = false"
      />

      <v-card-text class="pb-2 px-4 px-md-8">
        <slot />
      </v-card-text>

      <v-divider v-if="fullscreen" />

      <v-card-actions
        class="pa-4 px-md-8"
        :class="{ 'py-lg-8': !fullscreen }"
      >
        <slot name="actions">
          <CDButton
            v-if="cancelLabel"
            color="secondary"
            class="mr-2"
            @click="isActive = false"
          >
            {{ cancelLabel }}
          </CDButton>
          <CDButton
            color="primary"
            :loading="confirmLoading"
            :disabled="confirmDisabled"
            @click="$emit('confirm')"
          >
            {{ confirmLabel }}
          </CDButton>
        </slot>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script lang="ts" setup>
import GenericDialogTitle from '@/components/generic-partials/GenericDialogTitle.vue'
import { type DialogSize, useDialogDimensions } from '@/composables/dialog'

const isActive = defineModel<boolean>({ default: false })

const {
  size = 'medium',
  dialogTitle = '',
  confirmLabel='navigation.dialogs.confirm',
  cancelLabel= 'navigation.dialogs.cancel',
  confirmLoading = false,
  confirmDisabled = false
} = defineProps<{
  size?: DialogSize
  dialogTitle?: string,
  confirmLabel?: string,
  cancelLabel?: string | null,
  confirmLoading?: boolean,
  confirmDisabled?: boolean
}>()

defineEmits<{
  confirm: []
}>()

const { fullscreen, width } = useDialogDimensions(() => size)
</script>
