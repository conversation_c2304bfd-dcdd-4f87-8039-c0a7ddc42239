<template>
  <v-toolbar
    v-if="fullscreen"
    height="72"
    color="white"
    class="px-4 px-md-8"
  >
    <v-toolbar-title :text="text" class="text-h3 ml-0" />
    <v-btn
      v-if="closable"
      icon="close"
      variant="text"
      density="compact"
      class="mr-0"
      @click="$emit('click:close')"
    />
  </v-toolbar>

  <v-card-title
    v-else
    class="d-flex align-top justify-space-between text-wrap pt-6 px-8"
  >
    <div class="d-flex align-center text-h3 text-lg-h2">
      {{ text }}
    </div>
    <v-btn
      v-if="closable"
      icon="close"
      variant="text"
      density="compact"
      @click="$emit('click:close')"
    />
  </v-card-title>

  <v-divider v-if="fullscreen" />
</template>

<script setup lang="ts">
defineProps<{
  text: string
  fullscreen: boolean
  closable: boolean
}>()

defineEmits<{
  'click:close': []
}>()
</script>
