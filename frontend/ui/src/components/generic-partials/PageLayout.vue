<template>
  <v-container fluid>
    <v-row
      v-if="title || titleActionsSlotPopulated"
      no-gutters
      class="mb-4"
    >
      <v-col
        cols="12"
        :lg="titleActionsSlotPopulated ? 8 : 12"
      >
        <h1 class="text-h1" data-testid="page-header">
          {{ title }}
        </h1>
      </v-col>
      <v-col
        v-if="titleActionsSlotPopulated"
        cols="12"
        lg="4"
        class="d-flex"
        :class="{ 'justify-end' : isDesktop, 'mt-4' : isMobile }"
      >
        <slot name="titleactions" />
      </v-col>
    </v-row>

    <v-row
      v-if="notifications.length > 0"
      no-gutters
      class="mb-10"
    >
      <v-col cols="12">
        <TheNotifications />
      </v-col>
    </v-row>
    <v-row
      no-gutters
      class="mb-10"
    >
      <v-col
        v-if="contentSlotPopulated"
        cols="12"
        class="page-content mb-16"
        :lg="sidebarSlotPopulated ? 7 : 12"
        :order="sidebarTopOnMobile && isMobile ? 1 : 0"
      >
        <slot name="content" />
      </v-col>
      <v-col
        v-if="sidebarSlotPopulated"
        cols="12"
        lg="4"
        offset-lg="1"
        class="page-sidebar"
        :class="{ 'mb-10' : isMobile }"
        :order="sidebarTopOnMobile && isMobile ? 0 : 1"
      >
        <slot name="sidebar" />
      </v-col>
    </v-row>
    <v-row
      v-if="bottomActionsSlotPopulated"
      no-gutters
    >
      <v-col
        cols="12"
        class="page-actions"
      >
        <slot name="bottomactions" />
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
import { useVuetifyDisplay } from '@bd/cd-system3'
import { computed } from 'vue'
import TheNotifications from '@/components/TheNotifications.vue'
import { useNotificationStore } from '@/stores/notification-store'

const { notifications } = useNotificationStore()

interface Props {
    title?: string,
    sidebarTopOnMobile?: boolean
}

const { isMobile, isDesktop } = useVuetifyDisplay()

withDefaults(defineProps<Props>(), {
  title: '',
  sidebarTopOnMobile: true
})

const slots = defineSlots<{
    titleactions(): unknown,
    content(): unknown,
    sidebar(): unknown,
    bottomactions(): unknown
}>()

const titleActionsSlotPopulated = computed(() => !!slots.titleactions)
const contentSlotPopulated = computed(() => !!slots.content)
const sidebarSlotPopulated = computed(() => !!slots.sidebar)
const bottomActionsSlotPopulated = computed(() => !!slots.bottomactions)

</script>
