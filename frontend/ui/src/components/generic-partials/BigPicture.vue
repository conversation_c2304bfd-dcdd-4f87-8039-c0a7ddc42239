<template>
  <v-parallax
    :src="url"
    :height="height"
    class="mb-5"
  >
    <div class="d-flex flex-column fill-height justify-center align-center">
      <div class="text-box">
        <h1>{{ text }}</h1>
      </div>
    </div>
  </v-parallax>
</template>

<script setup lang="ts">
defineProps<{
  url: string,
  height?: number,
  text?: string
}>()
</script>

<style scoped lang="scss">
  h1 {
    font-size: 68px;
  }
</style>
