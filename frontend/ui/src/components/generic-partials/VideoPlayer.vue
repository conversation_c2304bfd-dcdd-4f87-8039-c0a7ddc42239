<template>
  <v-container class="mb-6">
    <h2 class="mb-3">
      {{ header }}
    </h2>
    <p class="mb-4">
      {{ subHeader }}
    </p>
    <div class="video-player">
      <iframe
        height="480px"
        width="100%"
        :src="url"
        frameborder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        referrerpolicy="strict-origin-when-cross-origin"
        allowfullscreen
      />
    </div>
  </v-container>
</template>

<script setup lang="ts">
defineProps<{
  url: string,
  header?: string,
  subHeader?: string
}>()
</script>

<style scoped lang="scss">
  h1 {
    font-size: 68px;
  }
</style>
