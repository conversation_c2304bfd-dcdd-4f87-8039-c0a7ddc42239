<template>
  <CDFooter
    v-if="!hideFooter"
    :country-code-iso="locale"
    tenant="rexroth"
  >
    <a
      v-if="!authStore.loggedIn"
      class="d-flex mr-4 pt-2 pb-2"
      @click="countryLanguageDialogStore.open"
    >
      {{ countryName }} - {{ countryLanguageStore.activeLanguage.toUpperCase() }}
    </a>

    <a
      v-for="link in footerLinks"
      :key="link.text"
      v-bind="link"
      class="d-flex mr-4 pt-2 pb-2"
      @click="link.click"
    >
      {{ link.text }}
    </a>
  </CDFooter>
  <dock-privacy-settings
    :consent-domain="appConfig.dockDomain"
    :link-url-policy="footerUrls.privacyPolicy"
    :link-url-imprint="footerUrls.corporateInformation"
    :link-url-more-info="footerUrls.privacyPolicy"
    :locale="locale"
    links-target="_blank"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouteMeta } from '@/composables/routing'
import appConfig from '@/helpers/app-config'
import { useAuthStore } from '@/stores/auth-store.ts'
import { useCountryLanguageStore } from '@/stores/country-language-store.ts'
import { useCountryLanguageDialogStore } from '@/stores/country-language-dialog-store.ts'

const { t, locale } = useI18n()
const { hideFooter } = useRouteMeta()
const authStore = useAuthStore()
const countryLanguageStore = useCountryLanguageStore()
const countryLanguageDialogStore = useCountryLanguageDialogStore()

const countryName = computed(() =>
  new Intl.DisplayNames(countryLanguageStore.activeLanguage, { type: 'region' }).of(countryLanguageStore.activeCountry)
)

const footerUrls = computed(() => {
  const footerUrls = { ...appConfig.footerUrls }

  for (const key in footerUrls) {
    const k = key as keyof typeof footerUrls
    footerUrls[k] = footerUrls[k].replace('%country', countryLanguageStore.activeCountry.toLowerCase())
  }

  return footerUrls
})

const footerLinks = computed(() => ([
  { text: t('footer.corporateInformation'), href: footerUrls.value.corporateInformation, target: "_blank" },
  { text: t('footer.privacyPolicy'), href: footerUrls.value.privacyPolicy, target: "_blank" },
  { text: t('footer.legalNote'), href: footerUrls.value.legalNote, target: "_blank" },
  { text: t('footer.termsAndConditions'), href: footerUrls.value.termsAndConditions, target: "_blank" },
  { text: t('footer.reportInfringement'), href: footerUrls.value.reportInfringement, target: "_blank" },
  { text: t('footer.privacySettings'), click: showPrivacySettings },
]))

async function showPrivacySettings () {
  await customElements.whenDefined('dock-privacy-settings')
  const dockPrivacySettings = document.querySelector('dock-privacy-settings')
  dockPrivacySettings?.setAttribute('visible', 'true')
}
</script>

<style lang="scss" scoped>
footer {
  bottom: 0 !important;
}
</style>
