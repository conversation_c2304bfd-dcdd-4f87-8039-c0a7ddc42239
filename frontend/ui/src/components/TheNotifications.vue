<template>
  <template
    v-for="notification in notifications"
    :key="notification.prependMessage + notification.message"
  >
    <CDNotification
      v-if="!notification.toast"
      :color="notification.severity"
      :closable="!notification.action"
      class="mb-4"
      @click:close="closeNotification(notification)"
    >
      <b v-if="notification.prependMessage">
        {{ notification.prependMessage }} |
      </b>
      {{ notification.message }}
      <span
        v-if="notification.helpAction && notification.helpActionMessage"
      >
        <a
          class="helpLink"
          @click="notification.helpAction"
        >
          {{ notification.helpActionMessage }}
        </a>
      </span>
      <div
        v-if="notification.action && notification.actionMessage"
        class="mt-2"
      >
        <CDButton
          variant="text"
          class="font-weight-bold"
          color="black pa-0"
          @click="notification.action"
        >
          {{ notification.actionMessage }}
        </CDButton>
      </div>
    </CDNotification>
    <CDToast
      v-else
      v-model="notification.toast"
      :color="notification.severity"
      :location="isDesktop ? toast.props.location.desktop : toast.props.location.mobile"
    >
      {{ notification.message }}
    </CDToast>
  </template>
</template>

<script setup lang="ts">
import { useNotificationStore } from '@/stores/notification-store'
import { useVuetifyDisplay } from '@bd/cd-system3'
import { reactive } from 'vue'

const { isDesktop } = useVuetifyDisplay()
const { closeNotification, notifications } = useNotificationStore()

const toast = reactive({
  props: {
    location: {
      desktop: 'top right',
      mobile: 'bottom left'
    }
  }
})

</script>

<style lang="scss" scoped>
  .helpButton {
      height: 10px;
  }
  .helpLink {
    cursor: pointer;
    // TODO should be styled in cd-system
    color: #00738f !important;
  }
</style>
