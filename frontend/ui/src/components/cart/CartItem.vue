<template>
  <CartItemBase
    :cart-item="cartItem"
    :no-quantity="hasQuantity"
    :can-edit-quantity="canEdit && !hasQuantity"
    :can-delete="canEdit"
    @quantity-change="(quantity, lineItemId) => $emit('quantityChange', quantity, lineItemId)"
  >
    <template
      v-if="hasAddons"
      #append
    >
      <p
        v-for="(addon, i) in cartItem.addons"
        :key="addon.addonLineItemId"
        class="test-body-1"
        :class="{ 'mt-2': i > 0 }"
      >
        {{ addon.name }}: {{ addon.addonVariant.name }}
      </p>
    </template>
  </CartItemBase>
</template>

<script setup lang="ts">
import CartItemBase from '@/components/cart/CartItemBase.vue'
import { computed } from 'vue'

const { cartItem, canEdit = true } = defineProps<{
  cartItem: CartWithProductVariantAddons
  canEdit?: boolean
}>()

const hasQuantity = computed(() => !!cartItem.variant?.addons && cartItem.variant?.addons.length > 0)
const hasAddons = computed(() => !!cartItem.addons && cartItem.addons.length > 0)

defineEmits<{
  quantityChange: [quantity: number, lineItemId: string]
}>()
</script>

