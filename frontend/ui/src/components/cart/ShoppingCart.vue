<template>
  <v-badge
    v-if="cart && !loading"
    :content="cartCounter"
    color="primary"
    style="cursor: pointer;"
    class="pa-2 mx-2"
    @click="router.push('/cart')"
  >
    <CDIcon
      icon="cart"
      @click="router.push('/cart')"
    />
  </v-badge>
  <CDButton
    v-else-if="loading"
    color="grey"
    icon="cart"
    :loading="true"
    density="compact"
  />
</template>

<script setup lang="ts">
import { useCartStore } from '@/stores/cart-store'
import { useRouter } from 'vue-router'
import { computed } from 'vue'

const router = useRouter()
const { cart, loading } = useCartStore()

const cartCounter = computed(() => (cart.value?.lineItems ?? []).reduce((a, item) => a + item.quantity, 0))
</script>
