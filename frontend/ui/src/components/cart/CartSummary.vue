<template>
  <v-card
    variant="outlined"
    class="rounded-0 align-self-start"
    border="cd-stroke-separator thin opacity-100"
  >
    <v-card-item class="pa-6">
      <v-row>
        <v-col cols="6">
          <h3 class="text-h3" data-testid="cart-total-price">
            {{ $t('shop.checkout.miniSummary.totalPrice') }}
          </h3>
        </v-col>
        <v-col
          cols="6"
          class="text-right"
        >
          <div
            v-if="totalPrice"
            class="text-h3 mb-1"
          >
            {{ $n(totalPrice.value, 'currency', { currency: totalPrice.currencyCode }) }}
          </div>
        </v-col>
      </v-row>
      <div
        v-if="sellerCompanyName"
        class="text-body-2"
      >
        {{ $t('shop.checkout.miniSummary.soldBy', { companyName: sellerCompanyName }) }}
      </div>
    </v-card-item>
    <v-card-actions class="pt-4 px-6 pb-6">
      <v-spacer />
      <CDButton
        color="primary"
        data-testid="cart-checkout"
        @click="router.push(ShopRoute.CHECKOUT)"
      >
        {{ $t('shop.cart.checkout') }}
      </CDButton>
    </v-card-actions>
  </v-card>
</template>

<script setup lang="ts">
import { ShopRoute } from '@/constants'
import { useRouter } from 'vue-router'

defineProps<{
  totalPrice?: Money
  sellerCompanyName?: string
}>()

const router = useRouter()
</script>
