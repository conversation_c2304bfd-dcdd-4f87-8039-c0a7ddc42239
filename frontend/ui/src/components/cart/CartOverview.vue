<template>
  <PageLayout :title="$t('shop.cart.cart')">
    <template #content>
      <template v-if="cartPopulated">
        <CartItem
          v-for="item in cartWithProductVariant"
          :key="item.lineItemId"
          :cart-item="item"
          class="mb-10"
          @quantity-change="onQuantityChange"
        />
      </template>
      <template v-else-if="loading">
        <v-skeleton-loader type="avatar, paragraph" />
      </template>
    </template>

    <template #sidebar>
      <template v-if="cartPopulated">
        <CartSummary
          :total-price="cart?.totalPrice"
          :seller-company-name="cart?.sellerCompany?.name"
          class="cart-summary"
        />
      </template>
    </template>

    <template #bottomactions>
      <template v-if="cartPopulated">
        <CDButton
          :block="isMobile"
          color="secondary"
          :to="{ path: ShopRoute.HOME }"
          data-testid="cart-continue-shopping"
        >
          {{ $t('shop.cart.continueShopping') }}
        </CDButton>
      </template>
      <template v-else-if="cartEmpty">
        <div class="d-flex flex-column align-center justify-center empty-content pa-4" data-testid="cart-empty-msg">
          <h2 class="text-h2 ma-4">
            {{ $t('shop.cart.emptyCartMessage') }}
          </h2>
          <div>{{ $t('shop.cart.emptyCartDescription') }}</div>
          <CDButton
            color="primary"
            class="mt-10"
            :to="{ path: ShopRoute.HOME }"
          >
            {{ $t('shop.cart.continueShopping') }}
          </CDButton>
        </div>
      </template>
    </template>
  </PageLayout>

  <v-overlay
    v-model="updating"
    class="align-center justify-center"
  >
    <v-progress-circular
      color="primary"
      indeterminate
    />
  </v-overlay>
</template>

<script setup lang="ts">
import CartItem from '@/components/cart/CartItem.vue'
import CartSummary from '@/components/cart/CartSummary.vue'
import PageLayout from '@/components/generic-partials/PageLayout.vue'
import { computed } from 'vue'
import { useCartStore } from '@/stores/cart-store'
import { useProductStore } from '@/stores/product-store'
import { useVuetifyDisplay } from '@bd/cd-system3'
import { ShopRoute } from '@/constants'
import { calculateTotalPrice } from '@/helpers/money'

const { isMobile } = useVuetifyDisplay()
const { cart, loading, updateCart, updating } = useCartStore()
const { getProduct } = useProductStore()

const cartPopulated = computed(() => cartWithProductVariant.value && !isCartEmpty.value)
const cartEmpty = computed(() => isCartEmpty.value && !loading.value)
let timeout: NodeJS.Timeout

const cartWithProductVariant = computed(() => {
  return cart.value?.lineItems.map((item) => {
    const product = getProduct(item.productId)
    const variant = product?.variants.find((variant) => variant.sku === item.sku)
    const addons = item.addons?.filter((addon) => addon.parentLineItemId === item.lineItemId)
    const totalPrice = calculateTotalPrice(item.totalPrice, addons?.map(addon => addon.totalPrice))
    return {
      ...item,
      product,
      variant,
      addons,
      totalPrice
    }
  })
})

const isCartEmpty = computed(() => {
  return cart.value?.lineItems.length === 0
})

function onQuantityChange (quantity: number, lineItemId: string) {
  if (timeout) {
    clearTimeout(timeout)
  }
  timeout = setTimeout(() => {
    updateCart(lineItemId, quantity)
  }, 1000)
}
</script>
