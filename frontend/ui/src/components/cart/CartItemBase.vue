<template>
  <v-row no-gutters :data-testid="`cart-item-${cartItem.variant?.sku}`">
    <v-col cols="auto">
      <div
        style="width: 80px"
        class="mt-1 mt-lg-2 mr-8"
      >
        <v-img
          v-if="cartItem.product"
          :src="cartItem.product.images[1]"
        />
        <v-skeleton-loader
          v-else
          type="avatar"
        />
      </div>
    </v-col>

    <v-col>
      <v-row no-gutters>
        <v-col
          order="1"
          class="d-flex align-center"
        >
          <h3 class="text-h3">
            {{ cartItem.name }}
          </h3>
        </v-col>

        <v-col
          v-if="cartItem.variant"
          order="3"
          cols="12"
          class="mt-1"
        >
          <p class="text-body-1">
            {{ cartItem.variant.name }}
          </p>
        </v-col>

        <v-col
          v-if="!noQuantity && cartItem.variant"
          order="4"
          cols="12"
          class="mt-1"
        >
          <p class="text-body-1">
            {{ lineItemPrice?.price }} {{ lineItemPrice?.runtime }}
          </p>
        </v-col>

        <v-col
          v-if="!noQuantity"
          order="5"
          cols="12"
          class="mt-4"
        >
          <CDInput
            v-if="canEditQuantity"
            v-model="quantity"
            prevent-special-input
            hide-details
            :label="$t('quantity')"
            class="mt-3"
            style="width: 168px"
            type="number"
            density="compact"
            min="0"
            max="100"
            @input="handleQuantityChange"
          />
          <p v-else>
            {{ $t('quantity') }} : {{ quantity}}
          </p>
        </v-col>

        <v-col
          order="6"
          order-lg="2"
          cols="12"
          lg="auto"
          class="d-flex align-center mt-lg-0"
          :class="{
            'mt-4 mt-lg-0': !noQuantity,
            'mt-0': noQuantity,
            'justify-space-between': isMobile
          }"
        >
          <h4 class="text-h4 mr-2">
            {{ lineItemTotalPrice?.price }} <span v-if="cartItem.variant?.priceList">*</span>
            <span
              v-if="lineItemTotalPrice?.runtime"
              class="text-body-2"
            >
              {{ lineItemTotalPrice.runtime }}
            </span>
          </h4>

          <VBtn
            v-if="canDelete"
            icon="trash"
            density="compact"
            elevation="0"
            :data-testid="`cart-remove-${cartItem.variant?.sku}`"
            :data-id="`${cartItem.variant?.sku}`"
            @click="deleteCartItem" />

        </v-col>

        <v-col
          v-if="$slots.append && isDesktop"
          order="7"
          cols="12"
          class="mt-2"
        >
          <slot name="append" />
        </v-col>
      </v-row>
      <v-row v-if="cartItem.variant?.priceList">
        <v-col class="text-body-2">
          {{ $t('shop.cart.consumptionDescription') }}
          <a :href="cartItem.variant?.priceList" target="_blank">{{ $t('shop.cart.priceList') }}</a>
          </v-col>
      </v-row>
    </v-col>

    <v-col
      v-if="$slots.append && isMobile"
      cols="12"
      class="mt-2"
    >
      <slot name="append" />
    </v-col>
  </v-row>
</template>

<script setup lang="ts">
import { ref, watchEffect } from 'vue'
import { useVuetifyDisplay } from '@bd/cd-system3'
import { useFormattedPrice } from '@/composables/price'

const { cartItem, noQuantity = false } = defineProps<{
  cartItem: CartWithProductVariant,
  noQuantity?: boolean,
  canEditQuantity?: boolean,
  canDelete?: boolean
}>()

const emit = defineEmits<{
  quantityChange: [quantity: number, lineItemId: string]
}>()

const { isDesktop, isMobile } = useVuetifyDisplay()
const quantity = ref(cartItem.quantity)

watchEffect(() => {
  quantity.value = cartItem.quantity
})

function handleQuantityChange (event: Event) {
  if (!cartItem.quantity || cartItem.quantity < 0) return
  emit('quantityChange', Number((event.target as HTMLInputElement).value), cartItem.lineItemId)
}

function deleteCartItem () {
  emit('quantityChange', 0, cartItem.lineItemId)
}

const { formattedPrice: lineItemPrice } = useFormattedPrice(() => (
  cartItem.variant
    ? { price: cartItem.itemPrice, runtime: cartItem.variant.runtime }
    : undefined
))
const { formattedPrice: lineItemTotalPrice } = useFormattedPrice(() => (
  cartItem.variant
    ? { price: cartItem.totalPrice, runtime: cartItem.variant.runtime }
    : undefined
))
</script>
