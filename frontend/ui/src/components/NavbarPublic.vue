<template>
  <CountryLanguageSwitcher :variant="isMobile ? 'icon' : 'text'" />
  <CountryLanguageSwitcherDialog />

  <CDButton
    v-if="isDesktop"
    prepend-icon="letter"
    class="mr-2"
    :href="appConfig.contactUrl"
    data-testid="contact-button"
  >
    <template v-if="isDesktop">
      {{ $t('navigation.items.getInTouch') }}
    </template>
  </CDButton>
  <VBtn
    v-else
    icon="letter"
    density="comfortable"
  />

  <CDButton
    :color="isMobile ? 'black' : 'primary'"
    :variant="isMobile ? 'text' : 'flat'"
    :prepend-icon="isMobile ? 'logout' : ''"
    data-testid="login-button"
    @click="() => authStore.login()"
  >
    {{ $t('navigation.items.storeLogin') }}
  </CDButton>
</template>

<script setup lang="ts">
import CountryLanguageSwitcher from '@/components/CountryLanguageSwitcher.vue'
import CountryLanguageSwitcherDialog from '@/components/CountryLanguageSwitcherDialog.vue'
import { useAuthStore } from '@/stores/auth-store'
import { useVuetifyDisplay } from '@bd/cd-system3'
import appConfig from '@/helpers/app-config'

const { isMobile, isDesktop } = useVuetifyDisplay()
const authStore = useAuthStore()
</script>
