<template>
  <GenericDialog
    v-model="countryLanguageDialogStore.isActive"
    :dialog-title="$t('countryLanguage.dialog.title')"
    :confirm-label="$t('countryLanguage.dialog.confirm')"
    :cancel-label="$t('countryLanguage.dialog.cancel')"
    :confirm-disabled="!selectedCountry || !selectedLanguage"
    size="small"
    @confirm="confirm"
  >
    <span>{{ $t('countryLanguage.dialog.selectCountry') }}</span>
    <v-select
      v-model="selectedCountry"
      :items="countryLanguageStore.countries"
      item-title="name"
      item-value="code"
      variant="outlined"
      hide-details
      class="mt-2 mb-8"
    />
    <span class="mt-8">{{ $t('countryLanguage.dialog.selectLanguage') }}</span>
    <v-select
      v-model="selectedLanguage"
      :items="countryLanguageStore.findCountry(selectedCountry)?.languages"
      item-title="name"
      item-value="code"
      variant="outlined"
      hide-details
      class="mt-2"
    />
  </GenericDialog>
</template>

<script setup lang="ts">
import GenericDialog from '@/components/generic-partials/GenericDialog.vue'
import { ref, watch } from 'vue'
import { useCountryLanguageStore } from '@/stores/country-language-store'
import { useCountryLanguageDialogStore } from '@/stores/country-language-dialog-store.ts'

const countryLanguageDialogStore = useCountryLanguageDialogStore()
const countryLanguageStore = useCountryLanguageStore()
const selectedCountry = ref(countryLanguageStore.activeCountry)
const selectedLanguage = ref(countryLanguageStore.activeLanguage)

watch(selectedCountry, (value) => {
  const country = countryLanguageStore.findCountry(value)
  if (!country) {
    selectedLanguage.value = ''
  } else if (!country.languages.map(l => l.code).includes(selectedLanguage.value)) {
    selectedLanguage.value = country.defaultLanguage
  }
})

watch(() => countryLanguageDialogStore.isActive, (value) => {
  if (value) {
    selectedCountry.value = countryLanguageStore.activeCountry
    selectedLanguage.value = countryLanguageStore.activeLanguage
  }
})

async function confirm() {
  await countryLanguageStore.set(selectedCountry.value, selectedLanguage.value)
  countryLanguageDialogStore.close()
}
</script>
