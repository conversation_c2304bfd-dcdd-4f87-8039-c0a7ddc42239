<template>
  <CDButton
    v-if="variant === 'text'"
    variant="text"
    prepend-icon="world"
    data-testid="country-language-switcher"
    @click="countryLanguageDialogStore.open"
  >
    {{ countryName }} - {{ countryLanguageStore.activeLanguage }}
  </CDButton>
  <CDButton
    v-else
    icon="world"
    density="comfortable"
    data-testid="country-language-switcher"
    @click="countryLanguageDialogStore.open"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useCountryLanguageStore } from '@/stores/country-language-store'
import { useCountryLanguageDialogStore } from '@/stores/country-language-dialog-store'

defineProps<{
  variant: 'icon' | 'text'
}>()

const countryLanguageDialogStore = useCountryLanguageDialogStore()
const countryLanguageStore = useCountryLanguageStore()

const countryName = computed(() =>
  new Intl.DisplayNames(countryLanguageStore.activeLanguage, { type: 'region' }).of(countryLanguageStore.activeCountry)
)
</script>
