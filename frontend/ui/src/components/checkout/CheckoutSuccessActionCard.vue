<template>
  <v-card
    variant="outlined"
    class="rounded-0 pa-8 pa-lg-16"
    border="cd-stroke-separator thin opacity-100"
  >
    <v-img
      width="208"
      height="134"
      :src="illustration"
      class="float-lg-left mr-lg-12 mb-11 mb-lg-9"
    />
    <v-card-title class="px-0 pt-0 text-wrap">
      <h2 class="text-h2" data-testid="checkout-success-action-card-title">
        {{ $t('shop.checkout.confirmation.actionCard.title') }}
      </h2>
    </v-card-title>
    <v-card-text class="px-0 pb-11">
      <p class="text-body-1" data-testid="checkout-success-action-card-text">
        {{ $t('shop.checkout.confirmation.actionCard.text') }}
      </p>
    </v-card-text>
    <v-card-actions class="pa-0">
      <CDButton
        color="primary"
        data-testid="checkout-success-action-card-button"
        :to="{ path: ShopRoute.SUBSCRIPTIONS }"
      >
        {{ $t('shop.checkout.confirmation.actionCard.action') }}
      </CDButton>
    </v-card-actions>
  </v-card>
</template>

<script setup lang="ts">
import { ShopRoute } from '@/constants'
import illustration from '@/assets/checkout-success-action-card-illustration.svg'
</script>
