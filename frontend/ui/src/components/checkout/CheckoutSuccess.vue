<template>
  <PageLayout :title="$t('shop.checkout.confirmation.confirmationHeader')">
    <template #titleactions>
      <CDButton
        v-if="!(loading || timeout)"
        color="secondary"
        data-testid="checkout-success-view-subscription-management"
        :to="{ path: ShopRoute.SUBSCRIPTIONS }"
      >
        {{ $t("shop.checkout.confirmation.viewSubscriptionManagement") }}
      </CDButton>
    </template>

    <template #content>
      <div
        v-if="loading"
        class="d-flex flex-column align-center justify-center pa-4"
      >
        <h2 class="text-h2 ma-4">
          {{ $t('shop.checkout.loading') }}
        </h2>
        <v-progress-circular
          color="primary"
          indeterminate
        />
      </div>

      <div v-else-if="timeout">
        <p class="text-body-1" data-testid="checkout-success-payment-failed">
          {{ $t('shop.error.checkout.paymentFailed') }}
        </p>
      </div>

      <template v-else>
        <v-row
          no-gutters
          class="mt-10"
        >
          <p class="text-h3" data-testid="checkout-success-confirmation-text">
            {{ $t('shop.checkout.confirmation.confirmationLine1') }}<br>
            <br>
            {{ $t('shop.checkout.confirmation.confirmationLine2') }}<br>
            {{ $t('shop.checkout.confirmation.confirmationLine3', { orderNumber: orderId }) }}
          </p>
        </v-row>
        <v-row
          no-gutters
          class="mt-10 mt-lg-16"
        >
          <v-col>
            <CheckoutSuccessActionCard />
          </v-col>
        </v-row>
      </template>
    </template>
  </PageLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useCartStore } from '@/stores/cart-store'
import { fetchPayment } from '@/composables/payments'
import { ShopRoute } from '@/constants'
import CheckoutSuccessActionCard from '@/components/checkout/CheckoutSuccessActionCard.vue'
import PageLayout from '@/components/generic-partials/PageLayout.vue'

const { fetchCart } = useCartStore()

const loading = ref(true)
const orderId = ref('')
const timeout = ref(false)

onMounted(async () => {
  const route = useRoute()
  const paymentId = route.query.id
  try {
    if (typeof paymentId === 'string') {
      await poll(async () => {
        const payment = await fetchPayment(paymentId)
        if (payment && payment.orderId && (payment.paymentStatus === 'SUCCESS' || payment.paymentStatus === 'PENDING')) {
          loading.value = false
          orderId.value = payment.orderId
          fetchCart()
          return true
        }
        return false
      }, 30)
    }
  } finally {
    loading.value = false
    timeout.value = !orderId.value
  }
})

async function poll (callback: () => Promise<boolean>, timeoutInSeconds: number) {
  for (let i = 0; i < timeoutInSeconds; i++) {
    if (await callback()) break
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
}
</script>
