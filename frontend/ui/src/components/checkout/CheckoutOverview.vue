<template>
  <PageLayout :title="t('shop.checkout.header')">
    <template #content>
      <template v-if="!isZeroPaymentCheckout">
        <h2 class="text-h2">
          {{ t('shop.checkout.paymentMethod') }}
        </h2>

        <v-radio-group
          v-model="paymentMethodId"
          class="mt-6 mt-lg-10"
          hide-details
        >
          <PaymentMethod
            v-for="(paymentMethod, index) in data?.paymentConfig.paymentMethods"
            :key="index"
            :payment-method="paymentMethod"
            :class="{ 'mt-10': index > 0 }"
          />
        </v-radio-group>
        <v-skeleton-loader
          v-if="isLoading"
          type="article"
          width="400px"
        />

        <hr class="mt-8 mt-lg-16">
      </template>

      <h2 class="text-h2 mt-8 mt-lg-10 mb-6">
        {{ t('shop.checkout.orderSummary') }}
      </h2>

      <template v-if="!isLoading">
        <CartItem
          v-for="item in cartWithProductVariant"
          :key="item.lineItemId"
          :cart-item="item"
          :can-edit="false"
          class="mb-8" />
      </template>
      <template v-else>
        <v-skeleton-loader
          type="text"
          width="300px"
          class="mb-2"
        />
      </template>

      <template v-if="!isZeroPaymentCheckout">
        <hr class="mt-8 mt-lg-16">

        <h3 class="text-h3 mt-8 mt-lg-10 mb-6">
          {{ $t('shop.checkout.notes.title') }}
        </h3>
        <p>{{ $t('shop.checkout.notes.description') }}</p>

        <CDInput
          v-model="noteLine1"
          :label="$t('shop.checkout.notes.label1')"
          :counter="50"
          class="mt-8"
          density="compact"
          :rules="noteRules"
        />
        <CDInput
          v-model="noteLine2"
          :label="$t('shop.checkout.notes.label2')"
          :counter="50"
          class="mt-2"
          density="compact"
          :rules="noteRules"
        />
      </template>
    </template>

    <template #sidebar>
      <CheckoutSummary
        :billing-address="data?.cart.billingAddress ?? undefined"
        :billing-email="data?.cart.billingAddress?.email ?? undefined"
        :payment-method-id="paymentMethodId"
        :notes="notes"
        :total-price="data?.cart.totalPrice"
        :seller-company-name="data?.cart.sellerCompany?.name"
        :agreements="agreements"
      />
    </template>

    <template #bottomactions>
      <CDButton
        :block="isMobile"
        :to="{ path: ShopRoute.CART }"
        color="secondary"
      >
        {{ t('shop.checkout.backToCart') }}
      </CDButton>
    </template>
  </PageLayout>
</template>

<script setup lang="ts">
import { ref, computed, watchEffect } from 'vue'
import { useI18n } from 'vue-i18n'
import { useCheckoutData } from '@/composables/checkout'
import { useVuetifyDisplay } from '@bd/cd-system3'
import CheckoutSummary from '@/components/checkout/CheckoutSummary.vue'
import CartItem from '@/components/cart/CartItem.vue'
import PaymentMethod from '@/components/checkout/PaymentMethod.vue'
import PageLayout from '@/components/generic-partials/PageLayout.vue'
import { ShopRoute, ZeroPaymentMethodId } from '@/constants'
import { calculateTotalPrice } from '@/helpers/money'
import { useProductStore } from '@/stores/product-store'

const { t } = useI18n()
const { data, isLoading } = useCheckoutData()
const { getProduct } = useProductStore()
const { isMobile } = useVuetifyDisplay()

const noteLine1 = ref<string>()
const noteLine2 = ref<string>()
const noteRules = [(v: string | undefined) => (v ?? '').length <= 50]

const notes = computed(() => {
  return [noteLine1.value, noteLine2.value].filter((n): n is string => n !== undefined).filter(n => n.length > 0)
})

const agreements = computed(() => {
  return Array.from(
    data.value?.cart.lineItems
      .flatMap(lineItem => lineItem.variant?.agreements ?? [])
      .reduce((acc, cur) => {
        if (!acc.has(cur.url + cur.linkType)) {
          acc.set(cur.url + cur.linkType, cur)
        }
        return acc
      }, new Map<string, LocalizedLink>())
      .values() ?? []
  )
})

const paymentMethods = computed(() => data.value?.paymentConfig.paymentMethods)
const isZeroPaymentCheckout = computed(() => paymentMethods.value?.length === 1 && paymentMethods.value[0].paymentMethodId === ZeroPaymentMethodId)
const paymentMethodId = ref<string>()

const cartWithProductVariant = computed(() => {
  return data.value?.cart.lineItems.map((item) => {
    // FIXME using product store for now. this should be loaded via a dependant query inside useCheckoutData
    const product = getProduct(item.productId)
    const totalPrice = calculateTotalPrice(item.totalPrice, item.addons?.map(addon => addon.totalPrice))
    return {
      ...item,
      product,
      totalPrice
    }
  })
})

watchEffect(() => {
  if (isZeroPaymentCheckout.value) {
    paymentMethodId.value = ZeroPaymentMethodId
  }
})
</script>

<style scoped lang="scss">
hr {
  border: 0;
  border-top: 1px solid rgb(var(--v-theme-cd-stroke-separator));
}
</style>
