<template>
  <template v-if="paymentMethod.paymentMethodId === 'PGW/SEPA_DIRECTDEBIT'">
    <h3 :class="['text-h3', 'mb-6', $attrs.class]">
      {{ $t('shop.payment.directdebit.name') }}
    </h3>
    <v-radio
      :value="paymentMethod.paymentMethodId"
      density="compact"
    >
      <template #label>
        <div class="payment-icon">
          <v-icon
            size="35"
            icon="invoicedollar"
          />
        </div>
        <p class="text-body-1">
          {{ $t('shop.payment.directdebit.new') }}
        </p>
      </template>
    </v-radio>
  </template>
  <template v-else-if="isBoschSepaCredit(paymentMethod)">
    <h3 :class="['text-h3', 'mb-6', $attrs.class]">
      {{ $t('shop.payment.transfer.name') }}
    </h3>
    <v-radio
      :value="paymentMethod.paymentMethodId"
      :data-testid="`checkout-payment-method-radio-${paymentMethod.paymentMethodId}`"
      density="compact"
    >
      <template #label>
        <div class="payment-icon">
          <v-icon
            size="35"
            icon="invoicedollar"
          />
        </div>
        <p class="text-body-1">
          {{ $t('shop.payment.transfer.new') }}
        </p>
      </template>
    </v-radio>
    <div class="sepa-information text-body-2 mt-4">
      <p>
        {{ $t('shop.payment.transfer.to') }}
      </p>
      <table class="sepa-information__table mt-2">
        <tbody>
          <tr>
            <td>
              {{ $t('shop.payment.transfer.account') }}
            </td>
            <td>
              {{ paymentMethod.accountHolder }}
            </td>
          </tr>
          <tr>
            <td>
              {{ $t('shop.payment.transfer.bankname') }}
            </td>
            <td>
              {{ paymentMethod.bankName }}
            </td>
          </tr>
          <tr>
            <td>
              {{ $t('shop.payment.transfer.iban') }}
            </td>
            <td>
              {{ paymentMethod.iban }}
            </td>
          </tr>
          <tr v-if="paymentMethod.bic">
            <td>
              {{ $t('shop.payment.transfer.bic') }}
            </td>
            <td>
              {{ paymentMethod.bic }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </template>
  <template v-else-if="isBoschAchCredit(paymentMethod)">
    <h3 :class="['text-h3', 'mb-6', $attrs.class]">
      {{ $t('shop.payment.achCreditTransfer.name') }}
    </h3>
    <v-radio
      :value="paymentMethod.paymentMethodId"
      density="compact"
    >
      <template #label>
        <div class="payment-icon">
          <v-icon
            size="35"
            icon="invoicedollar"
          />
        </div>
        <p class="text-body-1">
          {{ $t('shop.payment.achCreditTransfer.new') }}
        </p>
      </template>
    </v-radio>
    <div class="sepa-information text-body-2 mt-4">
      <p>
        {{ $t('shop.payment.achCreditTransfer.to') }}
      </p>
      <table class="sepa-information__table mt-2">
        <tbody>
        <tr>
          <td>
            {{ $t('shop.payment.achCreditTransfer.accountHolder') }}
          </td>
          <td>
            {{ paymentMethod.accountHolder }}
          </td>
        </tr>
        <tr>
          <td>
            {{ $t('shop.payment.achCreditTransfer.bankName') }}
          </td>
          <td>
            {{ paymentMethod.bankName }}
          </td>
        </tr>
        <tr>
          <td>
            {{ $t('shop.payment.achCreditTransfer.routingNumber') }}
          </td>
          <td>
            {{ paymentMethod.routingNumber }}
          </td>
        </tr>
        <tr>
          <td>
            {{ $t('shop.payment.achCreditTransfer.accountNumber') }}
          </td>
          <td>
            {{ paymentMethod.accountNumber }}
          </td>
        </tr>
        <tr>
          <td>
            {{ $t('shop.payment.achCreditTransfer.bic') }}
          </td>
          <td>
            {{ paymentMethod.bic }}
          </td>
        </tr>
        </tbody>
      </table>
    </div>
  </template>
</template>

<script setup lang="ts">
defineProps<{
  paymentMethod: PaymentMethod
}>()

function isBoschSepaCredit(paymentMethod: PaymentMethod): paymentMethod is BoschSepaCreditInformation {
  return paymentMethod.paymentMethodId === 'BOSCH_TRANSFER/SEPA_CREDIT'
}

function isBoschAchCredit(paymentMethod: PaymentMethod): paymentMethod is BoschAchCreditInformation {
  return paymentMethod.paymentMethodId === 'BOSCH_TRANSFER/ACH_CREDIT'
}
</script>

<style scoped lang="scss">
@use '@bd/cd-system3/dist/styles/settings';
@use 'sass:map';

.payment-icon {
  display: flex;
  justify-content: center;
  width: 60px;
  height: 35px;
  margin: 0 20px;
}

.sepa-information__table {
  --sepa-information__table-gap: 24px;
  border-collapse: separate;
  border-spacing: var(--sepa-information__table-gap) 0;
  margin: 0 calc(-1 * var(--sepa-information__table-gap));
}

@media (min-width: map.get(settings.$grid-breakpoints, 'lg')) {
  .sepa-information {
    margin-left: calc(28px + 60px + 2 * 20px);
  }

  .sepa-information__table {
    --sepa-information__table-gap: 56px;
  }
}
</style>
