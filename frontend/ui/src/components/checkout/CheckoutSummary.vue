<template>
  <v-card
    variant="outlined"
    class="rounded-0"
    border="cd-stroke-separator thin opacity-100"
  >
    <v-card-item class="pa-6">
      <v-row>
        <v-col>
          <CheckoutBillingAddress :address="billingAddress" />
        </v-col>
      </v-row>
      <v-row>
        <v-col>
          <CheckoutEmail :email="billingEmail" />
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="6">
          <h4 class="text-h4">
            {{ $t('shop.checkout.miniSummary.totalPrice') }}
          </h4>
          <p
            v-if="sellerCompanyName"
            class="text-body-2 text-no-wrap"
          >
            {{ $t('shop.checkout.miniSummary.soldBy', { companyName: sellerCompanyName }) }}
          </p>
        </v-col>
        <v-col
          cols="6"
          class="text-right"
        >
          <h4
            v-if="totalPrice"
            class="text-h4"
          >
            {{ $n(totalPrice.value, 'currency', { currency: totalPrice.currencyCode }) }}
          </h4>
        </v-col>
      </v-row>
      <v-row
        v-for="agreementType in agreementTypes"
        :key="agreementType"
        >
        <v-col
          v-if="findAgreements(agreementType).length"
          class="text-body-1">
          <v-checkbox
            v-model="agreementsSelected[agreementType]"
            density="compact"
            :data-testid="`checkout-agreement-checkbox-${agreementType}`"
          >
            <template #label>
              <div class="ml-4">
                {{ $t('shop.checkout.miniSummary.agreement.' + agreementType) }}
                <p
                  v-for="(agreement, index) in findAgreements(agreementType)"
                  :key="index"
                >
                  <a
                    :href="agreement.url ?? undefined"
                    target="_blank"
                  >
                    {{ agreement.name }}
                  </a>
                </p>
              </div>
            </template>
          </v-checkbox>
        </v-col>
      </v-row>
    </v-card-item>

    <v-card-actions class="pt-4 px-6 pb-6">
      <v-spacer />
      <CDButton
        v-if="cart && !cartLoading"
        color="primary"
        data-testId="checkout-place-order-button"
        :loading="loading"
        :disabled="isCheckoutDisabled"
        @click="checkout()"
      >
        {{ $t('shop.checkout.placeOrder') }}
      </CDButton>
    </v-card-actions>
  </v-card>
</template>

<script setup lang="ts">
import { useCartStore } from '@/stores/cart-store'
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import CheckoutBillingAddress from '@/components/checkout/CheckoutBillingAddress.vue'
import CheckoutEmail from '@/components/checkout/CheckoutEmail.vue'

const router = useRouter()
const { cart, checkoutCart, loading: cartLoading } = useCartStore()
const agreementsSelected = ref<{ [key: string]: boolean }>({ SOFTWARE_LICENSE: false, PRIVACY_POLICY: false })
const loading = ref(false)

const props = defineProps<{
  billingAddress?: BillingAddress
  billingEmail?: string
  paymentMethodId?: string
  notes?: string[]
  agreements?: LocalizedLink[]
  totalPrice?: Money
  sellerCompanyName?: string
}>()

const agreementTypes = ['SOFTWARE_LICENSE', 'PRIVACY_POLICY']

function findAgreements(agreementType: string): LocalizedLink[] {
  return props.agreements?.filter(agreement => agreement.linkType === agreementType) || []
}

const isCheckoutDisabled = computed(() => {
  const allAgreementsSelected = agreementTypes.every(agreementType =>  agreementsSelected.value[agreementType] || !findAgreements(agreementType).length)
  const notesValid = !props.notes || (props.notes.length <= 2 && props.notes.every(note => note!.length <= 50))
  return !allAgreementsSelected ||
         !props.paymentMethodId ||
         !notesValid
})

async function checkout () {
  if (!props.paymentMethodId) throw new Error('No payment method selected')

  loading.value = true
  try {
    const response = await checkoutCart(props.paymentMethodId, props.notes)
    if (response.redirectUrl) {
      window.location.href = response.redirectUrl
    } else {
      await router.push({ path: '/checkout-success', query: { id: response.paymentId } })
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
// top align the checkbox in relation to the label
.v-checkbox :deep(.v-selection-control__wrapper) {
  align-self: start;
}
</style>
