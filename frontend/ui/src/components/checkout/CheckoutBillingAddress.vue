<template>
  <h4 class="text-h4">
    {{ t('shop.checkout.miniSummary.billingAddress') }}
  </h4>
  <p
    v-if="address"
    class="text-body-1"
  >
    {{ address.streetName }} {{ address.streetNumber }}<br>
    {{ address.postalCode }} {{ address.city }}<br>
    {{ countryLabel }}
  </p>
  <v-skeleton-loader
    v-else
    type="paragraph"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

const { t, locale } = useI18n()

const props = defineProps<{
  address?: BillingAddress
}>()

const countryLabel = computed(() =>
  props.address?.country ? new Intl.DisplayNames([locale.value], { type: 'region' }).of(props.address.country) : undefined
)
</script>
