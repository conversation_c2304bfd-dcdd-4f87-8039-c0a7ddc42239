<template>
  <PageLayout>
    <template #content>
      <v-row class="my-12">
        <v-col
          cols="12"
          class="text-center"
        >
          <h1 class="text-h1 mb-8" data-testid="products-overview-header">
            {{ $t('shop.productsOverview.header.title') }}
          </h1>
        </v-col>
      </v-row>
      <v-container>
        <CategoryDetails
          v-for="category in categoriesWithProducts"
          :key="category.categoryId"
          :category="category"
          class="mb-8"
        />
        <DemoProduct
          v-for="product in productsWithoutCategory"
          :key="product.id"
          :product="product"
          class="mb-8"
        />
      </v-container>
    </template>
  </PageLayout>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useProductStore } from '@/stores/product-store'
import DemoProduct from '@/components/products/DemoProduct.vue'
import PageLayout from '@/components/generic-partials/PageLayout.vue'
import { useCategoryData } from '@/composables/categories'
import CategoryDetails from '@/components/products/CategoryDetails.vue'

defineProps<{
  header?: string
}>()

const { products } = useProductStore()
const { data: categories } = useCategoryData()

// FIXME quickly filtering out addon products for now to not appear in product overview
const productsWithoutAddons = computed(() => {
  const addonNames = products.value.state
    ?.flatMap(products => products.variants)
    .flatMap(variant => variant.addons ?? [])
    .flatMap(addon => addon.name) ?? []

  return products.value.state
    ?.filter(product => !addonNames.includes(product.name))
})

const productsWithoutCategory = computed(() => productsWithoutAddons.value?.filter(product => !product.categories?.length))

const productsWithCategory = computed(() => productsWithoutAddons.value?.filter(product => product.categories?.length > 0))

const categoriesWithProducts = computed(() => {
  return categories.value?.results?.map(category => {
    return {
      ...category,
      products: productsWithCategory.value?.filter(product => product.categories?.includes(category.categoryId)) ?? []
    }
  })
  .filter(category => category.products?.length)
  .sort((a, b) => a.order - b.order)
})
</script>
