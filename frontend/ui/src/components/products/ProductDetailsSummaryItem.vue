<template>
  <div class="product-selection-summary-item my-4">
    <v-img
      :src="product.images[1]"
      aspect-ratio="1"
      width="80"
    />
    <div>
      <h3 class="text-h3 mb-1">
        {{ product.name }}
      </h3>
      <p class="name mb-1">
        {{ variant.name }}
      </p>

      <p v-for="(addon, i) in selectedAddons" :key="i" class="mb-1">
        <span>
          {{ getAddonMeta(addon)!.name }}
        </span>:
        <span>
          {{ getAddonMeta(addon)!.value }}
        </span>
      </p>

      <p v-if="variant.addons.length === 0" class="itemprice mb-1">
        {{ formattedPrice.price }} {{ formattedPrice.runtime }}
      </p>
      <p v-if="variant.addons.length === 0" class="quantity mb-1">
        {{ $t('quantity') }}: {{ item.quantity }}
      </p>
      <p v-if="variant?.priceList" class="text-body-2">
        <i18n-t keypath="shop.cart.consumptionDescription" tag="span">
          <a :href="variant?.priceList" target="_blank">{{ $t('shop.cart.priceList') }}</a>
        </i18n-t>
      </p>
    </div>
    <p class="price d-flex justify-end">
      {{ $n(item.calculatedPrice, 'currency', {currency: variant.price.currencyCode}) }}
      <span v-if="variant?.priceList">*</span>
      {{ formattedPrice.runtime }}
    </p>

  </div>
</template>

<script setup lang="ts">
import { useFormattedPrice } from '@/composables/price'
import { usePrecartStore } from '@/stores/precart-store'
import { computed } from 'vue'

const { variant } = defineProps<{
  product: Product
  variant: Variant
  item: CartQuantity
}>()

const precartStore = usePrecartStore()
const { formattedPrice } = useFormattedPrice(() => variant)

const selectedAddons = computed(() => precartStore.getPrecartData(variant.sku, 'addons'))

const getAddonMeta = (sku: string) => {
  const addon = variant.addons.find(x => x.addonVariants.find(y => y.sku === sku))
  const addonValue = addon!.addonVariants.find(x => x.sku === sku)
  if (addon && addonValue) {
    return {
      name: addon.name,
      value: addonValue.name
    }
  }
}

</script>

<style scoped lang="scss">
.product-selection-summary-item {
  display: grid;
  grid-template-columns: 110px max-content auto;
  align-items: start;
  .name, .itemprice, .quantity {
    grid-column-start: 2;
  }
  .price {
    grid-column-start: 4;
    grid-row-start: 1;
  }
}
</style>
