<template>
  <div>
    <v-row
      v-for="(addon, i) in addons"
      :key="i"
      no-gutters
      class="mb-10"
    >
      <v-col>
        <h2 class="text-h2" data-testid='addon-name'>
          {{ addon.name }}
        </h2>
        <p class="text-body-1 pb-6" data-testid='addon-description'>
          {{ addon.description }}
        </p>
        <ProductAddonsRow :parent-sku="parentSku" :addon-variants="addon.addonVariants" />
      </v-col>
    </v-row>
  </div>
</template>
<script setup lang="ts">
import ProductAddonsRow from '@/components/products/ProductAddonsRow.vue'

defineProps<{
  parentSku: string
  addons: Addon[]
}>()
</script>
