<template>
  <PageLayout>
    <template #content>
      <template v-if="product">
        <ProductDetailsHeader :product="product" />
        <ProductDetailsInfoBox
          :variant="selectedVariant"
        />
        <ProductDetailsContractTypeSelection
          v-model="selected"
          :types="Array.from(variantMapWithoutTrial.keys())"
        />
        <ProductDetailsGrid
          v-model="selectedVariantName"
          :variants="variants"
          class="mb-12"
        />
        <ProductAddons
          v-if="selectedVariantName"
          :parent-sku="selectedVariantName"
          :addons="selectedVariant?.addons ?? []"
          class="addons-wrapper"
        />
        <ProductDetailsSummary />
      </template>
      <template v-else-if="products.loading">
        <v-skeleton-loader
          type="avatar, paragraph"
          class="mb-10"
        />
        <v-skeleton-loader
          type="article"
          class="mb-10 mx-auto"
          width="333px"
        />
        <h2>{{ $t('shop.productSelection.variantSelection.variant.titleBundles') }}</h2>
        <v-skeleton-loader
          type="card"
          class="mb-5"
          width="333px"
        />
      </template>
      <template v-else>
        <h2>{{ $t('shop.error.product.notFound') }}</h2>
      </template>
    </template>
  </PageLayout>
</template>

<script setup lang="ts">
import { ref, type Ref, computed, watchEffect } from 'vue'
import { useProductStore } from '@/stores/product-store'
import { usePrecartStore } from '@/stores/precart-store'
import { useBreadcrumb } from '@/composables/breadcrumb'
import { onBeforeRouteLeave, useRoute } from 'vue-router'
import PageLayout from '@/components/generic-partials/PageLayout.vue'
import ProductDetailsHeader from '@/components/products/ProductDetailsHeader.vue'
import ProductDetailsSummary from '@/components/products/ProductDetailsSummary.vue'
import ProductDetailsContractTypeSelection from '@/components/products/ProductDetailsContractTypeSelection.vue'
import ProductDetailsGrid from '@/components/products/ProductDetailsGrid.vue'
import ProductAddons from '@/components/products/ProductAddons.vue'
import ProductDetailsInfoBox from '@/components/products/ProductDetailsInfoBox.vue'

const { clearPrecart } = usePrecartStore()
const { getProduct, products } = useProductStore()
const route = useRoute()
const selected: Ref<string> = ref('')
const selectedVariantName: Ref<string> = ref('')

const selectedVariant = computed(() => {
  return variants.value.find(({ sku }) => sku === selectedVariantName.value)
})

const product = computed(() => {
  if (typeof route.params.productId === 'string') {
    return getProduct(route.params.productId)
  }
  return undefined
})

const variantsWithTrial = computed(() => {
  return (product.value?.variants || []).filter(({ licenseType }) => licenseType === 'TRIAL')
})

const variantMapWithoutTrial = computed(() => {
  const variants = (product.value?.variants || []).filter(({ licenseType }) => licenseType !== 'TRIAL')
  return groupBy(variants, [({ licenseType }: Variant) => licenseType, ({ runtime }: Variant) => runtime])
})

const variants = computed(() => [...variantsWithTrial.value, ...(variantMapWithoutTrial.value.get(selected.value) ?? [])])

useBreadcrumb(() => product.value?.name)

// in case of direct access to this page (CTRL+R) we need to wait until data is loaded
watchEffect(() => {
  selected.value = variantMapWithoutTrial.value.keys().next().value ?? ''
})

function groupBy<T> (list: T[], keyGetters: ((input: T) => unknown)[]): Map<string, T[]> {
  const map = new Map<string, T[]>()
  list.forEach((item) => {
    const key = keyGetters.map(keyGetter => keyGetter(item)).join('|')
    const collection = map.get(key)
    if (!collection) {
      map.set(key, [item])
    } else {
      collection.push(item)
    }
  })
  return map
}

// Prevent mixing items from different product selection pages
onBeforeRouteLeave(() => clearPrecart())
</script>

<style scoped>
.addons-wrapper {
  margin-bottom: 200px;
}
</style>
