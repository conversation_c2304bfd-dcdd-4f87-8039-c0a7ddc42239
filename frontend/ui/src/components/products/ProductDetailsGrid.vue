<template>
  <v-row
    no-gutters
    class="justify-center"
  >
    <v-col class="grid-container">
      <ProductDetailsBundle
        v-for="variant in variants"
        :key="variant.sku"
        :variant="variant"
        :quantity="getPrecartData(variant.sku, 'quantity') || 0"
        :selected="selected === variant.sku"
        :show-radios="variants.length > 1"
        :link="variant.addons?.length > 0"
        @variant-quantity-change="addToPrecart"
        @click="variant.addons?.length > 0 ? selected = variant.sku : undefined"
      />
    </v-col>
  </v-row>
</template>

<script setup lang="ts">
import { watchEffect } from 'vue'
import { usePrecartStore } from '@/stores/precart-store'
import ProductDetailsBundle from '@/components/products/ProductDetailsBundle.vue'

const { addToPrecart, getPrecartData } = usePrecartStore()
const selected = defineModel<string>()

const { variants } = defineProps<{
  variants: Variant[]
}>()

watchEffect(() => {
  selected.value = variants[0].addons?.length > 0 ? variants[0].sku : undefined
})
</script>

<style scoped lang="scss">
@use '@bd/cd-system3/dist/styles/settings';
@use 'sass:map';

$grid-gap: 24px;
$grid-max-width-single-col: 540px;
$card-min-width-multi-col: 312px;
$grid-max-width-multi-col: $card-min-width-multi-col * 3 + $grid-gap * 2;

.grid-container {
  display: grid;
  grid-template-columns: 1fr;
  grid-gap: $grid-gap;
  max-width: $grid-max-width-single-col;
}

@media (min-width: map.get(settings.$grid-breakpoints, 'lg')) {
  .grid-container {
    grid-template-columns: repeat(auto-fit, minmax($card-min-width-multi-col, 1fr));
    max-width: $grid-max-width-multi-col;
  }

  .grid-container:has(.v-card:only-child) {
    max-width: $grid-max-width-single-col;
  }
}
</style>
