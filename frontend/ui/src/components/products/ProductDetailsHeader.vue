<template>
  <div class="d-flex justify-center align-center flex-column mb-10">
    <v-row class="mt-2 mx-n2 mb-n2 align-center">
      <v-col
        cols="auto"
        class="pr-1"
      >
        <v-img
          :src="product.images[1]"
          :width="80"
          class="logo"
          cover
          :aspect-ratio="1/1"
        />
      </v-col>
      <v-col>
        <h1
          data-id="text-product-name"
          class="text-h1 mt-0"
        >
          {{ product.name }}
        </h1>
      </v-col>
    </v-row>
    <v-row class="mt-n2">
      <v-col>
        <p
          class="text-body-2"
        >
          <template v-if="product.sellerCompany?.name">
            {{ $t('shop.checkout.miniSummary.soldBy', { companyName: product.sellerCompany?.name }) }}
            <span> | </span>
          </template>
          <template
            v-for="(agreement, index) in product.externalDocuments"
            :key="index"
          >
            <span v-if="index !== 0"> | </span>
            <a
              :href="agreement.url ?? undefined"
              target="_blank"
            >
              {{ agreement.name }}
            </a>
          </template>
        </p>
      </v-col>
    </v-row>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  product: Product
}>()
</script>
