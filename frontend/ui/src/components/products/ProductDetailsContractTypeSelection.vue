<template>
  <v-row
    v-if="types.length > 1"
    no-gutters
    class="mb-4"
  >
    <v-col class="d-flex justify-center">
      <CDSegmentedControl v-model="selected">
        <v-btn
          v-for="type in types"
          :key="type"
          :value="type"
        >
          {{ $t('shop.productSelection.variantSelection.runtime.name.' + type) }}
        </v-btn>
      </CDSegmentedControl>
    </v-col>
  </v-row>

  <v-row
    v-if="types.length > 1"
    no-gutters
    class="mb-10"
  >
    <v-col class="d-flex justify-center">
      <ul class="text-body-2">
        <li>{{ $t('shop.productSelection.variantSelection.runtime.descriptionDetails.' + selected) }}</li>
      </ul>
    </v-col>
  </v-row>
</template>

<script setup lang="ts">
const selected = defineModel<string>()

export interface Props {
  types: string[]
}

defineProps<Props>()
</script>

<style lang="scss" scoped>
ul {
  text-align: center;
  list-style-type: none;
  li {
    margin: 6px 0;
  }
}
</style>
