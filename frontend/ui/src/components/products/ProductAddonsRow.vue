<template>

  <v-item-group
    v-model="selected"
    class="d-flex ga-4"
    selected-class="border-md">
    <v-row align-content="end">
      <v-col
        :class="{ 'v-col-12' : isMobile }">
        <v-item
          v-slot="{ isSelected, selectedClass, select }"
          :value="0">
          <v-card
            variant="outlined"
            :class="[ selectedClass, { 'border-opacity-100' : isSelected, 'pa-1' : isDesktop } ]"
            class="rounded-0"
            @click="select && select(true)">
            <v-card-text class="pa-1">
              <VRadio :model-value="isSelected">
                <template #label>
                  <h2 class="text-h2 mt-1">
                    {{ $t('shop.productSelection.addonSelection.none' )}}
                  </h2>
                </template>
              </VRadio>
            </v-card-text>
          </v-card>
        </v-item>
      </v-col>
      <v-col
        v-for="variant in addonVariants"
        :key="variant.sku"
        :class="{ 'v-col-12' : isMobile }">
        <v-item
          v-slot="{ isSelected, selectedClass, select }"
          :value="variant.sku">
          <v-card
            variant="outlined"
            :class="[ selectedClass, { 'border-opacity-100' : isSelected, 'pa-1' : isDesktop } ]"
            class="rounded-0"
            @click="select && select(true)">
            <v-card-text class="pa-1">
              <VRadio :model-value="isSelected">
                <template #label>
                  <h2 class="text-h2 mt-1">
                    {{ variant.name }}
                  </h2>
                </template>
              </VRadio>
            </v-card-text>
            <v-card-text>
              <p class="text-body-1">
                {{ variant.description }}
              </p>
            </v-card-text>
          </v-card>
        </v-item>
      </v-col>
    </v-row>
  </v-item-group>
</template>

<script setup lang="ts">
import { usePrecartStore } from '@/stores/precart-store'
import { watch } from 'vue'
import { useVuetifyDisplay } from '@bd/cd-system3'
import { useProductStore } from '@/stores/product-store'

const { isMobile, isDesktop } = useVuetifyDisplay()
const selected = defineModel<string | 0>({ default: 0 })

const { parentSku, addonVariants } = defineProps<{
  parentSku: string
  addonVariants: AddonVariant[]
}>()

const precartStore = usePrecartStore()
const productStore = useProductStore()

watch(selected, (newVal, oldVal) => {
  const addons = precartStore.getPrecartData(parentSku, 'addons') ?? []
  const newAddons = addons.filter(addon => addon !== oldVal)
  
  if (newVal) newAddons.push(newVal)
  precartStore.setPrecartData(parentSku, 'addons', newAddons)

  const basePrice = precartStore.getPrecartData(parentSku, 'price')?.value ?? 0;
  const quantity = precartStore.getPrecartData(parentSku, 'quantity') ?? 1;
  const baseTotal = basePrice * quantity;

  const addonsTotal = newAddons.reduce((total, addonSku) => {
    const addonPrice = productStore.getVariantBySku(addonSku)?.price?.value ?? 0;
    return total + addonPrice;
  }, 0);

  precartStore.setPrecartData(parentSku, 'calculatedPrice', baseTotal + addonsTotal)
})

watch(() => addonVariants, () => selected.value = 0)
</script>

<style lang="scss" scoped>
.v-selection-control {
  align-items: start;
}
.v-col {
  display: flex;
  align-self: stretch
}
.v-card {
  align-self: stretch;
  width: 100%;
}

</style>
