<template>
  <v-card
    :key="variant.sku"
    :border="props.selected ? 'm' : 'sm'"
    class="pa-3 rounded-0"
    variant="outlined"
    justify="center"
    :data-testid="`variant-${variant.sku}`"
    @click="hasAddons ? quantity = 1 : null"
  >
    <v-card-text class="pa-3">
      <template v-if="hasAddons && showRadios">
        <VRadio
          v-model="selected"
          density="compact"
        >
          <template #label>
            <span class="text-h2 ml-2">{{ variant.name }}</span>
          </template>
        </VRadio>
      </template>
      <template v-else>
        <h2 class="text-h2 d-flex align-center">
          {{ variant.name }}
        </h2>
      </template>
    </v-card-text>

    <v-card-text class="pa-3">
      <p class="text-body-1">
        {{ variant.description }}
      </p>
    </v-card-text>

    <v-card-text
      v-if="variant.price && !hasAddons"
      class="pa-3"
    >
      <p class="text-h1 mb-6">
        {{ formattedPrice.price }}
        <span
          v-if="formattedPrice.runtime"
          class="text-body-1"
        >
          {{ formattedPrice.runtime }}
        </span>
      </p>

      <p class="text-body-2">
        {{ pricingConditions }}
      </p>
    </v-card-text>

    <v-card-actions
      v-if="!hasAddons"
      class="pa-3"
    >
      <CDQuantity
        v-model.number="quantity"
        :data-id="variant.sku"
        :label="$t('shop.productSelection.variantSelection.variant.pricingSection.addButton')"
        style="width: 100%"
        :max="100"
      />
    </v-card-actions>

    <v-card-text class="pa-3">
      <v-list density="compact">
        <v-list-item
          v-for="feature in features"
          :key="feature"
          class="pl-0"
        >
          <template #prepend>
            <v-icon>$complete</v-icon>
          </template>
          <v-list-item-title class="text-wrap">
            {{ feature }}
          </v-list-item-title>
        </v-list-item>
      </v-list>
    </v-card-text>

    <v-card-text class="pa-3">
      <v-list
        density="compact"
        class="pa-0"
      >
        <v-list-item class="pa-0">
          <v-list-item-title class="text-body-2 mb-1">
            {{ $t('shop.productSelection.summary.termsAndConditions') }}
          </v-list-item-title>
          <v-list-item-action
            v-for="agreement in agreements"
            :key="agreement.name ?? undefined"
          >
            <a
              :href="agreement.url ?? undefined"
              class="d-flex mb-1"
              target="_blank"
            >
              {{ agreement.name }}
            </a>
          </v-list-item-action>
        </v-list-item>
      </v-list>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useFormattedPrice } from '@/composables/price'

const props = defineProps<{
  variant: Variant
  quantity: number
  selected: boolean,
  showRadios: boolean
}>()

const emit = defineEmits<{
  'variant-quantity-change': [CartQuantity]
}>()

const { t } = useI18n()
const { formattedPrice } = useFormattedPrice(() => props.variant)

const quantity = ref(props.quantity)
const selected = computed(() => props.selected)

const features = ref(props.variant.features.split('\n'))
const calculatedPrice = computed(() => quantity.value * props.variant.price.value)
const agreements = computed(() => props.variant.agreements)
const hasAddons = computed(() => props.variant.addons?.length > 0)
const pricingConditions = computed(() => {
  if (props.variant.licenseType === 'SUBSCRIPTION') {
    return t('shop.productSelection.summary.pricingConditions.subscription', {
      billingPeriod: t('shop.productSelection.summary.pricingConditions.duration.' + props.variant.runtime),
      noticePeriod: t('shop.productSelection.summary.pricingConditions.duration.' + props.variant.noticePeriod)
    })
  } else if (props.variant.licenseType === 'TRIAL') {
    return t('shop.productSelection.summary.pricingConditions.trial')
  } else {
    return ''
  }
})

// resets quantity to zero when selecting the 'other' card, only applies to products with addons
watch(selected, () => {
  if (hasAddons.value) {
    quantity.value = selected.value ? 1 : 0
  }
})

watch(quantity, () => {
  emit('variant-quantity-change', {
    quantity: quantity.value,
    sku: props.variant.sku,
    runtime: props.variant.runtime,
    price: props.variant.price,
    licenseType: props.variant.licenseType,
    calculatedPrice: calculatedPrice.value
  })
})
</script>

<style scoped lang="scss">
.v-card {
  display: grid;
  grid-row-end: span 6;
  grid-template-rows: subgrid;
  row-gap: 0;
}
:deep(.v-list-item__spacer) {
  width: 10px !important;
}
:deep(.v-list-item__prepend) {
  margin-top: 3px;
  height: 100%;
  .v-icon {
    align-self: baseline;
  }
}
:deep(.v-list-item__content) {
  align-self: baseline;
}
</style>
