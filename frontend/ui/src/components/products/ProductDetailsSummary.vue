<template>
  <div
    class="product-selection-summary"
  >
    <v-container
      class="pa-4"
    >
      <v-row>
        <v-col
          cols="12"
          md
        >
          <v-expansion-panels flat>
            <v-expansion-panel @group:selected="handleExpand">
              <v-expansion-panel-title
                class="pa-0"
                data-id="product-selection-summary-header"
                hide-actions
              >
                <v-row align="center">
                  <v-col cols="6">
                    <h3 class="text-h3 flex-grow-0">
                      {{ $t('shop.productSelection.summary.summary') }}
                      <template v-if="precart.length > 0">
                        ({{ precartQuantitiesTotal }})
                      </template>
                      <v-icon>
                        {{ expanded ? 'chevronup' : 'chevrondown' }}
                      </v-icon>
                    </h3>
                  </v-col>
                  <v-col
                    cols="6"
                    class="d-flex justify-end"
                  >
                    <span class="cd-text-large">
                      <template v-if="precartPricesTotal >= 0">
                        <span class="font-weight-bold mr-2">{{ $n(precartPricesTotal, 'currency', { currency: cartCurrency }) }}</span>
                      </template>
                    </span>
                  </v-col>
                </v-row>
              </v-expansion-panel-title>
              <v-expansion-panel-text class="mb-4">
                <p v-if="isPrecartEmpty">
                  {{ $t('shop.productSelection.summary.nothingSelectedInfo') }}
                </p>
                <v-row v-if="precartSummary.length > 0">
                  <v-col cols="12">
                    <ProductDetailsSummaryItem
                      v-for="(item, i) in precartSummary"
                      :key="i"
                      :product="item.product"
                      :variant="item.variant"
                      :item="item.item"
                    />
                  </v-col>
                </v-row>
              </v-expansion-panel-text>
            </v-expansion-panel>
          </v-expansion-panels>
        </v-col>

        <v-col
          cols="12"
          md="auto"
          class="d-flex"
        >
          <CDButton
            large
            color="primary"
            class="flex-grow-1"
            :disabled="isPrecartEmpty"
            data-id="product-selection-summary-addtocart"
            :loading="loading"
            @click="updateCart"
          >
            {{ $t('shop.productSelection.summary.addToCart') }}
          </CDButton>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script setup lang="ts">
import ProductDetailsSummaryItem from '@/components/products/ProductDetailsSummaryItem.vue'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { usePrecartStore } from '@/stores/precart-store'
import { useCartStore } from '@/stores/cart-store'

const router = useRouter()
const loading = ref(false)
const { precart, isPrecartEmpty, precartPricesTotal, precartQuantitiesTotal, clearPrecart, precartSummary } = usePrecartStore()
const { cartCurrency, addToCart } = useCartStore()

const expanded = ref(false)

function handleExpand () {
  expanded.value = !expanded.value
}

async function updateCart () {
  const products = precart.value
  try {
    loading.value = true
    await addToCart(products)
    await router.push('/cart')
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
    clearPrecart()
  }
}
</script>

<style scoped lang="scss">
:deep(.v-expansion-panel-text__wrapper) {
  padding: 0;
}

.product-selection-summary {
  background-color: rgb(var(--v-theme-surface));
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  border-top: 1px solid rgb(var(--v-theme-cd-stroke-separator));
  z-index: 1;
  grid-template-columns: auto auto;
}

.product-selection-summary-info {
  display: grid;
  grid-template-columns: 110px max-content auto;
  align-items: start;
  .name, .itemprice, .quantity {
    grid-column-start: 2;
  }
  .price {
    grid-column-start: 4;
    grid-row-start: 1;
  }
}

.v-expansion-panel-header {
  min-height: 48px;
  flex-wrap: wrap;
  gap: 8px;
}
</style>
