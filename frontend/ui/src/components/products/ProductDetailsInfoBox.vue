<template>
  <CDNotification v-if="variant?.entitlements.includes('BCCENTRAL')" color="info" class="mt-4 mb-12">
    <p>{{ $t('shop.productDetails.bccentralinfo.infotext') }}
      <a @click="showHelp = true">{{ $t('shop.productDetails.bccentralinfo.learnmore') }}</a></p>
    <div class="mt-2">
      <CDButton
variant="text" class="font-weight-bold" color="black pa-0"
      :href="variant?.priceList"
      target="_blank"
      >
        <CDIcon icon="download" class="mr-2" />
        {{ $t('shop.productDetails.bccentralinfo.download') }}
      </CDButton>
    </div>
  </CDNotification>
  <GenericDialog
    v-model="showHelp"
    :dialog-title="$t('shop.productDetails.bccentralinfo.helptitle')"
    :confirm-label="$t('shop.productDetails.bccentralinfo.helpclose')"
    :cancel-label="null"
    scrollable
    @confirm="showHelp = false"
  >
    <p style="white-space: pre-wrap;">{{ $t('shop.productDetails.bccentralinfo.helptext') }}</p>
  </GenericDialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import GenericDialog from '@/components/generic-partials/GenericDialog.vue'

const showHelp = ref(false)
defineProps<{
  variant?: Variant
}>()
</script>
<style scoped>
</style>
