<template>
  <v-row>
    <v-col
      cols="12"
      md="6"
      :data-testid="`product-${selectedProduct.id}`"
    >
      <v-img
        :src="selectedProduct.images[0]"
        cover
        :aspect-ratio="1/1"
      />
    </v-col>
    <v-col
      cols="12"
      md="6"
    >
      <v-row>
        <v-col cols="12">
          <h2 class="text-h1" data-testid="category-name">
            {{ category.name }}
          </h2>
        </v-col>
        <v-col cols="12">
          <p class="text-body-1">
            {{ category.description }}
          </p>
        </v-col>
        <v-col
          v-if="cheapestProduct"
          cols="12"
        >
          <p class="text-body-1 d-flex align-center flex-wrap">
            <span class="mr-4">
              {{ $t('shop.productDetails.productHeader.price') }} {{ formattedPrice?.price }} {{ formattedPrice?.runtime }}
            </span>
            <CDChip
              v-if="hasTrialVariant"
              color="success"
              size="large"
            >
              {{ $t('shop.productDetails.productHeader.chip') }}
            </CDChip>
          </p>
        </v-col>
        <v-col
          v-else-if="authStore.loggedIn"
          cols="12"
        >
          <v-skeleton-loader
            type="text"
            width="200px"
          />
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12">
          <v-row>
            <v-col
              v-for="product in category.products"
              :key="product.id"
              cols="6"
            >
              <v-card
                class="pa-3 rounded-0"
                variant="outlined"
                justify="center"
                :border="selectedProduct.id === product.id ? 'm' : 'sm'"
                @click="selectedProduct = product"
              >
                <v-card-title>
                  <h3 class="text-h3 text-center">
                    {{ product.name }}
                  </h3>
                </v-card-title>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12">
          <p class="text-body-1">
            {{ selectedProduct.description }}
          </p>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12">
          <CDButton
            :block="isMobile"
            color="primary"
            :loading="loading"
            data-testid="purchase-button"
            @click="$router.push(`/products/${selectedProduct.id}`)"
          >
            {{ $t('shop.productDetails.productHeader.purchaseButton') }}
          </CDButton>
        </v-col>
      </v-row>
    </v-col>
  </v-row>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useVuetifyDisplay } from '@bd/cd-system3'
import { useAuthStore } from '@/stores/auth-store'
import { useFormattedPrice } from '@/composables/price'

const loading = ref(false)
const { isMobile } = useVuetifyDisplay()

const props = defineProps<{
  category: Category
}>()

const selectedProduct = ref<Product>(props.category.products[0])
watch(() => props.category, () => selectedProduct.value = props.category.products[0])

const authStore = useAuthStore()

const cheapestProduct = computed(() => {
  return selectedProduct.value.variants.reduce((cheapest: Variant | undefined, product) => {
    if (!product?.price?.value) return cheapest
    if (!cheapest) return product
    return (product.price.value < cheapest.price.value) ? product : cheapest
  }, undefined)
})

const { formattedPrice } = useFormattedPrice(() => cheapestProduct.value)

const hasTrialVariant = computed(() => {
  return selectedProduct.value.variants.some(variant => variant.licenseType === 'TRIAL')
})
</script>
