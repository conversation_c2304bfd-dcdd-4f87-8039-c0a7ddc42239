{"navigation": {"breadcrumbs": {"root": "Marketplace", "route-cart": "<PERSON><PERSON>", "route-checkout": "Checkout", "route-checkout-success": "Confirmation", "route-subscriptions": "Subscriptions"}, "items": {"accountComponent": "Account", "deviceManagement": "Subscriptions", "getInTouch": "Get In Touch", "marketplaceComponent": "Marketplace", "storeLogin": "Log in", "storeSignOut": "Sign Out"}, "dialogs": {"confirm": "Confirm", "cancel": "Cancel"}, "tagline": "Marketplace powered by Bosch Digital Commerce GmbH"}, "footer": {"termsAndConditions": "Terms and Conditions", "privacyPolicy": "Data privacy notice", "privacySettings": "Privacy settings", "legalNote": "Legal notice", "corporateInformation": "Corporate information", "reportInfringement": "Report infringement"}, "quantity": "Quantity", "shop": {"cart": {"cart": "<PERSON><PERSON>", "checkout": "Checkout", "continueShopping": "Continue Shopping", "emptyCartMessage": "Your cart is currently empty", "emptyCartDescription": "You can purchase products by clicking on the button below", "consumptionDescription": "*Base fee. Billing will be calculated according to the {0}", "priceList": "price list"}, "checkout": {"confirmation": {"confirmationHeader": "Confirmation", "confirmationLine1": "Your order has been successfully placed.", "confirmationLine2": "The invoice with the payment details will be available soon.", "confirmationLine3": "Order number: {orderNumber}", "viewSubscriptionManagement": "View subscription management", "actionCard": {"title": "Important: Complete required actions to get started", "text": "Thank you for placing your order. Please complete the necessary actions now so we can set up your product and provide you with access. By clicking the button below, you will find the required actions at the top of the page.", "action": "Complete actions now"}}, "loading": "Your order is being processed ...", "header": "Secure checkout", "miniSummary": {"billingAddress": "Billing address", "ownPurchaseHeader": "Billing email", "totalPrice": "Total net price", "soldBy": "Licensed by: {companyName}", "agreement": {"SOFTWARE_LICENSE": "Accept all software license agreements listed below:", "PRIVACY_POLICY": "Accept the data privacy policies below:"}}, "backToCart": "Back to cart", "orderSummary": "Order summary", "paymentMethod": "Payment method", "placeOrder": "Place order", "notes": {"title": "Add internal notes to the invoice", "description": "Provide additional information such as internal purchase order number and project name. The notes will be displayed on the invoice.", "label1": "Note line 1 (optional)", "label2": "Note line 2 (optional)"}}, "error": {"checkout": {"paymentFailed": "There was an error with your payment. Please try again or use a different payment method."}, "product": {"notFound": "The product could not be found."}}, "payment": {"directdebit": {"name": "Direct debit", "new": "New SEPA Direct Debit"}, "transfer": {"name": "Bank transfer", "new": "SEPA Credit Transfer", "to": "Transfer to", "account": "Account Name", "bankname": "Bank Name", "bic": "BIC/SWIFT code", "iban": "IBAN"}, "achCreditTransfer": {"name": "Bank transfer", "new": "Bank Transfer", "to": "Transfer to", "accountHolder": "Account Holder", "bankName": "Bank Name", "routingNumber": "Routing Number", "accountNumber": "Account Number", "bic": "BIC/SWIFT code"}}, "productDetails": {"productHeader": {"purchaseButton": "Plans & Pricing", "chip": "Free trial available", "price": "from"}, "bccentralinfo": {"infotext": "BODAS Connect offers a flexible Pay-As-You-Grow pricing model, allowing you to scale costs with your fleet size. Your usage, based on connected devices, is tracked and billed monthly according to our price list.", "learnmore": "Learn more", "download": "Download price list", "helptitle": "Pay-As-You-Grow", "helpclose": "Close", "helptext": "With BODAS Connect, enjoy a flexible \"Pay-As-You-Grow\" pricing model. Start with a base fee and pay per connected asset, scaling effortlessly as your fleet expands. Access the full feature package and receive product updates. Your usage is tracked and billed monthly, with your ordered IoT services defining your subscription scope. Overconsumption is billed according to our price list, with automatic volume discounts. \n\nFor cloud services, the standard base fee includes all features and product updates, with the first 20 devices included. Cellular services are billed per device, with pooled data volume for the fleet. Overconsumption is charged on a per MB basis, ensuring no data loss (for Series 10 & 20). \n\nAdjust or terminate services monthly with one month's prior notice: Under \"Subscriptions\", you can view, adjust or terminate ongoing subscriptions anytime and change them up to 4 working days before end of the month. Downgrades & Upgrades are effective from next month while terminations are effective end of next month. \n\nThis model ensures you only pay for what you use, starting with a low base fee and adapting your plan as your needs evolve."}}, "productSelection": {"summary": {"nothingSelectedInfo": "To proceed, please add one or more product variants.", "summary": "Summary", "addToCart": "Add to cart", "termsAndConditions": "Terms and conditions", "pricingConditions": {"trial": "Free of charge as long as the subscription is provided. Both parties can cancel at any time.", "subscription": "Billed every {billingPeriod} at the start of each service period. Cancel {noticePeriod} before renewal.", "duration": {"P1Y": "1 year", "P3Y": "3 years", "P1M": "1 month", "P3M": "3 months", "P30D": "30 days"}}}, "variantSelection": {"runtime": {"descriptionDetails": {"SUBSCRIPTION|P1Y": "Renews annually, notice period 3 months", "SUBSCRIPTION|P3M": "Renews quarterly, notice period 1 month", "SUBSCRIPTION|P1M": "Renews monthly, notice period 1 month"}, "name": {"TRIAL|P1D": "Free trial", "TRIAL|P30D": "Free trial", "FULL|P3Y": "3-year contract", "FULL|": "One time purchase", "SUBSCRIPTION|P1Y": "Yearly", "SUBSCRIPTION|P3M": "Quarterly", "SUBSCRIPTION|P1M": "Monthly", "CONSUMPTION|P1Y": "Yearly", "CONSUMPTION|P3M": "Quarterly", "CONSUMPTION|P1M": "Monthly"}}, "variant": {"pricingSection": {"addButton": "Add"}, "titleBundles": "License bundle"}}, "addonSelection": {"none": "None"}}, "productsOverview": {"header": {"title": "Welcome to the marketplace for digital Rexroth solutions"}}}, "total": "Total", "errorMessages": {"authError": "An error occurred while authenticating. Please try again.", "cartCommunicationError": "The requested cart modification could not be performed. Please try again.", "cannotFetchOrders": "An error occurred while fetching orders. Please try again.", "cannotFetchPayments": "An error occurred while fetching payment data. Please try again.", "cannotFetchProducts": "An error occurred while fetching products. Please try again."}, "error": {"generic": "Something went wrong. Please try again.", "status": {"400": "The request was invalid and could not be processed.", "401": "You do not have sufficient permissions to access the requested resource.", "404": "The resource could not be found."}, "operations": {"fetchCategoryData": "An error occurred while fetching category data. Please try again.", "fetchCheckoutData": "An error occurred while fetching checkout data. Please try again.", "fetchContracts": "An error occurred while fetching contracts. Please try again.", "fetchLicenses": "An error occurred while fetching licenses. Please try again.", "fetchInvoices": "An error occurred while fetching invoices. Please try again."}}, "price": {"runtime": {"P1Y": "/year", "P3Y": "/3 years", "P1M": "/month", "P3M": "/3 months", "P30D": "/30 days", "P1D": "/1 day"}}, "invoices": {"title": "Invoices", "table": {"headings": {"invoiceId": "Invoice ID", "orderId": "Order ID", "invoiceDate": "Invoice Date", "status": "Status", "totalAmount": "Total amount", "download": ""}}, "status": {"ISSUED": "Issued"}}, "subscriptions": {"title": "Subscriptions", "validityActive": "Active subscription", "validityExpired": "Expired on {date}", "validityEnding": "Ends on {date}", "headsubscription": "Subscription", "headvalidity": "Validity", "headorder": "Order", "emptyTitle": "Get your first subscriptions", "emptyDescription": "Once you subscribe to a product on Rexroth Marketplace, you can manage your subscriptions here.", "emptyButton": "Browse Marketplace", "cancel": "Cancel subscription", "cancellationPending": "Cancellation requested", "canceldialog": {"title": "Cancel subscription", "cancel": "Keep subscription", "confirm": "Cancel subscription", "requested": "Subscription cancellation requested", "text": "You can cancel this subscription with a notice period of {noticePeriod} before the end of the current subscription period. When cancelling now, the projected end date of the subscription will be the {endDate}. You can use the product until then. After the end date of the subscription you will lose all access.\n\n Are you sure you want to proceed with cancelling the subscription?", "noticePeriod": {"P0D": "0 days", "P28D": "28 days", "P1M": "1 month", "P3M": "3 months", "P1Y": "1 year", "P3Y": "3 years"}}, "helpdialog": {"cancel": "Close", "confirm": "Assign subscriptions", "info": "If you are not the correct person to assign subscriptions, invite admin users to Rexroth Marketplace. They can manage and assign subscriptions via Subscription Management. Note: Admin users are not automatically assigned to any subscription.", "subtitle1": "1. Assign subscriptions to users", "subtitle2": "2. Access to Hydraulic Hub", "subtitle3": "3. Change subscription assignment", "text": "Assigning subscriptions to users is required, so the assigned users can subsequently get access and start using Hydraulic Hub. ", "text1": "Fill out the required fields (first name, last name, email address) to assign a subscription to a user.", "text2": "Once a subscription is successfully assigned, the respective users will be granted access and receive an email with instructions on how to get started.", "text3": "Admin users on Rexroth Marketplace can easily change/remove subscription assignments by clicking the more actions button (︙) of the desired subscription within Subscription Management.", "title": "Assign subscriptions to users"}, "notification": "Hydraulic Hub: Assign subscriptions to users to grant them access.", "notificationAssign": "Assign subscriptions", "notificationLearnMore": "Learn more", "notifications": {"hydraulicHub": {"text": "Hydraulic Hub: Assign subscriptions to users to grant them access.", "action": "Assign subscriptions", "learnMore": "Learn more"}, "bodasConnect": {"text": "BODAS Connect: Complete your setup by providing basic information to get started.", "action": "Complete BODAS Connect setup"}}, "assignment": {"description": "Assign your subscriptions to users by filling the required fields (first name, last name, and email address). Only the subscriptions with filled data will be assigned.", "firstname": "First name", "lastname": "Last name", "email": "Email", "title": "Assign subscriptions", "backbutton": "Back", "cancelbutton": "Cancel", "continuebutton": "Continue", "confirmbutton": "Assign", "stepperAssign": "Assignment", "stepperReview": "Review", "errorEmailRequired": "E-mail is required", "errorEmailInvalid": "Email must be valid", "errorEmailDuplicate": "Email is duplicate", "empty": "No subscriptions are available for assignment.", "dialog": {"success": "Subscriptions successfully assigned"}, "bodasConnect": {"title": "Complete actions now", "description": "Complete your setup by providing basic information to get started.", "name": "Portal name", "nameTooltip": "Only alphanumeric characters supported. Special characters (_#$*{'@'}) are not supported.", "email": "Admin email address", "errorNameRequired": "Portal name is required", "errorNameInvalid": "Special characters (_#$*{'@'}) not supported", "errorNameAlphabetical": "Only alphanumeric characters supported", "errorEmailRequired": "Admin email address is required", "errorEmailInvalid": "Email address must be valid", "dialog": {"success": "Success. Your cloud tenants will now be created in the next 6 working days. Once created, the login credentials will be sent to given email address."}}}, "assignedTo": "Assigned to", "unassign": "Remove assigned user", "unassignDialog": {"title": "Confirm removal of assigned user", "cancel": "Cancel", "confirm": "Remove assigned user", "text": "Are you sure you want to remove this user from the subscription? Once removed, the user will immediately lose access to the product and will no longer be able to use or manage it. The subscription will become available for assignment again.", "success": "Assigned user successfully removed"}}, "needHelp": {"dialog": {"title": "Support request", "name": "First name/Last name", "company": "Company", "customer": "Bosch Rexroth Customer number, if available", "address": "Address", "request": "Describe your request here", "cancel": "Cancel", "create": "Create email"}, "email": {"subject": "Request regarding Rexroth Marketplace", "body": "Dear Rexroth Marketplace Team,\n\nI would like to request support for Rexroth Marketplace for the product Hydraulic Hub.\n\nRequest:\n{request}\n\nFirst name/Last name: {name}\nCompany: {company}\nBosch Rexroth Customer number: {customer}\nAddress: {address}"}}, "countryLanguage": {"dialog": {"title": "Country & language", "confirm": "Apply", "cancel": "Cancel", "selectCountry": "Select your country", "selectLanguage": "Select your language"}}}