import { useAuthStore } from '@/stores/auth-store'
import type { Component } from 'vue'
import { type RouteRecordRaw, createRouter, createWebHistory } from 'vue-router'
import tenant from '@/../tenant.json'
import { fetchConfigurationData } from '@/composables/configuration'
import { fetchMeCompanyData } from '@/data/me.ts'
import { useCountryLanguageStore } from '@/stores/country-language-store'
import keycloak from '@/helpers/keycloak'

const routes = tenant.pageLayout.map((page) => {
  return {
    name: page.path.replace('/', 'route-'),
    path: page.path,
    redirect: 'redirect' in page ? page.redirect : undefined,
    component: page.components ? (): Component => import('@/pages/GenericPage.vue') : undefined,
    props: page.components ? { components: page.components } : undefined,
    meta: page.meta
  } as RouteRecordRaw
})

const router = createRouter({
  history: createWebHistory(),
  routes: [
    ...routes,
    {
      name: 'Login',
      path: '/login',
      beforeEnter: async (to) => {
        const authStore = useAuthStore()
        if (authStore.loggedIn) {
          return '/'
        }
        await authStore.login({ loginHint: to.query.login_hint ? String(to.query.login_hint) : undefined })
      },
      component: () => import('@/pages/GenericPage.vue')
    },
    {
      name: 'NotFound',
      path: '/:pathMatch(.*)*',
      redirect: '/'
    }
  ]
})

router.beforeEach(async (to, _from, next) => {
  const requiresLogin = to.meta.requiresLogin
  if (!requiresLogin) {
    return next()
  }
  const authStore = useAuthStore()
  if (authStore.loggedIn) {
    return next()
  }
  // at this point user is not logged in, but is required to be
  next(false)
  // prevent adding existing OIDC response parameters to the redirectUrl
  const toRoute = router.resolve({ ...to, hash: '' })
  const fullUrl = new URL(toRoute.href, window.location.origin).href
  await authStore.login({ redirectUri: fullUrl })
})

// Note: this navigation guard runs only once and unregisters itself during the initial navigation
const unregister = router.beforeEach(async () => {
  const tryFetch = async <T>(fetchFn: () => Promise<T>) => {
    try {
      return await fetchFn()
    } catch {
      return undefined
    }
  }

  const [configuration, company] = await Promise.all([
    tryFetch(() => fetchConfigurationData()),
    keycloak.client.value.authenticated ? tryFetch(() => fetchMeCompanyData()) : undefined
  ])

  if (configuration) {
    const countryLanguageStore = useCountryLanguageStore()

    const activeCountry = company?.country ?? configuration.defaultCountry
    const communicationLanguage = () => {
      const locale = keycloak.client.value.tokenParsed?.communication_language
      return locale ? new Intl.Locale(locale).language : undefined
    }
    const navigatorLanguage = () => {
      const country = countryLanguageStore.findCountry(activeCountry)
      const languages = country?.languages.map(l => l.code) ?? []
      return window.navigator.languages.find(language => languages.includes(language))
    }
    const activeLanguage = communicationLanguage() ?? navigatorLanguage()

    await countryLanguageStore.set(activeCountry, activeLanguage)
  }

  unregister()
})

router.isReady().then(async () => {
  // remove auth related query parameters after initial navigation
  const route = { ...router.currentRoute.value }
  await router.replace({ ...route, hash: '' })
})

export default router
