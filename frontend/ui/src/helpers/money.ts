  function calculateTotalPrice(mainPrice: Money, associatedPrices?: Money[]): Money {
    const associatedSumPrice = (associatedPrices ?? []).reduce((sum, price) => {
      if (price.currencyCode !== mainPrice.currencyCode) {
        throw new Error(`Currency mismatch: expected "${mainPrice.currencyCode}", got "${price.currencyCode}".`)
      }
      return sum + (price?.value ?? 0)
    }, 0)
  
    return {
      value: mainPrice.value + associatedSumPrice,
      currencyCode: mainPrice.currencyCode,
    }
  }
  
  export { calculateTotalPrice }