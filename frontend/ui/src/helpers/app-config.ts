type EnvironmentVariables = {
  K<PERSON><PERSON>CLOAK_URL?: string
  KEYCLOAK_CLIENTID?: string
  KEYCLOAK_REALM?: string
  STAGE?: string
  VERSION?: string
  API_URL?: string
  API_GRAPH_URL?: string
  USER_MANAGEMENT_URL?: string
  TRANSLATION_URL?: string
  CROWDIN_DISTRIBUTION_ID?: string
  CONTACT_URL?: string
  DOCK_DOMAIN?: string
  TERMS_AND_CONDITIONS_URL?: string
  PRIVACY_POLICY_URL?: string
  LEGAL_NOTE_URL?: string
  CORPORATE_INFORMATION_URL?: string
  REPORT_INFRINGEMENT_URL?: string
}

export type Environment = {
  keycloak: {
    url: string,
    clientId: string,
    realm: string,
  },
  stage: string
  version: string
  apiUrl: string
  apiGraphUrl: string
  userManagementUrl: string
  translationUrl: string
  crowdinDistributionId?: string
  contactUrl: string
  dockDomain: string
  footerUrls: {
    termsAndConditions: string
    privacyPolicy: string
    legalNote: string
    corporateInformation: string
    reportInfringement: string
  }
}

declare global {
  interface Window {
    configuration?: EnvironmentVariables;
  }
}

export default {
  keycloak: {
    url: window.configuration?.KEYCLOAK_URL ?? import.meta.env.VITE_LOCAL_KEYCLOAK_URL,
    clientId: window.configuration?.KEYCLOAK_CLIENTID ?? import.meta.env.VITE_LOCAL_KEYCLOAK_CLIENTID,
    realm: window.configuration?.KEYCLOAK_REALM ?? import.meta.env.VITE_LOCAL_KEYCLOAK_REALM
  },
  stage: window.configuration?.STAGE ?? 'LOCAL',
  version: window.configuration?.VERSION ?? '0.0.0',
  apiUrl: window.configuration?.API_URL ?? import.meta.env.VITE_LOCAL_API_URL,
  apiGraphUrl: window.configuration?.API_GRAPH_URL ?? import.meta.env.VITE_LOCAL_API_GRAPH_URL,
  userManagementUrl: window.configuration?.USER_MANAGEMENT_URL ?? 'https://accounts.mp-dc-d.com/my-profile',
  translationUrl: window.configuration?.TRANSLATION_URL ?? 'https://translations.store.twbd-dev.net',
  crowdinDistributionId: window.configuration?.CROWDIN_DISTRIBUTION_ID ?? 'e-15f5cc7f41ca56013cd73b33hc',
  contactUrl: window.configuration?.CONTACT_URL ?? 'mailto:<EMAIL>',
  dockDomain: window.configuration?.DOCK_DOMAIN ?? '',
  footerUrls: {
    termsAndConditions: window.configuration?.TERMS_AND_CONDITIONS_URL ?? 'https://legal.mp-dc-d.com/%country/terms.html',
    privacyPolicy: window.configuration?.PRIVACY_POLICY_URL ?? 'https://legal.mp-dc-d.com/%country/privacy-policy.html',
    legalNote: window.configuration?.LEGAL_NOTE_URL ?? 'https://legal.mp-dc-d.com/%country/legal-notice.html',
    corporateInformation: window.configuration?.CORPORATE_INFORMATION_URL ?? 'https://legal.mp-dc-d.com/%country/imprint.html',
    reportInfringement: window.configuration?.REPORT_INFRINGEMENT_URL ?? 'https://legal.mp-dc-d.com/%country/report-infringement.html'
  }
} satisfies Environment
