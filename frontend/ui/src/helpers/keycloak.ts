import {shallowRef, triggerRef} from 'vue'
import Keycloak from 'keycloak-js'
import appConfig from '@/helpers/app-config'

async function createKeycloak() {
  const keycloak = new Keycloak(appConfig.keycloak)
  const client = shallowRef(keycloak)

  keycloak.onAuthSuccess = () => triggerRef(client)
  keycloak.onAuthError = () => triggerRef(client)
  keycloak.onAuthLogout = () => triggerRef(client)
  keycloak.onTokenExpired = () => updateToken(keycloak)

  try {
    await keycloak.init({ onLoad: 'check-sso' })
  } catch (e) {
    console.error('Failed to initialize adapter:', e)
  }

  return {
    client,
    updateToken: () => updateToken(keycloak)
  }
}

async function updateToken(keycloak: Keycloak) {
  try {
    const refreshed = await keycloak.updateToken(30)
    if (!refreshed && !keycloak.token) {
      console.warn('Tried to refresh token, but no token available')
    }
  } catch (err) {
    if (!keycloak.authenticated) {
      await keycloak.login({ redirectUri: window.location.href })
    }
    console.warn('Failed to refresh token, or session has expired', err)
  }

  if (!keycloak.token) {
    throw new Error('no token')
  }

  return keycloak.token
}

const keycloak = await createKeycloak()

export default keycloak
