<template>
  <component
    :is="component.component"
    v-for="component in computedComps"
    v-bind="component.props"
    :key="component.name"
  />
</template>

<script setup lang="ts">
import type { ComputedRef, Component } from 'vue'
import { computed, defineAsyncComponent } from 'vue'
const comps = import.meta.glob('@/components/**/*.vue')
const props = defineProps<{
  components: UIComponent[]
}>()

const computedComps: ComputedRef<(UIComponent & {component: Component})[]> = computed(() => {
  return props.components.map((comp) => {
    return {
      ...comp,
      component: defineAsyncComponent(comps[`/src/components/${comp.name}.vue`] as () => Promise<Component>),
    }
  })
})

</script>
