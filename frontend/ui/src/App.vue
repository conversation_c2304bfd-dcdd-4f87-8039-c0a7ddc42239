<template>
  <v-app>
    <TheNavbar />
    <v-main>
      <router-view />
      <template v-if="!hideNeedHelp">
        <CDNeedHelpButton
          v-if="!hideNeedHelp"
          fixed
          data-testid="need-help-button"
          @click="openNeedHelpDialog"
        />
        <NeedHelpDialog v-model="needHelpDialog" />
      </template>
    </v-main>
    <TheFooter />
  </v-app>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useTitle } from '@vueuse/core'
import { useRouteMeta } from '@/composables/routing'
import { useCountryLanguageStore } from '@/stores/country-language-store'
import TheNavbar from '@/components/TheNavbar.vue'
import TheFooter from '@/components/TheFooter.vue'
import NeedHelpDialog from '@/components/NeedHelpDialog.vue'

const { hideNeedHelp } = useRouteMeta()
const needHelpDialog = ref(false)

function openNeedHelpDialog(e: MouseEvent) {
  e.preventDefault()
  needHelpDialog.value = true
}

const countryLanguageStore = useCountryLanguageStore()
useTitle(() => `Bosch Rexroth ${countryLanguageStore.findCountry(countryLanguageStore.activeCountry)?.name}`)
</script>
