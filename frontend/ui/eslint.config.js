import path from 'node:path'
import { fileURLToPath } from 'node:url'
import { includeIgnoreFile } from '@eslint/compat'
import pluginVue from 'eslint-plugin-vue'
import vueTsEslintConfig from '@vue/eslint-config-typescript'
import pluginVitest from '@vitest/eslint-plugin'
import plugin<PERSON>laywright from 'eslint-plugin-playwright'
import vueI18n from '@intlify/eslint-plugin-vue-i18n'
import skipFormatting from '@vue/eslint-config-prettier/skip-formatting'
import graphQl from '@graphql-eslint/eslint-plugin'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const gitignorePath = path.resolve(__dirname, '.gitignore')

export default [
  includeIgnoreFile(gitignorePath),

  {
    name: 'app/files-to-lint',
    files: ['**/*.{js,cjs,mjs,jsx,ts,cts,mts,tsx,vue}']
  },

  {
    name: 'app/files-to-ignore',
    ignores: ['**/dist/**', '**/dist-ssr/**', '**/coverage/**']
  },

  ...pluginVue.configs['flat/recommended'],
  ...vueTsEslintConfig(),

  {
    ...pluginVitest.configs.recommended,
    files: ['src/**/__tests__/*']
  },

  {
    ...pluginPlaywright.configs['flat/recommended'],
    files: ['e2e/**/*.{test,spec}.{js,ts,jsx,tsx}']
  },

  ...vueI18n.configs['flat/recommended'],
  {
    rules: {
      // disabling detection of inline text for now so it doesn't complain about small untranslated text like : or ,
      '@intlify/vue-i18n/no-raw-text': 'off'
    },
    settings: {
      'vue-i18n': {
        localeDir: 'src/locales/*.json',
        messageSyntaxVersion: '^9.0.0'
      }
    }
  },

  {
    files: ['**/*.ts'],
    processor: graphQl.processor
  },
  {
    ...graphQl.configs['flat/operations-recommended'],
    files: ['**/*.graphql'],
    languageOptions: {
      parser: graphQl.parser
    },
    plugins: {
      '@graphql-eslint': graphQl
    }
  },

  skipFormatting
]
