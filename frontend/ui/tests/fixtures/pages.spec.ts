import { test, expect } from '@fixtures'
import { users } from '../../playwright/users'

/* eslint-disable @typescript-eslint/no-unused-vars */

test.use({
  credentials: users['Manager German Buyer 1'],
  productCode: 'dummy-product-code'
})

test('cart-page fixture goes to cart page when authenticated', async ({ authentication, cartPage: page }) => {
  await expect(page).toHaveURL(/.*\/cart/)
})

test('product-selection-page fixture goes to product selection page', async ({ authentication, productSelectionPage: page }) => {
  await expect(page).toHaveURL(/.*\/products\/.*/)
})

test('products-overview-page fixture goes to products overview page', async ({ productsOverviewPage: page }) => {
  await expect(page).toHaveURL(/.*\//)
})

test('checkout-page fixture goes to checkout page', async ({ authentication, checkoutPage: page }) => {
  await expect(page).toHaveURL(/.*\/checkout/)
})
