import { test, expect } from '@fixtures'
import { users } from '@fixtures/auth'

/* eslint-disable @typescript-eslint/no-unused-vars */

test.use({ credentials: users['Manager German Buyer 1'] })

test('auth fixture authenticates', async ({ authentication, page }) => {
  await page.goto('/cart')
  await expect.soft(page).not.toBeLogin()
  await expect.soft(page).toHaveURL(/.*\/cart/)
})

test('auth fixture allows logging out', async ({ authentication, page }) => {
  await authentication.logout()
  await page.goto('/cart')
  await expect.soft(page).toBeLogin()
  await expect.soft(page).not.toHaveURL(/.*\/cart/)
})
