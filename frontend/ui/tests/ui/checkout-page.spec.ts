import { test, expect } from '@fixtures'
import { users } from '@fixtures/auth'
import { configurations } from '@fixtures/configuration-mock'
import { companies } from '@fixtures/me-mock'
import { carts, categories, checkout, products } from '@fixtures/graphql-mock'

/* eslint-disable @typescript-eslint/no-unused-vars */
test.describe('checkout page', () => {
  test.use({
    credentials: users['Manager German Buyer 1'],
    cart: carts['Test Cart 1'],
    configuration: configurations['Default configuration'],
    company: companies['Default company'],
    checkout: checkout['Checkout Data 1'],
    products: products['Test Products 1'],
    categories: categories['Test categories 1']
  })

  test('looks as expected', async ({ configurationMock, meMock, graphQlMock, authentication, checkoutPage: page }, testInfo) => {
    await page
        .locator('h3')
        .filter({ hasText: 'Hydraulic Hub' })
        .first()
        .waitFor()

    await expect(page).toHaveScreenshot({
      fullPage: true,
      mask: [page.getByTestId('navbar-logo')]
    })
  })
})
