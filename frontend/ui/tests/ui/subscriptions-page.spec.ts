import { test, expect } from '@fixtures'
import { users } from '@fixtures/auth'
import { configurations } from '@fixtures/configuration-mock'
import { companies } from '@fixtures/me-mock'
import { carts, categories, products, contracts, subscriptions, invoices, } from '@fixtures/graphql-mock'

/* eslint-disable @typescript-eslint/no-unused-vars */
test.describe('subscriptions overview', () => {
  test.use({
    credentials: users['Manager German Buyer 1'],
    products: products['Test Products 1'],
    categories: categories['Test categories 1'],
    cart: carts['Empty Cart'],
    configuration: configurations['Default configuration'],
    company: companies['Default company']
  })

  test('looks as expected', async ({ configurationMock, meMock, graphQlMock, authentication, subscriptionsPage: page }, testInfo) => {
    await graphQlMock.fulfill('AllContracts', contracts['Contract Data 1'])

    await page
      .locator('h5')
      .filter({ hasText: 'Hydraulic Hub' })
      .first()
      .waitFor()

    await expect(page).toHaveScreenshot({
      fullPage: true,
      mask: [page.getByTestId('navbar-logo')]
    })
  })

  test('complete bodas connect setup back and force', async ({ configurationMock, meMock, graphQlMock, authentication, subscriptionsPage: page }, testInfo) => {
    await graphQlMock.fulfill('AllContracts', contracts['Contract Data 1'])
    await graphQlMock.fulfill('Subscriptions', subscriptions['Subscriptions Data 1'])
    await graphQlMock.fulfill('Products', products['Test Products 2'])

    await page
      .locator('h5')
      .filter({ hasText: 'Hydraulic Hub' })
      .first()
      .waitFor()

    await page.locator('button:has-text("COMPLETE BODAS CONNECT SETUP")').click();
    // await page.locator('div:has-text("Assignment")').waitFor();
    await page.locator('div.v-stepper-item__title', { hasText: 'Assignment' }).waitFor();
    await page.locator('button', { hasText: 'CANCEL' }).click();
    await page.goto('/'); // testing bug BOSS-2835
    // await page.goto('/subscriptions');

    await page.locator('button', { hasText: 'SUBSCRIPTIONS' }).click();
    await page.locator('button:has-text("COMPLETE BODAS CONNECT SETUP")').click();
    await page.locator('div.v-stepper-item__title', { hasText: 'Assignment' }).waitFor();


    await expect(page).toHaveScreenshot({
      fullPage: true,
      mask: [page.getByTestId('navbar-logo')]
    })
  })
})

test.describe('subscriptions overview with cancellation menu', () => {
  test.use({
    credentials: users['Manager German Buyer 1'],
    products: products['Test Products 2'],
    categories: categories['Test categories 1'],
    cart: carts['Empty Cart'],
    configuration: configurations['Default configuration'],
    company: companies['Default company']
  })

  test('looks as expected', async ({ configurationMock, meMock, graphQlMock, authentication, subscriptionsPage: page }, testInfo) => {
    await graphQlMock.fulfill('AllContracts', contracts['Contract Data 2'])

    await page
      .locator('h5')
      .first()
      .waitFor()

    await expect(page).toHaveScreenshot({
      fullPage: true,
      mask: [page.getByTestId('navbar-logo')]
    })
  })
})

test.describe('subscriptions overview with cancellation dialog', () => {
  test.use({
    credentials: users['Manager German Buyer 1'],
    products: products['Test Products 2'],
    categories: categories['Test categories 1'],
    cart: carts['Empty Cart'],
    configuration: configurations['Default configuration'],
    company: companies['Default company'],
    contracts: contracts['Contract Data 2']
  })

  test('looks as expected', async ({ configurationMock, meMock, graphQlMock, authentication, subscriptionsPage: page }, testInfo) => {
    await page
      .locator('h5')
      .first()
      .waitFor()

    await page.locator('td.text-right >> button').first().click()
    await page.locator('div.v-overlay__content >> div.v-list-item').first().click()

    await expect(page).toHaveScreenshot({
      fullPage: true,
      mask: [page.getByTestId('navbar-logo')]
    })
  })
})

test.describe('empty subscriptions overview', () => {
  test.use({
    credentials: users['Manager German Buyer 1'],
    products: products['Test Products 1'],
    categories: categories['Test categories 1'],
    cart: carts['Empty Cart'],
    configuration: configurations['Default configuration'],
    company: companies['Default company'],
    contracts: contracts['No Contracts']
  })

  test('looks as expected', async ({ configurationMock, meMock, graphQlMock, authentication, subscriptionsPage: page }, testInfo) => {
    await expect(page).toHaveScreenshot({
      fullPage: true,
      mask: [page.getByTestId('navbar-logo')]
    })
  })
})

test.describe('empty invoices overview', () => {
  test.use({
    credentials: users['Manager German Buyer 1'],
    products: products['Test Products 1'],
    categories: categories['Test categories 1'],
    cart: carts['Empty Cart'],
    configuration: configurations['Default configuration'],
    company: companies['Default company'],
    contracts: contracts['No Contracts'],
    invoices: invoices['No Invoices'],
  })

  test('looks as expected', async ({ configurationMock, meMock, graphQlMock, authentication, subscriptionsPage: page }, testInfo) => {
    await page.locator('button[value="invoices"]').click()
    await expect(page).toHaveScreenshot({
      fullPage: true,
      mask: [page.getByTestId('navbar-logo')]
    })
  })
})


test.describe('invoices overview', () => {
  test.use({
    credentials: users['Manager German Buyer 1'],
    products: products['Test Products 1'],
    categories: categories['Test categories 1'],
    cart: carts['Empty Cart'],
    configuration: configurations['Default configuration'],
    company: companies['Default company'],
    invoices: invoices['Invoice Data 1'],
  })

  test('looks as expected', async ({ configurationMock, meMock, graphQlMock, authentication, subscriptionsPage: page }, testInfo) => {
    await page.locator('button[value="invoices"]').click()
    await page.waitForSelector('tr[data-testid="invoice-row"]', {state: 'visible'})

    await expect(page).toHaveScreenshot({
      fullPage: true,
      mask: [page.getByTestId('navbar-logo')]
    })
  })
})
