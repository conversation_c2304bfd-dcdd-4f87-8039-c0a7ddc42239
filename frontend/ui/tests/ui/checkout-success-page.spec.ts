import { test, expect } from '@fixtures'
import { users } from '@fixtures/auth'
import { configurations } from '@fixtures/configuration-mock'
import { companies } from '@fixtures/me-mock'
import { carts, categories, products, payments } from '@fixtures/graphql-mock'

/* eslint-disable @typescript-eslint/no-unused-vars */
test.describe('checkout success page', () => {
  test.use({
    credentials: users['Manager German Buyer 1'],
    cart: carts['Empty Cart'],
    configuration: configurations['Default configuration'],
    payment: payments['Test Payment 1'],
    company: companies['Default company'],
    paymentId: '3a33be55-facf-4623-9983-c941620b57b',
    products: products['Test Products 1'],
    categories: categories['Test categories 1']
  })

  test('looks as expected', async ({ configurationMock, meMock, graphQlMock, authentication, checkoutSuccessPage: page }, testInfo) => {
    await page
        .getByText('Your order has been successfully placed')
        .waitFor()

    await expect(page).toHaveScreenshot({
      fullPage: true,
      mask: [page.getByTestId('navbar-logo')]
    })
  })
})

test.describe('pending checkout success page', () => {
  test.use({
    credentials: users['Manager German Buyer 1'],
    cart: carts['Empty Cart'],
    configuration: configurations['Default configuration'],
    company: companies['Default company'],
    payment: payments['Initial Status Payment'],
    paymentId: '3a33be55-facf-4623-9983-c941620b57b',
    products: products['Test Products 1'],
    categories: categories['Test categories 1']
  })

  test('looks as expected', async ({ configurationMock, meMock, graphQlMock, authentication, checkoutSuccessPage: page }, testInfo) => {
    await page
        .getByText('Your order is being processed')
        .waitFor()

    await expect(page).toHaveScreenshot({
      fullPage: true,
      mask: [page.getByTestId('navbar-logo')]
    })
  })
})
