import { test, expect } from '@fixtures'
import { configurations } from '@fixtures/configuration-mock'
import { categories, products } from '@fixtures/graphql-mock'

/* eslint-disable @typescript-eslint/no-unused-vars */
test.describe('products overview', () => {
  test.use({
    configuration: configurations['Default configuration'],
    products: products['Test Products 1'],
    categories: categories['Test categories 1']
  })

  test('looks as expected', async ({ configurationMock, graphQlMock, productsOverviewPage: page }) => {
    await page
      .locator('h2')
      .filter({ hasText: 'Hydraulic Hub' })
      .waitFor()

    await expect(page).toHaveScreenshot({
      fullPage: true,
      mask: [page.getByTestId('navbar-logo')]
    })
  })
})
