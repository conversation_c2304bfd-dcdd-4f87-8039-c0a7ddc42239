import { test, expect } from '@fixtures'
import { users } from '@fixtures/auth'
import { configurations } from '@fixtures/configuration-mock'
import { companies } from '@fixtures/me-mock'
import { carts, categories, products } from '@fixtures/graphql-mock'

/* eslint-disable @typescript-eslint/no-unused-vars */
test.describe('product selection page', () => {
  test.use({
    credentials: users['Manager German Buyer 1'],
    cart: carts['Empty Cart'],
    configuration: configurations['Default configuration'],
    company: companies['Default company'],
    productCode: '6d8ab4be-e24b-473b-a6a5-f1228380b86c',
    products: products['Test Products 1'],
    categories: categories['Test categories 1']
  })

  test('looks as expected', async ({ configurationMock, meMock, graphQlMock, authentication, productSelectionPage: page }) => {
    await page
        .locator('h1')
        .filter({ hasText: 'Hydraulic Hub' })
        .waitFor()

    await expect(page).toHaveScreenshot({
      fullPage: true,
      mask: [page.getByTestId('navbar-logo')]
    })
  })
})
