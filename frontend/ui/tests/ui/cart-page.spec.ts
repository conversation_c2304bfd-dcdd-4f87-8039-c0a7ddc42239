import { test, expect } from '@fixtures'
import { users } from '@fixtures/auth'
import { configurations } from '@fixtures/configuration-mock'
import { companies } from '@fixtures/me-mock'
import { carts, categories, products } from '@fixtures/graphql-mock'

/* eslint-disable @typescript-eslint/no-unused-vars */
test.describe('cart page', () => {
  test.use({
    credentials: users['Manager German Buyer 1'],
    cart: carts['Cart with addons'],
    configuration: configurations['Default configuration'],
    company: companies['Default company'],
    products: products['Test Products 1'],
    categories: categories['Test categories 1']
  })

  test('looks as expected', async ({ configurationMock, meMock, graphQlMock, authentication, cartPage: page }, testInfo) => {
    await page
        .locator('h3')
        .filter({ hasText: 'Hydraulic Hub' })
        .first()
        .waitFor()

    await expect(page).toHaveScreenshot({
      fullPage: true,
      mask: [page.getByTestId('navbar-logo')]
    })
  })
})

test.describe('empty cart page', () => {
  test.use({
    credentials: users['Manager German Buyer 1'],
    cart: carts['Empty Cart'],
    configuration: configurations['Default configuration'],
    company: companies['Default company'],
    products: products['Test Products 1'],
    categories: categories['Test categories 1']
  })

  test('looks as expected', async ({ configurationMock, meMock, graphQlMock, authentication, cartPage: page }, testInfo) => {
    await page
        .getByText('Your cart is currently empty')
        .waitFor()

    await expect(page).toHaveScreenshot({
      fullPage: true,
      mask: [page.getByTestId('navbar-logo')]
    })
  })
})
