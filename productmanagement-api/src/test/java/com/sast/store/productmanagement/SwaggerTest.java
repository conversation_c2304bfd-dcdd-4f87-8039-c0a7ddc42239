package com.sast.store.productmanagement;

import org.junit.jupiter.api.Test;

import java.io.File;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class SwaggerTest {

    @Test
    void testOpenApiHasBeenGenerated() {
        final String buildDir = "./build/swagger";
        final List<File> files = Arrays.stream(new File(buildDir).listFiles()).toList();

        assertThat(files).isNotEmpty();
        assertThat(files).matches(list -> list.stream().anyMatch(file -> file.getName().endsWith("productmanagement-api.json")));
    }
}
