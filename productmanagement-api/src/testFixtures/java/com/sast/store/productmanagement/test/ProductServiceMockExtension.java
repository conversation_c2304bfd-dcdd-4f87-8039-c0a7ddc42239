package com.sast.store.productmanagement.test;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.common.ClasspathFileSource;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.github.tomakehurst.wiremock.junit5.WireMockExtension;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathTemplate;

public class ProductServiceMockExtension extends WireMockExtension {
    public static final int PORT = 34642;

    private static ProductServiceMockExtension instance;

    public ProductServiceMockExtension() {
        super(WireMockExtension
            .extensionOptions()
            .options(WireMockConfiguration.options()
                .fileSource(new ClasspathFileSource("productservice"))
                .port(PORT))
            .configureStaticDsl(true));
        instance = this;
    }

    public static ProductServiceMockExtension get() {
        return instance;
    }

    public static ProductServiceMockExtension withDefaultResponse() {
        withGetAllCategories();
        withGetAllProductsResponse();
        withGetProductResponse();
        withGetVariantResponse();
        return instance;
    }

    public static ProductServiceMockExtension withGetAllCategories() {
        instance.stubFor(WireMock.get(urlPathTemplate("/rest/categories/"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("get-all-categories-response.json")
                .withTransformers("response-template")));

        return instance;
    }

    public static ProductServiceMockExtension withGetAllProductsResponse() {
        instance.stubFor(WireMock.get(urlPathTemplate("/rest/products/"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("get-all-products-response.json")
                .withTransformers("response-template")));

        return instance;
    }

    public static ProductServiceMockExtension withGetProductResponse() {
        instance.stubFor(WireMock.get(urlPathTemplate("/rest/products/{id}"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("get-product-response.json")
                .withTransformers("response-template")));

        return instance;
    }

    public static ProductServiceMockExtension withGetProductServerErrorResponse(final int status) {
        instance.stubFor(WireMock.get(urlPathTemplate("/rest/products/{id}"))
            .willReturn(aResponse()
                .withStatus(status)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBody("{}")));

        return instance;
    }

    public static ProductServiceMockExtension withGetVariantResponse() {
        instance.stubFor(WireMock.get(urlPathMatching("/rest/products/variants/.*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("get-variant-response.json.hbs")
                .withTransformers("response-template")));

        return instance;
    }

    public static ProductServiceMockExtension withGetVariantResponse(final String entitlement) {
        instance.stubFor(WireMock.get(urlPathMatching("/rest/products/variants/.*"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("content-type", "application/json;charset=UTF-8")
                .withBodyFile("get-variant-response.json.hbs")
                .withTransformers("response-template")
                .withTransformerParameter("entitlements", "[\"%s\"]".formatted(entitlement))));

        return instance;
    }
}
