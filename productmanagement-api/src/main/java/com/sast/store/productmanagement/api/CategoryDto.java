package com.sast.store.productmanagement.api;

import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.util.List;

public record CategoryDto(
    @NotNull String categoryId,
    @NotNull String name,
    String description,
    String parentCategoryId,
    List<String> parentCategories,
    @NotNull BigDecimal order) {

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private String categoryId;
        private String name;
        private String description;
        private String parentCategoryId;
        private List<String> parentCategories;
        private BigDecimal order;

        public Builder categoryId(final String categoryId) {
            this.categoryId = categoryId;
            return this;
        }

        public Builder name(final String name) {
            this.name = name;
            return this;
        }

        public Builder description(final String description) {
            this.description = description;
            return this;
        }

        public Builder parentCategoryId(final String parentCategoryId) {
            this.parentCategoryId = parentCategoryId;
            return this;
        }

        public Builder parentCategories(final List<String> parentCategories) {
            this.parentCategories = parentCategories;
            return this;
        }

        public Builder order(final BigDecimal order) {
            this.order = order;
            return this;
        }

        public CategoryDto build() {
            return new CategoryDto(categoryId, name, description, parentCategoryId, parentCategories, order);
        }
    }

}
