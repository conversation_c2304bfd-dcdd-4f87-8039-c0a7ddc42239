package com.sast.store.productmanagement.api;

import com.sast.store.commons.tenant.api.Tenant;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import java.io.InputStream;
import java.util.List;
import java.util.Optional;

@Path("/products")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public interface ProductApi {

    @GET
    @Path("/")
    List<ProductDto> getAllProducts(@NotNull @HeaderParam("X-Tenant") Tenant tenantId,
        @QueryParam("language") @DefaultValue("de") String languageCode,
        @QueryParam("country") @DefaultValue("DE") String countryCode,
        @QueryParam("limit") @DefaultValue("100") Integer limit);

    @GET
    @Path("/{productId}")
    Optional<ProductDto> getProduct(@NotNull @HeaderParam("X-Tenant") Tenant tenantId,
        @PathParam("productId") String productId,
        @QueryParam("language") @DefaultValue("de") String languageCode,
        @QueryParam("country") @DefaultValue("DE") String countryCode);

    @GET
    @Path("/variants/{sku}")
    Optional<ProductDto> getProductVariant(@NotNull @HeaderParam("X-Tenant") Tenant tenantId,
        @NotNull @PathParam("sku") String sku, @QueryParam("language") @DefaultValue("de") String languageCode);

    @GET
    @Path("/")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    Response exportProducts(@NotNull @HeaderParam("X-Tenant") Tenant tenantId);

    @POST
    @Path("/")
    @Consumes(MediaType.APPLICATION_OCTET_STREAM)
    void importProducts(@NotNull @HeaderParam("X-Tenant") Tenant tenantId, InputStream inputStream);
}
