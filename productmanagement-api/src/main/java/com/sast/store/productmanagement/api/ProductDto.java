package com.sast.store.productmanagement.api;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.net.URI;
import java.time.Period;
import java.util.ArrayList;
import java.util.List;

public record ProductDto(
    String id,
    String name,
    String description,
    String productType,
    List<URI> images,
    List<Variant> variants,
    List<LocalizedLink> externalDocuments,
    Company sellerCompany,
    List<String> categories) {

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {
        private String id;
        private String name;
        private String description;
        private String productType;
        private List<URI> images;
        private List<Variant> variants;
        private List<LocalizedLink> externalDocuments;
        private Company sellerCompany;
        private List<String> categories;

        private Builder() {
        }

        public Builder id(final String id) {
            this.id = id;
            return this;
        }

        public Builder name(final String name) {
            this.name = name;
            return this;
        }

        public Builder description(final String description) {
            this.description = description;
            return this;
        }

        public Builder productType(final String productType) {
            this.productType = productType;
            return this;
        }

        public Builder images(final List<URI> images) {
            this.images = images;
            return this;
        }

        public Builder variants(final List<Variant> variants) {
            if (this.variants != null) {
                this.variants.addAll(variants);
            } else {
                this.variants = new ArrayList<>(variants);
            }
            return this;
        }

        public Builder externalDocuments(final List<LocalizedLink> externalDocuments) {
            this.externalDocuments = externalDocuments;
            return this;
        }

        public Builder sellerCompany(final Company sellerCompany) {
            this.sellerCompany = sellerCompany;
            return this;
        }

        public Builder categories(final List<String> categories) {
            this.categories = categories;
            return this;
        }

        public ProductDto build() {
            return new ProductDto(id, name, description, productType, images, variants, externalDocuments, sellerCompany, categories);
        }
    }

    public record Money(
        Double value,
        String currencyCode) {

        public static Builder builder() {
            return new Builder();
        }

        public static final class Builder {
            private Double value;
            private String currencyCode;

            private Builder() {
            }

            public Builder value(final Double value) {
                this.value = value;
                return this;
            }

            public Builder currencyCode(final String currencyCode) {
                this.currencyCode = currencyCode;
                return this;
            }

            public Money build() {
                return new Money(value, currencyCode);
            }
        }
    }

    public record Variant(
        String sku,
        String externalProductId,
        Money price,
        Long bundleAmount,
        String licenseType,
        @Schema(implementation = String.class) Period runtime,
        @Schema(implementation = String.class) Period noticePeriod,
        String name,
        String description,
        String features,
        List<LocalizedLink> agreements,
        URI priceList,
        List<String> entitlements,
        List<Addon> addons) {

        public static Builder builder() {
            return new Builder();
        }

        public static final class Builder {
            private String sku;
            private String externalProductId;
            private Money price;
            private Long bundleAmount;
            private String licenseType;
            private Period runtime;
            private Period noticePeriod;
            private String name;
            private String description;
            private String features;
            private List<LocalizedLink> agreements;
            private List<String> entitlements;
            private List<Addon> addons;
            private URI priceList;

            private Builder() {
            }

            public Builder sku(final String sku) {
                this.sku = sku;
                return this;
            }

            public Builder externalProductId(final String externalProductId) {
                this.externalProductId = externalProductId;
                return this;
            }

            public Builder price(final Money price) {
                this.price = price;
                return this;
            }

            public Builder bundleAmount(final Long bundleAmount) {
                this.bundleAmount = bundleAmount;
                return this;
            }

            public Builder licenseType(final String licenseType) {
                this.licenseType = licenseType;
                return this;
            }

            public Builder runtime(final Period runtime) {
                this.runtime = runtime;
                return this;
            }

            public Builder noticePeriod(final Period noticePeriod) {
                this.noticePeriod = noticePeriod;
                return this;
            }

            public Builder name(final String name) {
                this.name = name;
                return this;
            }

            public Builder description(final String description) {
                this.description = description;
                return this;
            }

            public Builder features(final String features) {
                this.features = features;
                return this;
            }

            public Builder agreements(final List<LocalizedLink> agreements) {
                this.agreements = agreements;
                return this;
            }

            public Builder priceList(final URI priceList) {
                this.priceList = priceList;
                return this;
            }

            public Builder entitlements(final List<String> entitlements) {
                this.entitlements = entitlements;
                return this;
            }

            public Builder addons(final List<Addon> addons) {
                this.addons = addons;
                return this;
            }

            public Variant build() {
                return new Variant(sku, externalProductId, price, bundleAmount, licenseType, runtime, noticePeriod, name, description,
                    features, agreements, priceList, entitlements, addons);
            }
        }
    }

    public record LocalizedLink(
        String name,
        @NotNull URI url,
        LinkType linkType) {

        public static Builder builder() {
            return new Builder();
        }

        public static final class Builder {
            private String name;
            private URI url;
            private LinkType linkType;

            private Builder() {
            }

            public Builder name(final String name) {
                this.name = name;
                return this;
            }

            public Builder url(final URI url) {
                this.url = url;
                return this;
            }

            public Builder linkType(final LinkType linkType) {
                this.linkType = linkType;
                return this;
            }

            public LocalizedLink build() {
                return new LocalizedLink(name, url, linkType);
            }
        }
    }

    public enum LinkType {
        SOFTWARE_LICENSE, PRIVACY_POLICY
    }

    public record Company(
        String name) {

        public static Builder builder() {
            return new Builder();
        }

        public static final class Builder {
            private String name;

            private Builder() {
            }

            public Builder name(final String name) {
                this.name = name;
                return this;
            }

            public Company build() {
                return new Company(name);
            }
        }
    }

    public record Addon(
        String name,
        String description,
        List<AddonVariant> addonVariants) {

        public static Builder builder() {
            return new Builder();
        }

        public static final class Builder {
            private String name;
            private String description;
            private List<AddonVariant> addonVariants;

            public Builder name(final String name) {
                this.name = name;
                return this;
            }

            public Builder description(final String description) {
                this.description = description;
                return this;
            }

            public Builder addonVariants(final List<AddonVariant> addonVariants) {
                this.addonVariants = addonVariants;
                return this;
            }

            public Addon build() {
                return new Addon(name, description, addonVariants);
            }
        }
    }

    public record AddonVariant(
        String sku,
        Money price,
        String name,
        String description) {

        public static Builder builder() {
            return new Builder();
        }

        public static final class Builder {
            private String sku;
            private Money price;
            private String name;
            private String description;

            private Builder() {
            }

            public Builder sku(final String sku) {
                this.sku = sku;
                return this;
            }

            public Builder price(final Money price) {
                this.price = price;
                return this;
            }

            public Builder name(final String name) {
                this.name = name;
                return this;
            }

            public Builder description(final String description) {
                this.description = description;
                return this;
            }

            public AddonVariant build() {
                return new AddonVariant(sku, price, name, description);
            }
        }
    }
}