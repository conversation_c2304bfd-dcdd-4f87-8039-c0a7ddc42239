package com.sast.store.productmanagement.api;

import com.sast.store.commons.tenant.api.Tenant;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import java.io.InputStream;

@Path("/prices")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public interface PriceApi {

    @GET
    @Path("/")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    Response exportPrices(@NotNull @HeaderParam("X-Tenant") Tenant tenantId);

    @POST
    @Path("/")
    @Consumes(MediaType.APPLICATION_OCTET_STREAM)
    void importPrices(@NotNull @HeaderParam("X-Tenant") Tenant tenantId, InputStream inputStream);
}
