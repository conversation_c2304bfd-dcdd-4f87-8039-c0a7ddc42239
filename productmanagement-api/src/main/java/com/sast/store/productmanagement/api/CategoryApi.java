package com.sast.store.productmanagement.api;

import com.sast.store.commons.tenant.api.Tenant;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;

import java.util.List;
import java.util.Optional;

@Path("/categories")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public interface CategoryApi {

    @GET
    @Path("/")
    List<CategoryDto> getAllCategories(
        @NotNull @HeaderParam("X-Tenant") Tenant tenantId,
        @QueryParam("language") Optional<String> languageCode,
        @QueryParam("limit") @DefaultValue("100") Integer limit);

    @GET
    @Path("/{categoryId}")
    Optional<CategoryDto> getCategory(
        @NotNull @HeaderParam("X-Tenant") Tenant tenantId,
        @NotNull @PathParam("categoryId") String categoryId,
        @QueryParam("language") Optional<String> languageCode);
}
